defmodule MisReports.Workers.BozReq.Schedule11d do
  alias MisReports.Workers.BalanceSheet

  def perform(item) do

   decoded_item =
      case item.schedule_11d do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    total = total_outstanding_amt(decoded_item)

    settings = MisReports.Utilities.get_comapany_settings_params()

    rate = MisReports.Utilities.get_exchange_rate_for_11d(item.end_date)

    %{
      "returnKey" => "ZM-8PSCH11D8P001",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1026_00001",
          "Value" => "#{rate.exchange_rate_lcy}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1026_00002",
          "Value" => "#{total}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 407,
          "_areaName" => "DEPOSITS INPUT SCHEDULE",
          "DynamicItems" => map_data(decoded_item)
        }
      ]
    }

  end

  def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->

        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => "#{map["cust_type"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => "#{map["economic_sector"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => "#{map["institutional_units_and_sectors"] || "0"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => "N/A", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => "#{map["deposit_type"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.6", "Value" => "#{map["type_of_time_deposit"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.7", "Value" => "#{convert_to_number(map["amount"]) || "N/A"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.8", "Value" => "#{map["currency"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.9", "Value" => "#{map["fl_desposits"] || "N/A"}", "_dataType" => "text"},
          %{"Code" => "#{index}.10", "Value" => "#{convert_to_number(map["amt_outstanding"]) || "N/A"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.11", "Value" => "#{map["economic_sub_sector"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.12", "Value" => "N/A", "_dataType" => "TEXT"}

        ]
      end)
  end

  def total_outstanding_amt(items) do
    formatted_values =
      items
      |> Enum.map(fn item ->
        value = convert_to_number(item["amt_outstanding"])
        value
      end)
    Enum.reduce(formatted_values, Decimal.new(0), &get_sum/2)
  end


  def get_sum(acc, amt) do
    amt_decimal = if is_nil(amt), do: Decimal.new(0), else: amt
    Decimal.add(acc, amt_decimal)
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    case Integer.parse(value) do
      {int_value, ""} -> Decimal.new(int_value)
      _ -> Decimal.new(String.to_float(value))
    end
  rescue
    # Handle any parsing errors
    _ -> Decimal.new(0)
  end

  defp convert_to_number(_), do: Decimal.new(0)
end
