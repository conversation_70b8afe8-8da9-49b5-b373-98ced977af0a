defmodule MisReportsWeb.AdjustmentsLive.AdjustmentsComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo}
  alias MisReports.{Utilities, Utilities.Adjustments}
  alias MisReportsWeb.UserController

@impl true
def render(assigns) do
  # IO.inspect(assigns)
  ~H"""
  <div>
  <%= case @action do %>
    <% action when action in [:new, :edit] -> %>
    <%= Phoenix.View.render(MisReportsWeb.AdjustmentsView , "adjustment.html", assigns) %>
    <% :update_status -> %>
      <%= Phoenix.View.render(MisReportsWeb.AdjustmentsView , "approve_new.html", assigns) %>
    <% _ -> %>
      <div class="text-red-600">Invalid action</div>
  <% end %>
  </div>
  """
end

@impl true
def update(assigns, socket) do
  adjustments =
    case assigns[:reference] do
      nil -> []
      reference ->
        Utilities.get_adjustments_reference!(reference)
    end

  IO.inspect(assigns[:reference], label: "============REFERENCE IN assigns===========")

  IO.inspect(adjustments, label: "============adjustments===========")

  # changeset = Utilities.change_adjustments(List.first(adjustments))
  changeset = Utilities.change_adjustments(List.first(adjustments))

  socket =
    socket
    |> assign(assigns)
    |> assign(:changeset, changeset)
    |> assign(:adjustments, adjustments)
    |> assign(:selected_adjustment, nil)
    |> assign(:schedule_content, nil)
    |> assign(:schedule_name, nil)
    |> assign(:columns, [])

  {:ok, socket}
end

@impl true
def handle_event("validate", %{"adjustment" => adjustment}, socket) do
  changeset =
    socket.assigns.adjustment
    |> Adjustments.changeset(adjustment)
    |> Map.put(:action, :validate)

  {:noreply, assign(socket, :changeset, changeset)}
end

@impl true
def handle_event("change_state_debit", %{"adjustment" => params}, socket) do
  cur_cat_debit_state = if(params["type_debit"] == "GUT", do: false, else: true)
  business_unit_debit_state = if(params["type_debit"] == "GUT", do: false, else: true)

  # Only update debit-related states
  {:noreply,
   assign(socket,
     cur_cat_debit_state: cur_cat_debit_state,
     business_unit_debit_state: business_unit_debit_state
   )
  }
end

@impl true
def handle_event("change_state_credit", %{"adjustment" => params}, socket) do
  cur_cat_credit_state = if(params["type_credit"] == "GUT", do: false, else: true)
  business_unit_credit_state = if(params["type_credit"] == "GUT", do: false, else: true)

  # Only update credit-related states
  {:noreply,
   assign(socket,
     cur_cat_credit_state: cur_cat_credit_state,
     business_unit_credit_state: business_unit_credit_state
   )
  }
end

@impl true
  def handle_event("select_double_adjustment", %{"value" => adjustment_id}, socket) do
    case Enum.find(socket.assigns.adjustments, &(&1.id == String.to_integer(adjustment_id))) do
      nil ->
        {:noreply, socket}

      adjustment ->
        lines = adjustment.adjustment_lines || "[]"
        decoded_lines =
          case Jason.decode(lines) do
            {:ok, parsed} -> parsed
            _ -> []
          end

        columns = Enum.map(decoded_lines, fn i ->
          %{
            amount: i["amount"],
            credit: i["credit"],
            credit_col: i["credit_col"],
            debit: i["debit"],
            debit_col: i["debit_col"],
            type_credit: i["type_credit"],
            cur_cat_credit: i["cur_cat_credit"],
            business_unit_credit: i["business_unit_credit"],
            business_unit_credit_col: i["business_unit_credit_col"],
            cur_cat_credit_col: i["cur_cat_credit_col"],
            type_debit: i["type_debit"],
            cur_cat_debit: i["cur_cat_debit"],
            business_unit_debit: i["business_unit_debit"],
            business_unit_debit_col: i["business_unit_debit_col"],
            cur_cat_debit_col: i["cur_cat_debit_col"]
          }
        end)

      IO.inspect(adjustment, label: "===========adjustment=============")

        {:noreply,
         socket
         |> assign(:selected_adjustment, adjustment)
         |> assign(:columns, columns)
         |> assign(:changeset, Utilities.change_adjustments(adjustment))}
    end
  end


@impl true
def handle_event("select_double_adjustment", %{"_target" => ["double_adjustments"], "double_adjustments" => id}, socket) do
  handle_event("select_double_adjustment", %{"value" => id}, socket)
end


def handle_event("add-column", %{"adjustment" => params}, socket) do
  debit_column = MisReports.Mappings.gl_list(params["debit"])
  credit_column = MisReports.Mappings.gl_list(params["credit"])
  temp_cols = Enum.reject(socket.assigns.columns, &(&1.debit == params["debit"] and &1.credit == params["credit"]))
  columns = [
              %{
                # debit: debit_value,
                debit: params["debit"],
                debit_col: debit_column,
                # credit: credit_value,
                credit: params["credit"],
                credit_col: credit_column,
                amount: params["amount"],
                type_credit: params["type_credit"],
                cur_cat_credit: params["cur_cat_credit"],
                business_unit_credit: params["business_unit_credit"],
                business_unit_credit_col: if(params["business_unit_credit"] == "C", do: "Corporate and Investment Banking", else: if(params["business_unit_credit"] == "R", do: "Personal and Business Banking", else: "")),
                cur_cat_credit_col: if(params["cur_cat_credit"] == "F", do: "Foreign Currency", else: if(params["cur_cat_credit"] == "L", do: "Local Currency", else: "")),
                type_debit: params["type_debit"],
                cur_cat_debit: params["cur_cat_debit"],
                business_unit_debit: params["business_unit_debit"],
                business_unit_debit_col: if(params["business_unit_debit"] == "C", do: "Corporate and Investment Banking", else: if(params["business_unit_debit"] == "R", do: "Personal and Business Banking", else: "")),
                cur_cat_debit_col: if(params["cur_cat_debit"] == "F", do: "Foreign Currency", else: if(params["cur_cat_debit"] == "L", do: "Local Currency", else: "")),
              }
              ] ++ temp_cols

  {:noreply, assign(socket, :columns, columns)}

end



@impl true
def handle_event("remove-column", params, socket) do

  temp_cols = socket.assigns.columns
              |> Enum.filter(fn data -> data[:debit] != params["gl"] end)

  {:noreply,
   socket
   |> assign(:columns, temp_cols)}
end

def handle_event("save", params, socket) do
  socket = assign(socket, :process_id, socket.assigns.process_id)
  |> assign(:reference, socket.assigns.reference)
  |> assign(:step_id, socket.assigns.step_id)
  adjustment_lines = Poison.encode!(socket.assigns.columns)
  params = Map.merge(socket.assigns.changeset.changes, %{adjustment_lines:  adjustment_lines})
  handle_save(socket, socket.assigns.action, params)
end

@impl true
  def handle_event("save", %{"action" => action} = params, socket) do

    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    params = for {k, v} <- params, into: %{}, do: {to_string(k), v} # Normalize keys

    current_user_id = to_string(socket.assigns.current_user.id)
    user_id = socket.assigns.current_user.id
    type = Map.get(params, "type", "DOUBLE") # Default to DOUBLE if not specified
    audit_msg = "Created #{type} Adjustments for report date \"#{params["report_date"]}\""

    # Check if there is any record with status INITIAL_RECORD
    initial_record = Utilities.check_status(socket.assigns.reference)

    IO.inspect(initial_record, label: "============initial_record===========")

    multi = cond do
      initial_record && is_nil(initial_record.adjustment_lines) ->
        string_initial_record = for {k, v} <- initial_record, into: %{}, do: {to_string(k), v}
        filtered_params = Map.drop(params, ["report_date", "reference", "status"])
        merged_data = Map.merge(string_initial_record, filtered_params)
        changeset = Adjustments.changeset(%Adjustments{id: initial_record.id}, merged_data)

        Ecto.Multi.new()
        |> Ecto.Multi.update(:adjustment, changeset)
        |> UserController.audit_log(user_id, "Updated #{type} Adjustments for existing initial record")

      initial_record && !is_nil(initial_record.adjustment_lines) ->
        new_params = params
        |> Map.put("report_date", initial_record.report_date)
        |> Map.put("reference", socket.assigns.reference)

        Ecto.Multi.new()
        |> Ecto.Multi.insert(:adjustment, Adjustments.changeset(%Adjustments{maker_id: user_id}, new_params))
        |> UserController.audit_log(user_id, audit_msg)

      true ->
        Ecto.Multi.new()
        |> Ecto.Multi.insert(:adjustment, Adjustments.changeset(%Adjustments{maker_id: user_id}, params))
        |> UserController.audit_log(user_id, audit_msg)
    end

    IO.inspect(socket.assigns.reference, label: "============REFERENCE IN handle_save===========")
    IO.inspect(socket.assigns.step_id, label: "============STEP_ID IN handle_save of adjustments===========")
    step_id = socket.assigns.step_id

    multi
    |> Repo.transaction()
    |> case do
      {:ok, %{adjustment: adjustment}} ->
        redirect_path = if type == "SINGLE",
          do: Routes.insertion_index_path(socket, :new,
          reference: socket.assigns.reference, step_id: step_id, process_id: socket.assigns.process_id),
          else: Routes.adjustments_index_path(socket, :new,
          reference: socket.assigns.reference, step_id: step_id, process_id: socket.assigns.process_id)

        {:noreply,
         socket
         |> put_flash(:info, "#{type} Adjustment processed successfully.")
         |> push_redirect(to: redirect_path)}

      {:error, _operation, changeset, _changes} ->
        {:noreply,
         socket
         |> assign(:changeset, changeset)
         |> put_flash(:error, "Failed to process adjustment")}
    end
  end


def handle_save(socket, :reject, params) do
  reference = socket.assigns.reference
  current_user_id = to_string(socket.assigns.current_user.id)
  comment = "Adjustments Rejected"
  process_id = socket.assigns.process_id

  case MisReports.Workflow.call_workflow(
         reference,
         process_id,
         current_user_id,
         96,
         "",
         "",
         comment
       ) do
    {:ok, reference_number} ->
      {:noreply,
       socket
       |> put_flash(:info, "Adjustments rejected successfully. Reference: #{reference_number}")
       |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

    {:error, reason} ->
      {:noreply,
       socket
       |> put_flash(:error, "Failed to reject business unit: #{reason}")
       |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
  end
end

def handle_save(socket, :approve, params) do
  adjustment = socket.assigns.adjustment
  current_user = socket.assigns.current_user
  reference = socket.assigns.reference
  process_id = socket.assigns.process_id
  comment = "Adjustments Approval"

  Ecto.Multi.new()
  |> Ecto.Multi.update(
    :update,
    Adjustments.changeset(adjustment, %{
      status: "A",
      checker_id: current_user.id,
      checker_date: NaiveDateTime.utc_now()
    })
  )
  |> Repo.transaction()
  |> case do
    {:ok, %{update: updated_adjustment}} ->
      case MisReports.Workflow.call_workflow(
             reference,
             process_id,
             to_string(current_user.id),
             97,
             "",
             "",
             comment
           ) do
        {:ok, reference_number} ->
          {:noreply,
           socket
           |> put_flash(:info, "Adjustments approved successfully. Reference: #{reference_number}")
           |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

        {:error, reason} ->
          {:noreply,
           socket
           |> put_flash(:error, "Adjustments approved but workflow failed: #{reason}")
           |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
      end

    {:error, _failed_operation, failed_value, _changes_so_far} ->
      {:noreply,
       socket
       |> put_flash(:error, "Failed to approve the adjustment")
       |> assign(:changeset, %{adjustment.changeset | errors: failed_value.errors})}
  end
end

def handle_save(socket, :edit, params) do
  adjustment = socket.assigns.adjustment
  socket
  |> handle_update(params, adjustment)
  |> case do
    {:ok, adjustment} ->
      {:noreply, socket
      |> assign(:initial_record_created, true)  # Mark as handled
      |> put_flash(:info, "Adjustments updated successfully")
      |> push_redirect(to: Routes.adjustments_index_path(socket, :edit, adjustment))}

    {:error, %Ecto.Changeset{} = changeset} ->
      {:noreply, assign(socket, changeset: changeset)}
  end
 end

def handle_update(socket, params, adjustment) do
  audit_msg = "Updated Adjustments for report date \"#{params["report_date"]}\""

  Ecto.Multi.new()
  |> Ecto.Multi.update(:adjustment, Adjustments.changeset(adjustment, params))
  |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
  |> Repo.transaction()
  |> case do
    {:ok, %{adjustment: adjustment, audit_log: _user_log}} ->
      {:ok, adjustment}

    {:error, _failed_operation, failed_value, _changes_so_far} ->
      {:error, failed_value}
  end
end




end
