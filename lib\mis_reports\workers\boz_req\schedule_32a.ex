defmodule MisReports.Workers.BozReq.Schedule32a do


  def perform(item) do

   decoded_item =
      case item.schedule_32a do
        nil -> %{}  # Handle nil case
        content ->
case Poison.decode(content) do
  {:ok, decoded} ->
    IO.inspect(decoded, label: "Raw Decoded JSON")
    decoded
  {:error, error} ->
    IO.inspect(error, label: "JSON Decode Error")
    %{}
end

      end



    settings = MisReports.Utilities.get_comapany_settings_params()



    %{
      "returnKey" => "ZM-9MSCH32A9M002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
[
  %{
    "Code" => "1191_00002",
    "Value" => "#{decoded_item["K13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00003",
    "Value" => "#{decoded_item["L13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00004",
    "Value" => "#{decoded_item["M13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00005",
    "Value" => "#{decoded_item["N13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00006",
    "Value" => "#{decoded_item["O13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00007",
    "Value" => "#{decoded_item["P13"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00008",
    "Value" => "#{decoded_item["K14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00009",
    "Value" => "#{decoded_item["L14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00010",
    "Value" => "#{decoded_item["M14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00011",
    "Value" => "#{decoded_item["N14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00012",
    "Value" => "#{decoded_item["O14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00013",
    "Value" => "#{decoded_item["P14"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00014",
    "Value" => "#{decoded_item["K15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00015",
    "Value" => "#{decoded_item["L15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00016",
    "Value" => "#{decoded_item["M15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00017",
    "Value" => "#{decoded_item["N15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00018",
    "Value" => "#{decoded_item["O15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00019",
    "Value" => "#{decoded_item["P15"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00020",
    "Value" => "#{decoded_item["K16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00021",
    "Value" => "#{decoded_item["L16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00022",
    "Value" => "#{decoded_item["M16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00023",
    "Value" => "#{decoded_item["N16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00024",
    "Value" => "#{decoded_item["O16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00025",
    "Value" => "#{decoded_item["P16"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00026",
    "Value" => "#{decoded_item["K17"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00027",
    "Value" => "#{decoded_item["L17"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00028",
    "Value" => "#{decoded_item["M17"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00029",
    "Value" => "#{decoded_item["N17"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00030",
    "Value" => "#{decoded_item["O17"] || "0"}",
    "_dataType" => "NUMERIC"
  },
  %{
    "Code" => "1191_00031",
    "Value" => "#{decoded_item["P17"] || "0"}",
    "_dataType" => "NUMERIC"
  }
]
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 464,
          "_areaName" => "OTHER BORROWED FUNDS (MATURITY PROFILE)",
          "DynamicItems" => map_data(decoded_item)
        }
      ]
    }

  end

  def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->

        index = index + 1
        [
%{"Code" => "#{index}.1", "Value" => "#{map["Name of Lender"] || "N/A"}", "_dataType" => "TEXT"},
%{"Code" => "#{index}.2", "Value" => "#{map["Type of Borrowing"] || "N/A"}", "_dataType" => "TEXT"},
%{"Code" => "#{index}.3", "Value" => "#{convert_to_number(map["0 - 6 Months"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.4", "Value" => "#{convert_to_number(map["6 - 12 Months"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.5", "Value" => "#{convert_to_number(map["1 - 3 Years"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.6", "Value" => "#{convert_to_number(map["3 - 5 Years"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.7", "Value" => "#{convert_to_number(map["Over 5 Years"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.8", "Value" => "#{convert_to_number(map["TOTAL"]) || "N/A"}", "_dataType" => "NUMERIC"}


        ]
      end)
  end





  def total_outstanding_amt(items) do
    formatted_values =
      items
      |> Enum.map(fn item ->
        value = convert_to_number(item["TOTAL"])
        value
      end)
    Enum.reduce(formatted_values, Decimal.new(0), &get_sum/2)
  end


  def get_sum(acc, amt) do
    amt_decimal = if is_nil(amt), do: Decimal.new(0), else: amt
    Decimal.add(acc, amt_decimal)
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    case Integer.parse(value) do
      {int_value, ""} -> Decimal.new(int_value)
      _ -> Decimal.new(String.to_float(value))
    end
  rescue
    # Handle any parsing errors
    _ -> Decimal.new(0)
  end

  defp convert_to_number(_), do: Decimal.new(0)
end
