defmodule MisReportsWeb.ExchangePlacementLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo, Utilities}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities.ExchangePlacement
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       action: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do

      socket =
      socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

       {:noreply,
         socket
         |> apply_action(socket.assigns.live_action, params)}
     else
       UserLiveAuth.unauthorized(socket)
     end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :exchange_placement, %ExchangePlacement{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    exchange_placement = Utilities.get_exchange_placement!(id)
    changeset = Utilities.change_exchange_placement(exchange_placement)

    socket
    |> assign(:exchange_placement, exchange_placement)
    |> assign(:changeset, changeset)
    |> assign(:action, :edit)
  end

  defp apply_action(socket, :index, _params), do: list_exchange_placement(socket)

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        exchange_placement = Utilities.get_exchange_placement_by_reference!(reference)

        socket
        |> assign(:exchange_placement, exchange_placement)
        |> assign(:changeset, Utilities.change_exchange_placement(exchange_placement))
        # Explicitly reassign reference
        |> assign(:reference, reference)
    end
  end

  defp list_exchange_placement(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_tbl_fcy_exchange_placement_inst(params)
        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_exchange_placement()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_exchange_placement()}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_exchange_placement()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:institution_name, :rating, :rating_agency, :report_date, :inserted_at, :maker_id], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_exchange_placement()}
  end

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    exchange_placement = Utilities.get_exchange_placement!(id)
    audit_msg = "Changed status for Exchange Placement with ID: #{id} to: #{status}"
    current_user = socket.assigns.current_user
    IO.inspect(current_user.id, label: "Current User")
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      ExchangePlacement.changeset(exchange_placement, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _secure_holding, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.exchange_placement_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()
        {:noreply,
        socket
        |> put_flash(:error, reason)
        |> push_redirect(
          to: Routes.exchange_placement_index_path(socket, :index)
        )}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    exchange_placement = Utilities.get_exchange_placement!(id)
    audit_msg = "Deleted Exchange Placement with ID: #{id}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_exchange_placement, exchange_placement)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
    {:ok, %{del_exchange_placement: _loan_sector, audit_log: _audit_log}} ->
      {:noreply,
      socket
      |> put_flash(:info, "Record Deleted successfully!")
      |> push_redirect(to: Routes.exchange_placement_index_path(socket, :index))}

    {:error, failed_value} ->
      {:error, failed_value}
    end
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"exchange_placement", "new"}

      act when act in ~w(edit)a ->
        {"exchange_placement", "edit"}

      act when act in ~w(update_status)a ->
        {"exchange_placement", "update_status"}

      act when act in ~w(delete)a ->
        {"exchange_placement", "delete"}

      act when act in ~w(index)a ->
        {"exchange_placement", "index"}

      _ ->
        {"exchange_placement", "unknown"}
    end
  end



end
