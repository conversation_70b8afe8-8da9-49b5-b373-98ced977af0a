defmodule MisReportsWeb.LoanClassificationLive.LoanClassificationComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo}
  alias MisReports.{Prudentials, Prudentials.LoanClassifications}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.LoanClassificationView , "loan_classification.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.LoanClassificationView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{loan_classification: loan_classification} = assigns, socket) do
    changeset = Prudentials.change_loan_classification(loan_classification)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"loan_classification" => loan_classification}, socket) do
    changeset =
      socket.assigns.loan_classification
      |> LoanClassifications.changeset(loan_classification)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"loan_classification" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_approval(socket, :reject, params)
      "97" -> handle_approval(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg = "Created Loan Classification  \"#{params["loan_classification"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:loan_classification, LoanClassifications.changeset(%LoanClassifications{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
        {:ok, %{loan_classification: loan_classification}} ->
          case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Loan classification Creation"
             ) do
          {:ok, reference_number} ->
            case Prudentials.update_loan_classification(loan_classification, %{reference: reference_number}) do
              {:ok, updated_loan_classification} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Loan classification created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update loan classification reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Loan classification created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

        {:error, %Ecto.Changeset{} = changeset} ->
          {:noreply, assign(socket, changeset: changeset)}
      end
  end

  def handle_save(socket, :edit, params) do
    loan_classification = socket.assigns.loan_classification
    socket
    |> handle_update(params, loan_classification)
    |> case do
      {:ok, loan_classification} ->
        {:noreply,
          socket
          |> put_flash(:info, "Loan classification updated successfully")
          |> push_redirect(to: Routes.loan_classification_index_path(socket, :edit, loan_classification))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, loan_classification) do
    audit_msg = "Updated Loan Classification  \"#{params["loan_classification"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:loan_classification, LoanClassifications.changeset(loan_classification, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_classification: loan_classification, audit_log: _user_log}} ->
        {:ok, loan_classification}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_approval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Loan classification Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Loan classification rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject loan classification: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_approval(socket, :approve, params) do
    loan_classification = socket.assigns.loan_classification
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Loan classification Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      LoanClassifications.changeset(loan_classification, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_loan_classification}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Loan classification approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Loan classification approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve loan classification")
         |> assign(:changeset, %{loan_classification.changeset | errors: failed_value.errors})}
    end
  end

end
