defmodule MisReports.Utilities.InstitutionDetails do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_institution_type" do
    field :foreign_domestic, :string
    field :institution_name, :string
    field :institution_type, :string
    field :relationship, :string
    field :report_date, :date
    field :status, :string, default: "D"
    field :type_of_financial_institution, :string
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(institution_details, attrs) do
    institution_details
    |> cast(attrs, [:institution_name, :institution_type, :relationship, :foreign_domestic, :type_of_financial_institution, :status, :report_date, :checker_id, :maker_id, :reference])
    |> validate_required([:institution_name])
  end
end
