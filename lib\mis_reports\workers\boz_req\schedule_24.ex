defmodule MisReports.Workers.BozReq.Schedule24 do
  def perform(item) do
    decoded_item =
      case item.schedule_24 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
      dynamic_list = decoded_item["list"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-7QSCH247Q003",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" =>
        "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" =>
        [
        %{"Code" => "1173_00001", "Value" => "#{decoded_item["B14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00002", "Value" => "#{decoded_item["C14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00003", "Value" => "#{decoded_item["D14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00004", "Value" => "#{decoded_item["E14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00005", "Value" => "#{decoded_item["F14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00006", "Value" => "#{decoded_item["G14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00007", "Value" => "#{decoded_item["H14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00008", "Value" => "#{decoded_item["I14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00009", "Value" => "#{decoded_item["J14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00010", "Value" => "#{decoded_item["K14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00011", "Value" => "#{decoded_item["B15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00012", "Value" => "#{decoded_item["C15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00013", "Value" => "#{decoded_item["D15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00014", "Value" => "#{decoded_item["E15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00015", "Value" => "#{decoded_item["F15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00016", "Value" => "#{decoded_item["G15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00017", "Value" => "#{decoded_item["H15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00018", "Value" => "#{decoded_item["I15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00019", "Value" => "#{decoded_item["J15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00020", "Value" => "#{decoded_item["K15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00021", "Value" => "#{decoded_item["B16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00022", "Value" => "#{decoded_item["C16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00023", "Value" => "#{decoded_item["D16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00024", "Value" => "#{decoded_item["E16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00025", "Value" => "#{decoded_item["F16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00026", "Value" => "#{decoded_item["G16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00027", "Value" => "#{decoded_item["H16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00028", "Value" => "#{decoded_item["I16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00029", "Value" => "#{decoded_item["J16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00030", "Value" => "#{decoded_item["K16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00031", "Value" => "#{decoded_item["B17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00032", "Value" => "#{decoded_item["C17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00033", "Value" => "#{decoded_item["D17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00034", "Value" => "#{decoded_item["E17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00035", "Value" => "#{decoded_item["F17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00036", "Value" => "#{decoded_item["G17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00037", "Value" => "#{decoded_item["H17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1173_00038", "Value" => "#{decoded_item["I17"]}", "_dataType" => "NUMERIC"},
      ] |> format_map(),
      "DynamicItemsList" => [
        %{
          "Area" => 450,
          "_areaName" => "Other adjustments - Current tax Description",
          "DynamicItems" => map_data_450(dynamic_list) |> format_values()
        },
        %{
          "Area" => 451,
          "_areaName" => "Other adjustments - Deferred tax Description",
          "DynamicItems" => map_data_451(dynamic_list) |> format_values()
        }
      ]
    }


  end

  def format_map(list_of_maps) do
    Enum.map(list_of_maps, fn map ->
      Enum.map(map, fn {key, value} ->
        {key, format_number(value)}
      end)
      |> Map.new()
    end)
  end

  defp format_number(string) do
    string
    |> String.replace(",", "")
    |> case do
      "" -> "0"
      value -> value
    end
  end

   def map_data_450(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "1.#{index}", "Value" => map["B"], "_dataType" => "TEXT"},
          %{"Code" => "1.#{index}", "Value" => convert_decimal_map_to_decimal(map["C"]), "_dataType" => "NUMERIC"},
        ]
      end)
  end

  def map_data_451(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "1.#{index}", "Value" => map["F"], "_dataType" => "TEXT"},
          %{"Code" => "1.#{index}", "Value" => convert_decimal_map_to_decimal(map["H"]), "_dataType" => "NUMERIC"},
        ]
      end)
  end

  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

   def convert_decimal_map_to_decimal(value) do
    value = if value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
    "#{value}"
  end
end
