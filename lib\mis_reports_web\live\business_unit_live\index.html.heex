
<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
   <div class="mt-5 font-semibold text-xl">Business Units</div>
   <%= if @live_action == :new do %>
      <div class="text-sm">New Business Unit</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Business Unit </div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">View Business Units </div>
    <% end %><br>

   <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
   <.error :if={live_flash(@flash, :error)} flash={@flash} />
   
   <%= if @live_action == :index do %>
   <%= Phoenix.View.render(MisReportsWeb.BusinessUnitView, "business_units.html", assigns) %>
   <% end %>

    <%= if @live_action == :update_status do %>
      <.live_component 
        module={MisReportsWeb.BusinessUnitLive.BusinessUnitComponent} 
        id={@business_unit.id}
        current_user={@current_user} 
        business_unit={@business_unit}
        changeset={@changeset}
        process_id={@process_id}
        reference={@reference}
        step_id={@step_id}
        action={@live_action}
      />
    <% end %>

   <.live_component 
   :if={@live_action in [:new, :edit]} 
   module={MisReportsWeb.BusinessUnitLive.BusinessUnitComponent} 
   id="new-Business-Unit" 
   current_user={@current_user} 
   business_unit={@business_unit} 
   process_id={@process_id}
   reference={@reference}
   step_id={@step_id}
   action={@live_action} />
   
</div>
   
<.confirm_modal />

<.info_notification />

<.error_notification />

