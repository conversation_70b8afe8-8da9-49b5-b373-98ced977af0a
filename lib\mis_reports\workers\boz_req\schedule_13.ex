defmodule MisReports.Workers.BozReq.Schedule13 do

  def perform(item) do

    decoded_item =
      case item.schedule_13 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    decoded_item = format_map(decoded_item)

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-3WSCH133W001",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1193_00001",
          "Value" => "#{decoded_item["C22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00002",
          "Value" => "#{decoded_item["D22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00003",
          "Value" => "#{decoded_item["E22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00004",
          "Value" => "#{decoded_item["F22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00005",
          "Value" => "#{decoded_item["G22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00006",
          "Value" => "#{decoded_item["H22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00007",
          "Value" => "#{decoded_item["C24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00008",
          "Value" => "#{decoded_item["D24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00009",
          "Value" => "#{decoded_item["E24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00010",
          "Value" => "#{decoded_item["F24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00011",
          "Value" => "#{decoded_item["G24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00012",
          "Value" => "#{decoded_item["H24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00013",
          "Value" => "#{decoded_item["C26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00014",
          "Value" => "#{decoded_item["D26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00015",
          "Value" => "#{decoded_item["E26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00016",
          "Value" => "#{decoded_item["F26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00017",
          "Value" => "#{decoded_item["G26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00018",
          "Value" => "#{decoded_item["H26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00019",
          "Value" => "#{decoded_item["C28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00020",
          "Value" => "#{decoded_item["D28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00021",
          "Value" => "#{decoded_item["E28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00022",
          "Value" => "#{decoded_item["F28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00023",
          "Value" => "#{decoded_item["G28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00024",
          "Value" => "#{decoded_item["H28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00025",
          "Value" => "#{decoded_item["C31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00026",
          "Value" => "#{decoded_item["D31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00027",
          "Value" => "#{decoded_item["E31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00028",
          "Value" => "#{decoded_item["F31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00029",
          "Value" => "#{decoded_item["G31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00030",
          "Value" => "#{decoded_item["H31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00031",
          "Value" => "#{decoded_item["C33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00032",
          "Value" => "#{decoded_item["D33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00033",
          "Value" => "#{decoded_item["E33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00034",
          "Value" => "#{decoded_item["F33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00035",
          "Value" => "#{decoded_item["G33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00036",
          "Value" => "#{decoded_item["H33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00037",
          "Value" => "#{decoded_item["C35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00038",
          "Value" => "#{decoded_item["D35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00039",
          "Value" => "#{decoded_item["E35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00040",
          "Value" => "#{decoded_item["F35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00041",
          "Value" => "#{decoded_item["G35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00042",
          "Value" => "#{decoded_item["H35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00043",
          "Value" => "#{decoded_item["C37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00044",
          "Value" => "#{decoded_item["D37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00045",
          "Value" => "#{decoded_item["E37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00046",
          "Value" => "#{decoded_item["F37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00047",
          "Value" => "#{decoded_item["G37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00048",
          "Value" => "#{decoded_item["H37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00049",
          "Value" => "#{decoded_item["C39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00050",
          "Value" => "#{decoded_item["D39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00051",
          "Value" => "#{decoded_item["E39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00052",
          "Value" => "#{decoded_item["F39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00053",
          "Value" => "#{decoded_item["G39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1193_00054",
          "Value" => "#{decoded_item["H39"] || "0"}",
          "_dataType" => "NUMERIC"
        }


      ]
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
