defmodule MisReportsWeb.BranchLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.LiveHelpers
  alias MisReports.{Utilities, Repo}
  alias MisReports.Workers.Utils
  alias MisReports.Utilities.Branch
  alias MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserController

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       process_id: nil,
       reference: nil,
       step_id: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      socket =
        socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

      {:noreply,
       socket
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :new, _params) do
    changeset = Utilities.change_branch(%Branch{})

    socket
    |> assign(:changeset, changeset)
    |> assign(:branch, %Branch{})
    |> assign(:provinces, Utils.provinces())
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(page: %{prev: "Branches", current: "Edit Branch"})
    |> assign(:branch, Utilities.get_branch!(id))
    |> assign(:provinces, Utils.provinces())
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        branch = Utilities.get_branch_by_reference!(reference)

        socket
        |> assign(:page, %{prev: "Branches", current: "Approve Branch"})
        |> assign(:branch, branch)
        |> assign(:changeset, Utilities.change_branch(branch))
        |> assign(:provinces, Utils.provinces())
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_branches(socket)

  defp list_branches(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_branches(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, users: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  @impl true
  def handle_event("validate", %{"branch" => branch_params}, socket) do
    changeset =
      socket.assigns.branch
      |> Branch.changeset(branch_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_branches()}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_branches()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_branches()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value(
        [:number, :email, :name, :phone, :inserted_at, :status, :checker_id, :maker_id],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_branches()}
  end

  # def handle_event("update_status", params, socket) do
  #   opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

  #   if UserLiveAuth.authorize?(socket, opts) do
  #     handle_update_status(params, socket)
  #   else
  #     UserLiveAuth.unauthorized(socket)
  #   end
  # end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    branch = Utilities.get_branch!(id)
    audit_msg = "changed status for template to: #{status}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Branch.changeset(branch, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.branch_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    branch = Utilities.get_branch!(id)
    audit_msg = "Deleted Loan sector:"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_branch, branch)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_branch: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Branch Deleted successfully!")
         |> push_redirect(to: Routes.branch_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"branch", "new"}

      act when act in ~w(edit)a ->
        {"branch", "edit"}

      act when act in ~w(update_status)a ->
        {"branch", "update_status"}

      act when act in ~w(delete)a ->
        {"branch", "delete"}

      act when act in ~w(index)a ->
        {"branch", "index"}

      _ ->
        {"branch", "unknown"}
    end
  end
end
