defmodule MisReports.Workers.BozReq.Schedule17e do
  def perform(item) do
    decoded_item =
      case item.schedule_15 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)

    settings = MisReports.Utilities.get_comapany_settings_params()


    %{
      "returnKey" => "ZM-1GSCH17E1G002",
      "instCode" => "#{settings.institution_code}",
       "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",

      "returnItemsList" => default_return_items_list(),
      "dynamicItemsList" => [
        %{
          "area" => 470,
          "areaName" => "INVESTMENTS",
          "dynamicItems" => map_data(decoded_item)
        }
      ]
    }
  end

  # Format the map to ensure consistent structure
  def format_map(nil), do: %{}
  def format_map(map) when is_map(map), do: map
  def format_map(_), do: %{}

  def build_return_items_list(nil), do: default_return_items_list()

  def build_return_items_list(decoded_item) do
    Map.get(decoded_item, "return_list", default_return_items_list())
  end

  def map_data(nil), do: default_dynamic_items()

  def map_data(decoded_item) do
    institutions = Map.get(decoded_item, "institutions", [])

    if Enum.empty?(institutions) do
      default_dynamic_items()
    else
      Enum.map(institutions, fn institution ->
        %{
          "code" => "1.1",
          "value" => institution["name"] || "N/A",
          "dataType" => "TEXT",
          "data" => [
            %{"code" => "1.2", "value" => convert_to_number(institution["insurance"]), "dataType" => "NUMERIC"},
            %{"code" => "1.3", "value" => convert_to_number(institution["finance"]), "dataType" => "NUMERIC"},
            %{"code" => "1.4", "value" => convert_to_number(institution["securities"]), "dataType" => "NUMERIC"},
            %{"code" => "1.5", "value" => convert_to_number(institution["pension"]), "dataType" => "NUMERIC"},
            %{"code" => "1.6", "value" => convert_to_number(institution["mortgage"]), "dataType" => "NUMERIC"},
            %{"code" => "1.7", "value" => convert_to_number(institution["leasing"]), "dataType" => "NUMERIC"},
            %{"code" => "1.8", "value" => convert_to_number(institution["microfinance"]), "dataType" => "NUMERIC"},
            %{"code" => "1.9", "value" => convert_to_number(institution["bureau_de_change"]), "dataType" => "NUMERIC"},
            %{"code" => "1.10", "value" => convert_to_number(institution["credit_reference"]), "dataType" => "NUMERIC"}
          ]
        }
      end)
    end
  end

  def default_dynamic_items do
    [%{
      "code" => "1.1",
      "value" => "N/A",
      "dataType" => "TEXT",
      "data" => [
        %{"code" => "1.2", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.3", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.4", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.5", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.6", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.7", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.8", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.9", "value" => "0", "dataType" => "NUMERIC"},
        %{"code" => "1.10", "value" => "0", "dataType" => "NUMERIC"}
      ]
    }]
  end

  # Default ReturnItemsList when no return_list is provided in the input
  defp default_return_items_list() do
    [
      %{
        "code" => "1203_00001",
        "value" => "TOTAL",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00002",
        "value" => "Lending (exposure) to shareholders",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00003",
        "value" => "Lending to (LCY): K",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00004",
        "value" => "Lending to (LCY): Insurance Companies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00005",
        "value" => "Lending to (LCY): Finance Companies/Development Finance",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00006",
        "value" => "Lending to (LCY): Securities Firms",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00007",
        "value" => "Lending to (LCY): Pension Funds",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00008",
        "value" => "Lending to (LCY): Mortgage institutions/Building Societies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00009",
        "value" => "Lending to (LCY): Leasing companies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00010",
        "value" => "Lending to (LCY): Microfinance institutions",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00011",
        "value" => "Lending to (LCY): Bureau de change",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00012",
        "value" => "Lending to (LCY): Credit Reference Bureau",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00013",
        "value" => "Lending to (FCY) in Kwacha Equivalent: USD",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00014",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Insurance Companies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00015",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Finance Companies/Development Finance",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00016",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Securities Firms",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00017",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Pension Funds",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00018",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Mortgage institutions/Building Societies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00019",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Leasing companies",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00020",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Microfinance institutions",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00021",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Bureau de change",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00022",
        "value" => "Lending to (FCY) in Kwacha Equivalent: Credit Reference Bureau",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00023",
        "value" => "Investments in Government Securities (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00024",
        "value" => "Investments in Government Securities (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00025",
        "value" => "Investments in Other Securities (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00026",
        "value" => "Investments in Other Securities (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00027",
        "value" => "Total Assets (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00028",
        "value" => "Total Assets (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00029",
        "value" => "Total Liabilities (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00030",
        "value" => "Total Liabilities (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00031",
        "value" => "Net Assets (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00032",
        "value" => "Net Assets (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00033",
        "value" => "Contingent Liabilities (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00034",
        "value" => "Contingent Liabilities (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00035",
        "value" => "Commitments (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00036",
        "value" => "Commitments (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00037",
        "value" => "Derivatives (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00038",
        "value" => "Derivatives (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00039",
        "value" => "Securitised Assets (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00040",
        "value" => "Securitised Assets (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00041",
        "value" => "Other Assets (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00042",
        "value" => "Other Assets (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00043",
        "value" => "Due to Banks (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00044",
        "value" => "Due to Banks (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00045",
        "value" => "Due to Other Financial Institutions (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00046",
        "value" => "Due to Other Financial Institutions (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00047",
        "value" => "Total Deposits (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00048",
        "value" => "Total Deposits (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00049",
        "value" => "Total Equity (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00050",
        "value" => "Total Equity (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00051",
        "value" => "Retained Earnings (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00052",
        "value" => "Retained Earnings (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00053",
        "value" => "Current Year Earnings (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00054",
        "value" => "Current Year Earnings (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00055",
        "value" => "Prior Year Earnings (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00056",
        "value" => "Prior Year Earnings (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00057",
        "value" => "Proposed Dividends (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00058",
        "value" => "Proposed Dividends (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00059",
        "value" => "Other Comprehensive Income (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00060",
        "value" => "Other Comprehensive Income (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00061",
        "value" => "Total Income (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00062",
        "value" => "Total Income (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00063",
        "value" => "Total Expenses (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00064",
        "value" => "Total Expenses (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00065",
        "value" => "Net Income (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00066",
        "value" => "Net Income (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00067",
        "value" => "Cash and Cash Equivalents (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00068",
        "value" => "Cash and Cash Equivalents (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00069",
        "value" => "Short-term Investments (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00070",
        "value" => "Short-term Investments (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00071",
        "value" => "Long-term Investments (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00072",
        "value" => "Long-term Investments (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00073",
        "value" => "Property, Plant and Equipment (LCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00074",
        "value" => "Property, Plant and Equipment (FCY)",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00075",
        "value" => "Balances due to other financial institutions (LCY): Microfinance institutions",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00076",
        "value" => "Balances due to other financial institutions (LCY): Bureau de change",
        "dataType" => "NUMERIC"
      },
      %{
        "code" => "1203_00077",
        "value" => "Balances due to other financial institutions (LCY): Credit Reference Bureau",
        "dataType" => "NUMERIC"
      }
    ]
  end

  # Helper functions for number conversion - consistent with Schedule11d
  def convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  def convert_to_number(value) when is_binary(value) do
    case Integer.parse(value) do
      {int_value, ""} -> Decimal.new(int_value)
      _ -> Decimal.new(String.to_float(value))
    end
  rescue
    _ -> Decimal.new(0)
  end

  def convert_to_number(value) when is_number(value) do
    Decimal.new(value)
  end
  def convert_to_number(_), do: Decimal.new(0)
end
