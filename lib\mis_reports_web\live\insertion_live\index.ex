defmodule MisReportsWeb.InsertionLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  import Ecto.Query, warn: false
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReports.Utilities.Insertion
  alias MisReports.Utilities
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.LiveHelpers
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Utilities.Adjustments
  alias MisReportsWeb.AdjustmentsLive.Index
  alias MisReportsWeb.Components.TabsForAdj

  @impl true
  def mount(_params, _session, socket) do
    assigns = [
      list: [],
      data_list: [],
      page: 1,
      page_size: 10,
      isearch: nil,
      currency_code: "",
      active_tab: "insertions",
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000],
      process_id: nil,
      reference: nil,
      step_id: nil
    ]

    {:ok, assign(socket, assigns)}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    {:noreply,
     socket
     |> assign(:active_tab, tab)}
  end

  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      reference = params["reference"]
      process_id = params["process_id"]
      step_id = params["step_id"]

      # Add logging to track values
      IO.inspect(params, label: "Received params")
      IO.inspect(step_id, label: "step_id from params")
      IO.inspect(reference, label: "reference from params")

      socket =
        socket
        |> assign(:process_id, process_id)
        |> assign(:reference, reference)
        |> assign(:step_id, step_id)
        |> assign(:page_title, page_title(socket.assigns.live_action))

      # Pass reference to TabsForAdj component
      {:noreply,
       socket
       |> assign(:tabs_assigns, %{
         reference: reference,
         process_id: process_id,
         step_id: step_id
       })
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :index, _params), do: list_insertions(socket)

  defp apply_action(socket, :new, _params) do
    # Create a new Adjustments struct for insertion
    insertion = %Adjustments{
      type: "SINGLE",
      maker_id: socket.assigns.current_user.id,
      reference: socket.assigns.reference
    }

    step_id = socket.assigns.step_id

    socket
    |> assign(:insertion, insertion)
    |> assign(:changeset, Utilities.change_adjustments(insertion))
    |> assign(:active_tab, "insertions")
    |> assign(:step_id, step_id)
    |> assign(:action, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    socket
    |> assign(:insertion, Utilities.get_insertion!(id))
    |> assign(page: %{prev: "insertion", current: "Edit Insertions"})
    |> assign(:action, :edit)
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference

    IO.inspect(reference, label: "==Reference")

    case Utilities.get_adjustments_reference!(reference) do
      nil ->
        socket
        |> put_flash(:error, "Adjustment not found")
        |> push_redirect(to: Routes.adjustments_index_path(socket, :index))

      insertions when is_list(insertions) ->
        socket
        |> assign(:insertion, insertions)
        |> assign(page: %{prev: "insertion", current: "Review Insertions"})
        |> assign(:action, :update_status)
        |> assign(:reference, reference)

      insertion ->
        # Single adjustment case (fallback)
        columns = AdjustmentsLive.Index.parse_adjustment_lines(insertion.adjustment_lines)

        socket
        |> assign(:insertion, [insertion])
        |> assign(:selected_insertion, insertion)
        |> assign(:columns, columns)
        |> assign(:action, :update_status)
        |> assign(:page, %{prev: "insertion", current: "Review Insertion"})
        |> assign(:reference, reference)
    end
  end

  @impl true
  def handle_event("select_insertion", %{"id" => insertion_id}, socket) do
    case Enum.find(socket.assigns.insertion, &(&1.id == String.to_integer(insertion_id))) do
      nil ->
        {:noreply, socket}

      insertion ->
        columns = AdjustmentsLive.Index.parse_adjustment_lines(insertion.adjustment_lines)

        {:noreply,
         socket
         |> assign(:selected_insertion, insertion)
         |> assign(:columns, columns)}
    end
  end

  def handle_event("select_option", %{"option" => option}, socket) do
    {:noreply, assign(socket, selected_option: option)}
  end

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_insertions()}
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  defp list_insertions(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_adjustments(params, "SINGLE")

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, adjustments: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    adjustment = Utilities.get_adjustments!(id)

    audit_msg =
      "changed status for Adjustments for report date \"#{adjustment.report_date}\" to status \"#{status}\""

    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Adjustments.changeset(adjustment, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _adjustment, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.adjustments_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    adjustment = Utilities.get_adjustments!(id)
    audit_msg = "Deleted adjustments for report date \"#{adjustment.report_date}\" "
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_adjustment, adjustment)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_adjustment: _adjustment, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Adjustments Deleted successfully!")
         |> push_redirect(to: Routes.adjustments_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  @impl true
  def handle_info({:tab_switched, "adjustments"}, socket) do
    IO.inspect(socket.assigns.step_id, label: "============step_id===========")
    IO.inspect(socket.assigns.reference, label: "============reference===========")

    case socket.assigns.step_id do
      # APPROVAL step
      "67" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :update_status,
               process_id: socket.assigns.process_id,
               reference: socket.assigns.reference,
               step_id: socket.assigns.step_id
             )
         )}

      # APPROVAL step
      "10079" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :update_status,
               process_id: socket.assigns.process_id,
               reference: socket.assigns.reference,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "66" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "10078" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}
    end
  end

  @impl true
  def handle_info({:tab_switched, "insertions"}, socket) do
    IO.inspect(socket.assigns.step_id, label: "============step_id===========")
    IO.inspect(socket.assigns.reference, label: "============reference===========")

    case socket.assigns.step_id do
      # APPROVAL step
      "67" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :update_status,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "66" ->
        # Initialize a new insertion record
        insertion = %Adjustments{
          type: "SINGLE",
          maker_id: socket.assigns.current_user.id,
          reference: socket.assigns.reference
        }

        {:noreply,
         socket
         |> assign(:insertion, insertion)
         |> assign(:changeset, Utilities.change_adjustments(insertion))
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}
    end
  end

  defp determine_route("adjustments", action, socket) when action in [:new, :edit] do
    Routes.adjustments_index_path(socket, action)
  end

  defp determine_route("insertions", action, socket) when action in [:new, :edit] do
    Routes.insertion_index_path(socket, action)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"insertion", "new"}

      act when act in ~w(edit)a ->
        {"insertion", "edit"}

      act when act in ~w(update_status)a ->
        {"insertion", "update_status"}

      act when act in ~w(delete)a ->
        {"insertion", "delete"}

      act when act in ~w(index)a ->
        {"insertion", "index"}

      _ ->
        {"insertion", "unknown"}
    end
  end

  defp page_title(:new), do: "New Insertion"
  defp page_title(:edit), do: "Edit Insertion"
  defp page_title(:update_status), do: "Review Insertion"
  defp page_title(_), do: "Listing Insertions"
end
