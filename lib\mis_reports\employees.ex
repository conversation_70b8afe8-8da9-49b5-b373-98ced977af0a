defmodule MisReports.Employees do
  @moduledoc """
  The Employees context.
  """

  import Ecto.Query, warn: false
  alias MisReports.Repo

  alias MisReports.Employees.EmployeeStats

  @doc """
  Returns the list of tbl_employee_stats.

  ## Examples

      iex> list_tbl_employee_stats()
      [%EmployeeStats{}, ...]

  """
  # def list_employee_stats(params) do
  #   EmployeeStats
  #   |> preload([:checker, :maker])
  #   |> isearch_filter_stats(params.isearch)
  #   |> order_by(^[params.sort_by])
  #   |> Repo.paginate(page: params.page, page_size: params.page_size)
  #   # |> Repo.paginate(page: params.page, page_size: params.page_size)
  # end

  def list_employee_stats(params) do
    EmployeeStats
    |> preload([:checker, :maker])
    |> isearch_filter_stats(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
    # |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_stats(query, nil), do: query

  defp isearch_filter_stats(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.descript, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))

  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  @doc """
  Gets a single employee_stats.

  Raises `Ecto.NoResultsError` if the Employee stats does not exist.

  ## Examples

      iex> get_employee_stats!(123)
      %EmployeeStats{}

      iex> get_employee_stats!(456)
      ** (Ecto.NoResultsError)

  """
  def get_employee_stats!(id), do: Repo.get!(EmployeeStats, id)


  def get_stats() do
    EmployeeStats
    # |> where(status: "ACTIVE")
    |> Repo.all()
    |> List.first()
    # |> Repo.one
  end

  def get_employee_stats_by_reference!(reference) do
    Repo.get_by!(EmployeeStats, reference: reference)
  end

  @doc """
  Creates a employee_stats.

  ## Examples

      iex> create_employee_stats(%{field: value})
      {:ok, %EmployeeStats{}}

      iex> create_employee_stats(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_employee_stats(attrs \\ %{}) do
    %EmployeeStats{}
    |> EmployeeStats.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a employee_stats.

  ## Examples

      iex> update_employee_stats(employee_stats, %{field: new_value})
      {:ok, %EmployeeStats{}}

      iex> update_employee_stats(employee_stats, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_employee_stats(%EmployeeStats{} = employee_stats, attrs) do
    employee_stats
    |> EmployeeStats.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a employee_stats.

  ## Examples

      iex> delete_employee_stats(employee_stats)
      {:ok, %EmployeeStats{}}

      iex> delete_employee_stats(employee_stats)
      {:error, %Ecto.Changeset{}}

  """
  def delete_employee_stats(%EmployeeStats{} = employee_stats) do
    Repo.delete(employee_stats)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking employee_stats changes.

  ## Examples

      iex> change_employee_stats(employee_stats)
      %Ecto.Changeset{data: %EmployeeStats{}}

  """
  def change_employee_stats(%EmployeeStats{} = employee_stats, attrs \\ %{}) do
    EmployeeStats.changeset(employee_stats, attrs)
  end










  alias MisReports.Employees.EmployeeBenefit

  @doc """
  Returns the list of tbl_employee_benefit.

  ## Examples

      iex> list_tbl_employee_benefit()
      [%EmployeeBenefit{}, ...]

  """
  def list_employee_benefits(params) do
    EmployeeBenefit
    |> preload([:checker, :maker])
    |> isearch_filter_benefit(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
    # Repo.all()
  end

  defp isearch_filter_benefit(query, nil), do: query

  defp isearch_filter_benefit(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.descript, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))

  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  @doc """
  Gets a single employee_benefit.

  Raises `Ecto.NoResultsError` if the Employee benefit does not exist.

  ## Examples

      iex> get_employee_benefit!(123)
      %EmployeeBenefit{}

      iex> get_employee_benefit!(456)
      ** (Ecto.NoResultsError)

  """
  def get_employee_benefit!(id), do: Repo.get!(EmployeeBenefit, id)


  def get_employee_benefit(month, year) do
    EmployeeBenefit
    |> where([a], a.month == ^month and a.year == ^year)
    |> Repo.all()
    |> List.first()
    # |> Repo.one
  end

  def get_employee_benefit_by_reference!(reference) do
    Repo.get_by!(EmployeeBenefit, reference: reference)
  end

  @doc """
  Creates a employee_benefit.

  ## Examples

      iex> create_employee_benefit(%{field: value})
      {:ok, %EmployeeBenefit{}}

      iex> create_employee_benefit(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_employee_benefit(attrs \\ %{}) do
    %EmployeeBenefit{}
    |> EmployeeBenefit.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a employee_benefit.

  ## Examples

      iex> update_employee_benefit(employee_benefit, %{field: new_value})
      {:ok, %EmployeeBenefit{}}

      iex> update_employee_benefit(employee_benefit, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_employee_benefit(%EmployeeBenefit{} = employee_benefit, attrs) do
    employee_benefit
    |> EmployeeBenefit.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a employee_benefit.

  ## Examples

      iex> delete_employee_benefit(employee_benefit)
      {:ok, %EmployeeBenefit{}}

      iex> delete_employee_benefit(employee_benefit)
      {:error, %Ecto.Changeset{}}

  """
  def delete_employee_benefit(%EmployeeBenefit{} = employee_benefit) do
    Repo.delete(employee_benefit)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking employee_benefit changes.

  ## Examples

      iex> change_employee_benefit(employee_benefit)
      %Ecto.Changeset{data: %EmployeeBenefit{}}

  """
  def change_employee_benefit(%EmployeeBenefit{} = employee_benefit, attrs \\ %{}) do
    EmployeeBenefit.changeset(employee_benefit, attrs)
  end
end
