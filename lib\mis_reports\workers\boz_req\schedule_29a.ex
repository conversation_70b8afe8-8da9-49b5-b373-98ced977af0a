defmodule MisReports.Workers.BozReq.Schedule29a do
  def perform(item) do

    decoded_item =
      case item.schedule_29a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # dynamic_list = decoded_item["list"]
    decoded_item = format_map(decoded_item)
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-N0SCH29AN0003",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1237_00001", "Value" => "#{decoded_item["D15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00002", "Value" => "#{decoded_item["E15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00003", "Value" => "#{decoded_item["F15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00004", "Value" => "#{decoded_item["G15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00005", "Value" => "#{decoded_item["H15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00006", "Value" => "#{decoded_item["I15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00007", "Value" => "#{decoded_item["D16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00008", "Value" => "#{decoded_item["E16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00009", "Value" => "#{decoded_item["F16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00010", "Value" => "#{decoded_item["G16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00011", "Value" => "#{decoded_item["H16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00012", "Value" => "#{decoded_item["I16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00013", "Value" => "#{decoded_item["D17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00014", "Value" => "#{decoded_item["E17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00015", "Value" => "#{decoded_item["F17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00016", "Value" => "#{decoded_item["G17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00017", "Value" => "#{decoded_item["H17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00018", "Value" => "#{decoded_item["I17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00019", "Value" => "#{decoded_item["D18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00020", "Value" => "#{decoded_item["E18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00021", "Value" => "#{decoded_item["F18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00022", "Value" => "#{decoded_item["G18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00023", "Value" => "#{decoded_item["H18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00024", "Value" => "#{decoded_item["I18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00025", "Value" => "#{decoded_item["D19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00026", "Value" => "#{decoded_item["E19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00027", "Value" => "#{decoded_item["F19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00028", "Value" => "#{decoded_item["G19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00029", "Value" => "#{decoded_item["H19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00030", "Value" => "#{decoded_item["I19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00031", "Value" => "#{decoded_item["D20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00032", "Value" => "#{decoded_item["E20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00033", "Value" => "#{decoded_item["F20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00034", "Value" => "#{decoded_item["G20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00035", "Value" => "#{decoded_item["H20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00036", "Value" => "#{decoded_item["I20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00037", "Value" => "#{decoded_item["D21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00038", "Value" => "#{decoded_item["E21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00039", "Value" => "#{decoded_item["F21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00040", "Value" => "#{decoded_item["G21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00041", "Value" => "#{decoded_item["H21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00042", "Value" => "#{decoded_item["I21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00043", "Value" => "#{decoded_item["D22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00044", "Value" => "#{decoded_item["E22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00045", "Value" => "#{decoded_item["F22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00046", "Value" => "#{decoded_item["G22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00047", "Value" => "#{decoded_item["H22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00048", "Value" => "#{decoded_item["I22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00049", "Value" => "#{decoded_item["D24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00050", "Value" => "#{decoded_item["E24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00051", "Value" => "#{decoded_item["F24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00052", "Value" => "#{decoded_item["G24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00053", "Value" => "#{decoded_item["H24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00054", "Value" => "#{decoded_item["I24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00055", "Value" => "#{decoded_item["D27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00056", "Value" => "#{decoded_item["E27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00057", "Value" => "#{decoded_item["F27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00058", "Value" => "#{decoded_item["G27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00059", "Value" => "#{decoded_item["H27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00060", "Value" => "#{decoded_item["I27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00061", "Value" => "#{decoded_item["D28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00062", "Value" => "#{decoded_item["E28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00063", "Value" => "#{decoded_item["F28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00064", "Value" => "#{decoded_item["G28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00065", "Value" => "#{decoded_item["H28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00066", "Value" => "#{decoded_item["I28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00067", "Value" => "#{decoded_item["D29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00068", "Value" => "#{decoded_item["E29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00069", "Value" => "#{decoded_item["F29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00070", "Value" => "#{decoded_item["G29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00071", "Value" => "#{decoded_item["H29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00072", "Value" => "#{decoded_item["I29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00073", "Value" => "#{decoded_item["D30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00074", "Value" => "#{decoded_item["E30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00075", "Value" => "#{decoded_item["F30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00076", "Value" => "#{decoded_item["G30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00077", "Value" => "#{decoded_item["H30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00078", "Value" => "#{decoded_item["I30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00079", "Value" => "#{decoded_item["D31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00080", "Value" => "#{decoded_item["E31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00081", "Value" => "#{decoded_item["F31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00082", "Value" => "#{decoded_item["G31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00083", "Value" => "#{decoded_item["H31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00084", "Value" => "#{decoded_item["I31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00085", "Value" => "#{decoded_item["D32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00086", "Value" => "#{decoded_item["E32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00087", "Value" => "#{decoded_item["F32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00088", "Value" => "#{decoded_item["G32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00089", "Value" => "#{decoded_item["H32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00090", "Value" => "#{decoded_item["I32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00091", "Value" => "#{decoded_item["D33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00092", "Value" => "#{decoded_item["E33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00093", "Value" => "#{decoded_item["F33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00094", "Value" => "#{decoded_item["G33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00095", "Value" => "#{decoded_item["H33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00096", "Value" => "#{decoded_item["I33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00097", "Value" => "#{decoded_item["D34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00098", "Value" => "#{decoded_item["E34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00099", "Value" => "#{decoded_item["F34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00100", "Value" => "#{decoded_item["G34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00101", "Value" => "#{decoded_item["H34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00102", "Value" => "#{decoded_item["I34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00103", "Value" => "#{decoded_item["D35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00104", "Value" => "#{decoded_item["E35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00105", "Value" => "#{decoded_item["F35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00106", "Value" => "#{decoded_item["G35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00107", "Value" => "#{decoded_item["H35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00108", "Value" => "#{decoded_item["I35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00109", "Value" => "#{decoded_item["D37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00110", "Value" => "#{decoded_item["E37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00111", "Value" => "#{decoded_item["F37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00112", "Value" => "#{decoded_item["G37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00113", "Value" => "#{decoded_item["H37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00114", "Value" => "#{decoded_item["I37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00115", "Value" => "#{decoded_item["D39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00116", "Value" => "#{decoded_item["E39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00117", "Value" => "#{decoded_item["F39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00118", "Value" => "#{decoded_item["G39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00119", "Value" => "#{decoded_item["H39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00120", "Value" => "#{decoded_item["I39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00121", "Value" => "#{decoded_item["D41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00122", "Value" => "#{decoded_item["E41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00123", "Value" => "#{decoded_item["F41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00124", "Value" => "#{decoded_item["G41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00125", "Value" => "#{decoded_item["H41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00126", "Value" => "#{decoded_item["I41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00127", "Value" => "#{decoded_item["D42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00128", "Value" => "#{decoded_item["E42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00129", "Value" => "#{decoded_item["F42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00130", "Value" => "#{decoded_item["G42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00131", "Value" => "#{decoded_item["H42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00132", "Value" => "#{decoded_item["I42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00133", "Value" => "#{decoded_item["D44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00134", "Value" => "#{decoded_item["E44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00135", "Value" => "#{decoded_item["F44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00136", "Value" => "#{decoded_item["G44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00137", "Value" => "#{decoded_item["H44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00138", "Value" => "#{decoded_item["I44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00139", "Value" => "#{decoded_item["D45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00140", "Value" => "#{decoded_item["E45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00141", "Value" => "#{decoded_item["F45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00142", "Value" => "#{decoded_item["G45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00143", "Value" => "#{decoded_item["H45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00144", "Value" => "#{decoded_item["I45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00145", "Value" => "#{decoded_item["D46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00146", "Value" => "#{decoded_item["E46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00147", "Value" => "#{decoded_item["F46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00148", "Value" => "#{decoded_item["G46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00149", "Value" => "#{decoded_item["H46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00150", "Value" => "#{decoded_item["I46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00151", "Value" => "#{decoded_item["D47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00152", "Value" => "#{decoded_item["E47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00153", "Value" => "#{decoded_item["F47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00154", "Value" => "#{decoded_item["G47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00155", "Value" => "#{decoded_item["H47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1237_00156", "Value" => "#{decoded_item["I47"]}", "_dataType" => "NUMERIC"}
      ],
      "DynamicItemsList" => []
    }


  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, if(is_map(value), do: convert_decimal_map_to_decimal(value), else: format_number(value))}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


  def map_data(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => map["A"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => map["B"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => map["C"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => map["D"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => convert_decimal_map_to_decimal(map["E"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.6", "Value" => convert_decimal_map_to_decimal(map["F"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.7", "Value" => map["G"], "_dataType" => "TEXT"}
        ]
      end)
  end

  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  # defp compare_keys(a, b) do
  #   case {String.length(a), String.length(b)} do
  #     {1, 1} -> a <= b  # Compare single-letter keys normally (A-Z)
  #     {1, _} -> true     # Single-letter keys (A-Z) always come before double-letter keys (AA, AB, ...)
  #     {_, 1} -> false    # Double-letter keys come after single-letter keys
  #     _ -> a <= b        # Compare multi-letter keys lexicographically (AA, AB, AC, AD...)
  #   end
  # end

  def convert_exponent(%{"coef" => coef, "exp" => exp, "sign" => sign}) do
    Decimal.new(sign, coef, exp) |> Decimal.to_float()
  end

  def convert_exponent(value), do: value

  def convert_decimal_map_to_decimal(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end
end
