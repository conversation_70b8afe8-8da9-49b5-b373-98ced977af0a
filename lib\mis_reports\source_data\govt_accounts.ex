defmodule MisReports.SourceData.GovtAccounts do
  use Ecto.Schema
  import Ecto.Changeset

  schema "govt_accounts" do
    field :account_name, :string
    field :account_number, :string
    field :product_code_source, :string
    field :reference, :string
    field :status, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(govt_accounts, attrs) do
    govt_accounts
    |> cast(attrs, [:account_name, :account_number, :product_code_source, :maker_id, :checker_id, :reference, :status])
    |> validate_required([:account_name, :account_number, :product_code_source])
  end
end
