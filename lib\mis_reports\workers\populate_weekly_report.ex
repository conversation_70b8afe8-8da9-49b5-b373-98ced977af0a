defmodule MisReports.Workers.PopulateWeeklyReport do
  require <PERSON><PERSON>
  alias MisReports.Workers.Utils
  alias MisReports.Utilities

  # Helper function to inspect data before populating
  defp inspect_data(data_name, data, limit \\ 30) do

    case data do
      nil ->
        IO.puts("DATA IS NIL")

      data when is_map(data) ->
        # Check if it's an Ecto struct
        if Map.has_key?(data, :__struct__) do
          # Handle Ecto structs specially
          struct_name = data.__struct__ |> to_string()
          IO.puts("DATA TYPE: Ecto struct #{struct_name}")

          # Convert to map and remove meta fields
          map_data =
            data
            |> Map.from_struct()
            |> Map.drop([:__meta__, :__struct__])

          # Display the map data
          entries =
            map_data
            |> Enum.take(limit)
            |> Enum.map(fn {k, v} -> {k, inspect_value(v)} end)
            |> Enum.map(fn {k, v} -> "#{k}: #{v}" end)
            |> Enum.join("\n")

          IO.puts("STRUCT FIELDS (#{map_size(map_data)} fields):")
          IO.puts(entries)
        else
          # Regular map handling
          try do
            # Get the first 30 entries for maps
            entries =
              data
              |> Enum.take(limit)
              |> Enum.map(fn {k, v} -> {k, inspect_value(v)} end)
              |> Enum.map(fn {k, v} -> "#{k}: #{v}" end)
              |> Enum.join("\n")

            IO.puts("DATA TYPE: Map with #{map_size(data)} entries")
            IO.puts("FIRST #{min(limit, map_size(data))} ENTRIES:")
            IO.puts(entries)
          rescue
            Protocol.UndefinedError ->
              # Handle maps that don't implement Enumerable
              IO.puts("DATA TYPE: Map (not enumerable)")
              IO.puts("VALUE: #{inspect(data, limit: 1000)}")
          end
        end

      data when is_list(data) ->
        # Get the first 30 entries for lists
        entries =
          data
          |> Enum.take(limit)
          |> Enum.map(&inspect_value/1)
          |> Enum.join("\n")

        IO.puts("DATA TYPE: List with #{length(data)} entries")
        IO.puts("FIRST #{min(limit, length(data))} ENTRIES:")
        IO.puts(entries)

      _ ->
        # For other data types, just inspect the value
        IO.puts("DATA TYPE: #{typeof(data)}")
        IO.puts("VALUE: #{inspect(data, limit: 1000)}")
    end

    IO.puts("==== END OF #{data_name} INSPECTION ====\n")
    data
  end

  # Helper function to inspect values of different types
  defp inspect_value(value) do
    cond do
      # Handle Ecto structs
      is_map(value) && Map.has_key?(value, :__struct__) ->
        struct_name = value.__struct__ |> to_string()
        "{Ecto struct #{struct_name}}"

      # Handle maps that might not implement Enumerable
      is_map(value) ->
        try do
          if map_size(value) <= 10 do
            # For small maps, show all entries
            map_entries =
              value
              |> Enum.map(fn {k, v} -> "#{k}: #{inspect(v)}" end)
              |> Enum.join(", ")

            "{#{map_entries}}"
          else
            # For larger maps, show just the size
            "{Map with #{map_size(value)} entries}"
          end
        rescue
          Protocol.UndefinedError ->
            # For maps that don't implement Enumerable
            "{Map (not enumerable): #{inspect(value, limit: 100)}}"
        end

      is_list(value) && length(value) <= 10 ->
        # For small lists, show all entries
        list_entries =
          value
          |> Enum.map(&inspect/1)
          |> Enum.join(", ")

        "[#{list_entries}]"

      is_list(value) ->
        # For larger lists, show just the length
        "[List with #{length(value)} entries]"

      is_binary(value) && byte_size(value) > 100 ->
        # For long strings, truncate
        truncated = String.slice(value, 0, 100)
        "\"#{truncated}...\" (#{byte_size(value)} bytes)"

      true ->
        # For other types, use standard inspect
        inspect(value, limit: 100)
    end
  end

  def populate(file_id, data) do
    # Check if we have an item in the process dictionary with file_path
    current_item = Process.get(:current_item)

    data =
      if current_item && Map.has_key?(current_item, :file_path) do
        # Merge the file_path into the data
        Map.put(data, :file_path, current_item.file_path)
      else
        data
      end

    # Get the report_data field which contains the WeeklyReport data
    report_data = data.report_data

    # Handle report_data that might be an Ecto struct
    report_data =
      if is_map(report_data) && Map.has_key?(report_data, :__struct__) do
        # Convert Ecto struct to map
        IO.puts("\n==== CONVERTING REPORT_DATA FROM ECTO STRUCT ====")
        IO.puts("Original struct type: #{report_data.__struct__}")
        Map.from_struct(report_data)
      else
        report_data
      end

    # Inspect the full report data
    IO.puts("\n==== FULL REPORT DATA INSPECTION ====")
    IO.puts("Export Type: #{data.export_type}")
    IO.puts("Reference: #{data.reference}")
    IO.puts("Report Date: #{data.report_date}")

    # Inspect the report_data structure
    IO.puts("\n==== REPORT DATA STRUCTURE ====")
    inspect_data("Report Data Structure", report_data)

    # Log basic information
    Logger.info("Populating Excel file for #{data.export_type} report")

    # Safely access fields with error handling
    safe_get = fn field ->
      try do
        Map.get(report_data, field)
      rescue
        e ->
          IO.puts("Error accessing field #{field}: #{inspect(e)}")
          nil
      end
    end

    # Populate based on export type
    case data.export_type do
      "CORE_LIQUID_ASSETS" ->
        # Populate Core Liquid Assets schedules
        Logger.info("Populating Core Liquid Assets schedules")

        # Inspect each schedule data before populating
        IO.puts("\n==== CORE LIQUID ASSETS SCHEDULES DATA ====")

        IO.puts("\n--- Schedule 27 Data ---")
        schedule_27_data = safe_get.(:schedule_27)
        inspect_data("Schedule 27", schedule_27_data)
        schedule_27(file_id, %{schedule_27: schedule_27_data})

        IO.puts("\n--- Schedule 27A1 Data ---")
        schedule_27A1_data = safe_get.(:schedule_27A1)
        inspect_data("Schedule 27A1", schedule_27A1_data)
        schedule_27A1(file_id, %{schedule_27A1: schedule_27A1_data})

        IO.puts("\n--- Schedule 27A2 Data ---")
        schedule_27A2_data = safe_get.(:schedule_27A2)
        inspect_data("Schedule 27A2", schedule_27A2_data)
        schedule_27A2(file_id, %{schedule_27A2: schedule_27A2_data})

        IO.puts("\n--- Schedule 27A3 Data ---")
        schedule_27A3_data = safe_get.(:schedule_27A3)
        inspect_data("Schedule 27A3", schedule_27A3_data)
        schedule_27A3(file_id, %{schedule_27A3: schedule_27A3_data})

        IO.puts("\n--- Schedule 27A4 Data ---")
        schedule_27A4_data = safe_get.(:schedule_27A4)
        inspect_data("Schedule 27A4", schedule_27A4_data)
        schedule_27A4(file_id, %{schedule_27A4: schedule_27A4_data})

        IO.puts("\n--- Schedule 27A5 Data ---")
        schedule_27A5_data = safe_get.(:schedule_27A5)
        inspect_data("Schedule 27A5", schedule_27A5_data)
        schedule_27A5(file_id, %{schedule_27A5: schedule_27A5_data})

        IO.puts("\n--- Schedule 27B1 Data ---")
        schedule_27B1_data = safe_get.(:schedule_27B1)
        inspect_data("Schedule 27B1", schedule_27B1_data)
        schedule_27B1(file_id, %{schedule_27B1: schedule_27B1_data})

        IO.puts("\n--- Schedule 27B2 Data ---")
        schedule_27B2_data = safe_get.(:schedule_27B2)
        inspect_data("Schedule 27B2", schedule_27B2_data)
        schedule_27B2(file_id, %{schedule_27B2: schedule_27B2_data})

      "FOREX_RISK" ->
        # Populate Forex Risk schedules
        Logger.info("Populating Forex Risk schedules")

        # Inspect each schedule data before populating
        IO.puts("\n==== FOREX RISK SCHEDULES DATA ====")

        IO.puts("\n--- Schedule 21A Data ---")
        schedule_21A_data = safe_get.(:schedule_21A)

        case schedule_21A_data do
          %{} = data ->
            # Data is already a map
            inspect_data("Schedule 21A", data)
            schedule_21A(file_id, %{schedule_21A: data})

          binary when is_binary(binary) ->
            # Data is a JSON string, pass it directly to schedule_21A for decoding
            IO.puts("Schedule 21A data is a JSON string (length: #{String.length(binary)} chars)")
            schedule_21A(file_id, %{schedule_21A: binary})

          nil ->
            Logger.warn("Skipping Schedule 21A - missing data for reference #{data.reference}")

          _ ->
            Logger.warn("Unexpected Schedule 21A data type: #{typeof(schedule_21A_data)}")
        end

        IO.puts("\n--- Schedule 21B Data ---")
        schedule_21B_data = safe_get.(:schedule_21B)
        inspect_data("Schedule 21B", schedule_21B_data)
        schedule_21B(file_id, %{schedule_21B: schedule_21B_data})

      _ ->
        Logger.error("Unsupported export type: #{data.export_type}")
    end

    # Save the workbook
    Logger.info("Saving workbook after population")

    # Check if we're working with a template file directly
    current_item = Process.get(:current_item)

    if current_item && Map.has_key?(current_item, :file_path) do
      file_path = current_item.file_path

      # If we're working with a template file directly, we need to save to a new location
      if String.contains?(file_path, "template") do
        Logger.info("Working with template file directly, saving to new location")

        # Generate a new filename for the completed report
        dir = MisReports.Utilities.get_directory_params()

        date_str =
          if is_binary(data.report_date),
            do: data.report_date,
            else: Date.to_string(data.report_date)

        report_type =
          case data.export_type do
            "CORE_LIQUID_ASSETS" -> "CLA"
            "FOREX_RISK" -> "FX"
            _ -> "UNKNOWN"
          end

        new_filename = "Weekly_#{report_type}_Report_#{date_str}.xlsx"

        # Use the complete directory from database
        complete_dir = dir.complete

        # Ensure the directory exists
        File.mkdir_p!(complete_dir)

        new_file_path = Path.join(complete_dir, new_filename)

        # Save to the new location
        try do
          # Check if file already exists and try to remove it first
          if File.exists?(new_file_path) do
            Logger.info("File already exists, attempting to remove: #{new_file_path}")
            File.rm!(new_file_path)
            Logger.info("Successfully removed existing file")
          end

          # Ensure the directory exists
          File.mkdir_p!(Path.dirname(new_file_path))

          Excelizer.Workbook.save_as(file_id, new_file_path)
          Logger.info("Successfully saved workbook to: #{new_file_path}")

          # Update the export record with the new filename and status
          case MisReports.Utilities.get_weekly_export_by_reference(data.reference) do
            nil ->
              Logger.error(
                "Failed to find weekly export record with reference: #{data.reference}"
              )

            export_record ->
              MisReports.Utilities.update_weekly_file_export(export_record, %{
                filename: new_filename,
                status: "EXPORT_COMPLETE"
              })
          end
        rescue
          e ->
            Logger.error("Failed to save workbook to new location: #{inspect(e)}")
            Logger.error("Target path was: #{new_file_path}")

            # Try saving with a timestamp to avoid conflicts
            timestamp = DateTime.utc_now() |> DateTime.to_unix()
            fallback_filename = "Weekly_#{report_type}_Report_#{date_str}_#{timestamp}.xlsx"
            fallback_path = Path.join(complete_dir, fallback_filename)

            try do
              Logger.info("Attempting fallback save to: #{fallback_path}")
              Excelizer.Workbook.save_as(file_id, fallback_path)
              Logger.info("Successfully saved workbook to fallback location: #{fallback_path}")

              # Update with fallback filename
              case MisReports.Utilities.get_weekly_export_by_reference(data.reference) do
                nil ->
                  Logger.error(
                    "Failed to find weekly export record with reference: #{data.reference}"
                  )

                export_record ->
                  MisReports.Utilities.update_weekly_file_export(export_record, %{
                    filename: fallback_filename,
                    status: "EXPORT_COMPLETE"
                  })
              end
            rescue
              fallback_error ->
                Logger.error("Fallback save also failed: #{inspect(fallback_error)}")
                # Final fallback to regular save
                Excelizer.Workbook.save(file_id)
            end
        end
      else
        # Regular save for copied files
        Excelizer.Workbook.save(file_id)

        # Update the export status
        case MisReports.Utilities.get_weekly_export_by_reference(data.reference) do
          nil ->
            Logger.error("Failed to find weekly export record with reference: #{data.reference}")

          export_record ->
            MisReports.Utilities.update_weekly_file_export(export_record, %{
              status: "EXPORT_COMPLETE"
            })
        end
      end
    else
      # Default save
      Excelizer.Workbook.save(file_id)
    end

    # Update the export status to EXPORT_COMPLETE
    case MisReports.Utilities.get_weekly_export_by_reference(data.reference) do
      nil ->
        Logger.error("Failed to find weekly export record with reference: #{data.reference}")

      export_record ->
        # Update the export record
        case MisReports.Utilities.update_weekly_file_export(export_record, %{
               status: "EXPORT_COMPLETE"
             }) do
          {:ok, updated_record} ->
            Logger.info(
              "Successfully updated weekly file export status to EXPORT_COMPLETE for reference: #{data.reference}"
            )

            # Broadcast the updated status to the LiveView
            Phoenix.PubSub.broadcast(
              MisReports.PubSub,
              "weekly_export:#{data.reference}",
              {:weekly_export_complete, updated_record}
            )

          {:error, changeset} ->
            error_msg = traverse_errors(changeset.errors) |> Enum.join("\r\n")

            Logger.error(
              "Failed to update weekly file export status to EXPORT_COMPLETE: #{error_msg} for reference: #{data.reference}"
            )
        end
    end

    # Return success
    :ok
  end

  def perform(data) do
    Logger.info("------------------------WEEKLY REPORT EXPORTING-----------------------------")

    # Inspect the input data
    IO.puts("\n==== WEEKLY REPORT EXPORT STARTED ====")
    IO.puts("Input data inspection:")
    IO.puts("Export Type: #{data.export_type}")
    IO.puts("Reference: #{data.reference}")
    IO.puts("Report Date: #{data.report_date}")

    # Inspect the full data structure
    IO.puts("\n==== INPUT DATA STRUCTURE ====")
    inspect_data("Input Data", data)

    # Validate the data
    case validate_data(data) do
      :ok ->
        IO.puts("\n==== VALIDATION SUCCESSFUL ====")

        # Generate the Excel file
        IO.puts("\n==== GENERATING EXCEL FILE ====")
        file = gen_file(data)
        IO.puts("Generated file: #{file}")

        # Open the file and populate it with data
        IO.puts("\n==== POPULATING EXCEL FILE ====")

        case Excelizer.open(file, &populate(&1, data)) do
          :ok ->
            IO.puts("\n==== EXCEL POPULATION SUCCESSFUL ====")
            Logger.info("Successfully populated Excel file: #{file}")
            {:ok, file}

          error ->
            IO.puts("\n==== EXCEL POPULATION FAILED ====")
            IO.puts("Error: #{inspect(error)}")
            Logger.error("Error populating Excel file: #{inspect(error)}")
            error
        end

      {:error, reason} ->
        IO.puts("\n==== VALIDATION FAILED ====")
        IO.puts("Error: #{reason}")

        # Update the export status to reflect the error
        IO.puts("\n==== UPDATING EXPORT STATUS TO ERROR ====")

        case MisReports.Utilities.get_weekly_export_by_reference(data.reference) do
          nil ->
            IO.puts("Failed to find weekly export record with reference: #{data.reference}")
            Logger.error("Failed to find weekly export record with reference: #{data.reference}")

          export_record ->
            IO.puts("Updating export record to EXPORT_ERROR")

            MisReports.Utilities.update_weekly_file_export(export_record, %{
              status: "EXPORT_ERROR",
              filename: nil
            })
        end

        # Return the error
        {:error, reason}
    end
  end

  defp create_schedule_27A2_cell_mapping(decoded_data) do
    # Extract data from the list/total_balance structure
    cell_mapping = %{}
    start_row = 13

    # Get the top_1000 items (top 400 deposits)
    top_1000_items = get_in(decoded_data, ["top_1000", "items"]) || []
    top_1000_count = length(top_1000_items)

    # Calculate the row for "others" entry
    others_row = start_row + top_1000_count

    # Calculate the starting row for top_600 items (top 250 withdrawals)
    top_600_start_row = others_row + 1

    # Map grand_total to a specific cell (at the bottom after all entries)
    cell_mapping =
      case Map.get(decoded_data, "grand_total") do
        nil -> cell_mapping
        # Total in amount column, place at the bottom
        value -> Map.put(cell_mapping, "E#{top_600_start_row + length(get_in(decoded_data, ["top_600", "items"]) || []) + 1}", value)
      end

    # Process top_1000 items starting from row 13 (top 400 deposits)
    cell_mapping =
      if top_1000_count > 0 do
        top_1000_items
        # Start from row 13
        |> Enum.with_index(start_row)
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Map each field to the correct Excel column starting from row 13
          acc
          # TPIN in column A
          |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
          # ACCOUNT HOLDER in column B
          |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
          # SECTOR in column C
          |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
          # ACTIVITY in column D
          |> Map.put("D#{row_index}", Map.get(item, "activity_type", ""))
          # AMOUNT in column E
          |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
        end)
      else
        cell_mapping
      end

    # Map "others" data to row after top_1000 items
    cell_mapping =
      case Map.get(decoded_data, "others") do
        nil ->
          cell_mapping

        others ->
          acc_name = Map.get(others, "name", "Other Deposits")
          activity = Map.get(decoded_data, "primary_activity", "")
          total = Map.get(others, "total", "0.00")

          cell_mapping
          # TPIN in column A (empty for "others")
          |> Map.put("A#{others_row}", "")
          # ACCOUNT HOLDER in column B
          |> Map.put("B#{others_row}", acc_name)
          # SECTOR in column C (empty for "others")
          |> Map.put("C#{others_row}", "")
          # ACTIVITY in column D
          |> Map.put("D#{others_row}", activity)
          # AMOUNT in column E
          |> Map.put("E#{others_row}", total)
        end

    # Process top_600 items starting from row after "others" (top 250 withdrawals)
    cell_mapping =
      case get_in(decoded_data, ["top_600", "items"]) do
        nil ->
          cell_mapping

        items when is_list(items) ->
          items
          # Start from row after "others"
          |> Enum.with_index(top_600_start_row)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            # Map each field to the correct Excel column
            acc
            # TPIN in column A
            |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
            # ACCOUNT HOLDER in column B
            |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
            # SECTOR in column C
            |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
            # ACTIVITY in column D
            |> Map.put("D#{row_index}", Map.get(item, "activity_type", ""))
            # AMOUNT in column E
            |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
          end)

        _ ->
          cell_mapping
      end

    Enum.each(cell_mapping, fn {cell, value} ->
      IO.puts("#{cell}: #{inspect(value)}")
    end)

    IO.puts("==== END SCHEDULE 27A2 CELL MAPPING ====\n")

    cell_mapping
  end

  # defp schedule_27A2(file_id, data) do
  #   if is_nil(data.schedule_27A2) do
  #     Logger.warning("Schedule 27A2 data is nil, skipping")
  #     :ok
  #   else
  #     # Decode the JSON data following Prudential pattern for nested structure
  #     case Poison.decode(data.schedule_27A2) do
  #       {:ok, decoded_data} ->
  #         Logger.info("Populating Schedule 27A2 with nested data structure")
  #         IO.puts("\n==== SCHEDULE 27A2 EXCEL POPULATION ====")
  #         IO.puts("Processing nested data structure")

  #         # Inspect the decoded data
  #         IO.puts("\n==== SCHEDULE 27A2 DECODED DATA ====")
  #         inspect_data("Schedule 27A2 Decoded", decoded_data)

  #         # Map nested data to Excel cells (following Prudential pattern for complex structures)
  #         cell_mapping = create_schedule_27A2_cell_mapping(decoded_data)

  #         # Process each mapped cell using Prudential Excel population patterns
  #         cell_mapping
  #         |> Stream.each(fn
  #           # Handle zero values (Prudential pattern)
  #           {cell_index, value} when value in ["0", "0.0", "0.00"] ->
  #             Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", 0.0)
  #             IO.puts("Set cell #{cell_index} to float: 0.0")

  #           # Handle nil values (Prudential pattern)
  #           {cell_index, value} when is_nil(value) ->
  #             Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", 0.0)
  #             IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

  #           # Handle string values that represent numbers (Prudential pattern)
  #           {cell_index, value} when is_binary(value) ->
  #             case Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) do
  #               true ->
  #                 float_value = Utils.string_to_float(value)
  #                 Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", float_value)
  #                 IO.puts("Set cell #{cell_index} to float: #{float_value} (converted from string)")
  #               false ->
  #                 Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", value)
  #                 IO.puts("Set cell #{cell_index} to string: #{value}")
  #             end

  #           # Handle integer values (Prudential pattern)
  #           {cell_index, value} when is_integer(value) ->
  #             Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "int", value)
  #             IO.puts("Set cell #{cell_index} to int: #{value}")

  #           # Handle float values (Prudential pattern)
  #           {cell_index, value} when is_float(value) ->
  #             Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", value)
  #             IO.puts("Set cell #{cell_index} to float: #{value}")

  #           # Handle any other value types (Prudential pattern)
  #           {cell_index, value} ->
  #             string_value = to_string(value)
  #             Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
  #             IO.puts("Set cell #{cell_index} to string: #{string_value} (converted from #{typeof(value)})")
  #         end)
  #         |> Stream.run()

  #         # Save workbook after population (Prudential pattern)
  #         Logger.info("Saving workbook after Schedule 27A2 population")
  #         Excelizer.Workbook.save(file_id)
  #         IO.puts("==== SCHEDULE 27A2 EXCEL POPULATION COMPLETE ====\n")

  #       {:error, error} ->
  #         Logger.error("Failed to decode Schedule 27A2 data: #{inspect(error)}")
  #         IO.puts("\n==== SCHEDULE 27A2 DECODE ERROR ====")
  #         IO.puts("Error: #{inspect(error)}")
  #         IO.puts("Raw data: #{inspect(data.schedule_27A2)}")
  #         IO.puts("==== END SCHEDULE 27A2 DECODE ERROR ====\n")
  #     end
  #   end
  # end
  defp schedule_27A2(file_id, data) do
    if is_nil(data.schedule_27A2) do
      Logger.warning("Schedule 27A2 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27A2) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27A2")
          cell_mapping = create_schedule_27A2_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn
            # Handle TPIN and Account Number columns (A and C)
            {<<"A", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (TPIN)")

            {<<"C", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (Account Number)")

            # Handle zero values
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            # Handle nil values
            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            # Handle string values
            {cell_index, value} when is_binary(value) ->
              column = String.first(cell_index)
              cond do
                column in ["A", "C"] ->
                  # Keep as string for identifier columns
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value} (identifier column)")

                Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) ->
                  float_value = Utils.string_to_float(value)
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", float_value)
                  IO.puts("Set cell #{cell_index} to float: #{float_value}")

                true ->
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            # Handle integer values
            {cell_index, value} when is_integer(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "int", value)
                IO.puts("Set cell #{cell_index} to int: #{value}")
              end

            # Handle float values
            {cell_index, value} when is_float(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "float", value)
                IO.puts("Set cell #{cell_index} to float: #{value}")
              end

            # Handle any other value types
            {cell_index, value} ->
              string_value = to_string(value)
              if String.first(cell_index) in ["A", "C"] do
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A2", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value}")
              end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27A2 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27A2 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  # Validate the data to ensure all required fields are present
  defp validate_data(data) do
    Logger.info("Validating data for export type: #{data.export_type}")
    IO.puts("\n==== VALIDATING DATA ====")
    IO.puts("Export Type: #{data.export_type}")
    IO.puts("Reference: #{data.reference}")
    IO.puts("Report Date: #{data.report_date}")

    # Check if report_data exists
    if is_nil(data.report_data) do
      IO.puts("ERROR: Report data is missing")
      {:error, "Report data is missing"}
    else
      # Define required fields based on export type
      required_fields =
        case data.export_type do
          "CORE_LIQUID_ASSETS" ->
            fields = [
              :schedule_27,
              :schedule_27A1,
              :schedule_27A2,
              :schedule_27A3,
              :schedule_27A4,
              :schedule_27A5,
              :schedule_27B1,
              :schedule_27B2
            ]

            IO.puts("Required fields for CORE_LIQUID_ASSETS: #{inspect(fields)}")
            fields

          "FOREX_RISK" ->
            fields = [:schedule_21A, :schedule_21B]
            IO.puts("Required fields for FOREX_RISK: #{inspect(fields)}")
            fields

          _ ->
            IO.puts("ERROR: Unsupported export type: #{data.export_type}")
            []
        end

      if Enum.empty?(required_fields) do
        IO.puts("ERROR: No required fields defined for export type: #{data.export_type}")
        {:error, "Unsupported export type: #{data.export_type}"}
      else
        # Handle report_data that might be an Ecto struct
        report_data =
          if Map.has_key?(data.report_data, :__struct__) do
            # Convert Ecto struct to map
            IO.puts("Converting report_data from Ecto struct to map")
            Map.from_struct(data.report_data)
          else
            data.report_data
          end

        # Check for missing fields
        missing_fields =
          Enum.filter(required_fields, fn field ->
            is_nil(Map.get(report_data, field))
          end)

        # Print available fields
        available_fields =
          Enum.filter(required_fields, fn field ->
            !is_nil(Map.get(report_data, field))
          end)

        IO.puts("\nAvailable fields:")

        Enum.each(available_fields, fn field ->
          field_data = Map.get(report_data, field)
          IO.puts("- #{field}: #{typeof(field_data)}")
        end)

        if Enum.empty?(missing_fields) do
          IO.puts("\nValidation successful: All required fields are present")
          :ok
        else
          missing_field_names = Enum.map_join(missing_fields, ", ", &Atom.to_string/1)
          IO.puts("\nERROR: Missing required fields: #{missing_field_names}")
          {:error, "Missing required data for #{data.export_type} report: #{missing_field_names}"}
        end
      end
    end
  end

  # New function to just copy the template without modifying it
  def gen_file_only(item) do
    try do
      dir = MisReports.Utilities.get_directory_params()

      # Use the report date from the item for the filename
      date_str =
        if is_binary(item.report_date),
          do: item.report_date,
          else: Date.to_string(item.report_date)

      # Add report type to filename for clarity
      report_type =
        case item.export_type do
          "CORE_LIQUID_ASSETS" -> "CLA"
          "FOREX_RISK" -> "FX"
          _ -> "UNKNOWN"
        end

      # Use the standardized filename format: Weekly_[TYPE]_Report_[DATE].xlsx
      name = "Weekly_#{report_type}_Report_#{date_str}.xlsx"
      Logger.info("Generated filename: #{name}")

      # Use Windows-style paths for the destination file
      dest_file = Path.join(dir.complete, name)
      # Also try with forward slashes for compatibility
      dest_file = String.replace(dest_file, "\\", "/")

      # Get template path for this export type
      template_file =
        case get_template_path(item.export_type) do
          {:ok, path} ->
            Logger.info("Successfully found template for #{item.export_type} at: #{path}")
            path

          {:error, reason} ->
            # Log the error and re-raise with a more specific message
            Logger.error("Failed to find template for #{item.export_type}: #{reason}")

            error_msg =
              "Cannot generate #{item.export_type} report: Template not found. #{reason}"

            raise error_msg
        end

      # Log the paths for debugging
      Logger.info("Template file path: #{template_file}")
      Logger.info("Destination file path: #{dest_file}")
      Logger.info("Export type: #{item.export_type}")

      # Ensure directories exist
      dest_dir = Path.dirname(dest_file)
      Logger.info("Creating destination directory if it doesn't exist: #{dest_dir}")
      File.mkdir_p!(dest_dir)

      # Copy the template file
      Logger.info("Copying template file from #{template_file} to #{dest_file}")

      # First verify the template file exists and has content
      case File.stat(template_file) do
        {:ok, %{size: template_size}} ->
          if template_size == 0 do
            Logger.error("Template file exists but is empty (0 bytes): #{template_file}")
            raise "Template file is empty (0 bytes): #{template_file}"
          else
            Logger.info("Template file exists and has size: #{template_size} bytes")
          end

        {:error, reason} ->
          Logger.error("Failed to stat template file: #{inspect(reason)}")
          raise "Failed to stat template file: #{inspect(reason)}"
      end

      # Try to copy the file using File.cp! which will raise an error if it fails
      # This is more reliable than File.copy for cross-platform operations
      try do
        File.cp!(template_file, dest_file)

        # Verify the copied file exists and has content
        case File.stat(dest_file) do
          {:ok, %{size: dest_size}} ->
            if dest_size == 0 do
              Logger.error("Destination file was created but is empty (0 bytes): #{dest_file}")
              raise "Destination file is empty after copy: #{dest_file}"
            else
              Logger.info(
                "Successfully copied template from #{template_file} to #{dest_file} (#{dest_size} bytes)"
              )

              dest_file
            end

          {:error, reason} ->
            Logger.error("Failed to stat destination file after copy: #{inspect(reason)}")
            raise "Failed to verify destination file after copy: #{inspect(reason)}"
        end
      rescue
        e ->
          Logger.error("Failed to copy template: #{inspect(e)}")

          # Check if the destination directory exists
          if File.dir?(dest_dir) do
            Logger.info("Destination directory exists: #{dest_dir}")
            # Check if we have write permissions
            case File.touch("#{dest_dir}/test_write_permission") do
              :ok ->
                File.rm("#{dest_dir}/test_write_permission")
                Logger.info("Have write permissions to destination directory")

              {:error, write_reason} ->
                Logger.error(
                  "No write permissions to destination directory: #{inspect(write_reason)}"
                )
            end
          else
            Logger.error(
              "Destination directory does not exist after creation attempt: #{dest_dir}"
            )
          end

          # Try an alternative copy method using system command
          try do
            Logger.info("Attempting alternative copy method using system command")
            # Use PowerShell for Windows
            {output, exit_code} =
              System.cmd("powershell", [
                "-Command",
                "Copy-Item",
                "-Path",
                template_file,
                "-Destination",
                dest_file,
                "-Force"
              ])

            if exit_code == 0 do
              # Verify the file was copied successfully
              case File.stat(dest_file) do
                {:ok, %{size: alt_size}} ->
                  if alt_size > 0 do
                    Logger.info("Alternative copy method succeeded: #{alt_size} bytes")
                    dest_file
                  else
                    Logger.error("Alternative copy method created empty file")
                    raise "Alternative copy method created empty file"
                  end

                _ ->
                  Logger.error("Failed to verify file after alternative copy")
                  raise "Failed to verify file after alternative copy"
              end
            else
              Logger.error("Alternative copy method failed: #{output}")
              raise "Alternative copy method failed: #{output}"
            end
          rescue
            alt_e ->
              Logger.error("Alternative copy method also failed: #{inspect(alt_e)}")
              raise "Failed to copy template using all available methods"
          end
      end
    rescue
      e ->
        Logger.error("Exception in gen_file_only: #{inspect(e)}")
        Logger.error(Exception.format_stacktrace(__STACKTRACE__))
        reraise e, __STACKTRACE__
    end
  end

  defp get_template_path(type) do
    try do
      # Get the templates directory from database
      dir = MisReports.Utilities.get_directory_params()
      templates_dir = dir.templates

      IO.puts("\n=== TEMPLATE PATH RESOLUTION ===")
      IO.puts("Export type: #{type}")
      IO.puts("Templates directory: #{templates_dir}")

      # Determine template name based on export type
      template_name = case type do
        "CORE_LIQUID_ASSETS" -> "core_liquid_assets_template.xlsx"
        "FOREX_RISK" -> "forex_risk_template.xlsx"
        _ ->
          error_msg = "Unsupported export type: #{type}. Only CORE_LIQUID_ASSETS and FOREX_RISK are supported."
          Logger.error(error_msg)
          raise error_msg
      end

      template_path = Path.join(templates_dir, template_name)
      IO.puts("Full template path: #{template_path}")

      # Verify the template exists and has content
      case File.stat(template_path) do
        {:ok, %{size: size}} when size > 0 ->
          IO.puts("Template file exists and has size: #{size} bytes")
          {:ok, template_path}

        {:ok, %{size: 0}} ->
          error_msg = "Template file exists but is empty: #{template_path}"
          Logger.error(error_msg)
          {:error, error_msg}

        {:error, reason} ->
          error_msg = "Template file not found or inaccessible: #{template_path} (#{inspect(reason)})"
          Logger.error(error_msg)
          {:error, error_msg}
      end
    rescue
      e ->
        error_msg = "Error resolving template path: #{inspect(e)}"
        Logger.error(error_msg)
        Logger.error(Exception.format_stacktrace(__STACKTRACE__))
        {:error, error_msg}
    end
  end

  def gen_file(item) do
    try do
      dir = MisReports.Utilities.get_directory_params()

      # Use the report date from the item for the filename
      date_str =
        if is_binary(item.report_date),
          do: item.report_date,
          else: Date.to_string(item.report_date)

      # Add report type to filename for clarity
      report_type =
        case item.export_type do
          "CORE_LIQUID_ASSETS" -> "CLA"
          "FOREX_RISK" -> "FX"
          _ -> "UNKNOWN"
        end

      # Use the standardized filename format: Weekly_[TYPE]_Report_[DATE].xlsx
      name = "Weekly_#{report_type}_Report_#{date_str}.xlsx"
      Logger.info("Generated filename: #{name}")

      # Update the file export record with the filename and status
      case MisReports.Utilities.get_weekly_export_by_reference(item.reference) do
        nil ->
          Logger.error("No weekly file export record found for reference: #{item.reference}")

        export_record ->
          # Update the record with the filename and status
          case MisReports.Utilities.update_weekly_file_export(export_record, %{
                 filename: name,
                 status: "PENDING_EXPORT"
               }) do
            {:ok, _} ->
              Logger.info(
                "Updated weekly file export record with filename: #{name} and status: PENDING_EXPORT for reference: #{item.reference}"
              )

            {:error, changeset} ->
              error_msg = traverse_errors(changeset.errors) |> Enum.join("\r\n")

              Logger.error(
                "Failed to update weekly file export record with filename and status: #{error_msg} for reference: #{item.reference}"
              )
          end
      end

      # Final destination file path - normalize with forward slashes for WSL compatibility
      dest_file =
        Path.join(dir.complete, name)
        |> String.replace("\\", "/")

      Logger.info("Destination file: #{dest_file}")

      # Get template path for this export type
      template_file =
        case get_template_path(item.export_type) do
          {:ok, path} ->
            Logger.info("Successfully found template for #{item.export_type} at: #{path}")
            path

          {:error, reason} ->
            # Log the error and re-raise with a more specific message
            Logger.error("Failed to find template for #{item.export_type}: #{reason}")

            error_msg =
              "Cannot generate #{item.export_type} report: Template not found. #{reason}"

            raise error_msg
        end

      Logger.info("Using template file: #{template_file}")

      # Create destination directory if it doesn't exist
      dest_dir = Path.dirname(dest_file)
      Logger.info("Creating destination directory if needed: #{dest_dir}")
      File.mkdir_p!(dest_dir)

      # Remove destination file if it exists (with error handling)
      if File.exists?(dest_file) do
        Logger.info("Removing existing destination file: #{dest_file}")

        case File.rm(dest_file) do
          :ok ->
            Logger.info("Successfully removed existing file: #{dest_file}")

          {:error, :eacces} ->
            Logger.warning("Permission denied removing file (may be open in Excel): #{dest_file}")
            Logger.info("Will attempt to overwrite the file instead")

          {:error, reason} ->
            Logger.warning("Failed to remove existing file: #{inspect(reason)}")
            Logger.info("Will attempt to overwrite the file instead")
        end
      end

      # Copy template directly to destination
      Logger.info("Copying template directly from #{template_file} to #{dest_file}")

      case try_copy_methods(template_file, dest_file) do
        {:ok, final_path} ->
          Logger.info("Successfully copied template to destination: #{final_path}")

          # Verify the file was created successfully
          case File.stat(final_path) do
            {:ok, %{size: size}} when size > 0 ->
              Logger.info("Destination file verified: #{size} bytes")

              # Update the item with the final file path
              item = Map.put(item, :file_path, final_path)
              Process.put(:current_item, item)

              final_path

            {:ok, %{size: 0}} ->
              Logger.error("Destination file was created but is empty")
              raise "Destination file is empty after copy"

            {:error, reason} ->
              Logger.error("Failed to verify destination file: #{inspect(reason)}")
              raise "Failed to verify destination file: #{inspect(reason)}"
          end

        {:error, error_msg} ->
          Logger.error("Failed to copy template: #{error_msg}")
          Logger.info("Attempting to use template file directly for processing")

          # Instead of raising an error, try to use the template file directly
          # This allows the process to continue even if copying fails
          if File.exists?(template_file) do
            Logger.info("Using template file directly: #{template_file}")

            # Update the item with the template file path
            item = Map.put(item, :file_path, template_file)
            Process.put(:current_item, item)

            template_file
          else
            Logger.error("Template file does not exist: #{template_file}")
            raise "Template file not found: #{template_file}"
          end
      end
    rescue
      e ->
        Logger.error("Exception in gen_file: #{inspect(e)}")
        Logger.error(Exception.format_stacktrace(__STACKTRACE__))
        reraise e, __STACKTRACE__
    end
  end

  # Helper function to try multiple copy methods
  defp try_copy_methods(source, destination) do
    Logger.info("Attempting to copy from #{source} to #{destination}")

    # Ensure destination directory exists
    dest_dir = Path.dirname(destination)
    File.mkdir_p!(dest_dir)

    # Try direct copy first
    try do
      Logger.info("Trying direct File.copy from #{source} to #{destination}")
      case File.copy(source, destination, [:overwrite]) do
        {:ok, bytes} ->
          Logger.info("Successfully copied #{bytes} bytes")
          {:ok, destination}
        {:error, reason} ->
          Logger.error("Direct copy failed: #{inspect(reason)}")
          raise "Direct copy failed: #{inspect(reason)}"
      end
    rescue
      e ->
        Logger.warning("Direct copy failed: #{inspect(e)}")

        # Try read/write method as fallback
        try do
          Logger.info("Trying read/write method")
          content = File.read!(source)
          :ok = File.write(destination, content, [:write])

          # Verify file was written successfully
          case File.stat(destination) do
            {:ok, %{size: size}} when size > 0 ->
              Logger.info("Successfully wrote #{size} bytes using read/write method")
              {:ok, destination}
            _ ->
              Logger.error("Read/write method failed verification")
              {:error, "Failed to copy file using read/write method"}
          end
        rescue
          e2 ->
            Logger.error("Read/write method failed: #{inspect(e2)}")
            {:error, "All copy methods failed"}
        end
    end
  end

  defp populate_core_liquid_assets(file_id, data) do
    # Use the report_data field which contains the WeeklyReport data
    report_data = data.report_data
    Logger.info("Populating Core Liquid Assets schedules")

    # Populate all schedules, continuing even if one fails
    [
      {"Schedule 27", &schedule_27/2},
      {"Schedule 27A1", &schedule_27A1/2},
      {"Schedule 27A2", &schedule_27A2/2},
      {"Schedule 27A3", &schedule_27A3/2},
      {"Schedule 27A4", &schedule_27A4/2},
      {"Schedule 27A5", &schedule_27A5/2},
      {"Schedule 27B1", &schedule_27B1/2},
      {"Schedule 27B2", &schedule_27B2/2}
    ]
    |> Enum.each(fn {name, func} ->
      try do
        Logger.info("Populating #{name}")
        func.(file_id, report_data)
      rescue
        e -> Logger.error("Error populating #{name}: #{inspect(e)}")
      end
    end)

    # Save the workbook
    try do
      Excelizer.Workbook.save(file_id)
    rescue
      e -> Logger.error("Error saving workbook: #{inspect(e)}")
    end
  end

  defp populate_forex_risk(file_id, data) do
    # Use the report_data field which contains the WeeklyReport data
    report_data = data.report_data
    Logger.info("Populating Forex Risk schedules")

    # Populate all schedules, continuing even if one fails
    [
      {"Schedule 21A", &schedule_21A/2},
      {"Schedule 21B", &schedule_21B/2}
    ]
    |> Enum.each(fn {name, func} ->
      try do
        Logger.info("Populating #{name}")
        func.(file_id, report_data)
      rescue
        e -> Logger.error("Error populating #{name}: #{inspect(e)}")
      end
    end)

    # Save the workbook
    try do
      Excelizer.Workbook.save(file_id)
    rescue
      e -> Logger.error("Error saving workbook: #{inspect(e)}")
    end
  end

  # Core Liquid Assets schedule population functions
  defp schedule_27(file_id, data) do
    if is_nil(data.schedule_27) do
      Logger.warning("Schedule 27 data is nil, skipping")
      :ok
    else
      # Decode the JSON data
      case Poison.decode(data.schedule_27) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27")

          # Map data to Excel cells
          cell_mapping = create_schedule_27_cell_mapping(decoded_data)

          # Process each mapped cell
          cell_mapping
          |> Stream.each(fn
            # Handle zero values
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            # Handle nil values
            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            # Handle string values that represent numbers
            {cell_index, value} when is_binary(value) ->
              case Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) do
                true ->
                  float_value = Utils.string_to_float(value)

                  Excelizer.Cell.set_cell_value(
                    file_id,
                    "Schedule 27",
                    cell_index,
                    "float",
                    float_value
                  )

                  IO.puts(
                    "Set cell #{cell_index} to float: #{float_value} (converted from string)"
                  )

                false ->
                  Excelizer.Cell.set_cell_value(
                    file_id,
                    "Schedule 27",
                    cell_index,
                    "string",
                    value
                  )

                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            # Handle integer values
            {cell_index, value} when is_integer(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27", cell_index, "int", value)
              IO.puts("Set cell #{cell_index} to int: #{value}")

            # Handle float values
            {cell_index, value} when is_float(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27", cell_index, "float", value)
              IO.puts("Set cell #{cell_index} to float: #{value}")

            # Handle any other value types
            {cell_index, value} ->
              string_value = to_string(value)

              Excelizer.Cell.set_cell_value(
                file_id,
                "Schedule 27",
                cell_index,
                "string",
                string_value
              )

              IO.puts(
                "Set cell #{cell_index} to string: #{string_value} (converted from #{typeof(value)})"
              )
          end)
          |> Stream.run()

          # Save workbook after population
          Logger.info("Saving workbook after Schedule 27 population")
          Excelizer.Workbook.save(file_id)
          IO.puts("==== SCHEDULE 27 EXCEL POPULATION COMPLETE ====\n")

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27 data: #{inspect(error)}")
          IO.puts("\n==== SCHEDULE 27 DECODE ERROR ====")
          IO.puts("Error: #{inspect(error)}")
          IO.puts("Raw data: #{inspect(data.schedule_27)}")
          IO.puts("==== END SCHEDULE 27 DECODE ERROR ====\n")
      end
    end
  end

  # Helper function to map Schedule 27 data to Excel cell positions
  defp create_schedule_27_cell_mapping(decoded_data) do
    # Map each field to the corresponding cell in the Excel sheet
    cell_mapping = %{
      # SELECTED ASSETS
      # 1. Total Core Liquid Assets (a+b+c+d+e+f-g)
      "C16" => Map.get(decoded_data, "total_core_liquid", "0.00"),
      # (a) Zambia Notes and Coins
      "C17" => Map.get(decoded_data, "zambia_notes_coins", "0.00"),
      # (b) Current Account Balances at BOZ
      "C18" => Map.get(decoded_data, "current_account_boz", "0.00"),
      # (c) Treasury Bill Holdings at face value
      "C19" => Map.get(decoded_data, "tbills_holdings_at_face_value", "0.00"),
      # (d) OMO Term Deposits
      "C20" => Map.get(decoded_data, "omo_term_deposits", "0.00"),
      # (e) OMO Repo Placements
      "C21" => Map.get(decoded_data, "omo_repo_placements", "0.00"),
      # (f) Collateralised Interbank Loans Made
      "C22" => Map.get(decoded_data, "col_interbank_loans_made", "0.00"),
      # (g) Collateralised Interbank Loans Received
      "C23" => Map.get(decoded_data, "col_interbank_loans_received", "0.00"),

      # 2. Kwacha Statutory Reserve Account Balances at BOZ
      "C25" => Map.get(decoded_data, "kwacha_statutory_reserve", "0.00"),

      # 3. Government securities (eligible for statutory reserve requirements):
      "C27" => Map.get(decoded_data, "govt_securities", "0.00"),
      # (a) Government bond ISIN ZM1000006370
      "C28" => Map.get(decoded_data, "govt_bond_ZM1000006370", "0.00"),
      # (b) Government bond ISIN ZM1000006388
      "C29" => Map.get(decoded_data, "govt_bond_ZM1000006388", "0.00"),
      # (c) Government bond ISIN ZM1000006396
      "C30" => Map.get(decoded_data, "govt_bond_ZM1000006396", "0.00"),

      # 4. Foreign Currency (FCY) Statutory Reserve Account Balances at BOZ (US $)
      "B32" => Map.get(decoded_data, "fcy_statutory_reserve", "0.00"),

      # 5. (a) Kwacha Loans and Advances Outstanding
      "C34" => Map.get(decoded_data, "kwacha_loans_advances_outstanding", "0.00"),
      # (b) Foreign Currency Loans and Advances Outstanding (US $)
      "B35" => Map.get(decoded_data, "fycla_outstanding", "0.00"),

      # 6. (a) New Kwacha Loans to Agriculture
      "C37" => Map.get(decoded_data, "new_kwacha_loans_agriculture", "0.00"),
      # (b) New Foreign Currency Loans to Agriculture (US $)
      "B38" => Map.get(decoded_data, "new_fcy_loans_agriculture", "0.00"),

      # SELECTED LIABILITIES
      # 7. Kwacha Deposit Liabilities to the Public (a) + (b)
      "C42" => Map.get(decoded_data, "add_kwacha_deposit_liabilities_public", "0.00"),
      # (a) Kwacha Deposit Liabilities to the Public
      "C43" => Map.get(decoded_data, "kwacha_deposit_liabilities_public", "0.00"),
      # (b) Bills Payable
      # Not provided in the data
      "C44" => "0.00",

      # 8. Total Kwacha Deposit Liabilities to Foreign Institutions (Vostro Accounts)
      "C46" => Map.get(decoded_data, "kwacha_deposit_foreign_vostro", "0.00"),
      # 9. Total Kwacha Deposit Liabilities to the Government (Govt Deposits)
      "C47" => Map.get(decoded_data, "total_kwacha_deposit_liabilities_govt", "0.00"),

      # 10. Foreign Currency Deposit Liabilities to the Public in US$
      "B49" => Map.get(decoded_data, "fyc_deposit_liabilities_public", "0.00"),
      # 11. Foreign Currency Deposit Liabilities to Foreign Institutions (Vostro Accounts) in US$
      "B50" => Map.get(decoded_data, "fcy_deposit_foreign_vostro", "0.00"),
      # 12. Foreign Currency Deposit Liabilities to Government (Govt Deposits) in US$
      "B52" => Map.get(decoded_data, "fcy_deposit_liabilities_govt", "0.00"),

      # 13. Kwacha Deposit Liabilities to the Public, to Foreign Institutions and to Government, less New Agricultural Lending in Kwacha
      "C55" => Map.get(decoded_data, "kwacha_deposit_foreign_govt_less_agr", "0.00"),

      # 14. Foreign Currency Deposit Liabilities to the Public, to Foreign Institutions and to Government, less New Agricultural Lending in Foreign Currency
      "B57" => Map.get(decoded_data, "fyc_deposit_foreign_govt_less_agr", "0.00"),

      # COMPLIANCE RATIOS
      # 15. Core Liquid Asset Ratio (%) (1/6)
      "C62" => Map.get(decoded_data, "core_liquid_asset_ratio", "0.00"),
      # 16. Minimum Required Core Liquid Assets (6% of 6)
      "C63" => Map.get(decoded_data, "minimum_required_CLA", "0.00"),
      # 17. Excess/shortfall in Core Liquid Assets (1 - 15)
      "C64" => Map.get(decoded_data, "excess_shortfall_CLA", "0.00"),

      # 18. Kwacha Statutory Reserve Ratio (%) (2/12)
      "C67" => Map.get(decoded_data, "kwacha_statutory_reserve_ratio", "0.00"),
      # 19. Minimum Kwacha Statutory Reserves (26.0% of 12)
      "C68" => Map.get(decoded_data, "minimum_KSR", "0.00"),
      # 20. Maximum Government securities (eligible for statutory reserve requirements)
      "C69" => Map.get(decoded_data, "max_govt_securities", "0.00"),
      # 21. Excess/shortfall in Kwacha Statutory Reserves (2 - 18)
      "C70" => Map.get(decoded_data, "excess_shortfall_KSR", "0.00"),

      # 22. FCY (US $) Statutory Reserve Ratio (%) (3/13)
      "C73" => Map.get(decoded_data, "fcy_statutory_reserve_ratio", "0.00"),
      # 23. Minimum FCY Statutory Reserves (26.0% of 13)
      "C74" => Map.get(decoded_data, "minimum_fcy_SR", "0.00"),
      # 24. Excess/shortfall in FCY Statutory Reserves (US $) (3 - 21)
      "C75" => Map.get(decoded_data, "excess_shortfall_fyc_SR", "0.00"),

      # Header information
      "B3" => "003000",
      "B4" => "Monday, 23 December 2024",
      "B5" => "Sunday, 29 December 2024"
    }


    cell_mapping
  end

  # Helper function to map Schedule 27A1 list/total_balance data to Excel cell positions
  # Following Prudential pattern for list/total_balance structure
  # Data mapping starts at row 13: A13=TPIN | B13=ACCOUNT HOLDER | C13=SECTOR | D13=ACTIVITY | E13=AMOUNT
  defp create_schedule_27A1_cell_mapping(decoded_data) do
    # Extract data from the list/total_balance structure
    cell_mapping = %{}
    start_row = 13

    # Get the top_1000 items (top 1000 deposits/withdrawals)
    top_1000_items = get_in(decoded_data, ["top_1000", "items"]) || []
    top_1000_count = length(top_1000_items)

    # Calculate the row for "others" entry
    others_row = start_row + top_1000_count

    # Calculate the starting row for top_600 items (top 600 withdrawals/deposits)
    top_600_start_row = others_row + 1

    # Map grand_total to a specific cell (at the bottom after all entries)
    cell_mapping =
      case Map.get(decoded_data, "grand_total") do
        nil ->
          cell_mapping

        # Total in amount column, place at the bottom
        value ->
          Map.put(
            cell_mapping,
            "E#{top_600_start_row + length(get_in(decoded_data, ["top_600", "items"]) || []) + 1}",
            value
          )
      end

    # Process top_1000 items starting from row 13 (top 1000 deposits/withdrawals)
    cell_mapping =
      if top_1000_count > 0 do
        top_1000_items
        # Start from row 13
        |> Enum.with_index(start_row)
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Map each field to the correct Excel column starting from row 13
          acc
          # TPIN in column A
          |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
          # ACCOUNT HOLDER in column B
          |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
          # SECTOR in column C
          |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
          # ACTIVITY in column D
          |> Map.put("D#{row_index}", Map.get(item, "activity_type", ""))
          # AMOUNT in column E
          |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
        end)
      else
        cell_mapping
      end

    # Map "others" data to row after top_1000 items
    cell_mapping =
      case Map.get(decoded_data, "others") do
        nil ->
          cell_mapping

        others ->
          acc_name = Map.get(others, "name", "Other Deposits")
          activity = Map.get(decoded_data, "primary_activity", "")
          total = Map.get(others, "total", "0.00")

          cell_mapping
          # TPIN in column A (empty for "others")
          |> Map.put("A#{others_row}", "")
          # ACCOUNT HOLDER in column B
          |> Map.put("B#{others_row}", acc_name)
          # SECTOR in column C (empty for "others")
          |> Map.put("C#{others_row}", "")
          # ACTIVITY in column D
          |> Map.put("D#{others_row}", activity)
          # AMOUNT in column E
          |> Map.put("E#{others_row}", total)
      end

    # Process top_600 items starting from row after "others" (top 600 withdrawals/deposits)
    cell_mapping =
      case get_in(decoded_data, ["top_600", "items"]) do
        nil ->
          cell_mapping

        items when is_list(items) ->
          items
          # Start from row after "others"
          |> Enum.with_index(top_600_start_row)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            # Map each field to the correct Excel column
            acc
            # TPIN in column A
            |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
            # ACCOUNT HOLDER in column B
            |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
            # SECTOR in column C
            |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
            # ACTIVITY in column D
            |> Map.put("D#{row_index}", Map.get(item, "activity_type", ""))
            # AMOUNT in column E
            |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
          end)

        _ ->
          cell_mapping
      end



    Enum.each(cell_mapping, fn {cell, value} ->
      IO.puts("#{cell}: #{inspect(value)}")
    end)


    cell_mapping
  end

  defp schedule_27A1(file_id, data) do
    if is_nil(data.schedule_27A1) do
      Logger.warning("Schedule 27A1 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27A1) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27A1")
          cell_mapping = create_schedule_27A1_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn
            # Handle TPIN and Account Number columns
            {<<"A", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (TPIN)")

            {<<"C", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (Account Number)")

            # Handle zero values
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            # Handle nil values
            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            # Handle string values
            {cell_index, value} when is_binary(value) ->
              column = String.first(cell_index)
              cond do
                column in ["A", "C"] ->
                  # Keep as string for identifier columns
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value} (identifier column)")

                Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) ->
                  float_value = Utils.string_to_float(value)
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "float", float_value)
                  IO.puts("Set cell #{cell_index} to float: #{float_value}")

                true ->
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            # Handle integer values
            {cell_index, value} when is_integer(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "int", value)
                IO.puts("Set cell #{cell_index} to int: #{value}")
              end

            # Handle float values
            {cell_index, value} when is_float(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "float", value)
                IO.puts("Set cell #{cell_index} to float: #{value}")
              end

            # Handle any other value types
            {cell_index, value} ->
              string_value = to_string(value)
              if String.first(cell_index) in ["A", "C"] do
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value}")
              end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27A1 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27A1 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  defp create_schedule_27A3_cell_mapping(decoded_data) do
    try do
      # Initialize the mapping
      cell_mapping = %{}
      start_row = 13  # Starting row for data entries

      # Log the input data structure
      IO.puts("\n==== SCHEDULE 27A3 DATA STRUCTURE DEBUG ====")
      IO.puts("Decoded data keys: #{inspect(Map.keys(decoded_data))}")

      # Map total_balance to specific cell
      cell_mapping = case Map.get(decoded_data, "grand_total") do
        nil -> cell_mapping
        total -> Map.put(cell_mapping, "E50", total)
      end

      # Handle primary activity if present
      cell_mapping = case Map.get(decoded_data, "primary_activity") do
        nil -> cell_mapping
        activity -> Map.put(cell_mapping, "D12", activity)
      end

      # Process the top_1000 list if present
      cell_mapping = case get_in(decoded_data, ["top_1000", "items"]) do
        nil ->
          IO.puts("No top_1000 items found")
          cell_mapping
        items when is_list(items) ->
          IO.puts("Processing #{length(items)} top_1000 items")
          items
          |> Enum.with_index(start_row)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            acc
            |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
            |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
            |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
            |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Advance"))
            |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
            |> Map.put("F#{row_index}", Map.get(item, "effective_debit_rate", ""))
          end)
        _ -> cell_mapping
      end

      # Handle others section if present
      cell_mapping = case Map.get(decoded_data, "others") do
        nil -> cell_mapping
        others ->
          # Calculate others row after top_1000 items
          others_row = start_row + (length(get_in(decoded_data, ["top_1000", "items"]) || []))

          acc_name = Map.get(others, "name", "Other Advances")
          activity = Map.get(decoded_data, "primary_activity", "")
          total = Map.get(others, "total", "0.00")
          rate = Map.get(others, "effective_debit_rate", "")

          cell_mapping
          |> Map.put("A#{others_row}", "")  # Empty TPIN for others
          |> Map.put("B#{others_row}", acc_name)
          |> Map.put("C#{others_row}", "")  # Empty sector for others
          |> Map.put("D#{others_row}", activity)
          |> Map.put("E#{others_row}", total)
          |> Map.put("F#{others_row}", rate)
      end

      # Process top_600 items after others section
      cell_mapping = case get_in(decoded_data, ["top_600", "items"]) do
        nil -> cell_mapping
        items when is_list(items) ->
          # Calculate starting row after others
          start_600 = start_row + length(get_in(decoded_data, ["top_1000", "items"]) || []) + 1

          items
          |> Enum.with_index(start_600)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            acc
            |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))
            |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))
            |> Map.put("C#{row_index}", Map.get(item, "bal_leg_num", ""))
            |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Advance"))
            |> Map.put("E#{row_index}", Map.get(item, "actual_this_year_difference", "0.00"))
            |> Map.put("F#{row_index}", Map.get(item, "effective_debit_rate", ""))
          end)
        _ -> cell_mapping
      end

      # Add section totals
      cell_mapping = case get_in(decoded_data, ["top_1000", "total"]) do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E48", value)
      end

      cell_mapping = case get_in(decoded_data, ["top_600", "total"]) do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E49", value)
      end

      # Log the final mapping for debugging
      IO.puts("\n==== SCHEDULE 27A3 CELL MAPPING ====")
      IO.puts("Created #{map_size(cell_mapping)} cell mappings")
      IO.puts("Column mapping: A=TPIN | B=ACCOUNT NAME | C=SECTOR | D=ACTIVITY | E=AMOUNT | F=INTEREST RATE")

      Enum.each(cell_mapping, fn {cell, value} ->
        IO.puts("#{cell}: #{inspect(value)}")
      end)

      cell_mapping
    rescue
      e ->
        Logger.error("Error creating Schedule 27A3 cell mapping: #{inspect(e)}")
        Logger.error(Exception.format_stacktrace(__STACKTRACE__))
        %{}  # Return empty mapping on error
    end
  end

  defp schedule_27A3(file_id, data) do
    if is_nil(data.schedule_27A3) do
      Logger.warning("Schedule 27A3 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27A3) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27A3")
          cell_mapping = create_schedule_27A3_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn {cell_index, value} ->
            # Determine column type and handle accordingly
            cond do
              # Handle TPIN columns (A column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "A") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set TPIN cell #{cell_index} to string: #{string_value} (FORCED STRING to prevent Excel conversion)")

              # Handle customer name columns (B column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "B") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set CUSTOMER NAME cell #{cell_index} to string: #{string_value}")

              # Handle sector columns (C column) - ALWAYS as string (blank)
              is_binary(cell_index) and String.starts_with?(cell_index, "C") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set SECTOR cell #{cell_index} to string: #{string_value}")

              # Handle activity columns (D column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "D") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set ACTIVITY cell #{cell_index} to string: #{string_value}")

              # Handle amount columns (E column) - Handle as numbers
              is_binary(cell_index) and String.starts_with?(cell_index, "E") ->
                case value do
                  v when v in ["0", "0.0", "0.00", nil] ->
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "float", 0.0)
                    IO.puts("Set AMOUNT cell #{cell_index} to float: 0.0")

                  v when is_binary(v) ->
                    # Remove commas and convert to float for amounts
                    cleaned_value = String.replace(v, ",", "")
                    case Float.parse(cleaned_value) do
                      {float_val, _} ->
                        Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "float", float_val)
                        IO.puts("Set AMOUNT cell #{cell_index} to float: #{float_val} (converted from '#{v}')")
                      :error ->
                        Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", v)
                        IO.puts("Set AMOUNT cell #{cell_index} to string: #{v} (could not parse as number)")
                    end

                  v when is_number(v) ->
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "float", v)
                    IO.puts("Set AMOUNT cell #{cell_index} to float: #{v}")

                  _ ->
                    string_value = to_string(value)
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                    IO.puts("Set AMOUNT cell #{cell_index} to string: #{string_value}")
                end

              # Handle interest rate columns (F column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "F") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set INTEREST RATE cell #{cell_index} to string: #{string_value}")

              # Handle any other cells
              true ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A3", cell_index, "string", string_value)
                IO.puts("Set OTHER cell #{cell_index} to string: #{string_value}")
            end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27A3 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27A3 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  # Helper function to map Schedule 27A4 list/total_balance data to Excel cell positions
  # Following Prudential pattern for list/total_balance structure: {"list":[],"total_balance":"0.00"}
  # Data mapping starts at row 13: A13=TPIN | B13=CUSTOMER NAME | C13=SECTOR | D13=ACTIVITY | E13=AMOUNT | F13=INTEREST RATE
  defp create_schedule_27A4_cell_mapping(decoded_data) do
    try do
      # Extract data from the nested structure (same as 27A3 but different activity)
      cell_mapping = %{}

      IO.puts("\n==== SCHEDULE 27A4 DATA STRUCTURE DEBUG ====")
      IO.puts("Decoded data keys: #{inspect(Map.keys(decoded_data))}")
      IO.puts("Decoded data: #{inspect(decoded_data)}")
      IO.puts("==== END SCHEDULE 27A4 DATA STRUCTURE DEBUG ====\n")

      # Map grand_total to a specific cell
      cell_mapping = case Map.get(decoded_data, "grand_total") do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E50", value)  # Total in amount column
      end

      # Process top_1000 items starting from row 13
      cell_mapping = case get_in(decoded_data, ["top_1000", "items"]) do
        nil ->
          IO.puts("No top_1000 items found")
          cell_mapping
        items when is_list(items) ->
          IO.puts("Processing #{length(items)} top_1000 items")
          items
          |> Enum.with_index(13)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            IO.puts("\n--- Processing Schedule 27A4 top_1000 item at row #{row_index} ---")
            IO.puts("Item keys: #{inspect(Map.keys(item))}")
            IO.puts("Item data: #{inspect(item)}")

            # Extract values with fallbacks
            tpin = Map.get(item, "tpin") || Map.get(item, :tpin) || ""
            customer_name = Map.get(item, "acc_name") || Map.get(item, "customer_name") ||
                           Map.get(item, "name") || Map.get(item, :acc_name) ||
                           Map.get(item, :customer_name) || Map.get(item, :name) || ""
            amount = Map.get(item, "actual_this_year_difference") || Map.get(item, "amount") ||
                    Map.get(item, "actual_this_year") || Map.get(item, :actual_this_year_difference) ||
                    Map.get(item, :amount) || Map.get(item, :actual_this_year) || "0.00"

            IO.puts("Extracted values:")
            IO.puts("  TPIN: #{inspect(tpin)}")
            IO.puts("  Customer Name: #{inspect(customer_name)}")
            IO.puts("  Sector: (blank)")
            IO.puts("  Activity: #{inspect(Map.get(decoded_data, "primary_activity", "Repayment"))}")
            IO.puts("  Amount: #{inspect(amount)}")
            IO.puts("  Interest Rate: #{inspect(Map.get(item, "effective_debit_rate", ""))}")

            acc
            |> Map.put("A#{row_index}", tpin)
            |> Map.put("B#{row_index}", customer_name)
            |> Map.put("C#{row_index}", "")
            |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Repayment"))
            |> Map.put("E#{row_index}", amount)
            |> Map.put("F#{row_index}", Map.get(item, "effective_debit_rate", ""))
          end)
        _ ->
          IO.puts("top_1000 items is not a list")
          cell_mapping
      end

      # Calculate current row after top_1000
      current_row = case get_in(decoded_data, ["top_1000", "items"]) do
        nil -> 13
        items when is_list(items) -> 13 + length(items)
        _ -> 13
      end

      # Process top_600 items
      cell_mapping = case get_in(decoded_data, ["top_600", "items"]) do
        nil ->
          IO.puts("No top_600 items found")
          cell_mapping
        items when is_list(items) ->
          IO.puts("Processing #{length(items)} top_600 items starting from row #{current_row}")
          items
          |> Enum.with_index(current_row)
          |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
            IO.puts("\n--- Processing Schedule 27A4 top_600 item at row #{row_index} ---")
            IO.puts("Item keys: #{inspect(Map.keys(item))}")
            IO.puts("Item data: #{inspect(item)}")

            # Extract values with fallbacks
            tpin = Map.get(item, "tpin") || Map.get(item, :tpin) || ""
            customer_name = Map.get(item, "acc_name") || Map.get(item, "customer_name") ||
                           Map.get(item, "name") || Map.get(item, :acc_name) ||
                           Map.get(item, :customer_name) || Map.get(item, :name) || ""
            amount = Map.get(item, "actual_this_year_difference") || Map.get(item, "amount") ||
                    Map.get(item, "actual_this_year") || Map.get(item, :actual_this_year_difference) ||
                    Map.get(item, :amount) || Map.get(item, :actual_this_year) || "0.00"

            IO.puts("Extracted values:")
            IO.puts("  TPIN: #{inspect(tpin)}")
            IO.puts("  Customer Name: #{inspect(customer_name)}")
            IO.puts("  Sector: (blank)")
            IO.puts("  Activity: #{inspect(Map.get(decoded_data, "primary_activity", "Repayment"))}")
            IO.puts("  Amount: #{inspect(amount)}")
            IO.puts("  Interest Rate: #{inspect(Map.get(item, "effective_debit_rate", ""))}")

            acc
            |> Map.put("A#{row_index}", tpin)
            |> Map.put("B#{row_index}", customer_name)
            |> Map.put("C#{row_index}", "")
            |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Repayment"))
            |> Map.put("E#{row_index}", amount)
            |> Map.put("F#{row_index}", Map.get(item, "effective_debit_rate", ""))
          end)
        _ ->
          IO.puts("top_600 items is not a list")
          cell_mapping
      end

      # Add totals
      cell_mapping = case get_in(decoded_data, ["top_1000", "total"]) do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E48", value)
      end

      cell_mapping = case get_in(decoded_data, ["top_600", "total"]) do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E49", value)
      end

      cell_mapping = case get_in(decoded_data, ["others", "total"]) do
        nil -> cell_mapping
        value -> Map.put(cell_mapping, "E51", value)
      end

      IO.puts("\n==== SCHEDULE 27A4 CELL MAPPING ====")
      IO.puts("Created #{map_size(cell_mapping)} cell mappings")
      IO.puts("Data mapping starts at row 13: A=TPIN | B=CUSTOMER NAME | C=SECTOR | D=ACTIVITY | E=AMOUNT | F=INTEREST RATE")
      IO.puts("Processing nested structure: top_1000, top_600, others, grand_total")

      Enum.each(cell_mapping, fn {cell, value} ->
        IO.puts("#{cell}: #{inspect(value)}")
      end)
      IO.puts("==== END SCHEDULE 27A4 CELL MAPPING ====\n")

      cell_mapping
    rescue
      e ->
        IO.puts("ERROR in create_schedule_27A4_cell_mapping: #{inspect(e)}")
        IO.puts(Exception.format_stacktrace(__STACKTRACE__))
        %{}  # Return empty map on error
    end
  end

  defp schedule_27A4(file_id, data) do
    if is_nil(data.schedule_27A4) do
      Logger.warning("Schedule 27A4 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27A4) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27A4")
          cell_mapping = create_schedule_27A4_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn {cell_index, value} ->
            # Determine column type and handle accordingly
            cond do
              # Handle TPIN columns (A column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "A") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set TPIN cell #{cell_index} to string: #{string_value} (FORCED STRING to prevent Excel conversion)")

              # Handle customer name columns (B column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "B") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set CUSTOMER NAME cell #{cell_index} to string: #{string_value}")

              # Handle sector columns (C column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "C") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set SECTOR cell #{cell_index} to string: #{string_value}")

              # Handle activity columns (D column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "D") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set ACTIVITY cell #{cell_index} to string: #{string_value}")

              # Handle amount columns (E column) - Handle as numbers
              is_binary(cell_index) and String.starts_with?(cell_index, "E") ->
                case value do
                  v when v in ["0", "0.0", "0.00", nil] ->
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "float", 0.0)
                    IO.puts("Set AMOUNT cell #{cell_index} to float: 0.0")

                  v when is_binary(v) ->
                    # Remove commas and convert to float for amounts
                    cleaned_value = String.replace(v, ",", "")
                    case Float.parse(cleaned_value) do
                      {float_val, _} ->
                        Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "float", float_val)
                        IO.puts("Set AMOUNT cell #{cell_index} to float: #{float_val} (converted from '#{v}')")
                      :error ->
                        Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", v)
                        IO.puts("Set AMOUNT cell #{cell_index} to string: #{v} (could not parse as number)")
                    end

                  v when is_number(v) ->
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "float", v)
                    IO.puts("Set AMOUNT cell #{cell_index} to float: #{v}")

                  _ ->
                    string_value = to_string(value)
                    Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                    IO.puts("Set AMOUNT cell #{cell_index} to string: #{string_value}")
                end

              # Handle interest rate columns (F column) - ALWAYS as string
              is_binary(cell_index) and String.starts_with?(cell_index, "F") ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set INTEREST RATE cell #{cell_index} to string: #{string_value}")

              # Handle any other cells
              true ->
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A4", cell_index, "string", string_value)
                IO.puts("Set OTHER cell #{cell_index} to string: #{string_value}")
            end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27A4 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27A4 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  defp schedule_27A5(file_id, data) do
    if is_nil(data.schedule_27A5) do
      Logger.warning("Schedule 27A5 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27A5) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27A5")
          cell_mapping = create_schedule_27A5_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn
            # Handle TPIN and Account Number columns
            {<<"A", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (TPIN)")

            {<<"C", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (Account Number)")

            # Handle zero values
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            # Handle nil values
            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            # Handle string values
            {cell_index, value} when is_binary(value) ->
              column = String.first(cell_index)
              cond do
                column in ["A", "C"] ->
                  # Keep as string for identifier columns
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value} (identifier column)")

                Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) ->
                  float_value = Utils.string_to_float(value)
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "float", float_value)
                  IO.puts("Set cell #{cell_index} to float: #{float_value}")

                true ->
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            # Handle integer values
            {cell_index, value} when is_integer(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "int", value)
                IO.puts("Set cell #{cell_index} to int: #{value}")
              end

            # Handle float values
            {cell_index, value} when is_float(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "float", value)
                IO.puts("Set cell #{cell_index} to float: #{value}")
              end

            # Handle any other value types
            {cell_index, value} ->
              string_value = to_string(value)
              if String.first(cell_index) in ["A", "C"] do
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27A5", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value}")
              end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27A5 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27A5 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  # Helper function to map Schedule 27A5 list/total_balance data to Excel cell positions
  # Following Prudential pattern for list/total_balance structure: {"list":[],"total_balance":"0.00"}
  # Data mapping starts at row 13: A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NO | D13=CURRENCY | E13=AMOUNT
  defp create_schedule_27A5_cell_mapping(decoded_data) do
    # Extract data from the list/total_balance structure
    cell_mapping = %{}

    # Map total_balance to a specific cell
    cell_mapping = case Map.get(decoded_data, "total_balance") do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "E50", value)  # Total in amount column
    end

    # Process list items starting from row 13
    # A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NO | D13=CURRENCY | E13=AMOUNT
    cell_mapping = case Map.get(decoded_data, "list") do
      nil -> cell_mapping
      items when is_list(items) ->
        items
        |> Enum.with_index(13)  # Start from row 13
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Map each field to the correct Excel column starting from row 13
          acc
          |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))  # TPIN in column A
          |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))  # ACCOUNT NAME in column B
          |> Map.put("C#{row_index}", Map.get(item, "acc_no", ""))  # ACCOUNT NO in column C
          |> Map.put("D#{row_index}", Map.get(item, "currency_code", ""))  # CURRENCY in column D
          |> Map.put("E#{row_index}", Map.get(item, "actual_this_year", "0.00"))  # AMOUNT in column E
        end)
      _ -> cell_mapping
    end

    cell_mapping
  end

  defp schedule_27B1(file_id, data) do
    if is_nil(data.schedule_27B1) do
      Logger.warning("Schedule 27B1 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27B1) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27B1")
          cell_mapping = create_schedule_27B1_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn
            # Handle TPIN and Account Number columns
            {<<"A", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (TPIN)")

            {<<"C", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (Account Number)")

            # Handle zero values
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            # Handle nil values
            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            # Handle string values
            {cell_index, value} when is_binary(value) ->
              column = String.first(cell_index)
              cond do
                column in ["A", "C"] ->
                  # Keep as string for identifier columns
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value} (identifier column)")

                Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) ->
                  float_value = Utils.string_to_float(value)
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "float", float_value)
                  IO.puts("Set cell #{cell_index} to float: #{float_value}")

                true ->
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            # Handle integer values
            {cell_index, value} when is_integer(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "int", value)
                IO.puts("Set cell #{cell_index} to int: #{value}")
              end

            # Handle float values
            {cell_index, value} when is_float(value) ->
              column = String.first(cell_index)
              if column in ["A", "C"] do
                string_value = to_string(value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "float", value)
                IO.puts("Set cell #{cell_index} to float: #{value}")
              end

            # Handle any other value types
            {cell_index, value} ->
              string_value = to_string(value)
              if String.first(cell_index) in ["A", "C"] do
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value} (identifier column)")
              else
                Excelizer.Cell.set_cell_value(file_id, "Schedule 27B1", cell_index, "string", string_value)
                IO.puts("Set cell #{cell_index} to string: #{string_value}")
              end
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27B1 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27B1 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  # Helper function to map Schedule 27B1 list/total_balance data to Excel cell positions
  # Following Prudential pattern for list/total_balance structure: {"list":[],"total_balance":"0.00"}
  # Data mapping starts at row 13: A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NO | D13=KWACHA BALANCE
  defp create_schedule_27B1_cell_mapping(decoded_data) do
    # Extract data from the list/total_balance structure
    cell_mapping = %{}

    # Map total_balance to a specific cell
    cell_mapping = case Map.get(decoded_data, "total_balance") do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "D50", value)  # Total in kwacha balance column
    end

    # Process list items starting from row 13
    # A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NO | D13=KWACHA BALANCE
    cell_mapping = case Map.get(decoded_data, "list") do
      nil -> cell_mapping
      items when is_list(items) ->
        items
        |> Enum.with_index(13)  # Start from row 13
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Map each field to the correct Excel column starting from row 13
          acc
          |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))  # TPIN in column A
          |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))  # ACCOUNT NAME in column B
          |> Map.put("C#{row_index}", Map.get(item, "acc_no", ""))  # ACCOUNT NO in column C
          |> Map.put("D#{row_index}", Map.get(item, "actual_this_year", "0.00"))  # KWACHA BALANCE in column D
        end)
      _ -> cell_mapping
    end


    cell_mapping
  end

  defp schedule_27B2(file_id, data) do
    if is_nil(data.schedule_27B2) do
      Logger.warning("Schedule 27B2 data is nil, skipping")
      :ok
    else
      case Poison.decode(data.schedule_27B2) do
        {:ok, decoded_data} ->
          Logger.info("Populating Schedule 27B2")
          cell_mapping = create_schedule_27B2_cell_mapping(decoded_data)

          cell_mapping
          |> Stream.each(fn
            # Handle TPIN and Account Number columns directly
            {<<"A", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (TPIN)")

            {<<"C", _::binary>> = cell_index, value} ->
              string_value = if is_nil(value), do: "", else: to_string(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "string", string_value)
              IO.puts("Set cell #{cell_index} to string: #{string_value} (Account Number)")

            # Handle all other columns normally
            {cell_index, value} when value in ["0", "0.0", "0.00"] ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0")

            {cell_index, value} when is_nil(value) ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "float", 0.0)
              IO.puts("Set cell #{cell_index} to float: 0.0 (was nil)")

            {cell_index, value} when is_binary(value) ->
              case Regex.match?(~r/^\d+(\.\d+)?$/, String.replace(value, ",", "")) do
                true ->
                  float_value = Utils.string_to_float(value)
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "float", float_value)
                  IO.puts("Set cell #{cell_index} to float: #{float_value}")
                false ->
                  Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "string", value)
                  IO.puts("Set cell #{cell_index} to string: #{value}")
              end

            {cell_index, value} ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 27B2", cell_index, "string", to_string(value))
              IO.puts("Set cell #{cell_index} to string: #{value}")
          end)
          |> Stream.run()

          Logger.info("Saving workbook after Schedule 27B2 population")
          Excelizer.Workbook.save(file_id)

        {:error, error} ->
          Logger.error("Failed to decode Schedule 27B2 data: #{inspect(error)}")
          {:error, "Failed to decode data"}
      end
    end
  end

  # Helper function to map Schedule 27A2 nested data to Excel cell positions
  # Following Prudential pattern for complex nested structures (same as 27A1 but different cell positions)
  defp create_schedule_27A2_cell_mapping(decoded_data) do
    # Extract data from the nested structure (same structure as 27A1)
    # Structure: {"grand_total":"0.00","others":{"name":"Other Withdrawals","total":"0.00"},"primary_activity":"Withdrawal","top_1000":{"items":[],"total":"0.00"},"top_600":{"items":[],"total":"0.00"}}

    cell_mapping = %{}

    # Map grand_total to a specific cell (different from 27A1)
    cell_mapping = case Map.get(decoded_data, "grand_total") do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "B20", value)
    end

    # Map primary_activity to a specific cell
    cell_mapping = case Map.get(decoded_data, "primary_activity") do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "D13", value)
    end

    # Map others.total to a specific cell
    cell_mapping = case get_in(decoded_data, ["others", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "B21", value)
    end

    # Map others.name to a specific cell
    cell_mapping = case get_in(decoded_data, ["others", "name"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "C13", value)
    end

    # Map top_1000.total to a specific cell
    cell_mapping = case get_in(decoded_data, ["top_1000", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "B22", value)
    end

    # Map top_600.total to a specific cell
    cell_mapping = case get_in(decoded_data, ["top_600", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "B23", value)
    end

    # Process top_1000 items starting from row 13 (same as 27A3/27A4)
    # A13=TPIN | B13=ACCOUNT NAME | C13=SECTOR | D13=ACTIVITY | E13=AMOUNT
    cell_mapping = case get_in(decoded_data, ["top_1000", "items"]) do
      nil ->
        IO.puts("No top_1000 items found")
        cell_mapping
      items when is_list(items) ->
        IO.puts("Processing #{length(items)} top_1000 items")
        items
        |> Enum.with_index(13)  # Start from row 13
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Debug: Inspect each item to see what fields are available
          IO.puts("\n--- Processing Schedule 27A2 top_1000 item at row #{row_index} ---")
          IO.puts("Item keys: #{inspect(Map.keys(item))}")
          IO.puts("Item data: #{inspect(item)}")

          # Try multiple possible field names for TPIN
          tpin = Map.get(item, "tpin") ||
                Map.get(item, :tpin) ||
                ""

          # Try multiple possible field names for account name
          account_name = Map.get(item, "acc_name") ||
                        Map.get(item, "account_holder") ||
                        Map.get(item, "customer_name") ||
                        Map.get(item, "name") ||
                        Map.get(item, :acc_name) ||
                        Map.get(item, :account_holder) ||
                        Map.get(item, :customer_name) ||
                        Map.get(item, :name) ||
                        ""

          # Try multiple possible field names for amount
          amount = Map.get(item, "actual_this_year_difference") ||
                  Map.get(item, "amount") ||
                  Map.get(item, "actual_this_year") ||
                  Map.get(item, :actual_this_year_difference) ||
                  Map.get(item, :amount) ||
                  Map.get(item, :actual_this_year) ||
                  "0.00"

          IO.puts("Extracted values:")
          IO.puts("  TPIN: #{inspect(tpin)}")
          IO.puts("  Account Name: #{inspect(account_name)}")
          IO.puts("  Sector: (blank)")
          IO.puts("  Activity: #{inspect(Map.get(decoded_data, "primary_activity", "Withdrawal"))}")
          IO.puts("  Amount: #{inspect(amount)}")

          # Map each field to the correct Excel column starting from row 13
          # A=TPIN | B=ACCOUNT NAME | C=SECTOR (blank) | D=ACTIVITY | E=AMOUNT
          acc
          |> Map.put("A#{row_index}", tpin)  # TPIN in column A
          |> Map.put("B#{row_index}", account_name)  # ACCOUNT NAME in column B
          |> Map.put("C#{row_index}", "")  # SECTOR in column C (blank)
          |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Withdrawal"))  # ACTIVITY in column D
          |> Map.put("E#{row_index}", amount)  # AMOUNT in column E
        end)
      _ ->
        IO.puts("top_1000 items is not a list")
        cell_mapping
    end

    # Process top_600 items continuing from where top_1000 left off
    current_row = case get_in(decoded_data, ["top_1000", "items"]) do
      nil -> 13
      items when is_list(items) -> 13 + length(items)
      _ -> 13
    end

    cell_mapping = case get_in(decoded_data, ["top_600", "items"]) do
      nil ->
        IO.puts("No top_600 items found")
        cell_mapping
      items when is_list(items) ->
        IO.puts("Processing #{length(items)} top_600 items starting from row #{current_row}")
        items
        |> Enum.with_index(current_row)  # Continue from where top_1000 left off
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Debug: Inspect each item to see what fields are available
          IO.puts("\n--- Processing Schedule 27A2 top_600 item at row #{row_index} ---")
          IO.puts("Item keys: #{inspect(Map.keys(item))}")
          IO.puts("Item data: #{inspect(item)}")

          # Try multiple possible field names for TPIN
          tpin = Map.get(item, "tpin") ||
                Map.get(item, :tpin) ||
                ""

          # Try multiple possible field names for account name
          account_name = Map.get(item, "acc_name") ||
                        Map.get(item, "account_holder") ||
                        Map.get(item, "customer_name") ||
                        Map.get(item, "name") ||
                        Map.get(item, :acc_name) ||
                        Map.get(item, :account_holder) ||
                        Map.get(item, :customer_name) ||
                        Map.get(item, :name) ||
                        ""

          # Try multiple possible field names for amount
          amount = Map.get(item, "actual_this_year_difference") ||
                  Map.get(item, "amount") ||
                  Map.get(item, "actual_this_year") ||
                  Map.get(item, :actual_this_year_difference) ||
                  Map.get(item, :amount) ||
                  Map.get(item, :actual_this_year) ||
                  "0.00"

          IO.puts("Extracted values:")
          IO.puts("  TPIN: #{inspect(tpin)}")
          IO.puts("  Account Name: #{inspect(account_name)}")
          IO.puts("  Sector: (blank)")
          IO.puts("  Activity: #{inspect(Map.get(decoded_data, "primary_activity", "Withdrawal"))}")
          IO.puts("  Amount: #{inspect(amount)}")

          # Map each field to the correct Excel column
          # A=TPIN | B=ACCOUNT NAME | C=SECTOR (blank) | D=ACTIVITY | E=AMOUNT
          acc
          |> Map.put("A#{row_index}", tpin)  # TPIN in column A
          |> Map.put("B#{row_index}", account_name)  # ACCOUNT NAME in column B
          |> Map.put("C#{row_index}", "")  # SECTOR in column C (blank)
          |> Map.put("D#{row_index}", Map.get(decoded_data, "primary_activity", "Withdrawal"))  # ACTIVITY in column D
          |> Map.put("E#{row_index}", amount)  # AMOUNT in column E
        end)
      _ ->
        IO.puts("top_600 items is not a list")
        cell_mapping
    end

    # Add totals for top_1000 and top_600 if they exist
    cell_mapping = case get_in(decoded_data, ["top_1000", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "E48", value)  # Top 1000 total
    end

    cell_mapping = case get_in(decoded_data, ["top_600", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "E49", value)  # Top 600 total
    end

    # Add others total if it exists
    cell_mapping = case get_in(decoded_data, ["others", "total"]) do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "E51", value)  # Others total
    end

    IO.puts("\n==== SCHEDULE 27A2 CELL MAPPING ====")
    IO.puts("Created #{map_size(cell_mapping)} cell mappings")
    IO.puts("Data mapping starts at row 13: A=TPIN | B=ACCOUNT NAME | C=SECTOR | D=ACTIVITY | E=AMOUNT")
    IO.puts("Processing nested structure: top_1000, top_600, others, grand_total")
    Enum.each(cell_mapping, fn {cell, value} ->
      IO.puts("#{cell}: #{inspect(value)}")
    end)
    IO.puts("==== END SCHEDULE 27A2 CELL MAPPING ====\n")

    cell_mapping
  end

  # Helper function to map Schedule 27B2 list/total_balance data to Excel cell positions
  # Following Prudential pattern for list/total_balance structure: {"list":[],"total_balance":"0.00"}
  # Data mapping starts at row 13: A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NUMBER | D13=CURRENCY | E13=AMOUNT
  defp create_schedule_27B2_cell_mapping(decoded_data) do
    # Extract data from the list/total_balance structure
    cell_mapping = %{}

    # Map total_balance to a specific cell
    cell_mapping = case Map.get(decoded_data, "total_balance") do
      nil -> cell_mapping
      value -> Map.put(cell_mapping, "E50", value)  # Total in amount column
    end

    # Process list items starting from row 13
    # A13=TPIN | B13=ACCOUNT NAME | C13=ACCOUNT NUMBER | D13=CURRENCY | E13=AMOUNT
    cell_mapping = case Map.get(decoded_data, "list") do
      nil -> cell_mapping
      items when is_list(items) ->
        items
        |> Enum.with_index(13)  # Start from row 13
        |> Enum.reduce(cell_mapping, fn {item, row_index}, acc ->
          # Map each field to the correct Excel column starting from row 13
          acc
          |> Map.put("A#{row_index}", Map.get(item, "tpin", ""))  # TPIN in column A
          |> Map.put("B#{row_index}", Map.get(item, "acc_name", ""))  # ACCOUNT NAME in column B
          |> Map.put("C#{row_index}", Map.get(item, "acc_no", ""))  # ACCOUNT NUMBER in column C
          |> Map.put("D#{row_index}", Map.get(item, "currency_code", ""))  # CURRENCY in column D
          |> Map.put("E#{row_index}", Map.get(item, "actual_this_year", "0.00"))  # AMOUNT in column E
        end)
      _ -> cell_mapping
    end


    cell_mapping
  end
  defp schedule_21A(file_id, data) do
    if is_nil(data.schedule_21A) do
      Logger.warning("Schedule 21A data is nil, skipping")
      :ok
    else
      # Decode the JSON data
      try do
        case Poison.decode(data.schedule_21A) do
          {:ok, decoded_data} ->
            Logger.info("Successfully decoded Schedule 21A data")
            IO.puts("\n==== SCHEDULE 21A EXCEL POPULATION ====")

            # Process the data using Prudential patterns
            process_schedule_21a_prudential_pattern(file_id, decoded_data)

            IO.puts("==== SCHEDULE 21A EXCEL POPULATION COMPLETE ====\n")
            :ok

          {:error, error} ->
            Logger.error("Failed to decode Schedule 21A data: #{inspect(error)}")
            IO.puts("\n==== SCHEDULE 21A DECODE ERROR ====")
            IO.puts("Error: #{inspect(error)}")
            IO.puts("Raw data: #{inspect(data.schedule_21A)}")
            IO.puts("==== END SCHEDULE 21A DECODE ERROR ====\n")
            :error
        end
      rescue
        e ->
          Logger.error("Exception in Schedule 21A: #{inspect(e)}")
          Logger.error(Exception.format_stacktrace(__STACKTRACE__))
          :error
      end
    end
  end
  defp process_schedule_21a_prudential_pattern(file_id, decoded_data) do
    IO.puts("\n==== PROCESSING SCHEDULE 21A WITH PRUDENTIAL PATTERNS ====")
    IO.puts("File ID: #{inspect(file_id)}")
    IO.puts("Decoded data type: #{typeof(decoded_data)}")
    IO.puts("Decoded data keys: #{inspect(Map.keys(decoded_data))}")

    # Check if this is simple cell mapping (like Schedule 20A) or complex nested structure
    is_simple = is_simple_cell_mapping?(decoded_data)
    IO.puts("Is simple cell mapping: #{is_simple}")

    if is_simple do
      # Handle simple cell mapping like Schedule 20A
      process_schedule_21a_simple_mapping(file_id, decoded_data)
    else
      # Handle complex nested structure with days (enhanced for weekly reports)
      IO.puts("Processing Schedule 21A as complex nested structure with days")
      process_schedule_21a_nested_structure(file_id, decoded_data)
    end

    IO.puts("==== FINISHED PROCESSING SCHEDULE 21A WITH PRUDENTIAL PATTERNS ====")
  end
  defp process_schedule_21a_nested_structure(file_id, decoded_data) do
    IO.puts("\n==== PROCESSING NESTED STRUCTURE FOR SCHEDULE 21A ====")
    IO.puts("Processing Schedule 21A nested structure with Prudential patterns")
    IO.puts("Input data keys: #{inspect(Map.keys(decoded_data))}")

    # Define currency to row mapping for yellow cells (starting from C19)
    # Yellow cells start from C19, with specific day starting positions:
    # Monday: C19, Tuesday: C84, Wednesday: C147, Thursday: C210, Friday: C272
    currency_rows = %{
      "CNY" => 19,
      "DKK" => 22,
      "EUR" => 25,
      "GBP" => 28,
      "JPY" => 31,
      "Others" => 34,
      "USD" => 37,
      "ZAR" => 40
    }

    # Define financial category to column mapping (enhanced with Prudential patterns)
    category_columns = create_schedule_21a_category_mapping()
    IO.puts("Category columns mapping size: #{map_size(category_columns)}")

    # Set currency labels in column A (Prudential pattern)
    IO.puts("\n==== SETTING CURRENCY LABELS ====")
    Enum.each(currency_rows, fn {currency, row} ->
      IO.puts("Setting currency label #{currency} in cell A#{row}")
      try do
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", "A#{row}", "string", currency)
        IO.puts("SUCCESS: Set currency label #{currency} in A#{row}")
      rescue
        e ->
          IO.puts("ERROR: Failed to set currency label #{currency}: #{inspect(e)}")
      end
    end)

    # Process each financial category using Prudential Excel population patterns
    IO.puts("\n==== PROCESSING FINANCIAL CATEGORIES ====")
    Enum.each(decoded_data, fn {category, category_data} ->
      IO.puts("\nProcessing category: #{category}")
      IO.puts("Category data type: #{typeof(category_data)}")
      IO.puts("Category data: #{inspect(category_data)}")

      case Map.get(category_columns, category) do
        nil ->
          IO.puts("Category #{category} not found in mapping, processing as special field")
          # Handle special cases that don't fit the standard pattern
          process_special_schedule_21a_field_prudential(file_id, category, category_data)

        base_column ->
          IO.puts("Category #{category} mapped to base column #{base_column}")
          # Handle standard categories with day/currency structure using Prudential patterns
          process_standard_schedule_21a_category_prudential(file_id, category, category_data, base_column, currency_rows)
      end
    end)

    IO.puts("==== FINISHED PROCESSING NESTED STRUCTURE FOR SCHEDULE 21A ====")
  end

  # Create Schedule 21A category mapping (updated for yellow cells only)
  # Yellow cells start from C19, with specific day starting positions:
  # Tuesday: C84, Wednesday: C147, Thursday: C210, Friday: C272
  defp create_schedule_21a_category_mapping() do
    %{
      "notes_coins_totals" => "C",        # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "short_posi_fcy" => "C",           # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "undelivered_spot_totals" => "C",   # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "forwd_totals" => "C",             # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "blc_fcy_inst_totals" => "C",      # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "long_position_totals" => "C",     # Yellow cells: Tuesday=C84, Wednesday=C147, Thursday=C210, Friday=C272
      "excess_exposure" => "AF",          # Monday=AF, Tuesday=AG, Wednesday=AH, Thursday=AI, Friday=AJ
      "allowable_exposure" => "AK",       # Monday=AK, Tuesday=AL, Wednesday=AM, Thursday=AN, Friday=AO
      "over_under_exposure" => "AP",      # Monday=AP, Tuesday=AQ, Wednesday=AR, Thursday=AS, Friday=AT
      "total_reg_cap_ratio" => "AU",      # Monday=AU, Tuesday=AV, Wednesday=AW, Thursday=AX, Friday=AY

      # Additional fields with enhanced day support
      "spot_totals" => "AZ",              # Monday=AZ, Tuesday=BA, etc.
      "grand_fcy_cur_totals" => "BB",     # Monday=BB, Tuesday=BC, etc.
      "oth_borrowed_funds" => "BD",       # Monday=BD, Tuesday=BE, etc.
      "regulatory_capital_ratio" => "BF", # Monday=BF, Tuesday=BG, etc.
      "daily_rates" => "BH",             # Monday=BH, Tuesday=BI, etc.

      # Extended mappings for comprehensive coverage
      "total_assets" => "BJ",
      "grand_other_off_bs_liabilities" => "BL",
      "fcy_inst_liab_total" => "BN",
      "oth_fcy_cur_total" => "BP",
      "total_short_pos_kwcha" => "BR",
      "aggregated_totals" => "BT",
      "grand_boz_totals" => "BV",
      "grand_aggregated_totals" => "BX",
      "boz_totals" => "BZ",

      # Additional comprehensive mappings
      "grand_blc_fcy_inst_totals" => "CB",
      "grand_cust_details_totals" => "CD",
      "grand_notes_coins" => "CF",
      "grand_govt_securities_totals" => "CH",
      "forwd_totals_liabilities" => "CJ",
      "converted_totals" => "CL",
      "grand_loans_totals" => "CN",
      "cust_details_totals" => "CP",
      "exposure_ratio" => "CR",
      "short_pos_kwcha" => "CT",
      "grand_undelivered_spot_totals" => "CV",
      "grand_off_blc_totals" => "CX",
      "off_blc_totals" => "CZ",
      "total_liabilities" => "DB",
      "grand_frwd_sales_liabilities" => "DD",
      "exposure_sum" => "DF",
      "grand_liab_totals" => "DH",
      "grand_spot_sales_liabilities_totals" => "DJ",
      "grand_fcy_inst_liab_total" => "DL",
      "total_long_posi_fcy" => "DN",
      "total_short_pos_fcy" => "DP",
      "govt_securities_totals" => "DR",
      "fcy_cur_totals" => "DT"
    }
  end

  # Process standard categories with day/currency structure using Prudential patterns
  defp process_standard_schedule_21a_category_prudential(file_id, category, category_data, base_column, currency_rows) do
    IO.puts("Processing standard category #{category} with base column #{base_column} (Prudential pattern)")
    IO.puts("Category data structure: #{inspect(category_data)}")

    if is_map(category_data) do
      # Define the days with their specific starting row positions for yellow cells
      # Yellow cells start from C19, with specific day starting positions:
      # Monday: C19, Tuesday: C84, Wednesday: C147, Thursday: C210, Friday: C272
      day_positions = %{
        "Monday" => 19,
        "Tuesday" => 84,
        "Wednesday" => 147,
        "Thursday" => 210,
        "Friday" => 272
      }

      # Process all days that have specific positions (Monday through Friday)
      # Only map to yellow cells starting from the specified positions
      Enum.each(day_positions, fn {day, start_row} ->
        if Map.has_key?(category_data, day) do
          day_data = category_data[day]
          IO.puts("Processing #{day} data for #{category} starting at row #{start_row}")

          # Check if this is a simple day total or currency breakdown
          if is_map(day_data) do
            # Handle currency breakdown using yellow cells only
            Enum.each(day_data, fn {currency, value} ->
              if Map.has_key?(currency_rows, currency) do
                # Calculate the row offset from the currency base row
                currency_base_row = currency_rows[currency]
                # Use the day's starting position plus the currency offset
                row_offset = currency_base_row - 19  # 19 is the base starting row for yellow cells
                actual_row = start_row + row_offset
                cell = "C#{actual_row}"  # All yellow cells are in column C
                IO.puts("  #{day} mapping: #{category} -> #{currency} -> Cell #{cell} = #{value}")
                write_schedule_21a_cell_prudential(file_id, cell, value, "#{category}_#{day}_#{currency}")
              else
                IO.puts("  Warning: Unknown currency #{currency} in #{day} data for #{category}")
              end
            end)
          else
            # Handle simple day total (no currency breakdown)
            # Use the day's starting position for totals
            cell = "C#{start_row}"
            IO.puts("  #{day} total: #{category} -> Cell #{cell} = #{day_data}")
            write_schedule_21a_cell_prudential(file_id, cell, day_data, "#{category}_#{day}_total")
          end
        end
      end)
    else
      IO.puts("Category data for #{category} is not a map: #{inspect(category_data)}")
    end
  end
  defp write_schedule_21a_cell_prudential(file_id, cell, value, context) do
    IO.puts("\n==== WRITING CELL #{cell} ====")
    IO.puts("Value: #{inspect(value)} (type: #{typeof(value)})")
    IO.puts("Context: #{context}")
    IO.puts("File ID: #{inspect(file_id)}")
    IO.puts("Sheet name: Schedule 21A")

    try do
      cond do
        # Handle zero values exactly like Schedule 20A
        value in ["0", "0.0", "0.00"] ->
          IO.puts("Processing as zero value")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", 0.0)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to float: 0.0")

        # Handle nil values (Prudential pattern)
        is_nil(value) ->
          IO.puts("Processing as nil value")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", 0.0)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to float: 0.0 (was nil)")

        # Handle string values that represent numbers (Schedule 20A pattern)
        is_binary(value) ->
          IO.puts("Processing as string value: #{value}")
          clean_value = String.replace(value, ",", "")
          case Regex.match?(~r/^\d+\.\d+$/, clean_value) do
            true ->
              IO.puts("String matches float pattern")
              float_value = Utils.string_to_float(value)
              IO.puts("Converted to float: #{float_value}")
              result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", float_value)
              IO.puts("Excelizer result: #{inspect(result)}")
              IO.puts("SUCCESS: Set cell #{cell} to float: #{float_value} (converted from string)")
            false ->
              IO.puts("String matches integer pattern")
              int_value = Utils.string_to_integer(value)
              IO.puts("Converted to int: #{int_value}")
              result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", int_value)
              IO.puts("Excelizer result: #{inspect(result)}")
              IO.puts("SUCCESS: Set cell #{cell} to int: #{int_value} (converted from string)")
          end

        # Handle integer values (Schedule 20A pattern)
        is_integer(value) ->
          IO.puts("Processing as integer value: #{value}")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", value)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to int: #{value}")

        # Handle float values (Schedule 20A pattern)
        is_float(value) ->
          IO.puts("Processing as float value: #{value}")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", value)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to float: #{value}")

        # Handle decimal map values (Prudential pattern)
        is_map(value) && Map.has_key?(value, "sign") && Map.has_key?(value, "coef") && Map.has_key?(value, "exp") ->
          IO.puts("Processing as decimal map value")
          decimal_value = Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
          IO.puts("Converted to float: #{decimal_value}")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", decimal_value)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to float: #{decimal_value} (converted from decimal map)")

        # Handle any other value types (Schedule 20A pattern)
        true ->
          IO.puts("Processing as other value type")
          string_value = to_string(value)
          IO.puts("Converted to string: #{string_value}")
          result = Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", string_value)
          IO.puts("Excelizer result: #{inspect(result)}")
          IO.puts("SUCCESS: Set cell #{cell} to string: #{string_value} (converted from #{typeof(value)})")
      end

      IO.puts("==== SUCCESSFULLY WROTE CELL #{cell} ====")
    rescue
      e ->
        IO.puts("==== ERROR WRITING CELL #{cell} ====")
        IO.puts("Error: #{inspect(e)}")
        IO.puts("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
        Logger.error("Error writing Schedule 21A cell #{cell}: #{inspect(e)}")
        Logger.error("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
    end
  end
  defp is_simple_cell_mapping?(data) when is_map(data) do
    # Check if all keys are cell references (like "D6", "E7", etc.)
    Map.keys(data)
    |> Enum.all?(fn key ->
      is_binary(key) && String.match?(key, ~r/^[A-Z]+\d+$/)
    end)
  end
  defp process_schedule_21a_simple_mapping(file_id, decoded_data) do
    IO.puts("\n==== PROCESSING SIMPLE CELL MAPPING FOR SCHEDULE 21A ====")
    IO.puts("Using Schedule 20A-style processing for Schedule 21A")
    IO.puts("Input data: #{inspect(decoded_data)}")

    # Apply the same filtering logic as Schedule 20A (if needed)
    # For now, process all cells directly like Schedule 20A
    cell_data = decoded_data
    |> case do
      nil ->
        IO.puts("Data is nil, returning empty list")
        []
      data ->
        IO.puts("Data is present, converting to list")
        data
    end
    |> Enum.map(fn {index, value} ->
      # Apply any cell transformation logic here if needed (like update_20a_cells)
      IO.puts("Processing cell: #{index} = #{inspect(value)}")
      {index, value}
    end)

    IO.puts("Total cells to process: #{length(cell_data)}")

    cell_data
    |> Stream.each(fn
      # Handle zero values exactly like Schedule 20A
      {index, value} when value in ["0", "0.0", "0.00"] ->
        IO.puts("Processing zero value for cell #{index}")
        try do
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", 0.0)
          IO.puts("SUCCESS: Set cell #{index} to float: 0.0")
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end

      # Handle string values that represent numbers (Schedule 20A pattern)
      {index, value} when is_binary(value) ->
        IO.puts("Processing string value for cell #{index}: #{value}")
        try do
          case Regex.match?(~r/^\d+\.\d+$/, String.replace(value, ",", "")) do
            true ->
              float_value = Utils.string_to_float(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", float_value)
              IO.puts("SUCCESS: Set cell #{index} to float: #{float_value} (converted from string)")
            false ->
              int_value = Utils.string_to_integer(value)
              Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", int_value)
              IO.puts("SUCCESS: Set cell #{index} to int: #{int_value} (converted from string)")
          end
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end

      # Handle integer values (Schedule 20A pattern)
      {index, value} when is_integer(value) ->
        IO.puts("Processing integer value for cell #{index}: #{value}")
        try do
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", value)
          IO.puts("SUCCESS: Set cell #{index} to int: #{value}")
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end

      # Handle float values (Schedule 20A pattern)
      {index, value} when is_float(value) ->
        IO.puts("Processing float value for cell #{index}: #{value}")
        try do
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", value)
          IO.puts("SUCCESS: Set cell #{index} to float: #{value}")
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end

      # Handle decimal map values (Prudential pattern)
      {index, value} when is_map(value) ->
        IO.puts("Processing map value for cell #{index}: #{inspect(value)}")
        try do
          if Map.has_key?(value, "sign") && Map.has_key?(value, "coef") && Map.has_key?(value, "exp") do
            decimal_value = Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", decimal_value)
            IO.puts("SUCCESS: Set cell #{index} to float: #{decimal_value} (converted from decimal map)")
          else
            string_value = inspect(value)
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", string_value)
            IO.puts("SUCCESS: Set cell #{index} to string: #{string_value} (map without decimal structure)")
          end
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end

      # Handle any other value types (Schedule 20A pattern)
      {index, value} ->
        IO.puts("Processing other value type for cell #{index}: #{inspect(value)} (type: #{typeof(value)})")
        try do
          string_value = to_string(value)
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", string_value)
          IO.puts("SUCCESS: Set cell #{index} to string: #{string_value} (converted from #{typeof(value)})")
        rescue
          e ->
            IO.puts("ERROR: Failed to set cell #{index}: #{inspect(e)}")
        end
    end)
    |> Stream.run()

  end
  defp is_simple_cell_mapping?(_), do: false

  # Process each category in the schedule 21A data
  defp process_schedule_21a_category(
         file_id,
         category,
         category_data,
         base_column,
         currency_rows,
         total_row
       ) do
    IO.puts("\nProcessing category: #{category} with base column: #{base_column}")

    if is_map(category_data) do
      # Check if this is a special category that only has day totals (no currency breakdown)
      if Map.has_key?(category_data, "is_total_only") && category_data["is_total_only"] == true do
        IO.puts("  Category #{category} is a total-only category")

        # Process Monday total
        if Map.has_key?(category_data, "Monday") do
          monday_value = category_data["Monday"]
          monday_cell = "#{base_column}#{total_row}"
          IO.puts("  Monday total: #{category} -> Cell #{monday_cell} = #{monday_value}")
          write_schedule_21a_cell(file_id, monday_cell, monday_value, "#{category}_monday_total")
        end

        if Map.has_key?(category_data, "Tuesday") do
          tuesday_value = category_data["Tuesday"]
          tuesday_column = next_column(base_column)
          tuesday_cell = "#{tuesday_column}#{total_row}"
          IO.puts("  Tuesday total: #{category} -> Cell #{tuesday_cell} = #{tuesday_value}")

          write_schedule_21a_cell(
            file_id,
            tuesday_cell,
            tuesday_value,
            "#{category}_tuesday_total"
          )
        end
      else
        # Handle standard categories with currency breakdown
        # Process Monday data (Monday uses base columns: B, D, F, H, J, L, N, P, R, T)
        if Map.has_key?(category_data, "Monday") do
          monday_data = category_data["Monday"]
          IO.puts("Processing Monday data for #{category}: #{inspect(monday_data)}")

          if is_map(monday_data) do
            Enum.each(monday_data, fn {currency, value} ->
              if Map.has_key?(currency_rows, currency) do
                row = currency_rows[currency]
                cell = "#{base_column}#{row}"
                IO.puts("  Monday mapping: #{category} -> #{currency} -> Cell #{cell} = #{value}")
                write_schedule_21a_cell(file_id, cell, value, "#{category}_monday_#{currency}")
              else
                IO.puts("  Warning: Unknown currency #{currency} in Monday data for #{category}")
              end
            end)
          else
            IO.puts("  Monday data for #{category} is not a map: #{inspect(monday_data)}")
          end
        end

        # Process Tuesday data (Tuesday uses next columns: C, E, G, I, K, M, O, Q, S, U)
        if Map.has_key?(category_data, "Tuesday") do
          tuesday_data = category_data["Tuesday"]
          IO.puts("Processing Tuesday data for #{category}: #{inspect(tuesday_data)}")

          if is_map(tuesday_data) do
            # Calculate Tuesday column (next column after base)
            tuesday_column = next_column(base_column)
            IO.puts("  Tuesday column for #{category}: #{base_column} -> #{tuesday_column}")

            Enum.each(tuesday_data, fn {currency, value} ->
              if Map.has_key?(currency_rows, currency) do
                row = currency_rows[currency]
                cell = "#{tuesday_column}#{row}"

                IO.puts(
                  "  Tuesday mapping: #{category} -> #{currency} -> Cell #{cell} = #{value}"
                )

                write_schedule_21a_cell(file_id, cell, value, "#{category}_tuesday_#{currency}")
              else
                IO.puts("  Warning: Unknown currency #{currency} in Tuesday data for #{category}")
              end
            end)
          else
            IO.puts("  Tuesday data for #{category} is not a map: #{inspect(tuesday_data)}")
          end
        end
      end
    else
      IO.puts("Category data for #{category} is not a map: #{inspect(category_data)}")
    end
  end

  # Process special fields that don't follow the standard pattern
  defp process_special_schedule_21a_field(file_id, field_name, field_data) do
    IO.puts("Processing special field: #{field_name}")

    case field_name do
      "allowable_exp_15" ->
        # Single value that goes in a specific cell (based on template structure)
        write_schedule_21a_cell(file_id, "V43", field_data, field_name)

      "report_date" ->
        # Report date is a single value, not day-based
        # Place it in a header cell or skip it since it's metadata
        IO.puts("Skipping report_date field as it's metadata: #{inspect(field_data)}")

      "over_under_exposure" when is_map(field_data) ->
        # Day-based totals without currency breakdown
        # These appear to go in the totals row at the bottom of the template
        if Map.has_key?(field_data, "Monday") do
          write_schedule_21a_cell(file_id, "R43", field_data["Monday"], "#{field_name}_monday")
        end

        if Map.has_key?(field_data, "Tuesday") do
          write_schedule_21a_cell(file_id, "S43", field_data["Tuesday"], "#{field_name}_tuesday")
        end

      "total_reg_cap_ratio" when is_map(field_data) ->
        # Day-based totals without currency breakdown
        # These appear to go in the totals row at the bottom of the template
        if Map.has_key?(field_data, "Monday") do
          write_schedule_21a_cell(file_id, "T43", field_data["Monday"], "#{field_name}_monday")
        end

        if Map.has_key?(field_data, "Tuesday") do
          write_schedule_21a_cell(file_id, "U43", field_data["Tuesday"], "#{field_name}_tuesday")
        end

      # Handle any other fields that might be in the data but not in our standard mapping
      _ when is_map(field_data) ->
        IO.puts("Processing unmapped field with day structure: #{field_name}")
        # Try to handle it as a day-based field
        if Map.has_key?(field_data, "Monday") or Map.has_key?(field_data, "Tuesday") do
          IO.puts("Field #{field_name} has day-based structure but no column mapping defined")
        end

      _ ->
        IO.puts("Unknown special field: #{field_name}, skipping")
    end
  end

  # Helper function to get the next column letter
  defp next_column(column) do
    case column do
      "A" -> "B"
      "B" -> "C"
      "C" -> "D"
      "D" -> "E"
      "E" -> "F"
      "F" -> "G"
      "G" -> "H"
      "H" -> "I"
      "I" -> "J"
      "J" -> "K"
      "K" -> "L"
      "L" -> "M"
      "M" -> "N"
      "N" -> "O"
      "O" -> "P"
      "P" -> "Q"
      "Q" -> "R"
      "R" -> "S"
      "S" -> "T"
      "T" -> "U"
      "U" -> "V"
      "V" -> "W"
      "W" -> "X"
      "X" -> "Y"
      "Y" -> "Z"
      "Z" -> "AA"
      "AA" -> "AB"
      "AB" -> "AC"
      "AC" -> "AD"
      "AD" -> "AE"
      "AE" -> "AF"
      "AF" -> "AG"
      "AG" -> "AH"
      "AH" -> "AI"
      "AI" -> "AJ"
      "AJ" -> "AK"
      "AK" -> "AL"
      "AL" -> "AM"
      "AM" -> "AN"
      "AN" -> "AO"
      "AO" -> "AP"
      "AP" -> "AQ"
      "AQ" -> "AR"
      "AR" -> "AS"
      "AS" -> "AT"
      "AT" -> "AU"
      "AU" -> "AV"
      "AV" -> "AW"
      "AW" -> "AX"
      "AX" -> "AY"
      "AY" -> "AZ"
      "AZ" -> "BA"
      "BA" -> "BB"
      "BB" -> "BC"
      "BC" -> "BD"
      "BD" -> "BE"
      "BE" -> "BF"
      "BF" -> "BG"
      "BG" -> "BH"
      "BH" -> "BI"
      "BI" -> "BJ"
      "BJ" -> "BK"
      "BK" -> "BL"
      "BL" -> "BM"
      "BM" -> "BN"
      "BN" -> "BO"
      "BO" -> "BP"
      "BP" -> "BQ"
      "BQ" -> "BR"
      "BR" -> "BS"
      "BS" -> "BT"
      "BT" -> "BU"
      "BU" -> "BV"
      "BV" -> "BW"
      "BW" -> "BX"
      "BX" -> "BY"
      "BY" -> "BZ"
      "BZ" -> "CA"
      "CA" -> "CB"
      "CB" -> "CC"
      "CC" -> "CD"
      "CD" -> "CE"
      "CE" -> "CF"
      "CF" -> "CG"
      "CG" -> "CH"
      "CH" -> "CI"
      "CI" -> "CJ"
      "CJ" -> "CK"
      "CK" -> "CL"
      "CL" -> "CM"
      "CM" -> "CN"
      "CN" -> "CO"
      "CO" -> "CP"
      "CP" -> "CQ"
      "CQ" -> "CR"
      "CR" -> "CS"
      "CS" -> "CT"
      "CT" -> "CU"
      "CU" -> "CV"
      "CV" -> "CW"
      "CW" -> "CX"
      "CX" -> "CY"
      "CY" -> "CZ"
      # Fallback for any column beyond our range
      _ -> "DZ"
    end
  end

  # Helper function to write a cell value with appropriate formatting
  defp write_schedule_21a_cell(file_id, cell, value, context) do
    IO.puts("Writing cell #{cell}: #{inspect(value)} (context: #{context})")

    try do
      # Determine if this is a percentage value
      is_percentage = is_binary(value) && String.contains?(value, "%")

      # Format and write the value
      cond do
        is_nil(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", 0)

        value in ["0", "0.0", "0.00"] ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", 0)

        is_percentage ->
          # Keep percentage as string
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", value)

        is_binary(value) ->
          # Try to convert numeric strings to numbers
          clean_value = String.replace(value, ",", "")

          case Regex.match?(~r/^\d+\.?\d*$/, clean_value) do
            true ->
              if String.contains?(clean_value, ".") do
                float_val = String.to_float(clean_value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", float_val)
              else
                int_val = String.to_integer(clean_value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", int_val)
              end

            false ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", value)
          end

        is_map(value) && Map.has_key?(value, "sign") && Map.has_key?(value, "coef") &&
            Map.has_key?(value, "exp") ->
          # Handle decimal map values
          decimal_value =
            Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()

          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", decimal_value)

        is_number(value) ->
          if is_float(value) do
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", value)
          else
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", value)
          end

        true ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", to_string(value))
      end

      IO.puts("  Successfully wrote cell #{cell}")
    rescue
      e ->
        IO.puts("  ERROR writing cell #{cell}: #{inspect(e)}")
    end
  end

  # Process simple Schedule 21A cell mapping (fallback to monthly report style)
  defp process_simple_schedule_21a(file_id, decoded_data) do

    decoded_data
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", 0)

      {index, value} when is_nil(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", 0)

      {index, value} when is_map(value) ->
        # Handle decimal values
        if Map.has_key?(value, "sign") && Map.has_key?(value, "coef") &&
             Map.has_key?(value, "exp") do
          decimal_value =
            Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()

          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", decimal_value)
        else
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", inspect(value))
        end

      {index, value} when is_binary(value) ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(
              file_id,
              "Schedule 21A",
              index,
              "int",
              Utils.string_to_integer(value)
            )

          false ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", value)
        end

      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", to_string(value))
    end)
    |> Stream.run()
  end

  defp schedule_21B(file_id, data) do
    if is_nil(data.schedule_21B) do
      Logger.warning("Schedule 21B data is nil, skipping")
      :ok
    else
      # Use the expected sheet name directly
      IO.puts("\n==== USING EXPECTED SHEET NAME FOR 21B ====")
      schedule_21b_sheet = "Schedule 21B"
      IO.puts("Using sheet name: #{schedule_21b_sheet}")

      # Try writing to a test cell to verify basic functionality
      IO.puts("\n==== TESTING BASIC CELL WRITING FOR 21B ====")

      try do
        Excelizer.Cell.set_cell_value(
          file_id,
          schedule_21b_sheet,
          "A1",
          "string",
          "TEST VALUE 21B"
        )

        IO.puts("Successfully wrote test value to cell A1 in 21B")
      rescue
        e ->
          IO.puts("ERROR writing test value to 21B: #{inspect(e)}")
          IO.puts("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
      end

      # Decode the JSON data
      case Poison.decode(data.schedule_21B) do
        {:ok, decoded_data} ->
          # Inspect the decoded data
          IO.puts("\n==== SCHEDULE 21B RAW DATA ====")
          inspect_data("Schedule 21B Raw", decoded_data)

          # Process the data using the prudential report style
          # First, check if we have a map with cell references or a data structure
          cell_data =
            if is_map(decoded_data) &&
                 Enum.any?(Map.keys(decoded_data), fn key ->
                   is_binary(key) && String.match?(key, ~r/^[A-Z]\d+$/)
                 end) do
              # Data is already in cell reference format
              Logger.info("Schedule 21B data is already in cell reference format")
              decoded_data
            else
              # Data needs processing
              Logger.info("Processing Schedule 21B data structure")

              # Get the row indices
              row_indices = schedule21b_rows_index()
              IO.puts("\n==== SCHEDULE 21B ROW INDICES ====")
              IO.inspect(row_indices, label: "Row indices for Schedule 21B")

              # Initialize an empty map for cell data
              cell_map = %{}

              # Check if we have a "data" key with a list of entries
              entries =
                cond do
                  is_map(decoded_data) && Map.has_key?(decoded_data, "data") &&
                      is_list(decoded_data["data"]) ->
                    IO.puts("\n==== SCHEDULE 21B DATA ENTRIES ====")

                    IO.puts(
                      "Found data key with list of entries: #{length(decoded_data["data"])} entries"
                    )

                    decoded_data["data"]

                  is_map(decoded_data) && Map.has_key?(decoded_data, "Totals") ->
                    IO.puts("\n==== SCHEDULE 21B TOTALS ENTRY ====")
                    IO.puts("Found Totals key, creating single entry")
                    IO.inspect(decoded_data["Totals"], label: "Totals data")
                    [%{"account_name" => "Totals", "balances" => decoded_data["Totals"]}]

                  true ->
                    # Default to a single Totals entry
                    IO.puts("\n==== SCHEDULE 21B DEFAULT ENTRY ====")
                    IO.puts("No data or Totals key found, creating default entry")

                    [
                      %{
                        "account_name" => "Totals",
                        "balances" => %{
                          "Monday" => "0.00",
                          "Tuesday" => "0.00",
                          "Wednesday" => "0.00",
                          "Thursday" => "0.00",
                          "Friday" => "0.00"
                        }
                      }
                    ]
                end

              # Inspect the entries
              IO.puts("\n==== SCHEDULE 21B ENTRIES ====")
              inspect_data("Schedule 21B Entries", entries)

              # Process each entry
              # Updated to use the new row indices that start from 22
              processed_data =
                Enum.zip(entries, schedule21b_rows_index())
                |> Enum.reduce(cell_map, fn {entry, row_index}, acc ->
                  # Get the account name
                  account_name = Map.get(entry, "account_name", "")
                  IO.puts("\nProcessing entry: #{account_name} at row #{row_index}")

                  # Add the account name to column A (updated from column B)
                  acc = Map.put(acc, "A#{row_index}", account_name)

                  # Add rating and rating agency if available
                  rating = Map.get(entry, "rating", "")
                  rating_agency = Map.get(entry, "rating_agency", "")
                  acc = Map.put(acc, "B#{row_index}", rating)
                  acc = Map.put(acc, "C#{row_index}", rating_agency)

                  # Get balances
                  balances = Map.get(entry, "balances", %{})
                  IO.puts("Balances for #{account_name}:")
                  IO.inspect(balances)

                  # Add balances for each day - updated column references to start from D
                  acc = Map.put(acc, "D#{row_index}", Map.get(balances, "Monday", "0.00"))
                  acc = Map.put(acc, "F#{row_index}", Map.get(balances, "Tuesday", "0.00"))
                  acc = Map.put(acc, "H#{row_index}", Map.get(balances, "Wednesday", "0.00"))
                  acc = Map.put(acc, "J#{row_index}", Map.get(balances, "Thursday", "0.00"))
                  acc = Map.put(acc, "L#{row_index}", Map.get(balances, "Friday", "0.00"))

                  # Get percentages
                  percentages = Map.get(entry, "percentages", %{})
                  IO.puts("Percentages for #{account_name}:")
                  IO.inspect(percentages)

                  # Add percentages for each day - updated column references
                  acc = Map.put(acc, "E#{row_index}", Map.get(percentages, "Monday", "0%"))
                  acc = Map.put(acc, "G#{row_index}", Map.get(percentages, "Tuesday", "0%"))
                  acc = Map.put(acc, "I#{row_index}", Map.get(percentages, "Wednesday", "0%"))
                  acc = Map.put(acc, "K#{row_index}", Map.get(percentages, "Thursday", "0%"))
                  acc = Map.put(acc, "M#{row_index}", Map.get(percentages, "Friday", "0%"))

                  # Add maximum value if available
                  maximum = Map.get(entry, "maximum", "")
                  Map.put(acc, "N#{row_index}", maximum)
                end)

              # Inspect the processed data
              IO.puts("\n==== SCHEDULE 21B PROCESSED DATA ====")
              inspect_data("Schedule 21B Processed", processed_data)

              processed_data
            end

          # Inspect the final cell data
          IO.puts("\n==== SCHEDULE 21B FINAL CELL DATA ====")
          inspect_data("Schedule 21B Final", cell_data)

          # Write the data to Excel and count cells
          IO.puts("\n==== WRITING SCHEDULE 21B DATA TO EXCEL ====")
          IO.puts("Using sheet name: #{schedule_21b_sheet}")

          # Create a list of cells to write for debugging
          cells_to_write =
            cell_data
            |> Enum.map(fn {index, value} ->
              {index, value, typeof(value)}
            end)
            |> Enum.take(30)

          IO.puts("First 30 cells to write for 21B:")

          Enum.each(cells_to_write, fn {index, value, type} ->
            IO.puts("  #{index}: #{inspect(value)} (#{type})")
          end)

          # Now write the actual data
          IO.puts("\n==== WRITING ACTUAL CELL DATA FOR 21B ====")

          {cell_count, success_count, error_count, _} =
            cell_data
            |> Enum.reduce({0, 0, 0, file_id}, fn {index, value},
                                                  {count, successes, errors, file_id} ->
              # Log the cell we're writing
              IO.puts("Writing to cell #{index}: #{inspect(value)}")

              # Increment cell count
              count = count + 1

              # Try to write the cell with error handling
              {new_successes, new_errors} =
                try do
                  # Handle different value types
                  cond do
                    # Handle decimal map values
                    is_map(value) && Map.has_key?(value, "sign") && Map.has_key?(value, "coef") &&
                        Map.has_key?(value, "exp") ->
                      decimal_value =
                        Decimal.new(value["sign"], value["coef"], value["exp"])
                        |> Decimal.to_float()

                      IO.puts("  Converting decimal map to float: #{decimal_value}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "float",
                        decimal_value
                      )

                    # Handle other map values
                    is_map(value) ->
                      IO.puts("  Handling map value as string")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "string",
                        inspect(value)
                      )

                    # Handle Decimal struct
                    match?(%Decimal{}, value) ->
                      decimal_float = Decimal.to_float(value)
                      IO.puts("  Converting Decimal struct to float: #{decimal_float}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "float",
                        decimal_float
                      )

                    # Handle zero values
                    value in ["0", "0.0", "0.00"] ->
                      IO.puts("  Handling zero string as integer")
                      Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "int", 0)

                    # Handle nil values
                    is_nil(value) ->
                      IO.puts("  Handling nil as integer zero")
                      Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "int", 0)

                    # Handle integer values
                    is_integer(value) ->
                      IO.puts("  Handling integer: #{value}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "int",
                        value
                      )

                    # Handle percentage values
                    is_binary(value) && String.ends_with?(value, "%") ->
                      IO.puts("  Handling percentage string: #{value}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "string",
                        value
                      )

                    # Handle numeric strings
                    is_binary(value) && Regex.match?(~r/^[\d,]+\.?\d*$/, value) ->
                      # Try to convert to integer first
                      try do
                        int_value = Utils.string_to_integer(value)
                        IO.puts("  Converting numeric string to integer: #{int_value}")

                        Excelizer.Cell.set_cell_value(
                          file_id,
                          schedule_21b_sheet,
                          index,
                          "int",
                          int_value
                        )
                      rescue
                        _ ->
                          # If that fails, try to convert to float
                          try do
                            float_value = String.replace(value, ",", "") |> String.to_float()
                            IO.puts("  Converting numeric string to float: #{float_value}")

                            Excelizer.Cell.set_cell_value(
                              file_id,
                              schedule_21b_sheet,
                              index,
                              "float",
                              float_value
                            )
                          rescue
                            _ ->
                              # If all else fails, keep as string
                              IO.puts("  Keeping as string: #{value}")

                              Excelizer.Cell.set_cell_value(
                                file_id,
                                schedule_21b_sheet,
                                index,
                                "string",
                                value
                              )
                          end
                      end

                    # Handle other string values
                    is_binary(value) ->
                      IO.puts("  Handling as string: #{value}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "string",
                        value
                      )

                    # Handle any other type
                    true ->
                      string_value = to_string(value)
                      IO.puts("  Converting to string: #{string_value}")

                      Excelizer.Cell.set_cell_value(
                        file_id,
                        schedule_21b_sheet,
                        index,
                        "string",
                        string_value
                      )
                  end

                  # If we get here, the cell was written successfully
                  IO.puts("  Cell #{index} written successfully")
                  {successes + 1, errors}
                rescue
                  e ->
                    IO.puts("  ERROR writing cell #{index}: #{inspect(e)}")
                    IO.puts("  Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
                    {successes, errors + 1}
                end

              # Save the workbook every 10 cells
              if rem(count, 10) == 0 do
                IO.puts("Saving workbook after #{count} cells...")

                try do
                  Excelizer.Workbook.save(file_id)
                rescue
                  e -> IO.puts("  ERROR saving workbook: #{inspect(e)}")
                end
              end

              # Return the updated counts and file_id
              {count, new_successes, new_errors, file_id}
            end)

          # Log the results
          IO.puts("\n==== CELL WRITING SUMMARY FOR 21B ====")
          IO.puts("Total cells processed: #{cell_count}")
          IO.puts("Successfully written: #{success_count}")
          IO.puts("Errors: #{error_count}")

          # Final save of the workbook
          IO.puts("\n==== FINAL SAVE OF WORKBOOK FOR 21B ====")

          try do
            IO.puts("Saving workbook...")
            Excelizer.Workbook.save(file_id)
            IO.puts("Workbook saved successfully")
          rescue
            e ->
              IO.puts("ERROR saving workbook: #{inspect(e)}")
              IO.puts("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
          end

        {:error, error} ->
          Logger.error("Failed to decode Schedule 21B data: #{inspect(error)}")

          # Notify UI about error
          if Process.get(:reference) do
            Phoenix.PubSub.broadcast(
              MisReports.PubSub,
              "weekly_export:#{Process.get(:reference)}",
              {:weekly_export_data_warning,
               %{
                 sheet: "Schedule 21B",
                 message: "Error processing Schedule 21B data. The sheet will be empty."
               }}
            )
          end
      end
    end
  end

  # Helper function that defines the row indices for Schedule 21A
  defp schedule21a_rows_index() do
    # Define row indices for Schedule 21A
    # These represent the starting rows for each section of data
    # Updated to start from row 19 as requested
    [19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55]
  end

  # Helper function that defines the row indices for Schedule 21B
  defp schedule21b_rows_index() do
    # Define row indices for Schedule 21B
    # These represent the starting rows for each institution entry
    # Updated to start from row 22 as requested
    [22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
  end

  # Helper function for data conversion
  defp string_to_decimal(string) do
    case string do
      nil ->
        Decimal.new(0)

      "" ->
        Decimal.new(0)

      str when is_binary(str) ->
        String.replace(str, ",", "")
        |> Decimal.new()

      _ ->
        Decimal.new(0)
    end
  end

  # These helper functions are no longer needed with our new approach
  # But we'll keep the function signatures for backward compatibility
  # in case they're called from elsewhere in the codebase

  # Helper function to prepare parameters for Schedule 21A (deprecated)
  defp prepare_params_21a(_entry, _index) do
    %{}
  end

  # Helper function to prepare header for Schedule 21A (deprecated)
  defp prepare_header_21a(_index, _entries) do
    %{}
  end

  # Helper function to prepare parameters for Schedule 21B (deprecated)
  defp prepare_params_21b(_entry, _index) do
    %{}
  end

  # Helper function to prepare header for Schedule 21B (deprecated)
  defp prepare_header_21b(_index, _entries) do
    %{}
  end

  # Helper function to traverse changeset errors
  defp traverse_errors(errors) do
    for {key, {msg, _opts}} <- errors do
      "#{key} #{msg}"
    end
  end

  # Helper function to convert string keys to atoms in a map
  defp convert_string_keys_to_atoms(map) when is_map(map) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      atom_key = if is_binary(key), do: String.to_atom(key), else: key

      atom_value =
        case value do
          v when is_map(v) ->
            convert_string_keys_to_atoms(v)

          v when is_list(v) ->
            Enum.map(v, fn item ->
              if is_map(item), do: convert_string_keys_to_atoms(item), else: item
            end)

          _ ->
            value
        end

      Map.put(acc, atom_key, atom_value)
    end)
  end
  defp process_special_schedule_21a_field_prudential(file_id, field_name, field_data) do
    IO.puts("Processing special field: #{field_name} (Prudential pattern)")

    case field_name do
      "allowable_exp_15" ->
        # Single value that goes in a specific cell
        write_schedule_21a_cell_prudential(file_id, "V43", field_data, field_name)

      "report_date" ->
        # Report date is metadata, skip it
        IO.puts("Skipping report_date field as it's metadata: #{inspect(field_data)}")

      # Handle day-based fields with Prudential patterns
      field when is_map(field_data) ->
        days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

        # Try to find appropriate cells for unmapped fields
        case field_name do
          "over_under_exposure" ->
            process_day_based_field_prudential(file_id, field_data, "AP", 43, field_name)
          "total_reg_cap_ratio" ->
            process_day_based_field_prudential(file_id, field_data, "AU", 43, field_name)
          _ ->
            IO.puts("Field #{field_name} has day-based structure but no specific mapping defined")
        end

      _ ->
        IO.puts("Unknown special field: #{field_name}, skipping")
    end
  end

  # Process day-based field with Prudential patterns
  defp process_day_based_field_prudential(file_id, field_data, base_column, row, field_name) do
    days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

    days
    |> Enum.with_index()
    |> Enum.each(fn {day, day_index} ->
      if Map.has_key?(field_data, day) do
        day_column = get_day_column(base_column, day_index)
        cell = "#{day_column}#{row}"
        value = field_data[day]
        write_schedule_21a_cell_prudential(file_id, cell, value, "#{field_name}_#{day}")
      end
    end)
  end
  defp get_day_column(base_column, day_index) do
    # Convert base column to column index, add day offset, convert back
    base_index = column_to_index(base_column)
    target_index = base_index + day_index
    index_to_column(target_index)
  end

  defp column_to_index(column) do
    column
    |> String.graphemes()
    |> Enum.reverse()
    |> Enum.with_index()
    |> Enum.reduce(0, fn {char, pos}, acc ->
      char_value = :binary.first(char) - ?A + 1
      acc + char_value * :math.pow(26, pos)
    end)
    |> round()
  end

  # Convert numeric index to column letter(s) (1=A, 2=B, ..., 27=AA, etc.)
  defp index_to_column(index) when index <= 26 do
    <<(?A + index - 1)>>
  end

  defp index_to_column(index) do
    quotient = div(index - 1, 26)
    remainder = rem(index - 1, 26) + 1
    index_to_column(quotient) <> <<(?A + remainder - 1)>>
  end

  # Check if the data has the complex nested structure
  defp is_nested_schedule_21a_structure?(data) when is_map(data) do
    # Check for known financial category fields that indicate nested structure
    nested_fields = ["short_posi_fcy", "undelivered_spot_totals", "forwd_totals",
                     "blc_fcy_inst_totals", "long_position_totals", "excess_exposure",
                     "allowable_exposure", "over_under_exposure", "total_reg_cap_ratio",
                     "allowable_exp_15", "notes_coins_totals", "spot_totals",
                     "grand_fcy_cur_totals", "oth_borrowed_funds", "regulatory_capital_ratio",
                     "daily_rates"]

    Enum.any?(nested_fields, &Map.has_key?(data, &1))
  end

  defp is_nested_schedule_21a_structure?(_), do: false

  # Process complex nested Schedule 21A structure
  defp process_nested_schedule_21a(file_id, data) do
    IO.puts("\n==== PROCESSING NESTED SCHEDULE 21A STRUCTURE ====")

    # Define currency to row mapping
    currency_rows = %{
      "CNY" => 19,
      "DKK" => 22,
      "EUR" => 25,
      "GBP" => 28,
      "JPY" => 31,
      "Others" => 34,
      "USD" => 37,
      "ZAR" => 40
    }

    # Define financial category to column mapping based on the Excel template
    # Looking at the template, the columns appear to be structured as:
    # B,C = Notes/Coins, D,E = Short Position FCY, F,G = Undelivered Spot, etc.
    category_columns = %{
      "notes_coins_totals" => "B",        # Monday=B, Tuesday=C
      "short_posi_fcy" => "D",           # Monday=D, Tuesday=E
      "undelivered_spot_totals" => "F",   # Monday=F, Tuesday=G
      "forwd_totals" => "H",             # Monday=H, Tuesday=I
      "blc_fcy_inst_totals" => "J",      # Monday=J, Tuesday=K
      "long_position_totals" => "L",     # Monday=L, Tuesday=M
      "excess_exposure" => "N",          # Monday=N, Tuesday=O
      "allowable_exposure" => "P",       # Monday=P, Tuesday=Q
      "over_under_exposure" => "R",      # Monday=R, Tuesday=S
      "total_reg_cap_ratio" => "T",      # Monday=T, Tuesday=U

      # Additional fields found in the HTML templates that were missing from Excel mapping
      "spot_totals" => "V",              # Monday=V, Tuesday=W (Undelivered spot Sales)
      "grand_fcy_cur_totals" => "X",     # Monday=X, Tuesday=Y (Grand FCY Currency Totals)
      "oth_borrowed_funds" => "Z",       # Monday=Z, Tuesday=AA (Other Borrowed Funds)
      "regulatory_capital_ratio" => "AB", # Monday=AB, Tuesday=AC (Net open position as % of regulatory capital)
      "daily_rates" => "AD",             # Monday=AD, Tuesday=AE (Exchange rates)

      # Additional mappings for fields that were showing "no column mapping defined"
      "total_assets" => "AF",            # Monday=AF, Tuesday=AG (Total Assets)
      "grand_other_off_bs_liabilities" => "AH", # Monday=AH, Tuesday=AI (Grand Other Off-BS Liabilities)
      "fcy_inst_liab_total" => "AJ",     # Monday=AJ, Tuesday=AK (FCY Institutional Liabilities Total)
      "oth_fcy_cur_total" => "AL",       # Monday=AL, Tuesday=AM (Other FCY Currency Total)
      "total_short_pos_kwcha" => "AN",   # Monday=AN, Tuesday=AO (Total Short Position Kwacha)
      "aggregated_totals" => "AP",       # Monday=AP, Tuesday=AQ (Aggregated Totals)
      "grand_boz_totals" => "AR",        # Monday=AR, Tuesday=AS (Grand BOZ Totals)
      "grand_aggregated_totals" => "AT", # Monday=AT, Tuesday=AU (Grand Aggregated Totals)
      "boz_totals" => "AV",              # Monday=AV, Tuesday=AW (BOZ Totals)

      # Additional fields found in the latest data
      "grand_blc_fcy_inst_totals" => "AX", # Monday=AX, Tuesday=AY (Grand BLC FCY Institutional Totals)
      "grand_cust_details_totals" => "AZ", # Monday=AZ, Tuesday=BA (Grand Customer Details Totals)
      "grand_notes_coins" => "BB",       # Monday=BB, Tuesday=BC (Grand Notes & Coins)
      "grand_govt_securities_totals" => "BD", # Monday=BD, Tuesday=BE (Grand Government Securities Totals)
      "forwd_totals_liabilities" => "BF", # Monday=BF, Tuesday=BG (Forward Totals Liabilities)
      "converted_totals" => "BH",        # Monday=BH, Tuesday=BI (Converted Totals)
      "grand_loans_totals" => "BJ",      # Monday=BJ, Tuesday=BK (Grand Loans Totals)
      "cust_details_totals" => "BL",     # Monday=BL, Tuesday=BM (Customer Details Totals)
      "exposure_ratio" => "BN",          # Monday=BN, Tuesday=BO (Exposure Ratio)
      "short_pos_kwcha" => "BP",         # Monday=BP, Tuesday=BQ (Short Position Kwacha)
      "grand_undelivered_spot_totals" => "BR", # Monday=BR, Tuesday=BS (Grand Undelivered Spot Totals)
      "grand_off_blc_totals" => "BT",    # Monday=BT, Tuesday=BU (Grand Off Balance Totals)
      "off_blc_totals" => "BV",          # Monday=BV, Tuesday=BW (Off Balance Totals)
      "total_liabilities" => "BX",       # Monday=BX, Tuesday=BY (Total Liabilities)
      "grand_frwd_sales_liabilities" => "BZ", # Monday=BZ, Tuesday=CA (Grand Forward Sales Liabilities)
      "exposure_sum" => "CB",            # Monday=CB, Tuesday=CC (Exposure Sum)

      # Even more additional fields found in the latest data
      "grand_liab_totals" => "CD",       # Monday=CD, Tuesday=CE (Grand Liability Totals)
      "grand_spot_sales_liabilities_totals" => "CF", # Monday=CF, Tuesday=CG (Grand Spot Sales Liabilities Totals)
      "grand_fcy_inst_liab_total" => "CH", # Monday=CH, Tuesday=CI (Grand FCY Institutional Liability Total)
      "total_long_posi_fcy" => "CJ",     # Monday=CJ, Tuesday=CK (Total Long Position FCY)
      "total_short_pos_fcy" => "CL",     # Monday=CL, Tuesday=CM (Total Short Position FCY)
      "govt_securities_totals" => "CN",  # Monday=CN, Tuesday=CO (Government Securities Totals)
      "fcy_cur_totals" => "CP"           # Monday=CP, Tuesday=CQ (FCY Currency Totals)
    }

    # Set currency labels in column A
    Enum.each(currency_rows, fn {currency, row} ->
      IO.puts("Setting currency label #{currency} in cell A#{row}")
      Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", "A#{row}", "string", currency)
    end)

    # Process each financial category
    Enum.each(data, fn {category, category_data} ->
      IO.puts("\nProcessing category: #{category}")

      case Map.get(category_columns, category) do
        nil ->
          # Handle special cases that don't fit the standard pattern
          process_special_schedule_21a_field(file_id, category, category_data)

        base_column ->
          # Handle standard categories with day/currency structure
          process_standard_schedule_21a_category(file_id, category, category_data, base_column, currency_rows)
      end
    end)
  end

  # Process standard categories with Monday/Tuesday and currency breakdown
  defp process_standard_schedule_21a_category(file_id, category, category_data, base_column, currency_rows) do
    IO.puts("Processing standard category #{category} with base column #{base_column}")
    IO.puts("Category data structure: #{inspect(category_data)}")

    if is_map(category_data) do
      # Check if this is a simple day-based total (no currency breakdown)
      is_simple_day_total = Map.has_key?(category_data, "Monday") &&
                           Map.has_key?(category_data, "Tuesday") &&
                           !is_map(category_data["Monday"]) &&
                           !is_map(category_data["Tuesday"])

      if is_simple_day_total do
        # Handle simple day-based totals without currency breakdown
        IO.puts("Processing #{category} as simple day-based total")

        # Use a default row (e.g., row 43 for totals) for simple day-based values
        total_row = 43

        if Map.has_key?(category_data, "Monday") do
          monday_value = category_data["Monday"]
          monday_cell = "#{base_column}#{total_row}"
          IO.puts("  Monday total: #{category} -> Cell #{monday_cell} = #{monday_value}")
          write_schedule_21a_cell(file_id, monday_cell, monday_value, "#{category}_monday_total")
        end

        if Map.has_key?(category_data, "Tuesday") do
          tuesday_value = category_data["Tuesday"]
          tuesday_column = next_column(base_column)
          tuesday_cell = "#{tuesday_column}#{total_row}"
          IO.puts("  Tuesday total: #{category} -> Cell #{tuesday_cell} = #{tuesday_value}")
          write_schedule_21a_cell(file_id, tuesday_cell, tuesday_value, "#{category}_tuesday_total")
        end
      else
        # Handle standard categories with currency breakdown
        # Process Monday data (Monday uses base columns: B, D, F, H, J, L, N, P, R, T)
        if Map.has_key?(category_data, "Monday") do
          monday_data = category_data["Monday"]
          IO.puts("Processing Monday data for #{category}: #{inspect(monday_data)}")

          if is_map(monday_data) do
            Enum.each(monday_data, fn {currency, value} ->
              if Map.has_key?(currency_rows, currency) do
                row = currency_rows[currency]
                cell = "#{base_column}#{row}"
                IO.puts("  Monday mapping: #{category} -> #{currency} -> Cell #{cell} = #{value}")
                write_schedule_21a_cell(file_id, cell, value, "#{category}_monday_#{currency}")
              else
                IO.puts("  Warning: Unknown currency #{currency} in Monday data for #{category}")
              end
            end)
          else
            IO.puts("  Monday data for #{category} is not a map: #{inspect(monday_data)}")
          end
        end

        # Process Tuesday data (Tuesday uses next columns: C, E, G, I, K, M, O, Q, S, U)
        if Map.has_key?(category_data, "Tuesday") do
          tuesday_data = category_data["Tuesday"]
          IO.puts("Processing Tuesday data for #{category}: #{inspect(tuesday_data)}")

          if is_map(tuesday_data) do
            # Calculate Tuesday column (next column after base)
            tuesday_column = next_column(base_column)
            IO.puts("  Tuesday column for #{category}: #{base_column} -> #{tuesday_column}")

            Enum.each(tuesday_data, fn {currency, value} ->
              if Map.has_key?(currency_rows, currency) do
                row = currency_rows[currency]
                cell = "#{tuesday_column}#{row}"
                IO.puts("  Tuesday mapping: #{category} -> #{currency} -> Cell #{cell} = #{value}")
                write_schedule_21a_cell(file_id, cell, value, "#{category}_tuesday_#{currency}")
              else
                IO.puts("  Warning: Unknown currency #{currency} in Tuesday data for #{category}")
              end
            end)
          else
            IO.puts("  Tuesday data for #{category} is not a map: #{inspect(tuesday_data)}")
          end
        end
      end
    else
      IO.puts("Category data for #{category} is not a map: #{inspect(category_data)}")
    end
  end

  # Process special fields that don't follow the standard pattern
  defp process_special_schedule_21a_field(file_id, field_name, field_data) do
    IO.puts("Processing special field: #{field_name}")

    case field_name do
      "allowable_exp_15" ->
        # Single value that goes in a specific cell (based on template structure)
        write_schedule_21a_cell(file_id, "V43", field_data, field_name)

      "report_date" ->
        # Report date is a single value, not day-based
        # Place it in a header cell or skip it since it's metadata
        IO.puts("Skipping report_date field as it's metadata: #{inspect(field_data)}")

      "over_under_exposure" when is_map(field_data) ->
        # Day-based totals without currency breakdown
        # These appear to go in the totals row at the bottom of the template
        if Map.has_key?(field_data, "Monday") do
          write_schedule_21a_cell(file_id, "R43", field_data["Monday"], "#{field_name}_monday")
        end
        if Map.has_key?(field_data, "Tuesday") do
          write_schedule_21a_cell(file_id, "S43", field_data["Tuesday"], "#{field_name}_tuesday")
        end

      "total_reg_cap_ratio" when is_map(field_data) ->
        # Day-based totals without currency breakdown
        # These appear to go in the totals row at the bottom of the template
        if Map.has_key?(field_data, "Monday") do
          write_schedule_21a_cell(file_id, "T43", field_data["Monday"], "#{field_name}_monday")
        end
        if Map.has_key?(field_data, "Tuesday") do
          write_schedule_21a_cell(file_id, "U43", field_data["Tuesday"], "#{field_name}_tuesday")
        end

      # Handle any other fields that might be in the data but not in our standard mapping
      _ when is_map(field_data) ->
        IO.puts("Processing unmapped field with day structure: #{field_name}")
        # Try to handle it as a day-based field
        if Map.has_key?(field_data, "Monday") or Map.has_key?(field_data, "Tuesday") do
          IO.puts("Field #{field_name} has day-based structure but no column mapping defined")
        end

      _ ->
        IO.puts("Unknown special field: #{field_name}, skipping")
    end
  end

  # Helper function to get the next column letter
  defp next_column(column) do
    case column do
      "A" -> "B"; "B" -> "C"; "C" -> "D"; "D" -> "E"; "E" -> "F"
      "F" -> "G"; "G" -> "H"; "H" -> "I"; "I" -> "J"; "J" -> "K"
      "K" -> "L"; "L" -> "M"; "M" -> "N"; "N" -> "O"; "O" -> "P"
      "P" -> "Q"; "Q" -> "R"; "R" -> "S"; "S" -> "T"; "T" -> "U"
      "U" -> "V"; "V" -> "W"; "W" -> "X"; "X" -> "Y"; "Y" -> "Z"
      "Z" -> "AA"; "AA" -> "AB"; "AB" -> "AC"; "AC" -> "AD"; "AD" -> "AE"
      "AE" -> "AF"; "AF" -> "AG"; "AG" -> "AH"; "AH" -> "AI"; "AI" -> "AJ"
      "AJ" -> "AK"; "AK" -> "AL"; "AL" -> "AM"; "AM" -> "AN"; "AN" -> "AO"
      "AO" -> "AP"; "AP" -> "AQ"; "AQ" -> "AR"; "AR" -> "AS"; "AS" -> "AT"
      "AT" -> "AU"; "AU" -> "AV"; "AV" -> "AW"; "AW" -> "AX"; "AX" -> "AY"
      "AY" -> "AZ"; "AZ" -> "BA"; "BA" -> "BB"; "BB" -> "BC"; "BC" -> "BD"
      "BD" -> "BE"; "BE" -> "BF"; "BF" -> "BG"; "BG" -> "BH"; "BH" -> "BI"
      "BI" -> "BJ"; "BJ" -> "BK"; "BK" -> "BL"; "BL" -> "BM"; "BM" -> "BN"
      "BN" -> "BO"; "BO" -> "BP"; "BP" -> "BQ"; "BQ" -> "BR"; "BR" -> "BS"
      "BS" -> "BT"; "BT" -> "BU"; "BU" -> "BV"; "BV" -> "BW"; "BW" -> "BX"
      "BX" -> "BY"; "BY" -> "BZ"; "BZ" -> "CA"; "CA" -> "CB"; "CB" -> "CC"
      "CC" -> "CD"; "CD" -> "CE"; "CE" -> "CF"; "CF" -> "CG"; "CG" -> "CH"
      "CH" -> "CI"; "CI" -> "CJ"; "CJ" -> "CK"; "CK" -> "CL"; "CL" -> "CM"
      "CM" -> "CN"; "CN" -> "CO"; "CO" -> "CP"; "CP" -> "CQ"; "CQ" -> "CR"
      "CR" -> "CS"; "CS" -> "CT"; "CT" -> "CU"; "CU" -> "CV"; "CV" -> "CW"
      "CW" -> "CX"; "CX" -> "CY"; "CY" -> "CZ"
      _ -> "DZ"  # Fallback for any column beyond our range
    end
  end

  # Helper function to write a cell value with appropriate formatting
  defp write_schedule_21a_cell(file_id, cell, value, context) do
    IO.puts("Writing cell #{cell}: #{inspect(value)} (context: #{context})")

    try do
      # Determine if this is a percentage value
      is_percentage = is_binary(value) && String.contains?(value, "%")

      # Format and write the value
      cond do
        is_nil(value) ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", 0)

        value in ["0", "0.0", "0.00"] ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", 0)

        is_percentage ->
          # Keep percentage as string
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", value)

        is_binary(value) ->
          # Try to convert numeric strings to numbers
          clean_value = String.replace(value, ",", "")
          case Regex.match?(~r/^\d+\.?\d*$/, clean_value) do
            true ->
              if String.contains?(clean_value, ".") do
                float_val = String.to_float(clean_value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", float_val)
              else
                int_val = String.to_integer(clean_value)
                Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", int_val)
              end
            false ->
              Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", value)
          end

        is_map(value) && Map.has_key?(value, "sign") && Map.has_key?(value, "coef") && Map.has_key?(value, "exp") ->
          # Handle decimal map values
          decimal_value = Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", decimal_value)

        is_number(value) ->
          if is_float(value) do
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "float", value)
          else
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "int", value)
          end

        true ->
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", cell, "string", to_string(value))
      end

      IO.puts("  Successfully wrote cell #{cell}")
    rescue
      e ->
        IO.puts("  ERROR writing cell #{cell}: #{inspect(e)}")
    end
  end

  # Process simple Schedule 21A cell mapping (fallback to monthly report style)
  defp process_simple_schedule_21a(file_id, decoded_data) do

    decoded_data
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", 0)

      {index, value} when is_nil(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", 0)

      {index, value} when is_map(value) ->
        # Handle decimal values
        if Map.has_key?(value, "sign") && Map.has_key?(value, "coef") && Map.has_key?(value, "exp") do
          decimal_value = Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "float", decimal_value)
        else
          Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", inspect(value))
        end

      {index, value} when is_binary(value) ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", value)
        end

      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 21A", index, "string", to_string(value))
    end)
    |> Stream.run()
  end

  defp schedule_21B(file_id, data) do
    if is_nil(data.schedule_21B) do
      Logger.warning("Schedule 21B data is nil, skipping")
      :ok
    else
      # Use the expected sheet name directly
      IO.puts("\n==== USING EXPECTED SHEET NAME FOR 21B ====")
      schedule_21b_sheet = "Schedule 21B"
      IO.puts("Using sheet name: #{schedule_21b_sheet}")

      # Try writing to a test cell to verify basic functionality
      IO.puts("\n==== TESTING BASIC CELL WRITING FOR 21B ====")
      try do
        Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, "A1", "string", "TEST VALUE 21B")
        IO.puts("Successfully wrote test value to cell A1 in 21B")
      rescue
        e ->
          IO.puts("ERROR writing test value to 21B: #{inspect(e)}")
          IO.puts("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
      end

      # Decode the JSON data
      case Poison.decode(data.schedule_21B) do
        {:ok, decoded_data} ->
          # Inspect the decoded data
          IO.puts("\n==== SCHEDULE 21B RAW DATA ====")
          inspect_data("Schedule 21B Raw", decoded_data)

          # Process the data using the prudential report style
          # First, check if we have a map with cell references or a data structure
          cell_data = if is_map(decoded_data) && Enum.any?(Map.keys(decoded_data), fn key ->
            is_binary(key) && String.match?(key, ~r/^[A-Z]\d+$/)
          end) do
            # Data is already in cell reference format
            Logger.info("Schedule 21B data is already in cell reference format")
            decoded_data
          else
            # Data needs processing
            Logger.info("Processing Schedule 21B data structure")

            # Get the row indices
            row_indices = schedule21b_rows_index()
            IO.puts("\n==== SCHEDULE 21B ROW INDICES ====")
            IO.inspect(row_indices, label: "Row indices for Schedule 21B")

            # Initialize an empty map for cell data
            cell_map = %{}

            # Check if we have a "data" key with a list of entries
            entries = cond do
              is_map(decoded_data) && Map.has_key?(decoded_data, "data") && is_list(decoded_data["data"]) ->
                IO.puts("\n==== SCHEDULE 21B DATA ENTRIES ====")
                IO.puts("Found data key with list of entries: #{length(decoded_data["data"])} entries")
                decoded_data["data"]

              is_map(decoded_data) && Map.has_key?(decoded_data, "Totals") ->
                IO.puts("\n==== SCHEDULE 21B TOTALS ENTRY ====")
                IO.puts("Found Totals key, creating single entry")
                IO.inspect(decoded_data["Totals"], label: "Totals data")
                [%{"account_name" => "Totals", "balances" => decoded_data["Totals"]}]

              true ->
                # Default to a single Totals entry
                IO.puts("\n==== SCHEDULE 21B DEFAULT ENTRY ====")
                IO.puts("No data or Totals key found, creating default entry")
                [%{"account_name" => "Totals", "balances" => %{
                  "Monday" => "0.00",
                  "Tuesday" => "0.00",
                  "Wednesday" => "0.00",
                  "Thursday" => "0.00",
                  "Friday" => "0.00"
                }}]
            end

            # Inspect the entries
            IO.puts("\n==== SCHEDULE 21B ENTRIES ====")
            inspect_data("Schedule 21B Entries", entries)

            # Process each entry
            # Updated to use the new row indices that start from 22
            processed_data = Enum.zip(entries, schedule21b_rows_index())
            |> Enum.reduce(cell_map, fn {entry, row_index}, acc ->
              # Debug: Inspect each entry to see what fields are available
              IO.puts("\n--- Processing Schedule 21B entry at row #{row_index} ---")
              IO.puts("Entry keys: #{inspect(Map.keys(entry))}")
              IO.puts("Entry data: #{inspect(entry)}")

              # Try multiple possible field names for account name
              account_name = Map.get(entry, "account_name") ||
                            Map.get(entry, "name") ||
                            Map.get(entry, "institution_name") ||
                            Map.get(entry, :account_name) ||
                            Map.get(entry, :name) ||
                            ""

              IO.puts("Extracted account name: #{inspect(account_name)}")
              IO.puts("Processing entry: #{account_name} at row #{row_index}")

              # Add the account name to column A (updated from column B)
              acc = Map.put(acc, "A#{row_index}", account_name)

              # Add rating and rating agency if available
              rating = Map.get(entry, "rating", "")
              rating_agency = Map.get(entry, "rating_agency", "")
              acc = Map.put(acc, "B#{row_index}", rating)
              acc = Map.put(acc, "C#{row_index}", rating_agency)

              # Get balances
              balances = Map.get(entry, "balances", %{})
              IO.puts("Balances for #{account_name}:")
              IO.inspect(balances)

              # Extract individual day values with detailed logging
              monday_val = Map.get(balances, "Monday", "0.00")
              tuesday_val = Map.get(balances, "Tuesday", "0.00")
              wednesday_val = Map.get(balances, "Wednesday", "0.00")
              thursday_val = Map.get(balances, "Thursday", "0.00")
              friday_val = Map.get(balances, "Friday", "0.00")

              IO.puts("  Monday: #{inspect(monday_val)}")
              IO.puts("  Tuesday: #{inspect(tuesday_val)}")
              IO.puts("  Wednesday: #{inspect(wednesday_val)} <- Should have large values!")
              IO.puts("  Thursday: #{inspect(thursday_val)}")
              IO.puts("  Friday: #{inspect(friday_val)}")

              # Add balances for each day - updated column references to start from D
              acc = Map.put(acc, "D#{row_index}", monday_val)
              acc = Map.put(acc, "F#{row_index}", tuesday_val)
              acc = Map.put(acc, "H#{row_index}", wednesday_val)
              acc = Map.put(acc, "J#{row_index}", thursday_val)
              acc = Map.put(acc, "L#{row_index}", friday_val)

              # Get percentages
              percentages = Map.get(entry, "percentages", %{})
              IO.puts("Percentages for #{account_name}:")
              IO.inspect(percentages)

              # Add percentages for each day - updated column references
              acc = Map.put(acc, "E#{row_index}", Map.get(percentages, "Monday", "0%"))
              acc = Map.put(acc, "G#{row_index}", Map.get(percentages, "Tuesday", "0%"))
              acc = Map.put(acc, "I#{row_index}", Map.get(percentages, "Wednesday", "0%"))
              acc = Map.put(acc, "K#{row_index}", Map.get(percentages, "Thursday", "0%"))
              acc = Map.put(acc, "M#{row_index}", Map.get(percentages, "Friday", "0%"))

              # Add maximum value if available
              maximum = Map.get(entry, "maximum", "")
              Map.put(acc, "N#{row_index}", maximum)
            end)

            # Inspect the processed data
            IO.puts("\n==== SCHEDULE 21B PROCESSED DATA ====")
            inspect_data("Schedule 21B Processed", processed_data)

            processed_data
          end

          # Inspect the final cell data
          IO.puts("\n==== SCHEDULE 21B FINAL CELL DATA ====")
          inspect_data("Schedule 21B Final", cell_data)

          # Write the data to Excel and count cells
          IO.puts("\n==== WRITING SCHEDULE 21B DATA TO EXCEL ====")
          IO.puts("Using sheet name: #{schedule_21b_sheet}")

          # Create a list of cells to write for debugging
          cells_to_write = cell_data
          |> Enum.map(fn {index, value} ->
            {index, value, typeof(value)}
          end)
          |> Enum.take(30)

          IO.puts("First 30 cells to write for 21B:")
          Enum.each(cells_to_write, fn {index, value, type} ->
            IO.puts("  #{index}: #{inspect(value)} (#{type})")
          end)

          # Now write the actual data
          IO.puts("\n==== WRITING ACTUAL CELL DATA FOR 21B ====")
          {cell_count, success_count, error_count, _} = cell_data
          |> Enum.reduce({0, 0, 0, file_id}, fn {index, value}, {count, successes, errors, file_id} ->
            # Log the cell we're writing with more detail
            value_type = cond do
              is_binary(value) -> "string"
              is_integer(value) -> "integer"
              is_float(value) -> "float"
              is_nil(value) -> "nil"
              is_map(value) -> "map"
              true -> "unknown"
            end
            IO.puts("Writing to cell #{index}: #{inspect(value)} (type: #{value_type})")

            # Special attention to Wednesday columns (H column)
            if String.starts_with?(index, "H") do
              IO.puts("  *** WEDNESDAY DATA *** Cell #{index} = #{inspect(value)} (#{value_type}) - This should be a large number!")
            end

            # Increment cell count
            count = count + 1

            # Try to write the cell with error handling
            {new_successes, new_errors} = try do
              # Handle different value types
              cond do
                # Handle decimal map values
                is_map(value) && Map.has_key?(value, "sign") && Map.has_key?(value, "coef") && Map.has_key?(value, "exp") ->
                  decimal_value = Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float()
                  IO.puts("  Converting decimal map to float: #{decimal_value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "float", decimal_value)

                # Handle other map values
                is_map(value) ->
                  IO.puts("  Handling map value as string")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "string", inspect(value))



                # Handle zero values (be more specific to avoid catching valid numbers)
                value in ["0", "0.0", "0.00", "0%"] ->
                  IO.puts("  Handling zero string as float: #{value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "float", 0.0)

                # Handle nil values
                is_nil(value) ->
                  IO.puts("  Handling nil as integer zero")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "int", 0)

                # Handle integer values
                is_integer(value) ->
                  IO.puts("  Handling integer: #{value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "int", value)

                # Handle percentage values
                is_binary(value) && String.ends_with?(value, "%") ->
                  IO.puts("  Handling percentage string: #{value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "string", value)

                # Handle numeric strings (improved logic for large numbers with commas)
                is_binary(value) && Regex.match?(~r/^[\d,]+\.?\d*$/, value) ->
                  # Remove commas and handle large decimal numbers properly
                  cleaned_value = String.replace(value, ",", "")
                  IO.puts("  Processing numeric string: '#{value}' -> cleaned: '#{cleaned_value}'")

                  try do
                    # Check if it contains a decimal point
                    if String.contains?(cleaned_value, ".") do
                      # Handle as float for decimal numbers
                      float_value = String.to_float(cleaned_value)
                      IO.puts("  Converting to float: #{float_value}")
                      Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "float", float_value)
                    else
                      # Handle as integer for whole numbers
                      int_value = String.to_integer(cleaned_value)
                      IO.puts("  Converting to integer: #{int_value}")
                      Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "int", int_value)
                    end
                  rescue
                    e ->
                      IO.puts("  ERROR converting numeric string '#{value}': #{inspect(e)}")
                      # Fallback: keep as string to preserve the data
                      IO.puts("  Keeping as string to preserve data: #{value}")
                      Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "string", value)
                  end

                # Handle other string values
                is_binary(value) ->
                  IO.puts("  Handling as string: #{value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "string", value)

                # Handle any other type
                true ->
                  string_value = to_string(value)
                  IO.puts("  Converting to string: #{string_value}")
                  Excelizer.Cell.set_cell_value(file_id, schedule_21b_sheet, index, "string", string_value)
              end

              # If we get here, the cell was written successfully
              IO.puts("  Cell #{index} written successfully")
              {successes + 1, errors}
            rescue
              e ->
                IO.puts("  ERROR writing cell #{index}: #{inspect(e)}")
                IO.puts("  Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
                {successes, errors + 1}
            end

            # Save the workbook every 10 cells
            if rem(count, 10) == 0 do
              IO.puts("Saving workbook after #{count} cells...")
              try do
                Excelizer.Workbook.save(file_id)
              rescue
                e -> IO.puts("  ERROR saving workbook: #{inspect(e)}")
              end
            end

            # Return the updated counts and file_id
            {count, new_successes, new_errors, file_id}
          end)

          # Log the results
          IO.puts("\n==== CELL WRITING SUMMARY FOR 21B ====")
          IO.puts("Total cells processed: #{cell_count}")
          IO.puts("Successfully written: #{success_count}")
          IO.puts("Errors: #{error_count}")

          # Final save of the workbook
          IO.puts("\n==== FINAL SAVE OF WORKBOOK FOR 21B ====")
          try do
            IO.puts("Saving workbook...")
            Excelizer.Workbook.save(file_id)
            IO.puts("Workbook saved successfully")
          rescue
            e ->
              IO.puts("ERROR saving workbook: #{inspect(e)}")
              IO.puts("Stack trace: #{Exception.format_stacktrace(__STACKTRACE__)}")
          end

        {:error, error} ->
          Logger.error("Failed to decode Schedule 21B data: #{inspect(error)}")

          # Notify UI about error
          if Process.get(:reference) do
            Phoenix.PubSub.broadcast(
              MisReports.PubSub,
              "weekly_export:#{Process.get(:reference)}",
              {:weekly_export_data_warning, %{
                sheet: "Schedule 21B",
                message: "Error processing Schedule 21B data. The sheet will be empty."
              }}
            )
          end
      end
    end
  end

  # Helper function that defines the row indices for Schedule 21A
  defp schedule21a_rows_index() do
    # Define row indices for Schedule 21A
    # These represent the starting rows for each section of data
    # Updated to start from row 19 as requested
    [19, 22, 25, 28, 31, 34, 37, 40, 43, 46, 49, 52, 55]
  end

  # Helper function that defines the row indices for Schedule 21B
  defp schedule21b_rows_index() do
    # Define row indices for Schedule 21B
    # These represent the starting rows for each institution entry
    # Updated to start from row 22 as requested
    [22, 23, 24, 25, 26, 27, 28, 29, 30, 31]
  end

  # Helper function for data conversion
  defp string_to_decimal(string) do
    case string do
      nil -> Decimal.new(0)
      "" -> Decimal.new(0)
      str when is_binary(str) ->
        String.replace(str, ",", "")
        |> Decimal.new()
      _ -> Decimal.new(0)
    end
  end

  # These helper functions are no longer needed with our new approach
  # But we'll keep the function signatures for backward compatibility
  # in case they're called from elsewhere in the codebase

  # Helper function to prepare parameters for Schedule 21A (deprecated)
  defp prepare_params_21a(_entry, _index) do
    %{}
  end

  # Helper function to prepare header for Schedule 21A (deprecated)
  defp prepare_header_21a(_index, _entries) do
    %{}
  end

  # Helper function to prepare parameters for Schedule 21B (deprecated)
  defp prepare_params_21b(_entry, _index) do
    %{}
  end

  # Helper function to prepare header for Schedule 21B (deprecated)
  defp prepare_header_21b(_index, _entries) do
    %{}
  end

  # Helper function to traverse changeset errors
  defp traverse_errors(errors) do
    for {key, {msg, _opts}} <- errors do
      "#{key} #{msg}"
    end
  end

  # Helper function to convert string keys to atoms in a map
  defp convert_string_keys_to_atoms(map) when is_map(map) do
    Enum.reduce(map, %{}, fn {key, value}, acc ->
      atom_key = if is_binary(key), do: String.to_atom(key), else: key
      atom_value = case value do
        v when is_map(v) -> convert_string_keys_to_atoms(v)
        v when is_list(v) -> Enum.map(v, fn item ->
          if is_map(item), do: convert_string_keys_to_atoms(item), else: item
        end)
        _ -> value
      end
      Map.put(acc, atom_key, atom_value)
    end)
  end

  # Helper function to get the type of a value
  defp typeof(value) do
    cond do
      is_nil(value) -> "nil"
      is_binary(value) -> "binary (string)"
      is_boolean(value) -> "boolean"
      is_number(value) -> "number"
      is_atom(value) -> "atom"
      is_list(value) -> "list"
      is_map(value) -> "map"
      is_tuple(value) -> "tuple"
      is_function(value) -> "function"
      is_pid(value) -> "pid"
      is_port(value) -> "port"
      is_reference(value) -> "reference"
      true -> "unknown"
    end
  end
end
