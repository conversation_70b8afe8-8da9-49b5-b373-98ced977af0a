
defmodule MisReports.Workers.BozReq.Schedule18b do
  def perform(item) do
    decoded_item =
      case item.schedule_18b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    decoded_item = format_scientific_values(decoded_item)
    settings = MisReports.Utilities.get_comapany_settings_params()



    %{
      "returnKey" => "ZM-1KSCH18B1K003",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",

      "period_frequency"  =>"Monthly",
      "returnItemsList" => return_items_list(decoded_item),
      "dynamicItemsList" => []
    }
  end

  defp return_items_list(decoded_item) do
    [
      %{"Code" => "1204_00001", "Value" => "#{decoded_item["C16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00002", "Value" => "#{decoded_item["D16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00003", "Value" => "#{decoded_item["E16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00004", "Value" => "#{decoded_item["F16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00005", "Value" => "#{decoded_item["G16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00006", "Value" => "#{decoded_item["H16"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00007", "Value" => "#{decoded_item["C17"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00008", "Value" => "#{decoded_item["D17"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00009", "Value" => "#{decoded_item["E17"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00010", "Value" => "#{decoded_item["F17"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00011", "Value" => "#{decoded_item["G17"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00012", "Value" => "#{decoded_item["H17"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00013", "Value" => "#{decoded_item["C18"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00014", "Value" => "#{decoded_item["D18"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00015", "Value" => "#{decoded_item["E18"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00016", "Value" => "#{decoded_item["F18"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00017", "Value" => "#{decoded_item["G18"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00018", "Value" => "#{decoded_item["H18"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00019", "Value" => "#{decoded_item["C19"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00020", "Value" => "#{decoded_item["D19"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00021", "Value" => "#{decoded_item["E19"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00022", "Value" => "#{decoded_item["F19"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00023", "Value" => "#{decoded_item["G19"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00024", "Value" => "#{decoded_item["H19"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00025", "Value" => "#{decoded_item["C20"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00026", "Value" => "#{decoded_item["D20"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00027", "Value" => "#{decoded_item["E20"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00028", "Value" => "#{decoded_item["F20"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00029", "Value" => "#{decoded_item["G20"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00030", "Value" => "#{decoded_item["H20"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00031", "Value" => "#{decoded_item["C24"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00032", "Value" => "#{decoded_item["D24"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00033", "Value" => "#{decoded_item["E24"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00034", "Value" => "#{decoded_item["F24"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00035", "Value" => "#{decoded_item["G24"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00036", "Value" => "#{decoded_item["H24"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00037", "Value" => "#{decoded_item["C25"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00038", "Value" => "#{decoded_item["D25"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00039", "Value" => "#{decoded_item["E25"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00040", "Value" => "#{decoded_item["F25"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00041", "Value" => "#{decoded_item["G25"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00042", "Value" => "#{decoded_item["H25"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00043", "Value" => "#{decoded_item["C26"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00044", "Value" => "#{decoded_item["D26"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00045", "Value" => "#{decoded_item["E26"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00046", "Value" => "#{decoded_item["F26"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00047", "Value" => "#{decoded_item["G26"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00048", "Value" => "#{decoded_item["H26"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00049", "Value" => "#{decoded_item["C27"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00050", "Value" => "#{decoded_item["D27"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00051", "Value" => "#{decoded_item["E27"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00052", "Value" => "#{decoded_item["F27"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00053", "Value" => "#{decoded_item["G27"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00054", "Value" => "#{decoded_item["H27"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00055", "Value" => "#{decoded_item["C28"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00056", "Value" => "#{decoded_item["D28"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00057", "Value" => "#{decoded_item["E28"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00058", "Value" => "#{decoded_item["F28"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00059", "Value" => "#{decoded_item["G28"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00060", "Value" => "#{decoded_item["H28"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00061", "Value" => "#{decoded_item["C29"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00062", "Value" => "#{decoded_item["D29"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00063", "Value" => "#{decoded_item["E29"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00064", "Value" => "#{decoded_item["F29"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00065", "Value" => "#{decoded_item["G29"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00066", "Value" => "#{decoded_item["H29"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00067", "Value" => "#{decoded_item["C30"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00068", "Value" => "#{decoded_item["D30"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00069", "Value" => "#{decoded_item["E30"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00070", "Value" => "#{decoded_item["F30"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00071", "Value" => "#{decoded_item["G30"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00072", "Value" => "#{decoded_item["H30"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00073", "Value" => "#{decoded_item["C31"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00074", "Value" => "#{decoded_item["D31"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00075", "Value" => "#{decoded_item["E31"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00076", "Value" => "#{decoded_item["F31"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00077", "Value" => "#{decoded_item["G31"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00078", "Value" => "#{decoded_item["H31"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00079", "Value" => "#{decoded_item["C32"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00080", "Value" => "#{decoded_item["D32"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00081", "Value" => "#{decoded_item["E32"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00082", "Value" => "#{decoded_item["F32"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00083", "Value" => "#{decoded_item["G32"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00084", "Value" => "#{decoded_item["H32"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00085", "Value" => "#{decoded_item["C33"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00086", "Value" => "#{decoded_item["D33"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00087", "Value" => "#{decoded_item["E33"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00088", "Value" => "#{decoded_item["F33"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00089", "Value" => "#{decoded_item["G33"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00090", "Value" => "#{decoded_item["H33"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00091", "Value" => "#{decoded_item["B38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00092", "Value" => "#{decoded_item["C38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00093", "Value" => "#{decoded_item["D38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00094", "Value" => "#{decoded_item["E38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00095", "Value" => "#{decoded_item["F38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00096", "Value" => "#{decoded_item["G38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00097", "Value" => "#{decoded_item["H38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00098", "Value" => "#{decoded_item["I38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00099", "Value" => "#{decoded_item["J38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00100", "Value" => "#{decoded_item["K38"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00101", "Value" => "#{decoded_item["L38"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00102", "Value" => "#{decoded_item["B39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00103", "Value" => "#{decoded_item["C39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00104", "Value" => "#{decoded_item["D39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00105", "Value" => "#{decoded_item["E39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00106", "Value" => "#{decoded_item["F39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00107", "Value" => "#{decoded_item["G39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00108", "Value" => "#{decoded_item["H39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00109", "Value" => "#{decoded_item["I39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00110", "Value" => "#{decoded_item["J39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00111", "Value" => "#{decoded_item["K39"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00112", "Value" => "#{decoded_item["L39"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00113", "Value" => "#{decoded_item["B40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00114", "Value" => "#{decoded_item["C40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00115", "Value" => "#{decoded_item["D40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00116", "Value" => "#{decoded_item["E40"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00117", "Value" => "#{decoded_item["F40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00118", "Value" => "#{decoded_item["G40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00119", "Value" => "#{decoded_item["H40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00120", "Value" => "#{decoded_item["I40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00121", "Value" => "#{decoded_item["J40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00122", "Value" => "#{decoded_item["K40"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00123", "Value" => "#{decoded_item["L40"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00124", "Value" => "#{decoded_item["B41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00125", "Value" => "#{decoded_item["C41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00126", "Value" => "#{decoded_item["D41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00127", "Value" => "#{decoded_item["E41"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00128", "Value" => "#{decoded_item["F41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00129", "Value" => "#{decoded_item["G41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00130", "Value" => "#{decoded_item["H41"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00131", "Value" => "#{decoded_item["I41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00132", "Value" => "#{decoded_item["J41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00133", "Value" => "#{decoded_item["K41"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00134", "Value" => "#{decoded_item["L41"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00135", "Value" => "#{decoded_item["B43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00136", "Value" => "#{decoded_item["C43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00137", "Value" => "#{decoded_item["D43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00138", "Value" => "#{decoded_item["E43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00139", "Value" => "#{decoded_item["F43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00140", "Value" => "#{decoded_item["G43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00141", "Value" => "#{decoded_item["H43"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00142", "Value" => "#{decoded_item["I43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00143", "Value" => "#{decoded_item["J43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00144", "Value" => "#{decoded_item["K43"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00145", "Value" => "#{decoded_item["L43"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00146", "Value" => "#{decoded_item["B47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00147", "Value" => "#{decoded_item["C47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00148", "Value" => "#{decoded_item["D47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00149", "Value" => "#{decoded_item["E47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00150", "Value" => "#{decoded_item["F47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00151", "Value" => "#{decoded_item["G47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00152", "Value" => "#{decoded_item["H47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00153", "Value" => "#{decoded_item["I47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00154", "Value" => "#{decoded_item["J47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00155", "Value" => "#{decoded_item["K47"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00156", "Value" => "#{decoded_item["L47"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00157", "Value" => "#{decoded_item["B49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00158", "Value" => "#{decoded_item["C49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00159", "Value" => "#{decoded_item["D49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00160", "Value" => "#{decoded_item["E49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00161", "Value" => "#{decoded_item["F49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00162", "Value" => "#{decoded_item["G49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00163", "Value" => "#{decoded_item["H49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00164", "Value" => "#{decoded_item["I49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00165", "Value" => "#{decoded_item["J49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00166", "Value" => "#{decoded_item["K49"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00167", "Value" => "#{decoded_item["L49"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00168", "Value" => "#{decoded_item["B50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00169", "Value" => "#{decoded_item["C50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00170", "Value" => "#{decoded_item["D50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00171", "Value" => "#{decoded_item["E50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00172", "Value" => "#{decoded_item["F50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00173", "Value" => "#{decoded_item["G50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00174", "Value" => "#{decoded_item["H50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00175", "Value" => "#{decoded_item["I50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00176", "Value" => "#{decoded_item["J50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00177", "Value" => "#{decoded_item["K50"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00178", "Value" => "#{decoded_item["L50"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00179", "Value" => "#{decoded_item["B51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00180", "Value" => "#{decoded_item["C51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00181", "Value" => "#{decoded_item["D51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00182", "Value" => "#{decoded_item["E51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00183", "Value" => "#{decoded_item["F51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00184", "Value" => "#{decoded_item["G51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00185", "Value" => "#{decoded_item["H51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00186", "Value" => "#{decoded_item["I51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00187", "Value" => "#{decoded_item["J51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00188", "Value" => "#{decoded_item["K51"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00189", "Value" => "#{decoded_item["L51"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00190", "Value" => "#{decoded_item["B52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00191", "Value" => "#{decoded_item["C52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00192", "Value" => "#{decoded_item["D52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00193", "Value" => "#{decoded_item["E52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00194", "Value" => "#{decoded_item["F52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00195", "Value" => "#{decoded_item["G52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00196", "Value" => "#{decoded_item["H52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00197", "Value" => "#{decoded_item["I52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00198", "Value" => "#{decoded_item["J52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00199", "Value" => "#{decoded_item["K52"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00200", "Value" => "#{decoded_item["L52"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00201", "Value" => "#{decoded_item["C53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00202", "Value" => "#{decoded_item["D53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00203", "Value" => "#{decoded_item["E53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00204", "Value" => "#{decoded_item["F53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00205", "Value" => "#{decoded_item["G53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00206", "Value" => "#{decoded_item["H53"] || "0"}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00207", "Value" => "#{decoded_item["I53"] || "0"}", "_dataType" => "NUMERIC"},

      %{"Code" => "1204_00208", "Value" => "#{decoded_item["J53"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00209", "Value" => "#{decoded_item["K53"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00210", "Value" => "#{decoded_item["L53"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00211", "Value" => "#{decoded_item["C55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00212", "Value" => "#{decoded_item["D55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00213", "Value" => "#{decoded_item["E55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00214", "Value" => "#{decoded_item["F55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00215", "Value" => "#{decoded_item["G55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00216", "Value" => "#{decoded_item["H55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00217", "Value" => "#{decoded_item["I55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00219", "Value" => "#{decoded_item["K55"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00220", "Value" => "#{decoded_item["L55"]}", "_dataType" => "NUMERIC "},
      %{"Code" => "1204_00212", "Value" => "#{decoded_item["C59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00213", "Value" => "#{decoded_item["D59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00214", "Value" => "#{decoded_item["E59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00215", "Value" => "#{decoded_item["F59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00216", "Value" => "#{decoded_item["G59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00217", "Value" => "#{decoded_item["H59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00218", "Value" => "#{decoded_item["I59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00219", "Value" => "#{decoded_item["J59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00220", "Value" => "#{decoded_item["K59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00221", "Value" => "#{decoded_item["L59"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00222", "Value" => "#{decoded_item["C60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00223", "Value" => "#{decoded_item["D60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00224", "Value" => "#{decoded_item["E60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00225", "Value" => "#{decoded_item["F60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00226", "Value" => "#{decoded_item["G60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00227", "Value" => "#{decoded_item["H60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00228", "Value" => "#{decoded_item["I60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00229", "Value" => "#{decoded_item["J60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00230", "Value" => "#{decoded_item["K60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00231", "Value" => "#{decoded_item["L60"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00232", "Value" => "#{decoded_item["C61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00233", "Value" => "#{decoded_item["D61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00234", "Value" => "#{decoded_item["E61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00235", "Value" => "#{decoded_item["F61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00236", "Value" => "#{decoded_item["G61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00237", "Value" => "#{decoded_item["H61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00238", "Value" => "#{decoded_item["I61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00239", "Value" => "#{decoded_item["J61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00240", "Value" => "#{decoded_item["K61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00241", "Value" => "#{decoded_item["L61"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00242", "Value" => "#{decoded_item["C63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00243", "Value" => "#{decoded_item["D63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00244", "Value" => "#{decoded_item["E63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00245", "Value" => "#{decoded_item["F63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00246", "Value" => "#{decoded_item["G63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00247", "Value" => "#{decoded_item["H63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00248", "Value" => "#{decoded_item["I63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00249", "Value" => "#{decoded_item["J63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00250", "Value" => "#{decoded_item["K63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00251", "Value" => "#{decoded_item["L63"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00253", "Value" => "#{decoded_item["B67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00254", "Value" => "#{decoded_item["C67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00255", "Value" => "#{decoded_item["D67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00256", "Value" => "#{decoded_item["E67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00257", "Value" => "#{decoded_item["F67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00258", "Value" => "#{decoded_item["G67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00259", "Value" => "#{decoded_item["H67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00260", "Value" => "#{decoded_item["I67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00261", "Value" => "#{decoded_item["J67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00262", "Value" => "#{decoded_item["K67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00263", "Value" => "#{decoded_item["L67"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00264", "Value" => "#{decoded_item["B68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00265", "Value" => "#{decoded_item["C68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00266", "Value" => "#{decoded_item["D68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00267", "Value" => "#{decoded_item["E68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00268", "Value" => "#{decoded_item["F68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00269", "Value" => "#{decoded_item["G68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00270", "Value" => "#{decoded_item["H68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00271", "Value" => "#{decoded_item["I68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00272", "Value" => "#{decoded_item["J68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00273", "Value" => "#{decoded_item["K68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00274", "Value" => "#{decoded_item["L68"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00275", "Value" => "#{decoded_item["B69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00276", "Value" => "#{decoded_item["C69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00277", "Value" => "#{decoded_item["D69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00278", "Value" => "#{decoded_item["E69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00279", "Value" => "#{decoded_item["F69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00280", "Value" => "#{decoded_item["G69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00281", "Value" => "#{decoded_item["H69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00282", "Value" => "#{decoded_item["I69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00283", "Value" => "#{decoded_item["J69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00284", "Value" => "#{decoded_item["K69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00285", "Value" => "#{decoded_item["L69"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00286", "Value" => "#{decoded_item["B70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00287", "Value" => "#{decoded_item["C70"]}", "_dataType" => "NUMERIC "},
      %{"Code" => "1204_00288", "Value" => "#{decoded_item["D70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00289", "Value" => "#{decoded_item["E70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00290", "Value" => "#{decoded_item["F70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00291", "Value" => "#{decoded_item["G70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00292", "Value" => "#{decoded_item["H70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00293", "Value" => "#{decoded_item["I70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00294", "Value" => "#{decoded_item["J70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00295", "Value" => "#{decoded_item["K70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00296", "Value" => "#{decoded_item["L70"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00297", "Value" => "#{decoded_item["B71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00298", "Value" => "#{decoded_item["C71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00299", "Value" => "#{decoded_item["D71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00300", "Value" => "#{decoded_item["E71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00301", "Value" => "#{decoded_item["F71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00302", "Value" => "#{decoded_item["G71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00303", "Value" => "#{decoded_item["H71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00304", "Value" => "#{decoded_item["I71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00305", "Value" => "#{decoded_item["J71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00306", "Value" => "#{decoded_item["K71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00307", "Value" => "#{decoded_item["L71"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00308", "Value" => "#{decoded_item["B72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00309", "Value" => "#{decoded_item["C72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00310", "Value" => "#{decoded_item["D72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00311", "Value" => "#{decoded_item["E72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00312", "Value" => "#{decoded_item["F72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00313", "Value" => "#{decoded_item["G72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00314", "Value" => "#{decoded_item["H72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00315", "Value" => "#{decoded_item["I72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00316", "Value" => "#{decoded_item["J72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00317", "Value" => "#{decoded_item["K72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00318", "Value" => "#{decoded_item["L72"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00319", "Value" => "#{decoded_item["B73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00320", "Value" => "#{decoded_item["C73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00321", "Value" => "#{decoded_item["D73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00322", "Value" => "#{decoded_item["E73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00323", "Value" => "#{decoded_item["F73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00324", "Value" => "#{decoded_item["G73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00325", "Value" => "#{decoded_item["H73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00326", "Value" => "#{decoded_item["I73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00327", "Value" => "#{decoded_item["J73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00328", "Value" => "#{decoded_item["K73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00329", "Value" => "#{decoded_item["L73"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00330", "Value" => "#{decoded_item["B74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00331", "Value" => "#{decoded_item["C74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00332", "Value" => "#{decoded_item["D74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00333", "Value" => "#{decoded_item["E74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00334", "Value" => "#{decoded_item["F74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00335", "Value" => "#{decoded_item["G74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00336", "Value" => "#{decoded_item["H74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00337", "Value" => "#{decoded_item["I74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00338", "Value" => "#{decoded_item["J74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00339", "Value" => "#{decoded_item["K74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00340", "Value" => "#{decoded_item["L74"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00341", "Value" => "#{decoded_item["B75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00342", "Value" => "#{decoded_item["C75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00343", "Value" => "#{decoded_item["D75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00344", "Value" => "#{decoded_item["E75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00345", "Value" => "#{decoded_item["F75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00346", "Value" => "#{decoded_item["G75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00347", "Value" => "#{decoded_item["H75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00348", "Value" => "#{decoded_item["I75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00349", "Value" => "#{decoded_item["J75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00350", "Value" => "#{decoded_item["K75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00351", "Value" => "#{decoded_item["L75"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00352", "Value" => "#{decoded_item["B76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00353", "Value" => "#{decoded_item["C76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00354", "Value" => "#{decoded_item["D76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00355", "Value" => "#{decoded_item["E76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00356", "Value" => "#{decoded_item["F76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00357", "Value" => "#{decoded_item["G76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00358", "Value" => "#{decoded_item["H76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00359", "Value" => "#{decoded_item["I76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00360", "Value" => "#{decoded_item["J76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00361", "Value" => "#{decoded_item["K76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00362", "Value" => "#{decoded_item["L76"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00363", "Value" => "#{decoded_item["B77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00364", "Value" => "#{decoded_item["C77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00365", "Value" => "#{decoded_item["D77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00366", "Value" => "#{decoded_item["E77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00367", "Value" => "#{decoded_item["F77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00368", "Value" => "#{decoded_item["G77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00369", "Value" => "#{decoded_item["H77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00370", "Value" => "#{decoded_item["I77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00371", "Value" => "#{decoded_item["J77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00372", "Value" => "#{decoded_item["K77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00373", "Value" => "#{decoded_item["L77"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00374", "Value" => "#{decoded_item["B79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00375", "Value" => "#{decoded_item["C79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00376", "Value" => "#{decoded_item["D79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00377", "Value" => "#{decoded_item["E79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00378", "Value" => "#{decoded_item["F79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00379", "Value" => "#{decoded_item["G79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00380", "Value" => "#{decoded_item["H79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00381", "Value" => "#{decoded_item["I79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00382", "Value" => "#{decoded_item["J79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00383", "Value" => "#{decoded_item["K79"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1204_00384", "Value" => "#{decoded_item["L79"]}", "_dataType" => "NUMERIC"}
    ]
  end

  def format_scientific_values(map) when is_map(map) do
    (get_in(map, ["list"]) || [])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  def format_scientific_values(_), do: %{}


  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end
end
