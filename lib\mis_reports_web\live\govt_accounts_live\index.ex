defmodule MisReportsWeb.GovtAccountsLive.Index do
      alias ElixirSense.Core.Source
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.SourceData.GovtAccounts
  alias MisReports.SourceData
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       currency_code: "",
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       action: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]
    if UserLiveAuth.authorize?(socket, opts) do

      socket =
      socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

       {:noreply,
         socket
         |> apply_action(socket.assigns.live_action, params)}
     else
       UserLiveAuth.unauthorized(socket)
     end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :govt_accounts, %GovtAccounts{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    govt_account = SourceData.get_govt_accounts!(id)
    socket
    |> assign(:govt_accounts, govt_account)
  end

  defp apply_action(socket, :index, _params), do: list_govt_accounts(socket)

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        govt_accounts = SourceData.get_govt_accounts_by_reference!(reference)

        socket
        |> assign(:govt_accounts, govt_accounts)
        |> assign(:changeset, SourceData.change_govt_accounts(govt_accounts))
        |> assign(:reference, reference)
    end
  end

  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_govt_accounts()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_govt_accounts()}
  end

  @impl true
  def handle_event("select_option", %{"option" => option}, socket) do
    {:noreply, assign(socket, selected_option: option)}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_govt_accounts()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:account_name, :account_number, :product_code_source, :maker_id, :inserted_at], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)
    {:noreply,
      socket
      |> assign(sort_by: sort_by)
      |> list_govt_accounts()}
  end



  def handle_delete(params, socket) do
    id = params["id"]
    govt_account = SourceData.get_govt_accounts!(id)
    audit_msg =
      "Deleted govt account: \"#{govt_account.account_name}\""
    current_user = socket.assigns.current_user
    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_govt_account, govt_account)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_govt_account: _govt_account, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Govt Account Deleted successfully!")
        |> push_redirect(
          to: Routes.govt_accounts_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()
        {:noreply,
        socket
        |> put_flash(:error, reason)
        |> push_redirect(
          to: Routes.govt_accounts_index_path(socket, :index)
        )}
    end
  end


  defp list_govt_accounts(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->

          SourceData.list_govt_accounts(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"govt_account", "new"}

      act when act in ~w(edit)a ->
        {"govt_account", "edit"}

      act when act in ~w(update_status)a ->
        {"govt_account", "update_status"}

      act when act in ~w(delete)a ->
        {"govt_account", "delete"}

      act when act in ~w(index)a ->
        {"govt_account", "index"}
      _ ->
        {"govt_account", "unknown"}
    end
  end
end
