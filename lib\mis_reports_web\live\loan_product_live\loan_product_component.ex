defmodule MisReportsWeb.LoanProductLive.LoanProductComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Repo}
  alias MisReports.{Prudentials, Prudentials.LoanProducts}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  require Logger

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.LoanProductView, "loan_product.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.LoanProductView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{loan_product: loan_product} = assigns, socket) do
    changeset = Prudentials.change_loan_product(loan_product)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"loan_product" => loan_product_params}, socket) do
    changeset =
      socket.assigns.loan_product
      |> LoanProducts.changeset(loan_product_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"loan_product" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    current_user_id = to_string(socket.assigns.current_user.id)
    user_id = socket.assigns.current_user.id
    audit_msg = "Created Loan Product \"#{params["product"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:loan_product, LoanProducts.changeset(%LoanProducts{maker_id: user_id}, params))
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_product: loan_product}} ->
        Task.start(fn ->
          case MisReports.Workflow.call_workflow(
            socket.assigns.reference,
            socket.assigns.process_id,
            current_user_id,
            80,
            "",
            "",
            "Submission of Loan Product Creation"
          ) do
            {:ok, reference_number} ->
              Prudentials.update_loan_product(loan_product, %{reference: reference_number})
            {:error, reason} ->
              Logger.error("""
              [LoanProductComponent] Workflow update failed:
              Loan Product ID: #{loan_product.id}
              Process ID: #{socket.assigns.process_id}
              Reference: #{socket.assigns.reference}
              Error: #{inspect(reason)}
              """)
              nil
          end
        end)

        # Return success immediately
        {:noreply,
         socket
         |> put_flash(:info, "Loan product created successfully")
         |> push_redirect(to: Routes.loan_product_index_path(socket, :new))}

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    loan_product = socket.assigns.loan_product
    socket
    |> handle_update(params, loan_product)
    |> case do
      {:ok, loan_product} ->
        {:noreply,
         socket
         |> put_flash(:info, "Loan product updated successfully")
         |> push_redirect(to: Routes.loan_product_index_path(socket, :edit, loan_product))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Loan Product Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Loan product rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject loan product: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :approve, params) do
    loan_product = socket.assigns.loan_product
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Loan Product Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      LoanProducts.changeset(loan_product, %{
        status: "A",
        checker_id: current_user.id,
        checker_date: NaiveDateTime.utc_now()
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_loan_product}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Loan product approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Loan product approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve loan product")
         |> assign(:changeset, %{loan_product.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, loan_product) do
    audit_msg = "Updated Loan Product for \"#{loan_product.product}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:loan_product, LoanProducts.changeset(loan_product, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_product: loan_product, audit_log: _user_log}} ->
        {:ok, loan_product}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def traverse_errors(errors) do
    for {key, {msg, opts}} <- errors, into: %{} do
      msg =
        Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
          opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
        end)

      {key, msg}
    end
  end
end
