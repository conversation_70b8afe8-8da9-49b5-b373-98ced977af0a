defmodule MisReportsWeb.BranchLive.BranchComponent do
  use MisReportsWeb, :live_component
  alias MisReports.Utilities
  alias MisReports.Utilities.Branch
  alias MisReportsWeb.BranchController
  alias MisReportsWeb.UserController
  alias MisReports.Repo
  # alias MisReportsWeb.LiveHelpers
  import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.BranchView, "new.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.BranchView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{branch: branch} = assigns, socket) do
    changeset = Utilities.change_branch(branch)

    # Extract URL parameters with default values
    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     # Explicitly assign action
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"branch" => branch_params}, socket) do
    changeset =
      socket.assigns.branch
      |> Branch.changeset(branch_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"branch" => _branch_params} = params, socket) do
    save_branch(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      # Reject action
      "96" ->
        save_branch(socket, :reject, params)

      # Approve action
      "97" ->
        save_branch(socket, :approve, params)

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_branch(socket, :edit, params) do
    put_in(params, ["branch", "id"], socket.assigns.branch.id)
    |> then(&(put_conn_user(socket) |> BranchController.update(&1)))
    |> case do
      {:ok, branch} ->
        {:noreply,
         socket
         |> put_flash(:info, "Branch updated successfully")
         |> push_redirect(to: Routes.branch_index_path(socket, :edit, branch))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_branch(socket, :new, params) do
    current_user_id = to_string(socket.assigns.current_user.id)

    # Use existing create function for branch creation with audit
    case put_conn_user(socket) |> BranchController.create(params) do
      {:ok, branch} ->
        # Handle workflow and reference update
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Branch Creation"
             ) do
          {:ok, reference_number} ->
            # Update branch with reference
            case Utilities.update_branch(branch, %{reference: reference_number}) do
              {:ok, updated_branch} ->
                {:noreply,
                 socket
                 |> put_flash(
                   :info,
                   "Branch created successfully. Reference: #{reference_number}"
                 )
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update branch reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Branch created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  defp save_branch(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Branch Rejected"

    # Add action_id from params or use default
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Branch rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject branch: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_branch(socket, :approve, params) do
    branch = socket.assigns.branch
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Branch Approval"

    # First update the branch status
    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Branch.changeset(branch, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_branch}} ->
        # Then call the workflow
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Branch approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Branch approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve branch")
         |> assign(:changeset, %{branch.changeset | errors: failed_value.errors})}
    end
  end

  def traverse_errors(errors) do
    for {key, {msg, opts}} <- errors, into: %{} do
      msg =
        Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
          opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
        end)

      {key, msg}
    end
  end
end
