defmodule MisReports.Employees.EmployeeStats do
  use Endon
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_employee_stats" do
    field :descript, :string
    field :lusaka, :map
    field :central, :map
    field :eastern, :map
    field :cb, :map
    field :southern, :map
    field :luapula, :map
    field :western, :map
    field :northern, :map
    field :north_west, :map
    field :muchinga, :map
    field :muchinga_tbl, :map
    field :status, :string, default: "PROGRESSING"
    field :maker_date, :naive_datetime
    field :checker_date, :naive_datetime
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id
    timestamps()
  end

  @doc false
  def changeset(employee_stats, attrs) do
    employee_stats
    |> cast(attrs, [
      :descript,
      :lusaka,
      :central,
      :eastern,
      :cb,
      :southern,
      :luapula,
      :western,
      :northern,
      :north_west,
      :muchinga,
      :muchinga_tbl,
      :status,
      :maker_date,
      :checker_date,
      :maker_id,
      :checker_id,
      :reference
    ])
    |> validate_required([:descript, :status, :maker_id, :maker_date])
  end
end
