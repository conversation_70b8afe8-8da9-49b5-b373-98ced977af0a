defmodule MisReports.Workers.BozReq.Schedule14 do

  def perform(item) do
    decoded_item =
      case item.schedule_14 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()


    %{
      "returnKey" => "ZM-5WSCH145W001",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1194_00001",
          "Value" => "#{decoded_item["E23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00002",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00003",
          "Value" => "#{decoded_item["E24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00004",
          "Value" => "#{decoded_item["F24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00005",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00006",
          "Value" => "#{decoded_item["E25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00007",
          "Value" => "#{decoded_item["F25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00008",
          "Value" => "#{decoded_item["E26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00009",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00010",
          "Value" => "#{decoded_item["E27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00011",
          "Value" => "#{decoded_item["F27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00012",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00013",
          "Value" => "#{decoded_item["E28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00014",
          "Value" => "#{decoded_item["F28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00015",
          "Value" => "#{decoded_item["E29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00016",
          "Value" => "#{decoded_item["E30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00017",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00018",
          "Value" => "#{decoded_item["E31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00019",
          "Value" => "#{decoded_item["F31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00020",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00021",
          "Value" => "#{decoded_item["E32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00022",
          "Value" => "#{decoded_item["F32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00023",
          "Value" => "#{decoded_item["E33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00024",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00025",
          "Value" => "#{decoded_item["E34"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00026",
          "Value" => "#{decoded_item["F34"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00027",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00028",
          "Value" => "#{decoded_item["E35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00029",
          "Value" => "#{decoded_item["F35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00030",
          "Value" => "#{decoded_item["E36"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00031",
          "Value" => "#{decoded_item["H50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00032",
          "Value" => "#{decoded_item["E37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00033",
          "Value" => "#{decoded_item["F37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00034",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00035",
          "Value" => "#{decoded_item["E38"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00036",
          "Value" => "#{decoded_item["F38"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00037",
          "Value" => "#{decoded_item["E39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00038",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00039",
          "Value" => "#{decoded_item["E40"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00040",
          "Value" => "#{decoded_item["F40"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00041",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00042",
          "Value" => "#{decoded_item["E41"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00043",
          "Value" => "#{decoded_item["F41"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00044",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00045",
          "Value" => "#{decoded_item["E42"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00046",
          "Value" => "#{decoded_item["F42"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00047",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00048",
          "Value" => "#{decoded_item["E43"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00049",
          "Value" => "#{decoded_item["F43"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00050",
          "Value" => "#{decoded_item["E44"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00051",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00052",
          "Value" => "#{decoded_item["E45"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00053",
          "Value" => "#{decoded_item["F45"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00054",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00055",
          "Value" => "#{decoded_item["E46"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00056",
          "Value" => "#{decoded_item["F46"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00057",
          "Value" => "#{decoded_item["E47"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00058",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00059",
          "Value" => "#{decoded_item["E48"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00060",
          "Value" => "#{decoded_item["F48"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00061",
          "Value" => "#{decoded_item["H50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00062",
          "Value" => "#{decoded_item["E49"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00063",
          "Value" => "#{decoded_item["F49"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00064",
          "Value" => "#{decoded_item["H50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00065",
          "Value" => "#{decoded_item["E50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00066",
          "Value" => "#{decoded_item["F50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00067",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00068",
          "Value" => "#{decoded_item["E51"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00069",
          "Value" => "#{decoded_item["F51"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00070",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00071",
          "Value" => "#{decoded_item["E52"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00072",
          "Value" => "#{decoded_item["F52"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00073",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00074",
          "Value" => "#{decoded_item["E53"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00075",
          "Value" => "#{decoded_item["F53"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00076",
          "Value" => "#{decoded_item["E54"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00077",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00078",
          "Value" => "#{decoded_item["E55"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00079",
          "Value" => "#{decoded_item["F55"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00080",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00081",
          "Value" => "#{decoded_item["E56"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00082",
          "Value" => "#{decoded_item["F56"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00083",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00084",
          "Value" => "#{decoded_item["E57"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00085",
          "Value" => "#{decoded_item["F57"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00086",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00087",
          "Value" => "#{decoded_item["E58"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00088",
          "Value" => "#{decoded_item["F58"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00089",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00090",
          "Value" => "#{decoded_item["E59"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00091",
          "Value" => "#{decoded_item["F59"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00092",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00093",
          "Value" => "#{decoded_item["E60"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00094",
          "Value" => "#{decoded_item["F60"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00095",
          "Value" => "#{decoded_item["E61"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00096",
          "Value" => "#{decoded_item["F61"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00097",
          "Value" => "#{decoded_item["C68"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00098",
          "Value" => "#{decoded_item["F68"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00099",
          "Value" => "#{decoded_item["C68"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00100",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00101",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00102",
          "Value" => "#{decoded_item["F68"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00103",
          "Value" => "#{decoded_item["C69"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00104",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00105",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00106",
          "Value" => "#{decoded_item["F69"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00107",
          "Value" => "#{decoded_item["C70"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00108",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00109",
          "Value" => "#{decoded_item["H1OO"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00110",
          "Value" => "#{decoded_item["F70"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00111",
          "Value" => "#{decoded_item["C71"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00112",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00113",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00114",
          "Value" => "#{decoded_item["F71"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00115",
          "Value" => "#{decoded_item["C72"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00116",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00117",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00118",
          "Value" => "#{decoded_item["F72"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00119",
          "Value" => "#{decoded_item["F38"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00120",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00121",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00122",
          "Value" => "#{decoded_item["F73"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00123",
          "Value" => "#{decoded_item["C74"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00124",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00125",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00126",
          "Value" => "#{decoded_item["F74"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00127",
          "Value" => "#{decoded_item["C75"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00128",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00129",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00130",
          "Value" => "#{decoded_item["F75"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00131",
          "Value" => "#{decoded_item["C76"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00132",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00133",
          "Value" => "#{decoded_item["H50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00134",
          "Value" => "#{decoded_item["F76"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00135",
          "Value" => "#{decoded_item["C77"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00136",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00137",
          "Value" => "#{decoded_item["H10"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00138",
          "Value" => "#{decoded_item["C77"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00139",
          "Value" => "#{decoded_item["C78"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00140",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00141",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00142",
          "Value" => "#{decoded_item["F78"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00143",
          "Value" => "#{decoded_item["C79"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00144",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00145",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00146",
          "Value" => "#{decoded_item["C79"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00147",
          "Value" => "#{decoded_item["C80"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00148",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00149",
          "Value" => "#{decoded_item["H100"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00150",
          "Value" => "#{decoded_item["F80"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00151",
          "Value" => "#{decoded_item["C81"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00152",
          "Value" => "#{decoded_item["F81"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00153",
          "Value" => "#{decoded_item["C82"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1194_00154",
          "Value" => "#{decoded_item["F82"] || "0"}",
          "_dataType" => "NUMERIC"
        }
      ]
}

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
