defmodule MisReports.Workers.BozReq.Schedule01f do
   def perform(item) do

    decoded_item =
      case item.schedule_01f do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
      dynamic_list = decoded_item["list"]
    # regulatory_capital = decoded_item["header"]

    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()
    %{
      "ReturnKey" => "ZM-0JSCH1F0J002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1132_00001", "Value" => "#{decoded_item["G15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1132_00002", "Value" => "#{decoded_item["H15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1132_00003", "Value" => "#{decoded_item["I15"]}", "_dataType" => "NUMERIC"},
      ] |> format_map(),
      "DynamicItemsList" => [
        %{
          "Area" => 432,
          "_areaName" => "LISTING OF ENCUMBERED SECURITIES",
          "DynamicItems" => map_data(dynamic_list) |> format_values()
        }
      ]
    }


  end

   def map_data(records) do
     Enum.flat_map(Enum.with_index(records), fn {map, index} ->
       index = index + 1
       [
         %{"Code" => "1.#{index}", "Value" => map["A"], "_dataType" => "TEXT"},
         %{"Code" => "1.#{index}", "Value" => map["B"], "_dataType" => "TEXT"},
         %{"Code" => "1.#{index}", "Value" => map["C"], "_dataType" => "TEXT"},
         %{"Code" => "1.#{index}", "Value" => map["D"], "_dataType" => "DATE"},
         %{"Code" => "1.#{index}", "Value" => map["E"], "_dataType" => "DATE"},
         %{"Code" => "1.#{index}", "Value" => map["F"], "_dataType" => "TEXT"},
         %{"Code" => "1.#{index}", "Value" => convert_decimal_map_to_decimal(map["G"]), "_dataType" => "NUMERIC"},
         %{"Code" => "1.#{index}", "Value" => convert_decimal_map_to_decimal(map["H"]), "_dataType" => "NUMERIC"},
         %{"Code" => "1.#{index}", "Value" => convert_decimal_map_to_decimal(map["I"]), "_dataType" => "NUMERIC"},

       ]
     end)
   end

   def format_map(list_of_maps) do
    Enum.map(list_of_maps, fn map ->
      Enum.map(map, fn {key, value} ->
        {key, format_number(value)}
      end)
      |> Map.new()
    end)
  end

  defp format_number(string) do
    string
    |> String.replace(",", "")
    |> case do
      "" -> "0"
      value -> value
    end
  end




  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  def convert_decimal_map_to_decimal(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end
end
