defmodule MisReportsWeb.ExchangePlacementLive.ExchangePlacementComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReportsWeb.UserController
  alias MisReports.Utilities.ExchangePlacement
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.ExchangePlacementView, "exchange_placement.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.ExchangePlacementView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{exchange_placement: exchange_placement} = assigns, socket) do
    changeset = Utilities.change_exchange_placement(exchange_placement)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"exchange_placement" => exchange_placement}, socket) do
    changeset =
      socket.assigns.exchange_placement
      |> ExchangePlacement.changeset(exchange_placement)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"exchange_placement" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_approval(socket, :reject, params)
      "97" -> handle_approval(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg = "Created new Exchange Placement for Institution \"#{params["institution_name"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :exchange_placement,
      ExchangePlacement.changeset(%ExchangePlacement{maker_id: user.id}, params)
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{exchange_placement: exchange_placement}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Exchange placements Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_exchange_placement(exchange_placement, %{reference: reference_number}) do
              {:ok, updated_exchange_placement} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Exchange placements created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update Exchange placements reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Exchange placements created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    exchange_placement = socket.assigns.exchange_placement

    socket
    |> handle_update(params, exchange_placement)
    |> case do
      {:ok, exchange_placement} ->
        {:noreply,
         socket
         |> put_flash(:info, "Exchange Placement updated successfully")
         |> push_redirect(to: Routes.exchange_placement_index_path(socket, :index))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, exchange_placement) do
    audit_msg = "Updated Exchange Placement for Institution \"#{params["institution_name"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :exchange_placement,
      ExchangePlacement.changeset(
        exchange_placement,
        Map.merge(params, %{"status" => "D", "checker_id" => nil})
      )
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{exchange_placement: exchange_placement, audit_log: _user_log}} ->
        {:ok, exchange_placement}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_approval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Exchange placements Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Exchange placements rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject Exchange placements: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_approval(socket, :approve, params) do
    exchange_placement = socket.assigns.exchange_placement
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Exchange placements Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      ExchangePlacement.changeset(exchange_placement, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_exchange_placement}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Exchange placements approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Exchange placements approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve Exchange placements")
         |> assign(:changeset, %{exchange_placement.changeset | errors: failed_value.errors})}
    end
  end
end
