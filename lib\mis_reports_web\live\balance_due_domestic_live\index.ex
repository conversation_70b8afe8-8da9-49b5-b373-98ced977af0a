defmodule MisReportsWeb.BalanceDueDomesticLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.{Repo}
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.Utilities.BalanceDueDomestic


  @impl true
  def mount(_params, _session, socket) do
    changeset = Utilities.change_balance_due_domestic(%BalanceDueDomestic{})
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       process_id: nil,
       reference: nil,
       step_id: nil
     )
     |> assign(:changeset, %{changeset | errors: %{}})
    }
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]
    if UserLiveAuth.authorize?(socket, opts) do

      socket =
        socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

       {:noreply,
         socket
         |> apply_action(socket.assigns.live_action, params)}

     else
       UserLiveAuth.unauthorized(socket)
     end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :balance_due_domestic, %BalanceDueDomestic{})
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    rate = Utilities.get_balance_due_domestic!(id)
    socket
    |> assign(:balance_due_domestic, Utilities.get_balance_due_domestic!(id))
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        balance_due_domestic = Utilities.get_balance_due_domestic_by_reference!(reference)

        socket
        |> assign(:balance_due_domestic, balance_due_domestic)
        |> assign(:changeset, Utilities.change_balance_due_domestic(balance_due_domestic))
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_balance_due_domestic(socket)

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]
    if UserLiveAuth.authorize?(socket, opts) do
    handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]
    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_balance_due_domestic()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_balance_due_domestic()}
  end

  @impl true
  def handle_event("select_option", %{"option" => option}, socket) do
    {:noreply, assign(socket, selected_option: option)}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_balance_due_domestic()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:report_dt, :inserted_at, :status, :maker_id], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
      socket
      |> assign(sort_by: sort_by)
      |> list_balance_due_domestic()}
  end

  defp list_balance_due_domestic(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->

          Utilities.list_balance_due_domestic(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end


  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    balance_due_domestic = Utilities.get_balance_due_domestic!(id)
    audit_msg = "Changed status for entry with report date of \"#{balance_due_domestic.report_dt}\"  to: #{status}"
    current_user = socket.assigns.current_user
    if current_user.id == balance_due_domestic.maker_id do
      {:noreply,
       socket
       |> put_flash(:error, "You cannot approve your own submission. Please have another user review and activate it.")
       |> push_redirect(to: Routes.balance_due_domestic_index_path(socket, :index))}
    else
      audit_msg = "Changed status for entry with report date of \"#{balance_due_domestic.report_dt}\"  to: #{status}"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      BalanceDueDomestic.changeset(balance_due_domestic, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _secure_holding, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.balance_due_domestic_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        reason = traverse_errors(failed_value.errors) |> List.first()
        {:noreply,
        socket
        |> put_flash(:error, reason)
        |> push_redirect(
          to: Routes.balance_due_domestic_index_path(socket, :index)
        )}
      end
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    balance_due_domestic = Utilities.get_balance_due_domestic!(id)
    audit_msg = "Deleted entry with report date \"#{balance_due_domestic.report_dt}\" "
    current_user = socket.assigns.current_user
    Ecto.Multi.new()
    |> Ecto.Multi.delete(:balance_due_domestic, balance_due_domestic)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{balance_due_domestic: _balance_due_domestic, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Record Deleted successfully!")
        |> push_redirect(to: Routes.balance_due_domestic_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end
  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")


  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"balance_due_domestic", "new"}

      act when act in ~w(edit)a ->
        {"balance_due_domestic", "edit"}

      act when act in ~w(update_status)a ->
        {"balance_due_domestic", "update_status"}

      act when act in ~w(delete)a ->
        {"balance_due_domestic", "delete"}

      act when act in ~w(index)a ->
        {"balance_due_domestic", "index"}

      _ ->
        {"balance_due_domestic", "unknown"}
    end
  end
end
