defmodule MisReports.Workers.Quartely.QuarterlyPublicationExport do
  alias MisReports.Workers.{Sh01e, Sh01c, BalanceSheet}

  #liquid position values - return all necessary liquidity position keys
  def generate_display_for_liquid_pos(blc_sheet, data, shd13, date, adjustments, usd_rate) do

    # Calculate components for liquid assets percentage
    notes_and_coins = extract_value(blc_sheet, "B11") |> Decimal.new()
    current_account = extract_value(blc_sheet, "B19") |> Decimal.new()
    treasury_bills = get_treasury_bills_issued_by_govt(blc_sheet)
    total_deposits = extract_value(blc_sheet, "B63") |> Decimal.new()

    # Calculate first percentage
    liquid_assets_percentage =
      if Decimal.compare(total_deposits, Decimal.new(0)) == :eq do
        BalanceSheet.format_number(Decimal.new(0))  # This will give you "0.00" in your standard format
      else
        [notes_and_coins, current_account, treasury_bills]
        |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
        |> Decimal.div(total_deposits)
        |> Decimal.mult(Decimal.new(100))
        |> Decimal.round(2)
        |> BalanceSheet.format_number()
    end


    # Calculate total liquid assets percentage
    total_liquid_assets = calculate_total_liquid_assets(blc_sheet, date)
    total_liquid_assets_decimal =
      total_liquid_assets
      |> String.replace(",", "")
      |> Decimal.new()

    total_liquid_assets_percentage =
      if Decimal.compare(total_deposits, Decimal.new(0)) == :eq do
        BalanceSheet.format_number(Decimal.new(0))  # This will give you "0.00" in your standard format
      else
      total_liquid_assets_decimal
      |> Decimal.div(total_deposits)
      |> Decimal.mult(Decimal.new(100))
      |> Decimal.round(2)
      |> BalanceSheet.format_number()

    end

    %{
      "B11" => get_in(blc_sheet, ["B11"]), # Notes and coins
      "B19" => get_in(blc_sheet, ["B19"]), # Current account
      "B64" => get_in(blc_sheet, ["B64"]), # Demand deposits
      "B65" => get_in(blc_sheet, ["B65"]), # Savings deposits
      "B66" => get_in(blc_sheet, ["B66"]), # Time deposits
      "B63" => get_in(blc_sheet, ["B63"]), # Total deposits
      "statutory" => get_statutory_total(blc_sheet),
      "Treasury_bills" => get_treasury_bills_issued_by_govt(blc_sheet),
      "local_registered_securities" => get_local_registered_securities(date),
      "TOTAL_ASSETS" => total_liquid_assets,
      "liquid_assets_percentage" => liquid_assets_percentage,
      "total_liquid_assets_percentage" => total_liquid_assets_percentage # Added total liquid assets percentage
    }
  end

  #balance sheet data - return all necessary balance sheet keys
  def generate_display_for_blc_sheet(blc_sheet, data, shd13, date, adjustments, usd_rate) do

    contingent_liabilities = extract_value(blc_sheet, "B108") |> Decimal.new()
    commitments = extract_value(blc_sheet, "B117") |> Decimal.new()
    total_off_balance = Decimal.add(contingent_liabilities, commitments)

    %{
      "B11" => get_in(blc_sheet, ["B11"]), # Notes and Coins
      "B16" => get_in(blc_sheet, ["B16"]), # Balances with Bank of Zambia
      "B22" => get_in(blc_sheet, ["B22"]), # Balances with Banks in Zambia
      "B26" => get_in(blc_sheet, ["B26"]), # Balances with Banks abroad
      "B29" => get_in(blc_sheet, ["B29"]), # Investments in Securities
      "B42" => get_in(blc_sheet, ["B42"]), # Net Loans and Advances
      "B49" => get_in(blc_sheet, ["B49"]), # Fixed Assets
      "B50" => get_in(blc_sheet, ["B50"]), # Other Assets
      "B60" => get_in(blc_sheet, ["B60"]), # Total Assets
      "B63" => get_in(blc_sheet, ["B63"]), # Deposits
      "B67" => get_in(blc_sheet, ["B67"]), # Due to Bank of Zambia
      "B72" => get_in(blc_sheet, ["B72"]), # Due to Banks in Zambia
      "B76" => get_in(blc_sheet, ["B76"]), # Due to Banks abroad
      "B81" => get_in(blc_sheet, ["B81"]), # Other Liabilities
      "B91" => get_in(blc_sheet, ["B91"]),
      "B96" => get_in(blc_sheet, ["B96"]), # Shareholders' equity
      "B105" => get_in(blc_sheet, ["B105"]), # Total liabilities and equity
      "B107" => get_in(blc_sheet, ["B107"]), # Off Balance Sheet Items
      "B108" => get_in(blc_sheet, ["B108"]), # Contingent liabilities
      "B117" => get_in(blc_sheet, ["B117"]), # Commitments
      "B118" => BalanceSheet.format_number(total_off_balance) # Total Off Balance Sheet Items
    }
  end

  #quarter to date and year to date from income statement trend
  def generate_display_for_income_stmt(start_date, end_date) do
    quarter_ended_IS = MisReports.Workers.Quartely.IncomeStatementTrend.generate_display(start_date, end_date)

    %{
      quarterly_totals_by_key: Map.merge(%{}, quarter_ended_IS[:quarterly_totals_by_key] || %{}, fn _k, v1, _v2 ->
        %{
          "C32" => get_in(v1, ["C32"]), # Other interest income
          "C57" => get_in(v1, ["C57"]), # Subordinated debt interest
          "C59" => get_in(v1, ["C59"]), # Other interest expense
          "C71" => get_in(v1, ["C71"]), # Commissions
          "C73" => get_in(v1, ["C73"]), # Fees from forex transactions
          "C77" => get_in(v1, ["C77"]), # Realized trading gains
          "C74" => get_in(v1, ["C74"]), # Unrealized forex gains
          "C80" => get_in(v1, ["C80"]), # Other income
          "C87" => get_in(v1, ["C87"]), # Depreciation
          "C94" => get_in(v1, ["C94"]), # Other expenses
          "C97" => get_in(v1, ["C97"])  # Taxation
        }
      end),
      sum_quarterly_totals_by_keys: Map.merge(%{}, quarter_ended_IS[:sum_quarterly_totals_by_keys] || %{}, fn _k, v1, _v2 ->
        %{
          loans_and_advances_from_normal_deposits: get_in(v1, ["loans_and_advances_from_normal_deposits"]),
          from_banks_and_other_financial_institutions: get_in(v1, ["from_banks_and_other_financial_institutions"]),
          securities: get_in(v1, ["securities"]),
          total_interest_income: get_in(v1, ["total_interest_income"]),
          deposits: get_in(v1, ["deposits"]),
          interest_paid_to_banks_and_financial_institutions: get_in(v1, ["interest_paid_to_banks_and_financial_institutions"]),
          total_interest_expense: get_in(v1, ["total_interest_expense"]),
          net_interest_income: get_in(v1, ["net_interest_income"]),
          total_provisions: get_in(v1, ["total_provisions"]),
          net_interest_income_after_provisions: get_in(v1, ["net_interest_income_after_provisions"]),
          total_non_interest_income: get_in(v1, ["total_non_interest_income"]),
          net_interest_and_other_income: get_in(v1, ["net_interest_and_other_income"]),
          total_non_interest_expenses: get_in(v1, ["total_non_interest_expenses"]),
          income_loss_before_taxes: get_in(v1, ["income_loss_before_taxes"]),
          income_loss_after_taxes: get_in(v1, ["income_loss_after_taxes"])
        }
      end),
      ytd_values: %{
        sum_across_quarters: %{
          loans_and_advances_from_normal_deposits: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :loans_and_advances_from_normal_deposits]),
          from_banks_and_other_financial_institutions: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :from_banks_and_other_financial_institutions]),
          securities: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :securities]),
          total_interest_income: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :total_interest_income]),
          deposits: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :deposits]),
          interest_paid_to_banks_and_financial_institutions: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :interest_paid_to_banks_and_financial_institutions]),
          total_interest_expense: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :total_interest_expense]),
          net_interest_income: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :net_interest_income]),
          total_provisions: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :total_provisions]),
          net_interest_income_after_provisions: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :net_interest_income_after_provisions]),
          total_non_interest_income: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :total_non_interest_income]),
          net_interest_and_other_income: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :net_interest_and_other_income]),
          total_non_interest_expenses: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :total_non_interest_expenses]),
          income_loss_before_taxes: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :income_loss_before_taxes]),
          income_loss_after_taxes: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters, :income_loss_after_taxes])
        },
        sum_across_quarters_2: %{
          C32: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C32]),  # Other interest income
          C57: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C57]),  # Subordinated debt interest
          C59: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C59]),  # Other interest expense
          C71: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C71]),  # Commissions
          C73: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C73]),  # Fees from forex transactions
          C77: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C77]),  # Realized trading gains
          C74: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C74]),  # Unrealized forex gains
          C80: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C80]),  # Other income
          C87: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C87]),  # Depreciation
          C94: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C94]),  # Other expenses
          C97: get_in(quarter_ended_IS, [:ytd_values, :sum_across_quarters_2, :C97])   # Taxation
        }
      }
    }
  end

  #capital position data - return all necessary capital position keys
  def generate_display_for_capital_pos(sh15) do


    %{
      "C21" => get_in(sh15, ["C21"]), # Paid-up common shares
      "C24" => get_in(sh15, ["C24"]), # Retained earnings
      "C35" => get_in(sh15, ["C35"]), # General Reserve
      "C26" => get_in(sh15, ["C26"]), # Statutory Reserves
      "C28" => get_in(sh15, ["C28"]), # Sub total
      "C25" => get_in(sh15, ["C25"]), # Other adjustments
      "C47" => get_in(sh15, ["C47"]), # Total Primary Capital
      "C50" => get_in(sh15, ["C50"]), # Eligible subordinated term debt
      "C53" => get_in(sh15, ["C53"]), # Revaluation Reserve
      "C55" => get_in(sh15, ["C55"]), # Eligible Secondary Capital
      "C61" => get_in(sh15, ["C61"]), # Eligible Total Capital
      "C58" => get_in(sh15, ["C58"]), # Minimum Total Capital Requirement
      "C60" => get_in(sh15, ["C60"]), # Excess/Deficiency
      "C62" => get_in(sh15, ["C62"])  # Risk Based Assets
    }
  end

  defp calculate_total_liquid_assets(data, date) do
    notes_and_coins = extract_value(data, "B11")
    current_account = extract_value(data, "B19")
    treasury_bills = get_treasury_bills_issued_by_govt(data)
    local_registered_securities = get_local_registered_securities(date)
    statutory = get_statutory_total(data)

    [notes_and_coins, current_account, treasury_bills, local_registered_securities, statutory]
    |> Enum.reduce(Decimal.new(0), &Decimal.add/2)
    |> BalanceSheet.format_number()
  end

  defp get_statutory_total(data) do
    local_statutory_reserves = extract_value(data, "B17")
    foreign_statutory_reserves = extract_value(data, "B18")
    total = Decimal.add(local_statutory_reserves, foreign_statutory_reserves)

    total
  end

  defp get_treasury_bills_issued_by_govt(data) do
    b31_value = extract_value(data, "B31")
    b33_value = extract_value(data, "B33")
    total = Decimal.add(b31_value, b33_value)

    total
  end

  defp get_local_registered_securities(date) do
    o1e_data = Sh01e.generate_display(date,date)
    o1c_data = Sh01c.generate_display(date, date)

    k29_value = extract_value(o1e_data, "K29")
    b30_value = extract_value(o1c_data, "B30")

    value = Decimal.sub(k29_value, b30_value)
    value
  end

  def extract_value(map, key) do
    map
    |> Map.get(key, "0")
    |> String.replace(",", "")
    |> String.trim()
    |> then(fn str ->
      case Float.parse(str) do
        {num, _} ->
          if num == trunc(num), do: trunc(num), else: num
        :error -> 0
      end
    end)
  end

end
