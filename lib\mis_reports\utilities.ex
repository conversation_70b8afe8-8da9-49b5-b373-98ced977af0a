defmodule MisReports.Utilities do
  @moduledoc """
  The Utilities context.
  """

  import Ecto.Query, warn: false
  alias MisReports.Repo

  alias MisReports.Utilities.SysDirectory
  alias MisReports.Prudentials.GdpFile

  @doc """
  Returns the list of tbl_sys_directories.

  ## Examples

      iex> list_tbl_sys_directories()
      [%SysDirectory{}, ...]

  """
  def list_tbl_sys_directories do
    Repo.all(SysDirectory)
  end

  @doc """
  Gets a single sys_directory.

  Raises `Ecto.NoResultsError` if the Sys directory does not exist.

  ## Examples

      iex> get_sys_directory!(123)
      %SysDirectory{}

      iex> get_sys_directory!(456)
      ** (Ecto.NoResultsError)

  """
  def get_sys_directory!(id), do: Repo.get!(SysDirectory, id)

  @doc """
  Creates a sys_directory.

  ## Examples

      iex> create_sys_directory(%{field: value})
      {:ok, %SysDirectory{}}

      iex> create_sys_directory(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_sys_directory(attrs \\ %{}) do
    %SysDirectory{}
    |> SysDirectory.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a sys_directory.

  ## Examples

      iex> update_sys_directory(sys_directory, %{field: new_value})
      {:ok, %SysDirectory{}}

      iex> update_sys_directory(sys_directory, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_sys_directory(%SysDirectory{} = sys_directory, attrs) do
    sys_directory
    |> SysDirectory.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a sys_directory.

  ## Examples

      iex> delete_sys_directory(sys_directory)
      {:ok, %SysDirectory{}}

      iex> delete_sys_directory(sys_directory)
      {:error, %Ecto.Changeset{}}

  """
  def delete_sys_directory(%SysDirectory{} = sys_directory) do
    Repo.delete(sys_directory)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking sys_directory changes.

  ## Examples

      iex> change_sys_directory(sys_directory)
      %Ecto.Changeset{data: %SysDirectory{}}

  """
  def change_sys_directory(%SysDirectory{} = sys_directory, attrs \\ %{}) do
    SysDirectory.changeset(sys_directory, attrs)
  end

def get_directory_params do
  try do
    case Cachex.get(:app_prereqs, "directories") do
      {:ok, nil} ->
        # Cache miss, try to get from DB first
        case directory_data() do
          %{__meta__: _, uploads: uploads, templates: templates, errors: errors, downloads: downloads, complete: complete} = db_paths ->
            # Get config paths for templates
            file_paths = Application.get_env(:mis_reports, :file_storage)
            base_path = file_paths[:base_path]
            templates_path = "#{base_path}/#{file_paths[:templates_path]}"

            # Create params with DB paths and config template path
            params = %{
              uploads: db_paths.uploads,
              downloads: db_paths.downloads,
              complete: db_paths.complete,
              errors: db_paths.errors,
              # templates: templates_path,
              templates: db_paths.templates,
              wip: "#{db_paths.complete}/wip"  # Fallback for wip
            }

            # Update cache
            Cachex.put(:app_prereqs, "directories", params)
            params

          _ ->
            # Not found in DB, use config paths
            file_paths = Application.get_env(:mis_reports, :file_storage)
            base_path = file_paths[:base_path]
            %{templates: "#{base_path}/#{file_paths[:templates_path]}"}
        end

      {:ok, params} ->
        # Cache hit, ensure template path is from config
        file_paths = Application.get_env(:mis_reports, :file_storage)
        base_path = file_paths[:base_path]
        templates_path = "#{base_path}/#{file_paths[:templates_path]}"
        Map.put(params, :templates, templates_path)

      _ ->
        # Cache error, use config paths
        file_paths = Application.get_env(:mis_reports, :file_storage)
        base_path = file_paths[:base_path]
        %{templates: "#{base_path}/#{file_paths[:templates_path]}"}
    end
  rescue
    _ ->
      # When all else fails, use config paths
      file_paths = Application.get_env(:mis_reports, :file_storage)
      base_path = file_paths[:base_path]
      %{templates: "#{base_path}/#{file_paths[:templates_path]}"}
end
end

  defp setup_directory_params do
    SysDirectory.Directories.all()
    |> Enum.reduce(%{}, &Map.put(&2, &1.param_name, &1.param_value))
    |> SysDirectory.from(with: &SysDirectory.changeset/2)
    |> Params.data()
  end

  def directory_data do
    SysDirectory.Directories.all()
    |> Enum.reduce(%{}, &Map.put(&2, &1.param_name, &1.param_value))
    |> SysDirectory.from(with: &SysDirectory.changeset/2)
    |> Params.data()
  end

  alias MisReports.Utilities.CompanySettings

  @doc """
  Returns the list of tbl_company_settings.

  ## Examples

      iex> list_tbl_company_settings()
      [%CompanySettings{}, ...]

  """
  def list_tbl_company_settings do
    Repo.all(CompanySettings)
  end

  @doc """
  Gets a single company_settings.

  Raises `Ecto.NoResultsError` if the Company settings does not exist.

  ## Examples

      iex> get_company_settings!(123)
      %CompanySettings{}

      iex> get_company_settings!(456)
      ** (Ecto.NoResultsError)

  """
  def get_company_settings!(id), do: Repo.get!(CompanySettings, id)

  @doc """
  Creates a company_settings.

  ## Examples

      iex> create_company_settings(%{field: value})
      {:ok, %CompanySettings{}}

      iex> create_company_settings(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_company_settings(attrs \\ %{}) do
    %CompanySettings{}
    |> CompanySettings.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a company_settings.

  ## Examples

      iex> update_company_settings(company_settings, %{field: new_value})
      {:ok, %CompanySettings{}}

      iex> update_company_settings(company_settings, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_company_settings(%CompanySettings{} = company_settings, attrs) do
    company_settings
    |> CompanySettings.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a company_settings.

  ## Examples

      iex> delete_company_settings(company_settings)
      {:ok, %CompanySettings{}}

      iex> delete_company_settings(company_settings)
      {:error, %Ecto.Changeset{}}

  """
  def delete_company_settings(%CompanySettings{} = company_settings) do
    Repo.delete(company_settings)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking company_settings changes.

  ## Examples

      iex> change_company_settings(company_settings)
      %Ecto.Changeset{data: %CompanySettings{}}

  """
  def change_company_settings(%CompanySettings{} = company_settings, attrs \\ %{}) do
    CompanySettings.changeset(company_settings, attrs)
  end

  def get_comapany_settings_params do
    case Cachex.get(:app_prereqs, "comapany_settings") do
      {:ok, nil} ->
        params =
          CompanySettings.SystemSettings.all()
          |> Enum.reduce(%{}, &Map.put(&2, &1.param_name, &1.param_value))
          |> CompanySettings.from(with: &CompanySettings.changeset/2)
          |> Params.data()

        Cachex.put(:app_prereqs, "comapany_settings", params, ttl: :timer.minutes(30))
        params

      {:ok, params} ->
        params
    end
  end

  def comapany_settings_data do
    CompanySettings.SystemSettings.all()
    |> Enum.reduce(%{}, &Map.put(&2, &1.param_name, &1.param_value))
    |> CompanySettings.from(with: &CompanySettings.changeset/2)
    |> Params.data()
  end

  alias MisReports.Utilities.GovAccount

  @doc """
  Returns the list of gov_accounts.

  ## Examples

      iex> list_gov_accounts()
      [%GovAccount{}, ...]

  """
  def list_gov_accounts do
    Repo.all(GovAccount)
  end

  def list_gov_accounts(params) do
    GovAccount
    # |> isearch_bank_acc_filter(params.isearch)
    # |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  @doc """
  Gets a single gov_account.

  Raises `Ecto.NoResultsError` if the Gov account does not exist.

  ## Examples

      iex> get_gov_account!(123)
      %GovAccount{}

      iex> get_gov_account!(456)
      ** (Ecto.NoResultsError)

  """
  def get_gov_account!(id), do: Repo.get!(GovAccount, id)

  @doc """
  Creates a gov_account.

  ## Examples

      iex> create_gov_account(%{field: value})
      {:ok, %GovAccount{}}

      iex> create_gov_account(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_gov_account(attrs \\ %{}) do
    %GovAccount{}
    |> GovAccount.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a gov_account.

  ## Examples

      iex> update_gov_account(gov_account, %{field: new_value})
      {:ok, %GovAccount{}}

      iex> update_gov_account(gov_account, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_gov_account(%GovAccount{} = gov_account, attrs) do
    gov_account
    |> GovAccount.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a gov_account.

  ## Examples

      iex> delete_gov_account(gov_account)
      {:ok, %GovAccount{}}

      iex> delete_gov_account(gov_account)
      {:error, %Ecto.Changeset{}}

  """
  def delete_gov_account(%GovAccount{} = gov_account) do
    Repo.delete(gov_account)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking gov_account changes.

  ## Examples

      iex> change_gov_account(gov_account)
      %Ecto.Changeset{data: %GovAccount{}}

  """
  def change_gov_account(%GovAccount{} = gov_account, attrs \\ %{}) do
    GovAccount.changeset(gov_account, attrs)
  end

  alias MisReports.Utilities.Customer

  @doc """
  Returns the list of tbl_customer_details.

  ## Examples

      iex> list_tbl_customer_details()
      [%Customer{}, ...]

  """
  def list_tbl_customer_details do
    Repo.all(Customer)
  end

  @doc """
  Gets a single customer.

  Raises `Ecto.NoResultsError` if the Customer does not exist.

  ## Examples

      iex> get_customer!(123)
      %Customer{}

      iex> get_customer!(456)
      ** (Ecto.NoResultsError)

  """
  def get_customer!(id), do: Repo.get!(Customer, id)

  @doc """
  Creates a customer.

  ## Examples

      iex> create_customer(%{field: value})
      {:ok, %Customer{}}

      iex> create_customer(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_customer(attrs \\ %{}) do
    %Customer{}
    |> Customer.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a customer.

  ## Examples

      iex> update_customer(customer, %{field: new_value})
      {:ok, %Customer{}}

      iex> update_customer(customer, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_customer(%Customer{} = customer, attrs) do
    customer
    |> Customer.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a customer.

  ## Examples

      iex> delete_customer(customer)
      {:ok, %Customer{}}

      iex> delete_customer(customer)
      {:error, %Ecto.Changeset{}}

  """
  def delete_customer(%Customer{} = customer) do
    Repo.delete(customer)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking customer changes.

  ## Examples

      iex> change_customer(customer)
      %Ecto.Changeset{data: %Customer{}}

  """
  def change_customer(%Customer{} = customer, attrs \\ %{}) do
    Customer.changeset(customer, attrs)
  end

  def cust_acc_list(params) do
    Customer
    |> preload([:checker, :maker])
    |> cust_acc_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp cust_acc_isearch_filter(query, nil), do: query

  defp cust_acc_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.account_no, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.currency, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.cust_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.cust_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.deposit_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.economic_sector, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.economic_sub_sector, ^search_term))
    |> or_where(
      [a],
      fragment("lower(?) like lower(?)", a.institutional_units_and_sectors, ^search_term)
    )
    |> or_where([a], fragment("lower(?) like lower(?)", a.time_deposit_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.group, ^search_term))
  end

  alias MisReports.Utilities.Branch

  @doc """
  Returns the list of tbl_branch.

  ## Examples

      iex> list_tbl_branch()
      [%Branch{}, ...]

  """
  def list_branches do
    Repo.all(Branch)
  end

  def list_branches(params) do
    Branch
    |> preload([:checker, :maker])
    |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter(query, nil), do: query

  defp isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.number, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.phone, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.email, ^search_term))
  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  @doc """
  Gets a single branch.

  Raises `Ecto.NoResultsError` if the Branch does not exist.

  ## Examples

      iex> get_branch!(123)
      %Branch{}

      iex> get_branch!(456)
      ** (Ecto.NoResultsError)

  """
  def get_branch!(id), do: Repo.get!(Branch, id)

  def get_branch_by_reference!(reference), do: Repo.get_by!(Branch, reference: reference)

  @doc """
  Creates a branch.

  ## Examples

      iex> create_branch(%{field: value})
      {:ok, %Branch{}}

      iex> create_branch(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_branch(attrs \\ %{}) do
    %Branch{}
    |> Branch.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a branch.

  ## Examples

      iex> update_branch(branch, %{field: new_value})
      {:ok, %Branch{}}

      iex> update_branch(branch, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_branch(%Branch{} = branch, attrs) do
    branch
    |> Branch.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a branch.

  ## Examples

      iex> delete_branch(branch)
      {:ok, %Branch{}}

      iex> delete_branch(branch)
      {:error, %Ecto.Changeset{}}

  """
  def delete_branch(%Branch{} = branch) do
    Repo.delete(branch)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking branch changes.

  ## Examples

      iex> change_branch(branch)
      %Ecto.Changeset{data: %Branch{}}

  """
  def change_branch(%Branch{} = branch) do
    Branch.changeset(branch, %{})
  end

  alias MisReports.Utilities.ExchangeRates

  def get_exchange_rate!(id), do: Repo.get!(ExchangeRates, id)

  def get_exchange_rate_by_reference!(reference),
    do: Repo.get_by!(ExchangeRates, reference: reference)

  def list_exchange_rates(params) do
    ExchangeRates
    |> preload([:checker, :maker])
    |> isearch_exchange_rate_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_exchange_rate_filter(query, nil), do: query

  defp isearch_exchange_rate_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.currency_code, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.currency_description, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
  end

  def get_exchange_rate_by_date_and_code(start_date, ccy_code) do
    ExchangeRates
    |> where(
      [a],
      fragment("CONVERT(DATE, ?) = ?", a.month, ^start_date) and a.currency_code == ^ccy_code and
        a.status == ^"A"
    )
    |> limit(1)
    |> select([a], %{
      currency_code: a.currency_code,
      currency_description: a.currency_description,
      amount: a.amount,
      exchange_rate_lcy: a.exchange_rate_lcy,
      month: a.month,
      status: a.status
    })
    |> Repo.one()
  end

  def update_exchange_rate_references(exchange_rate_ids, reference_number) when is_list(exchange_rate_ids) do
    import Ecto.Query

    Repo.update_all(
      from(e in ExchangeRates, where: e.id in ^exchange_rate_ids),
      set: [reference: reference_number]
    )
  end

  def get_exchange_rate_by_date(date) do
    {:ok, date} = Date.from_iso8601(date)

    last_week_start = Timex.beginning_of_week(Timex.shift(date, weeks: -1), :monday)
    last_week_end = Timex.end_of_week(Timex.shift(date, weeks: -1), :friday)

    query =
      from er in ExchangeRates,
        where: er.month >= ^last_week_start and er.month <= ^last_week_end and er.status == ^"A",
        select: %{
          currency_code: er.currency_code,
          currency_description: er.currency_description,
          amount: er.amount,
          exchange_rate_lcy: er.exchange_rate_lcy,
          month: er.month
        }

    results = Repo.all(query)

    weekday_names = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]

    results
    |> Enum.group_by(& &1.month)
    |> Enum.map(fn {day, entries} ->
      weekday_name = Enum.at(weekday_names, Date.day_of_week(day) - 1)

      %{day: weekday_name, entries: entries}
    end)
  end

  def change_exchange_rate(%ExchangeRates{} = exchange_rate) do
    ExchangeRates.changeset(exchange_rate, %{})
  end

  def update_exchange_rate(%ExchangeRates{} = exchange_rate, attrs) do
    exchange_rate
    |> ExchangeRates.changeset(attrs)
    |> Repo.update()
  end

  def get_usd_rates_for_periods(period_one_date, period_two_date) do
    period_one_rate =
      ExchangeRates
      |> where(
        [er],
        fragment("CONVERT(DATE, ?) = ?", er.month, ^period_one_date) and
          er.currency_code == "USD" and
          er.status == "A"
      )
      |> select([er], er.exchange_rate_lcy)
      |> limit(1)
      |> Repo.one() || Decimal.new("1.0")

    period_two_rate =
      ExchangeRates
      |> where(
        [er],
        fragment("CONVERT(DATE, ?) = ?", er.month, ^period_two_date) and
          er.currency_code == "USD" and
          er.status == "A"
      )
      |> select([er], er.exchange_rate_lcy)
      |> limit(1)
      |> Repo.one() || Decimal.new("1.0")

    %{
      period_one_rate: period_one_rate,
      period_two_rate: period_two_rate
    }
  end

  def exchange_rate_report do
    ExchangeRates
    |> where([a], a.status == "A")
    |> select(
      [a],
      %{
        currency_code: a.currency_code,
        amount: a.amount,
        exchange_rate_lcy: a.exchange_rate_lcy,
        month: a.month
      }
    )
    |> Repo.all()
    |> Enum.map(fn data ->
      %{
        currency_code: data.currency_code,
        amount: data.amount,
        exchange_rate_lcy: data.exchange_rate_lcy,
        month: if(data.month == nil, do: Timex.today(), else: data.month)
      }
    end)
    |> Enum.filter(fn data ->
      data.month.year == Timex.now().year and data.month == Timex.end_of_month(data.month)
    end)
  end

  def get_exchange_rates_by_reference!(reference) do
    from(e in ExchangeRates, where: e.reference == ^reference)
    |> Repo.all()
  end

  alias MisReports.Utilities.RepossedProp

  # def create_repossed_prop(socket, params) do
  #   user = socket.assigns.current_user
  #   map = Map.put(params, "maker_id", user.id)
  #   Ecto.Multi.new()
  #   |> Ecto.Multi.insert(:new, RepossedProp.changeset(%RepossedProp{}, map))
  #   |> Repo.transaction()
  #   |> case do
  #     {:ok, %{new: record}} ->
  #       {:ok, record}
  #     {:error, _, error_value, _} ->
  #       {:error, error_value}
  #   end
  # end
  def create_repossed_prop(attrs \\ %{}) do
    %RepossedProp{}
    |> RepossedProp.changeset(attrs)
    |> Repo.insert()
  end

  # ------------------query peter------------------>
  # def query_schedule_9A() do
  #   RepossedProp
  #   |> Repo.all()
  # end

  @doc """
  Returns the list of tbl_repossessed_properties.

  ## Examples

      iex> list_tbl_repossessed_properties()
      [%RepossedProp{}, ...]

  """
  def list_tbl_repossessed_properties do
    Repo.all(RepossedProp)
  end

  def list_tbl_repossessed_properties(params) do
    RepossedProp
    |> preload([:maker, :checker])
    # |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  # MisReports.Utilities.repossessed("2023-09-30", "2023-09-30")
  def repossessed(from, to) do
    RepossedProp
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.report_date, ^to) and a.status == "A"
    )
    # |> where([a], a.date_rep == ^date)
    |> Repo.all()
  end

  @doc """
  Gets a single RepossedProp.

  Raises `Ecto.NoResultsError` if the Repossed prop does not exist.

  ## Examples

      iex> get_RepossedProp!(123)
      %RepossedProp{}

      iex> get_RepossedProp!(456)
      ** (Ecto.NoResultsError)

  """
  def get_repossed_prop!(id), do: Repo.get!(RepossedProp, id)

  def get_repossed_prop_by_reference!(reference) do
    Repo.get_by!(RepossedProp, reference: reference)
  end

  @doc """
  Creates a RepossedProp.

  ## Examples

      iex> create_RepossedProp(%{field: value})
      {:ok, %RepossedProp{}}

      iex> create_RepossedProp(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_RepossedProp(attrs \\ %{}) do
    %RepossedProp{}
    |> RepossedProp.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a RepossedProp.

  ## Examples

      iex> update_RepossedProp(RepossedProp, %{field: new_value})
      {:ok, %RepossedProp{}}

      iex> update_RepossedProp(RepossedProp, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_RepossedProp(%RepossedProp{} = RepossedProp, attrs) do
    RepossedProp
    |> RepossedProp.changeset(attrs)
    |> Repo.update()
  end

  def update_repossed_prop(%RepossedProp{} = repossed_prop, attrs) do
    repossed_prop
    |> RepossedProp.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a RepossedProp.

  ## Examples

      iex> delete_RepossedProp(RepossedProp)
      {:ok, %RepossedProp{}}

      iex> delete_RepossedProp(RepossedProp)
      {:error, %Ecto.Changeset{}}

  """
  def delete_RepossedProp(%RepossedProp{} = RepossedProp) do
    Repo.delete(RepossedProp)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking RepossedProp changes.

  ## Examples

      iex> change_RepossedProp(RepossedProp)
      %Ecto.Changeset{data: %RepossedProp{}}

  """

  # def change_RepossedProp(%RepossedProp{} = RepossedProp, attrs \\ %{}) do
  #   RepossedProp.changeset(RepossedProp, attrs)
  # end

  def change_RepossedProp(%RepossedProp{} = repossed_prop) do
    RepossedProp.changeset(repossed_prop, %{})
  end

  # def change_branch(%Branch{} = branch) do
  #   Branch.changeset(branch, %{})
  # end
  # def change_off_blc_sheet(%OffBlcSheet{} = off_blc_sheet, attrs \\ %{}) do
  #   OffBlcSheet.changeset(off_blc_sheet, attrs)
  # end

  alias MisReports.Utilities.Tax

  def create_tax(socket, params) do
    user = socket.assigns.current_user
    map = Map.put(params, "maker_id", user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:new, Tax.changeset(%Tax{}, map))
    |> Repo.transaction()
    |> case do
      {:ok, %{new: record}} ->
        {:ok, record}

      {:error, _, error_value, _} ->
        {:error, error_value}
    end
  end

  # def list_taxes(params) do
  #   Tax
  #   |> preload([:checker, :maker])
  #   |> isearch_tax_filter(params.isearch)
  #   |> order_by(^[params.sort_by])
  #   |> Repo.paginate(page: params.page, page_size: params.page_size)
  # end

  # defp isearch_tax_filter(query, nil), do: query

  # defp isearch_tax_filter(query, isearch) do
  #   search_term = sanitize_term(isearch)

  #   query
  #   |> where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  #   |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
  #   |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  # end

  def list_taxes do
    Tax
    |> preload([:checker, :maker, :role])
    |> Repo.all()
  end

  def list_taxes(params) do
    Tax
    |> where([a], a.status != ^"DELETED")
    |> preload([:checker, :maker])
    |> taxisearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp taxisearch_filter(query, nil), do: query

  defp taxisearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  @doc """
  Returns the list of tbl_tax.

  ## Examples

      iex> list_tbl_tax()
      [%Tax{}, ...]

  """
  def list_tbl_tax do
    Repo.all(Tax)
  end

  @doc """
  Gets a single tax.

  Raises `Ecto.NoResultsError` if the Tax does not exist.

  ## Examples

      iex> get_tax!(123)
      %Tax{}

      iex> get_tax!(456)
      ** (Ecto.NoResultsError)

  """
  def get_tax!(id), do: Repo.get!(Tax, id)

  def get_tax_by_reference!(reference), do: Repo.get_by!(Tax, reference: reference)

  # MisReports.Utilities.tax("2023-09-30", "2023-09-30")
  # def get_tax_details(from, to) do
  #   Tax
  #   |> where([a], fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.report_date, ^to))
  #   # |> where([a], a.date_rep == ^date)
  #   |> Repo.all()
  # end

  def get_tax_details(date) do
    Tax
    |> where([a], a.report_date == ^date and a.status == "A")
    |> select([a], %{
      "C16" => a.income_tax,
      "C17" => a.tax_effect_non,
      "C20" => a.tax_payments,
      "C25" => fragment("COALESCE(?, 0) + COALESCE(?, 0)", a.income_tax, a.tax_effect_non),
      "C26" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.prop_mvt_month,
          a.ifrs_mvt_month,
          a.fair_mvt_month,
          a.deffered_amunt1,
          a.deffered_amunt3,
          a.deffered_amunt2,
          a.deffered_amunt4
        ),
      "D32" => a.prop_opening_asset,
      "D33" => a.prop_mvt_month,
      "D34" => a.prop_mvt_equity,
      # Sum of D32 and D33
      "D35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.prop_opening_asset,
          a.prop_mvt_month,
          a.prop_mvt_equity
        ),
      "E32" => a.ifrs_opening_assets,
      "E33" => a.ifrs_mvt_month,
      "E34" => a.ifrs_mvt_equity,
      # Sum of E32 and E33
      "E35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.ifrs_opening_assets,
          a.ifrs_mvt_month,
          a.ifrs_mvt_equity
        ),
      "F32" => a.fair_opening_assets,
      "F33" => a.fair_mvt_month,
      "F34" => a.fair_mvt_equity,
      # Sum of F32 and F34
      "F35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.fair_opening_assets,
          a.fair_mvt_month,
          a.fair_mvt_equity
        ),
      "H32" => a.oth_opening_asset,
      "H33" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.deffered_amunt1,
          a.deffered_amunt3,
          a.deffered_amunt2,
          a.deffered_amunt4
        ),
      "H34" => a.oth_mvt_equity,
      "H35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.oth_opening_asset,
          a.oth_mvt_equity,
          a.deffered_amunt1,
          a.deffered_amunt3,
          a.deffered_amunt2,
          a.deffered_amunt4
        ),
      "I33" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          # TOTAL C33:H33
          a.deffered_amunt1,
          a.deffered_amunt3,
          a.deffered_amunt2,
          a.deffered_amunt4,
          a.prop_mvt_month,
          a.carry_mvt_month,
          a.ifrs_mvt_month
        ),
      "I34" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.prop_mvt_equity,
          # TOTAL C34:H34
          a.ifrs_mvt_equity,
          a.fair_mvt_equity,
          a.oth_mvt_equity,
          a.carry_mvt_equity
        ),
      "G32" => a.unrealised_opening_asset,
      "G33" => a.unrealised_mvt_month,
      "G34" => a.unrealised_mvt_equity,
      # Sum of G32 and G34
      "G35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.unrealised_opening_asset,
          a.unrealised_mvt_month,
          a.unrealised_mvt_equity
        ),
      "C32" => a.carry_fwd_opening,
      "C33" => a.carry_mvt_month,
      "C34" => a.carry_mvt_equity,
      # Sum of C32 and C34
      "C35" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.carry_fwd_opening,
          a.carry_mvt_month,
          a.carry_mvt_equity
        ),
      "F41" => a.deffered_descr1,
      "F42" => a.deffered_descr3,
      "F43" => a.deffered_descr4,
      "F44" => a.deffered_descr2,
      "H40" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.deffered_amunt1,
          a.deffered_amunt3,
          a.deffered_amunt2,
          a.deffered_amunt4
        ),
      "H41" => a.deffered_amunt1,
      "H42" => a.deffered_amunt3,
      "H43" => a.deffered_amunt2,
      "H44" => a.deffered_amunt4,
      "B41" => a.b41,
      "C41" => coalesce(a.c41, 0),
      "B42" => a.b42,
      "C42" => coalesce(a.c42, 0),
      "B43" => a.b43,
      "C43" => coalesce(a.c43, 0),
      "B44" => a.b44,
      "C44" => coalesce(a.c44, 0),
      "B45" => a.b45,
      "C45" => coalesce(a.c45, 0),
      "C40" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.c41,
          a.c42,
          a.c43,
          a.c44,
          a.c45
        ),
      "C19" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.c41,
          a.c42,
          a.c43,
          a.c44,
          a.c45
        )

      # "C27" => fragment("COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) +
      #                    COALESCE(?, 0) + COALESCE(?, 0)",
      #          a.prop_mvt_month, a.ifrs_mvt_month, a.fair_mvt_month, a.deffered_amunt1, a.deffered_amunt3,
      #          a.deffered_amunt2, a.deffered_amunt4, a.income_tax, a.tax_effect_non)
    })
    |> Repo.all()
  end

  @doc """
  Creates a tax.

  ## Examples

      iex> create_tax(%{field: value})
      {:ok, %Tax{}}

      iex> create_tax(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_tax(attrs \\ %{}) do
    %Tax{}
    |> Tax.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a tax.

  ## Examples

      iex> update_tax(tax, %{field: new_value})
      {:ok, %Tax{}}

      iex> update_tax(tax, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_tax(%Tax{} = tax, attrs) do
    tax
    |> Tax.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a tax.

  ## Examples

      iex> delete_tax(tax)
      {:ok, %Tax{}}

      iex> delete_tax(tax)
      {:error, %Ecto.Changeset{}}

  """
  def delete_tax(%Tax{} = tax) do
    Repo.delete(tax)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking tax changes.

  ## Examples

      iex> change_tax(tax)
      %Ecto.Changeset{data: %Tax{}}

  """
  def change_tax(%Tax{} = tax, attrs \\ %{}) do
    Tax.changeset(tax, attrs)
  end

  alias MisReports.Utilities.Securities

  def get_securities!(id), do: Repo.get!(Securities, id)

  def get_securities_by_reference!(reference), do: Repo.get_by!(Securities, reference: reference)

  def update_securities(%Securities{} = securities, attrs) do
    securities
    |> Securities.changeset(attrs)
    |> Repo.update()
  end

  def list_securities_listing(params) do
    Securities
    |> preload([:checker, :maker])
    |> isearch_securities_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_securities_filter(query, nil), do: query

  defp isearch_securities_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.currency_code, ^search_term))

    # |> or_where([a], fragment("lower(?) like lower(?)", a.currency_description, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
  end

  # def get_securities_by_date_and_code(start_date, ccy_code) do
  #   Securities
  #   |> where([a], fragment("CONVERT(DATE, ?) = ?", a.month, ^start_date) and a.currency_code ==^ccy_code and a.status ==^"A")
  #   |> limit(1)
  #   |> select([a], %{
  #     currency_code: a.currency_code,
  #     currency_description: a.currency_description,
  #     amount: a.amount,
  #     exchange_rate_lcy: a.exchange_rate_lcy,
  #     month: a.month,
  #     status: a.status
  #   })
  #   |> Repo.one()
  # end

  # def change_securities(%SecuritiesRates{} = securities) do
  #   SecuritiesRates.changeset(securities, %{})
  # end
  @doc """
  Returns the list of tblm_security.

  ## Examples

      iex> list_tblm_security()
      [%Securities{}, ...]

  """
  def list_tblm_security do
    Repo.all(Securities)
  end

  @doc """
  Gets a single securities.

  Raises `Ecto.NoResultsError` if the Securities does not exist.

  ## Examples

      iex> get_securities!(123)
      %Securities{}

      iex> get_securities!(456)
      ** (Ecto.NoResultsError)

  """

  # def get_securities!(id), do: Repo.get!(Securities, id)

  @doc """
  Creates a securities.

  ## Examples

      iex> create_securities(%{field: value})
      {:ok, %Securities{}}

      iex> create_securities(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_securities(attrs \\ %{}) do
    %Securities{}
    |> Securities.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a securities.

  ## Examples

      iex> update_securities(securities, %{field: new_value})
      {:ok, %Securities{}}

      iex> update_securities(securities, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_securities(%Securities{} = securities, attrs) do
    securities
    |> Securities.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a securities.

  ## Examples

      iex> delete_securities(securities)
      {:ok, %Securities{}}

      iex> delete_securities(securities)
      {:error, %Ecto.Changeset{}}

  """
  def delete_securities(%Securities{} = securities) do
    Repo.delete(securities)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking securities changes.

  ## Examples

      iex> change_securities(securities)
      %Ecto.Changeset{data: %Securities{}}

  """

  def list_securities(params) do
    Securities
    |> preload([:checker, :maker])
    |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  def get_security_entry(from, to) do
    Securities
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.report_date, ^to) and a.status == "A"
    )
    |> select([a], %{
      currency: a.currency,
      counter_name: a.counter_name,
      expiry_date: a.expiry_date,
      pledge_date: a.pledge_date,
      purpose_pledge: a.purpose_pledge,
      security_type: a.security_type,
      report_date: a.report_date,
      currency_code: a.currency_code,
      cost_security: a.cost_security,
      security_pledge: a.security_pledge,
      total_secured: a.total_secured
    })
    |> Repo.all()
  end

  def change_securities(%Securities{} = securities, attrs \\ %{}) do
    Securities.changeset(securities, attrs)
  end

  alias MisReports.Utilities.Allowances

  @doc """
  Returns the list of tblm_allowances.

  ## Examples

      iex> list_tblm_allowances()
      [%Allowances{}, ...]

  """
  def list_tblm_allowances do
    Repo.all(Allowances)
  end

  def get_allowances!(id), do: Repo.get!(Allowances, id)

  def get_allowances_by_reference!(reference), do: Repo.get_by!(Allowances, reference: reference)

  def list_allowances_listing(params) do
    Allowances
    |> preload([:checker, :maker])
    |> isearch_allowances_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  def allowances(from, to) do
    Allowances
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.report_date, ^to)
    )
    |> select([a], %{
      comment: a.comment,
      currency: a.currency,
      decription: a.decription,
      type_adjust: a.type_adjust,
      amount: a.amount,
      currency_code: a.currency_code,
      report_date: a.report_date,
      specific_bal_allowaance: a.specific_bal_allowaance,
      specific_add_provision: a.specific_add_provision,
      specific_exchange_difference: a.specific_exchange_difference,
      general_add_provision: a.general_add_provision,
      general_exchange_differences: a.general_exchange_differences,
      general_any_adjust: a.general_any_adjust
    })
    |> Repo.all()
  end

  defp isearch_allowances_filter(query, nil), do: query

  defp isearch_allowances_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  def changes_in_the_allowance_for_loan_losses_account_3a(date) do
    Allowances
    |> where([a], a.report_date == ^date)
    |> select([a], %{
      "A26" => a.recoveries_description,
      "A27" => a.write_offs_description,
      "A28" => a.any_other_adjustment_description,
      "B26" => a.recoveries_currency,
      "B27" => a.write_offs_currency,
      "B28" => a.any_other_adjustment_currency,
      "C26" => "Recoveries",
      "C27" => "Write-offs",
      "C28" => "Any other adjustments",
      "D26" => a.recoveries_amount,
      "D27" => a.write_offs_amount,
      "D28" => a.any_other_adjustment_amount,
      "E26" => a.recoveries_comment,
      "E27" => a.write_offs_comment,
      "E28" => a.any_other_adjustment_comment,
      "B14" => a.specific_bal_allowaance,
      "B15" => a.recoveries_amount,
      "B16" => a.write_offs_amount,
      "B17" => a.specific_add_provision,
      "B18" => a.specific_exchange_difference,
      "B19" => a.any_other_adjustment_amount,
      # TOTAL
      "B20" => "",
      # B15 + B17
      "B21" => "",
      "C14" => a.general_bal_allowance,
      # "C17" => a.general_add_provision,
      "C18" => a.general_exchange_differences,
      "C19" => a.general_any_adjust,
      # TOTAL
      "C20" => a.amount,
      # C15 + C17
      "C21" => "",
      "pre_allowance_for_loan_losses_stg_three" => a.pre_allowance_for_loan_losses_stg_three,
      "curr_allowance_for_loan_losses_stg_three" => a.curr_allowance_for_loan_losses_stg_three
    })
    |> Repo.all()
    |> Enum.map(fn i ->
      Map.merge(
        i,
        %{
          "A26" => if(i["A26"] == nil, do: Decimal.new("0"), else: i["A26"]),
          "A27" => if(i["A27"] == nil, do: Decimal.new("0"), else: i["A27"]),
          "A28" => if(i["A28"] == nil, do: Decimal.new("0"), else: i["A28"]),
          "B26" => if(i["B26"] == nil, do: Decimal.new("0"), else: i["B26"]),
          "B27" => if(i["B27"] == nil, do: Decimal.new("0"), else: i["B27"]),
          "B28" => if(i["B28"] == nil, do: Decimal.new("0"), else: i["B28"]),
          "C26" => "Recoveries",
          "C27" => "Write-offs",
          "C28" => "Any other adjustments",
          "D26" => if(i["D26"] == nil, do: Decimal.new("0"), else: i["D26"]),
          "D27" => if(i["D27"] == nil, do: Decimal.new("0"), else: i["D27"]),
          "D28" => if(i["D28"] == nil, do: Decimal.new("0"), else: i["D28"]),
          "E26" => if(i["E26"] == nil, do: Decimal.new("0"), else: i["E26"]),
          "E27" => if(i["E27"] == nil, do: Decimal.new("0"), else: i["E27"]),
          "E28" => if(i["E28"] == nil, do: Decimal.new("0"), else: i["E28"]),
          "B14" => if(i["B14"] == nil, do: Decimal.new("0"), else: i["B14"]),
          "B15" => if(i["B15"] == nil, do: Decimal.new("0"), else: i["B15"]),
          "B16" => if(i["B16"] == nil, do: Decimal.new("0"), else: i["B16"]),
          "B17" => if(i["B17"] == nil, do: Decimal.new("0"), else: i["B17"]),
          "B18" => if(i["B18"] == nil, do: Decimal.new("0"), else: i["B18"]),
          "B19" => if(i["B19"] == nil, do: Decimal.new("0"), else: i["B19"]),
          "C14" => if(i["C14"] == nil, do: Decimal.new("0"), else: i["C14"]),
          "C18" => if(i["C18"] == nil, do: Decimal.new("0"), else: i["C18"]),
          "C19" => if(i["C19"] == nil, do: Decimal.new("0"), else: i["C19"]),
          "C20" => if(i["C20"] == nil, do: Decimal.new("0"), else: i["C20"]),
          "pre_allowance_for_loan_losses_stg_three" =>
            if(i["pre_allowance_for_loan_losses_stg_three"] == nil,
              do: Decimal.new("0"),
              else: i["pre_allowance_for_loan_losses_stg_three"]
            ),
          "curr_allowance_for_loan_losses_stg_three" =>
            if(i["curr_allowance_for_loan_losses_stg_three"] == nil,
              do: Decimal.new("0"),
              else: i["curr_allowance_for_loan_losses_stg_three"]
            )
        }
      )
    end)
  end

  def gdp_file(date) do
    GdpFile
    |> where([a], a.date == ^date)
    |> select([a], %{
      "cur_month_narration" => a.cur_month_narration,
      "cur_month_stage" => a.cur_month_stage,
      "cur_month_amount" => a.cur_month_amount,
      "pre_month_narration" => a.pre_month_narration,
      "pre_month_stage" => a.pre_month_stage,
      "pre_month_amount" => a.pre_month_amount
    })
    |> Repo.all()
    |> Enum.map(fn i ->
      Map.merge(
        i,
        %{
          "cur_month_amount" =>
            if(i["cur_month_amount"] == nil, do: Decimal.new("0"), else: i["cur_month_amount"]),
          "pre_month_amount" =>
            if(i["pre_month_amount"] == nil, do: Decimal.new("0"), else: i["pre_month_amount"])
        }
      )
    end)
  end

  @doc """
  Gets a single allowances.

  Raises `Ecto.NoResultsError` if the Allowances does not exist.

  ## Examples

      iex> get_allowances!(123)
      %Allowances{}

      iex> get_allowances!(456)
      ** (Ecto.NoResultsError)

  """
  def get_allowances!(id), do: Repo.get!(Allowances, id)

  @doc """
  Creates a allowances.

  ## Examples

      iex> create_allowances(%{field: value})
      {:ok, %Allowances{}}

      iex> create_allowances(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_allowances(attrs \\ %{}) do
    %Allowances{}
    |> Allowances.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a allowances.

  ## Examples

      iex> update_allowances(allowances, %{field: new_value})
      {:ok, %Allowances{}}

      iex> update_allowances(allowances, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_allowances(%Allowances{} = allowances, attrs) do
    allowances
    |> Allowances.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a allowances.

  ## Examples

      iex> delete_allowances(allowances)
      {:ok, %Allowances{}}

      iex> delete_allowances(allowances)
      {:error, %Ecto.Changeset{}}

  """

  def change_allowances(%Allowances{} = allowances, attrs \\ %{}) do
    Allowances.changeset(allowances, attrs)
  end

  def delete_allowances(%Allowances{} = allowances) do
    Repo.delete(allowances)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking allowances changes.

  ## Examples

      iex> change_allowances(allowances)
      %Ecto.Changeset{data: %Allowances{}}

  """

  alias MisReports.Utilities.OffBlcSheet

  def get_off_blc_sheet!(id), do: Repo.get!(OffBlcSheet, id)

  def get_off_blc_sheet_by_reference!(reference),
    do: Repo.get_by!(OffBlcSheet, reference: reference)

  def list_off_blc_sheet_listing(params) do
    OffBlcSheet
    |> isearch_off_blc_sheet_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_off_blc_sheet_filter(query, nil), do: query

  @doc """
  Returns the list of tbl_off_blc_sheet.

  ## Examples

      iex> list_tbl_off_blc_sheet()
      [%OffBlcSheet{}, ...]

  """
  def list_tbl_off_blc_sheet do
    Repo.all(OffBlcSheet)
  end

  @doc """
  Gets a single off_blc_sheet.

  Raises `Ecto.NoResultsError` if the Off blc sheet does not exist.

  ## Examples

      iex> get_off_blc_sheet!(123)
      %OffBlcSheet{}

      iex> get_off_blc_sheet!(456)
      ** (Ecto.NoResultsError)

  """
  def get_off_blc_sheet!(id), do: Repo.get!(OffBlcSheet, id)

  @doc """
  Creates a off_blc_sheet.

  ## Examples

      iex> create_off_blc_sheet(%{field: value})
      {:ok, %OffBlcSheet{}}

      iex> create_off_blc_sheet(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_off_blc_sheet(attrs \\ %{}) do
    %OffBlcSheet{}
    |> OffBlcSheet.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a off_blc_sheet.

  ## Examples

      iex> update_off_blc_sheet(off_blc_sheet, %{field: new_value})
      {:ok, %OffBlcSheet{}}

      iex> update_off_blc_sheet(off_blc_sheet, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_off_blc_sheet(%OffBlcSheet{} = off_blc_sheet, attrs) do
    off_blc_sheet
    |> OffBlcSheet.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a off_blc_sheet.

  ## Examples

      iex> delete_off_blc_sheet(off_blc_sheet)
      {:ok, %OffBlcSheet{}}

      iex> delete_off_blc_sheet(off_blc_sheet)
      {:error, %Ecto.Changeset{}}

  """
  def delete_off_blc_sheet(%OffBlcSheet{} = off_blc_sheet) do
    Repo.delete(off_blc_sheet)
  end

  def list_off_blc_sheet(params) do
    OffBlcSheet
    |> preload([:checker, :maker])
    # |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  def off_blc_sheet(from, to) do
    OffBlcSheet
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.report_date, ^from) and
        fragment("CAST(? AS DATE) <= ?", a.report_date, ^to)
    )
    |> Repo.all()
  end

  def off_blc_sheet_schedule_4d(date) do
    OffBlcSheet
    |> where([a], a.report_dt == ^date)
    |> select([a], %{
      "import_letters_credit" => a.import_letters_credit,
      "performance_bonds" => a.performance_bonds,
      "trade_securities" => a.trade_securities
    })
    |> Repo.all()
    |> Enum.reduce(Decimal.new("0"), fn x, acc ->
      Decimal.add(
        acc,
        Decimal.add(
          x["import_letters_credit"],
          Decimal.add(x["performance_bonds"], x["trade_securities"])
        )
      )
    end)
  end

  def off_blc_sheet_schedule(date) do
    OffBlcSheet
    |> where([a], a.report_dt == ^date)
    |> select([a], %{
      "C68" => a.import_letters_credit,
      "C76" => a.performance_bonds,
      "C74" => a.trade_securities,
      "C78" => a.resale_agreement,
      "C79" => a.contingent_liabilities,
      "B109" =>
        fragment("COALESCE(?, 0) + COALESCE(?, 0)", a.trade_securities, a.performance_bonds),
      "B108" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.trade_securities,
          a.performance_bonds,
          a.import_letters_credit
        ),
      "B107" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0) + COALESCE(?, 0)",
          a.trade_securities,
          a.performance_bonds,
          a.import_letters_credit,
          a.contingent_liabilities
        )
    })
    |> Repo.all()
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking off_blc_sheet changes.

  ## Examples

      iex> change_off_blc_sheet(off_blc_sheet)
      %Ecto.Changeset{data: %OffBlcSheet{}}

  """
  def change_off_blc_sheet(%OffBlcSheet{} = off_blc_sheet, attrs \\ %{}) do
    OffBlcSheet.changeset(off_blc_sheet, attrs)
  end

  alias MisReports.Utilities.ShareHolders

  @doc """
  Returns the list of tbl_shareholders_equity.

  ## Examples

      iex> list_tbl_shareholders_equity()
      [%ShareHolders{}, ...]

  """
  def list_tbl_shareholders_equity do
    Repo.all(ShareHolders)
  end

  def list_share_holders(params) do
    ShareHolders
    |> preload([:checker, :maker])
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  def get_adjusted_values(date) do
    current_entries =
      ShareHolders
      |> where([a], a.report_date == ^date)
      |> select([a], %{
        "C26" => a.c26,
        "C28" => a.c28,
        "C31" => a.c31,
        "C33" => a.c33,
        "C35" => a.c35,
        "G24" => a.g24,
        "G26" => a.g26,
        "G28" => a.g28,
        "G31" => a.g31,
        "G33" => a.g33,
        "G35" => a.g35,
        "D37" => a.d37,
        "D28" => a.d28,
        "E28" => a.e28,
        "F28" => a.f28,
        "G28" => a.g28,
        "C37" => a.c37,
        "E37" => a.e37,
        "F37" => a.f37,
        "G37" => a.g37
      })
      |> Repo.all()
      |> Enum.map(&handle_nil_values(&1))

    previous_date =
      Timex.shift(Timex.parse!(date, "{YYYY}-{0M}-{0D}"), months: -1)
      |> Timex.end_of_month()
      |> Timex.format!("{YYYY}-{0M}-{0D}")

    previous_entries =
      ShareHolders
      |> where([a], a.report_date == ^previous_date)
      |> select([a], %{
        "C26" => a.c26,
        "C28" => a.c28,
        "C31" => a.c31,
        "C33" => a.c33,
        "C35" => a.c35,
        "G24" => a.g24,
        "G26" => a.g26,
        "G28" => a.g28,
        "G31" => a.g31,
        "G33" => a.g33,
        "G35" => a.g35,
        "D37" => a.d37,
        "D28" => a.d28,
        "E28" => a.e28,
        "F28" => a.f28,
        "G28" => a.g28,
        "C37" => a.c37,
        "E37" => a.e37,
        "F37" => a.f37,
        "G37" => a.g37
      })
      |> Repo.all()
      |> Enum.map(&handle_nil_values(&1))

    %{current_entries: current_entries, previous_entries: previous_entries}
  end

  defp handle_nil_values(map) do
    map
    |> Enum.into(%{}, fn {k, v} -> {k, handle_nil(v)} end)
  end

  defp handle_nil(value), do: if(value == nil, do: Decimal.new("0"), else: value)

  @doc """
  Gets a single share_holders.

  Raises `Ecto.NoResultsError` if the Share holders does not exist.

  ## Examples

      iex> get_share_holders!(123)
      %ShareHolders{}

      iex> get_share_holders!(456)
      ** (Ecto.NoResultsError)

  """
  def get_share_holders!(id), do: Repo.get!(ShareHolders, id)

  def get_share_holders_by_reference!(reference) do
    Repo.get_by!(ShareHolders, reference: reference)
  end


  @doc """
  Creates a share_holders.

  ## Examples

      iex> create_share_holders(%{field: value})
      {:ok, %ShareHolders{}}

      iex> create_share_holders(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_share_holders(attrs \\ %{}) do
    %ShareHolders{}
    |> ShareHolders.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a share_holders.

  ## Examples

      iex> update_share_holders(share_holders, %{field: new_value})
      {:ok, %ShareHolders{}}

      iex> update_share_holders(share_holders, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_share_holders(%ShareHolders{} = share_holders, attrs) do
    share_holders
    |> ShareHolders.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a share_holders.

  ## Examples

      iex> delete_share_holders(share_holders)
      {:ok, %ShareHolders{}}

      iex> delete_share_holders(share_holders)
      {:error, %Ecto.Changeset{}}

  """
  def delete_share_holders(%ShareHolders{} = share_holders) do
    Repo.delete(share_holders)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking share_holders changes.

  ## Examples

      iex> change_share_holders(share_holders)
      %Ecto.Changeset{data: %ShareHolders{}}

  """
  def change_share_holders(%ShareHolders{} = share_holders, attrs \\ %{}) do
    ShareHolders.changeset(share_holders, attrs)
  end

  alias MisReports.Utilities.SecureHoldings
  def get_secures_holdings!(id), do: Repo.get!(SecureHoldings, id)

  def list_secure_holdings(params) do
    SecureHoldings
    |> preload([:checker, :maker])
    |> isearch_secures_holdings_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_secures_holdings_filter(query, nil), do: query

  defp isearch_secures_holdings_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.currency_code, ^search_term))

    # |> or_where([a], fragment("lower(?) like lower(?)", a.currency_description, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
  end

  @doc """
  Returns the list of tbl_secure_holdings.

  ## Examples

      iex> list_tbl_secure_holdings()
      [%SecureHoldings{}, ...]

  """
  def list_tbl_secure_holdings do
    Repo.all(SecureHoldings)
  end

  def get_adjustments_date(date) do
    SecureHoldings
    |> where([a], a.report_date == ^date and a.status == "A")
    |> select([a], %{
      "B15" => coalesce(a.cost_treasury_bills, 0),
      "B16" => coalesce(a.cost_govern_bonds, 0),
      "B19" =>
        fragment("COALESCE(?, 0) + COALESCE(?, 0)", a.cost_treasury_bills, a.cost_govern_bonds),
      "C15" => coalesce(a.amortised_treasury_bills, 0),
      "C16" => coalesce(a.amortised_govern_bonds, 0),
      "C19" =>
        fragment(
          "COALESCE(?, 0) + COALESCE(?, 0)",
          a.amortised_treasury_bills,
          a.amortised_govern_bonds
        ),
      "D15" => coalesce(a.fair_treasury_bills, 0),
      "D16" => coalesce(a.fair_govern_bonds, 0),
      "D19" =>
        fragment("COALESCE(?, 0) + COALESCE(?, 0)", a.fair_treasury_bills, a.fair_govern_bonds),
      "B28" => coalesce(a.cost_bonds_fcy, 0),
      "B30" => coalesce(a.cost_bonds_fcy, 0),
      "E28" => coalesce(a.fair_value_bonds, 0),
      "E30" => coalesce(a.fair_value_bonds, 0),
      "B17" => coalesce(a.b17, 0),
      "B18" => coalesce(a.b18, 0),
      "C17" => coalesce(a.c17, 0),
      "C18" => coalesce(a.c18, 0),
      "D17" => coalesce(a.d17, 0),
      "D18" => coalesce(a.d18, 0),
      "E17" => coalesce(a.e17, 0),
      "E18" => coalesce(a.e18, 0),
      "F17" => coalesce(a.f17, 0),
      "F18" => coalesce(a.f18, 0)
    })
    |> Repo.all()
  end

  @doc """
  Gets a single secure_holdings.

  Raises `Ecto.NoResultsError` if the Secure holdings does not exist.

  ## Examples

      iex> get_secure_holdings!(123)
      %SecureHoldings{}

      iex> get_secure_holdings!(456)
      ** (Ecto.NoResultsError)

  """
  def get_secure_holdings!(id), do: Repo.get!(SecureHoldings, id)

  @doc """
  Creates a secure_holdings.

  ## Examples

      iex> create_secure_holdings(%{field: value})
      {:ok, %SecureHoldings{}}

      iex> create_secure_holdings(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_secure_holdings(attrs \\ %{}) do
    %SecureHoldings{}
    |> SecureHoldings.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a secure_holdings.

  ## Examples

      iex> update_secure_holdings(secure_holdings, %{field: new_value})
      {:ok, %SecureHoldings{}}

      iex> update_secure_holdings(secure_holdings, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_secure_holdings(%SecureHoldings{} = secure_holdings, attrs) do
    secure_holdings
    |> SecureHoldings.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a secure_holdings.

  ## Examples

      iex> delete_secure_holdings(secure_holdings)
      {:ok, %SecureHoldings{}}

      iex> delete_secure_holdings(secure_holdings)
      {:error, %Ecto.Changeset{}}

  """
  def delete_secure_holdings(%SecureHoldings{} = secure_holdings) do
    Repo.delete(secure_holdings)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking secure_holdings changes.

  ## Examples

      iex> change_secure_holdings(secure_holdings)
      %Ecto.Changeset{data: %SecureHoldings{}}

  """
  def change_secure_holdings(%SecureHoldings{} = secure_holdings, attrs \\ %{}) do
    SecureHoldings.changeset(secure_holdings, attrs)
  end

  alias MisReports.Utilities.BalanceDueDomestic

  def change_balance_due_domestic(%BalanceDueDomestic{} = balance_due_domestic, attrs \\ %{}) do
    BalanceDueDomestic.changeset(balance_due_domestic, attrs)
  end

  alias MisReports.Utilities.BalanceDueDomestic
  def get_balance_due_domestic!(id), do: Repo.get!(BalanceDueDomestic, id)

  def list_balance_due_domestic(params) do
    BalanceDueDomestic
    |> preload([:checker, :maker])
    |> isearch_balance_due_domestic_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_balance_due_domestic_filter(query, nil), do: query

  defp isearch_balance_due_domestic_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.f16, ^search_term))

    # |> or_where([a], fragment("lower(?) like lower(?)", a.currency_description, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
  end

  @doc """
  Returns the list of tbl_balance_due_domestic.

  ## Examples

      iex> list_tbl_balance_due_domestic()
      [%BalanceDueDomestic{}, ...]

  """
  def list_tbl_balance_due_domestic do
    Repo.all(BalanceDueDomestic)
  end

  def get_manual_values(date) do
    BalanceDueDomestic
    |> where([a], a.report_dt == ^date and a.status == "D")
    |> select([a], %{
      account_name: a.d16,
      cur_code: a.f16,
      actual_credit: coalesce(a.g16, 0),
      interest_rate: a.j16,
      deal_date: a.k16,
      maturity_date: a.l16
    })
    |> Repo.all()
  end

  @doc """
  Gets a single balance_due_domestic.

  Raises `Ecto.NoResultsError` if the Balance due domestic does not exist.

  ## Examples

      iex> get_balance_due_domestic!(123)
      %BalanceDueDomestic{}

      iex> get_balance_due_domestic!(456)
      ** (Ecto.NoResultsError)

  """
  def get_balance_due_domestic!(id), do: Repo.get!(BalanceDueDomestic, id)

  def get_balance_due_domestic_by_reference!(reference) do
    Repo.get_by!(BalanceDueDomestic, reference: reference)
  end



  @doc """
  Creates a balance_due_domestic.

  ## Examples

      iex> create_balance_due_domestic(%{field: value})
      {:ok, %BalanceDueDomestic{}}

      iex> create_balance_due_domestic(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_balance_due_domestic(attrs \\ %{}) do
    %BalanceDueDomestic{}
    |> BalanceDueDomestic.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a balance_due_domestic.

  ## Examples

      iex> update_balance_due_domestic(balance_due_domestic, %{field: new_value})
      {:ok, %BalanceDueDomestic{}}

      iex> update_balance_due_domestic(balance_due_domestic, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_balance_due_domestic(%BalanceDueDomestic{} = balance_due_domestic, attrs) do
    balance_due_domestic
    |> BalanceDueDomestic.changeset(attrs)
    |> Repo.update()
  end



  @doc """
  Deletes a balance_due_domestic.

  ## Examples

      iex> delete_balance_due_domestic(balance_due_domestic)
      {:ok, %BalanceDueDomestic{}}

      iex> delete_balance_due_domestic(balance_due_domestic)
      {:error, %Ecto.Changeset{}}

  """
  def delete_balance_due_domestic(%BalanceDueDomestic{} = balance_due_domestic) do
    Repo.delete(balance_due_domestic)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking balance_due_domestic changes.

  ## Examples

      iex> change_balance_due_domestic(balance_due_domestic)
      %Ecto.Changeset{data: %BalanceDueDomestic{}}

  """

  alias MisReports.Utilities.Adjustments

  @doc """
  Returns the list of tbl_adjustments.

  ## Examples

      iex> list_tbl_adjustments()
      [%Adjustments{}, ...]

  """
  def list_tbl_adjustments do
    Repo.all(Adjustments)
  end

  def get_adjustments_reference!(reference) when is_nil(reference), do: []

  def get_adjustments_reference!(reference) do
    # Existing implementation
    from(a in Adjustments,
      where: a.reference == ^reference,
      order_by: [desc: a.id]
    )
    |> Repo.all()
  end

  # def check_status do
  #   Repo.one(from a in Adjustments, where: a.status == "INITIAL_RECORD", limit: 1)
  # end

  def check_status(reference) do
    from(a in Adjustments,
      where: a.status == "INITIAL_RECORD" and ^reference == a.reference,
      select: %{
        id: a.id,
        status: a.status,
        adjustment_lines: a.adjustment_lines,
        report_date: a.report_date
      },
      limit: 1
    )
    |> Repo.one()
  end

  def get_initial_record_details(reference) do
    from(a in Adjustments,
      where: a.status == "INITIAL_RECORD" and ^reference == a.reference,
      select: %{
        maker_id: a.maker_id,
        inserted_at: a.inserted_at,
        updated_at: a.updated_at
      },
      limit: 1
    )
    |> Repo.one()
  end

  @doc """
  Gets a single adjustments.

  Raises `Ecto.NoResultsError` if the Adjustments does not exist.

  ## Examples

      iex> get_adjustments!(123)
      %Adjustments{}

      iex> get_adjustments!(456)
      ** (Ecto.NoResultsError)

  """
  def get_adjustments!(id), do: Repo.get!(Adjustments, id)

  def get_adjustment_by_reference!(reference), do: Repo.get_by!(Adjustments, reference: reference)

  @doc """
  Creates a adjustments.

  ## Examples

      iex> create_adjustments(%{field: value})
      {:ok, %Adjustments{}}

      iex> create_adjustments(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_adjustments(attrs \\ %{}) do
    %Adjustments{}
    |> Adjustments.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a adjustments.

  ## Examples

      iex> update_adjustments(adjustments, %{field: new_value})
      {:ok, %Adjustments{}}

      iex> update_adjustments(adjustments, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_adjustments(%Adjustments{} = adjustments, attrs) do
    adjustments
    |> Adjustments.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a adjustments.

  ## Examples

      iex> delete_adjustments(adjustments)
      {:ok, %Adjustments{}}

      iex> delete_adjustments(adjustments)
      {:error, %Ecto.Changeset{}}

  """
  def delete_adjustments(%Adjustments{} = adjustments) do
    Repo.delete(adjustments)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking adjustments changes.

  ## Examples

      iex> change_adjustments(adjustments)
      %Ecto.Changeset{data: %Adjustments{}}

  """
  def change_adjustments(%Adjustments{} = adjustments, attrs \\ %{}) do
    Adjustments.changeset(adjustments, attrs)
  end

  # Add a fallback clause for nil
  def change_adjustments(nil, _attrs) do
    change_adjustments(%Adjustments{})
  end

  def list_adjustments(params, type) do
    Adjustments
    |> preload([:checker, :maker])
    |> adjustments_isearch_filter(params.isearch)
    |> where([a], a.status != ^type)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp adjustments_isearch_filter(query, nil), do: query

  defp adjustments_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def get_adjustments(start_date) do
    Adjustments
    |> where(
      [a],
      fragment("CONVERT(DATE, ?) = ?", a.report_date, ^start_date) and a.status == ^"A"
    )
    |> Repo.all()
  end

  def get_prev_adjustments(start_date) do
    date = Date.from_iso8601!(start_date)

    case Timex.month_name(date.month) do
      "January" ->
        %{}

      _ ->
        previous_month_date = Timex.shift(date, months: -1) |> Timex.end_of_month()

        Adjustments
        |> where(
          [a],
          fragment("CONVERT(DATE, ?) = ?", a.report_date, ^previous_month_date) and
            a.status == ^"A"
        )
        # |> limit(1)
        |> Repo.all()
    end
  end

  alias MisReports.Utilities.FileExport

  @doc """
  Returns the list of tbl_file_export.

  ## Examples

      iex> list_tbl_file_export()
      [%FileExport{}, ...]

  """
  def list_tbl_file_export do
    Repo.all(FileExport)
  end

  @doc """
  Gets a single file_export.

  Raises `Ecto.NoResultsError` if the File export does not exist.

  ## Examples

      iex> get_file_export!(123)
      %FileExport{}

      iex> get_file_export!(456)
      ** (Ecto.NoResultsError)

  """
  def get_file_export!(id), do: Repo.get!(FileExport, id)

  @doc """
  Creates a file_export.

  ## Examples

      iex> create_file_export(%{field: value})
      {:ok, %FileExport{}}

      iex> create_file_export(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_file_export(attrs \\ %{}) do
    %FileExport{}
    |> FileExport.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a file_export.

  ## Examples

      iex> update_file_export(file_export, %{field: new_value})
      {:ok, %FileExport{}}

      iex> update_file_export(file_export, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_file_export(%FileExport{} = file_export, attrs) do
    file_export
    |> FileExport.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a file_export.

  ## Examples

      iex> delete_file_export(file_export)
      {:ok, %FileExport{}}

      iex> delete_file_export(file_export)
      {:error, %Ecto.Changeset{}}

  """
  def delete_file_export(%FileExport{} = file_export) do
    Repo.delete(file_export)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking file_export changes.

  ## Examples

      iex> change_file_export(file_export)
      %Ecto.Changeset{data: %FileExport{}}

  """
  def change_file_export(%FileExport{} = file_export, attrs \\ %{}) do
    FileExport.changeset(file_export, attrs)
  end

  def get_prudential_report_status(status) do
    FileExport
    |> where([a], a.status == ^status)
    |> Repo.all()
  end

  def prudential_report_list(params) do
    FileExport
    |> preload([:checker, :maker])
    |> prudential_report_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp prudential_report_isearch_filter(query, nil), do: query

  defp prudential_report_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  alias MisReports.Utilities.Insertion

  @doc """
  Returns the list of tbl_insertion_adj.

  ## Examples

      iex> list_tbl_insertion_adj()
      [%Insertion{}, ...]

  """
  def list_tbl_insertion_adj do
    Repo.all(Insertion)
  end

  @doc """
  Gets a single insertion.

  Raises `Ecto.NoResultsError` if the Insertion does not exist.

  ## Examples

      iex> get_insertion!(123)
      %Insertion{}

      iex> get_insertion!(456)
      ** (Ecto.NoResultsError)

  """
  def get_insertion!(id), do: Repo.get!(Insertion, id)

  def list_insertions(params) do
    Insertions
    |> preload([:checker, :maker])
    |> isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  @doc """
  Creates a insertion.

  ## Examples

      iex> create_insertion(%{field: value})
      {:ok, %Insertion{}}

      iex> create_insertion(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_insertion(attrs \\ %{}) do
    %Insertion{}
    |> Insertion.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a insertion.

  ## Examples

      iex> update_insertion(insertion, %{field: new_value})
      {:ok, %Insertion{}}

      iex> update_insertion(insertion, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_insertion(%Insertion{} = insertion, attrs) do
    insertion
    |> Insertion.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a insertion.

  ## Examples

      iex> delete_insertion(insertion)
      {:ok, %Insertion{}}

      iex> delete_insertion(insertion)
      {:error, %Ecto.Changeset{}}

  """
  def delete_insertion(%Insertion{} = insertion) do
    Repo.delete(insertion)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking insertion changes.

  ## Examples

      iex> change_insertion(insertion)
      %Ecto.Changeset{data: %Insertion{}}

  """
  def change_insertion(%Insertion{} = insertion, attrs \\ %{}) do
    Insertion.changeset(insertion, attrs)
  end

  alias MisReports.Utilities.ExchangePlacement

  @doc """
  Returns the list of tbl_fcy_exchange_placement_inst.

  ## Examples

      iex> list_tbl_fcy_exchange_placement_inst()
      [%Exchange_Placement{}, ...]

  """
  def list_tbl_fcy_exchange_placement_inst do
    Repo.all(ExchangePlacement)
  end

  def list_tbl_fcy_exchange_placement_inst(params) do
    ExchangePlacement
    |> preload([:checker, :maker])
    |> isearch_fcy_exchange_placement_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_fcy_exchange_placement_filter(query, nil), do: query
  defp isearch_fcy_exchange_placement_filter(query, ""), do: query

  defp isearch_fcy_exchange_placement_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.institution_name, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.rating_agency, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.rating, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  @doc """
  Gets a single exchange__placement.

  Raises `Ecto.NoResultsError` if the Exchange  placement does not exist.

  ## Examples

      iex> get_exchange__placement!(123)
      %Exchange_Placement{}

      iex> get_exchange__placement!(456)
      ** (Ecto.NoResultsError)

  """
  def get_exchange_placement!(id), do: Repo.get!(ExchangePlacement, id)

  def get_exchange_placement_by_reference!(reference) do
    Repo.get_by!(ExchangePlacement, reference: reference)
  end

  @doc """
  Creates a exchange_placement.

  ## Examples

      iex> create_exchange_placement(%{field: value})
      {:ok, %ExchangePlacement{}}

      iex> create_exchange_placement(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_exchange_placement(attrs \\ %{}) do
    %ExchangePlacement{}
    |> ExchangePlacement.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a exchange__placement.

  ## Examples

      iex> update_exchange__placement(exchange__placement, %{field: new_value})
      {:ok, %ExchangePlacement{}}

      iex> update_exchange__placement(exchange__placement, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_exchange_placement(%ExchangePlacement{} = exchange_placement, attrs) do
    exchange_placement
    |> ExchangePlacement.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a exchange__placement.

  ## Examples

      iex> delete_exchange__placement(exchange_placement)
      {:ok, %ExchangePlacement{}}

      iex> delete_exchange__placement(exchange_placement)
      {:error, %Ecto.Changeset{}}

  """
  def delete_exchange_placement(%ExchangePlacement{} = exchange_placement) do
    Repo.delete(exchange_placement)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking exchange_placement changes.

  ## Examples

      iex> change_exchange__placement(exchange_placement)
      %Ecto.Changeset{data: %ExchangePlacement{}}

  """
  def change_exchange_placement(%ExchangePlacement{} = exchange_placement, attrs \\ %{}) do
    ExchangePlacement.changeset(exchange_placement, attrs)
  end

  alias MisReports.Utilities.InstitutionDetails

  @doc """
  Returns the list of tbl_institution_type.

  ## Examples

      iex> list_tbl_institution_type()
      [%InstitutionDetails{}, ...]

  """
  def list_tbl_institution_type do
    Repo.all(InstitutionDetails)
  end

  def list_tbl_institution_type(params) do
    InstitutionDetails
    |> preload([:checker, :maker])
    |> isearch_institution_type_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_institution_type_filter(query, nil), do: query
  defp isearch_institution_type_filter(query, ""), do: query

  defp isearch_institution_type_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.institution_name, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.rating_agency, ^search_term))
    # |> or_where([a], fragment("lower(?) like lower(?)", a.rating, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  @doc """
  Gets a single institution_details.

  Raises `Ecto.NoResultsError` if the Institution  details does not exist.

  ## Examples

      iex> get_institution_details!(123)
      %InstitutionDetails{}

      iex> get_institution_details!(456)
      ** (Ecto.NoResultsError)

  """
  def get_institution_details!(id), do: Repo.get!(InstitutionDetails, id)

  def get_institution_details_by_reference!(reference) do
    Repo.get_by!(InstitutionDetails, reference: reference)
  end

  @doc """
  Creates a institution_details.

  ## Examples

      iex> create_institution_details(%{field: value})
      {:ok, %InstitutionDetails{}}

      iex> create_institution_details(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_institution_details(attrs \\ %{}) do
    %InstitutionDetails{}
    |> InstitutionDetails.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a institution_details.

  ## Examples

      iex> update_institution_details(institution_details, %{field: new_value})
      {:ok, %InstitutionDetails{}}

      iex> update_institution_details(institution_details, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_institution_details(%InstitutionDetails{} = institution_details, attrs) do
    institution_details
    |> InstitutionDetails.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a institution_details.

  ## Examples

      iex> delete_institution_details(institution_details)
      {:ok, %InstitutionDetails{}}

      iex> delete_institution_details(institution_details)
      {:error, %Ecto.Changeset{}}

  """
  def delete_institution_details(%InstitutionDetails{} = institution_details) do
    Repo.delete(institution_details)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking institution_details changes.

  ## Examples

      iex> change_institution_details(institution_details)
      %Ecto.Changeset{data: %InstitutionDetails{}}

  """
  def change_institution_details(%InstitutionDetails{} = institution_details, attrs \\ %{}) do
    InstitutionDetails.changeset(institution_details, attrs)
  end

  alias MisReports.Utilities.TechnologicalInfrastructure

  @doc """
  Returns the list of tbl_technological_infrastructure.

  ## Examples

      iex> list_tbl_technological_infrastructure()
      [%TechnologicalInfrastructure{}, ...]

  """
  def list_tbl_technological_infrastructure do
    Repo.all(TechnologicalInfrastructure)
  end

  def list_tbl_technological_infrastructure(params) do
    TechnologicalInfrastructure
    |> preload([:checker, :maker])
    |> isearch_tech_infrastructure_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_tech_infrastructure_filter(query, nil), do: query
  defp isearch_tech_infrastructure_filter(query, ""), do: query

  defp isearch_tech_infrastructure_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.description, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  defp isearch_weekly_boz_balance_filter(query, nil), do: query
  defp isearch_weekly_boz_balance_filter(query, ""), do: query

  defp isearch_weekly_boz_balance_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.description, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
  end

  def get_technological_infrastructure_by_reference!(reference) do
    Repo.get_by!(TechnologicalInfrastructure, reference: reference)
  end

  def update_technological_infrastructure(%TechnologicalInfrastructure{} = tech_infra, attrs) do
    tech_infra
    |> TechnologicalInfrastructure.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Gets a single technological_infrastructure.

  Raises `Ecto.NoResultsError` if the Technological infrastructure does not exist.

  ## Examples

      iex> get_technological_infrastructure!(123)
      %TechnologicalInfrastructure{}

      iex> get_technological_infrastructure!(456)
      ** (Ecto.NoResultsError)

  """
  def get_technological_infrastructure!(id), do: Repo.get!(TechnologicalInfrastructure, id)

  @doc """
  Creates a technological_infrastructure.

  ## Examples

      iex> create_technological_infrastructure(%{field: value})
      {:ok, %TechnologicalInfrastructure{}}

      iex> create_technological_infrastructure(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_technological_infrastructure(attrs \\ %{}) do
    %TechnologicalInfrastructure{}
    |> TechnologicalInfrastructure.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a technological_infrastructure.

  ## Examples

      iex> update_technological_infrastructure(technological_infrastructure, %{field: new_value})
      {:ok, %TechnologicalInfrastructure{}}

      iex> update_technological_infrastructure(technological_infrastructure, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_technological_infrastructure(
        %TechnologicalInfrastructure{} = technological_infrastructure,
        attrs
      ) do
    technological_infrastructure
    |> TechnologicalInfrastructure.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a technological_infrastructure.

  ## Examples

      iex> delete_technological_infrastructure(technological_infrastructure)
      {:ok, %TechnologicalInfrastructure{}}

      iex> delete_technological_infrastructure(technological_infrastructure)
      {:error, %Ecto.Changeset{}}

  """
  def delete_technological_infrastructure(
        %TechnologicalInfrastructure{} = technological_infrastructure
      ) do
    Repo.delete(technological_infrastructure)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking technological_infrastructure changes.

  ## Examples

      iex> change_technological_infrastructure(technological_infrastructure)
      %Ecto.Changeset{data: %TechnologicalInfrastructure{}}

  """
  def change_technological_infrastructure(
        %TechnologicalInfrastructure{} = technological_infrastructure,
        attrs \\ %{}
      ) do
    TechnologicalInfrastructure.changeset(technological_infrastructure, attrs)
  end

  def get_tech_infrastructure_values(date) do
    from(t in MisReports.Utilities.TechnologicalInfrastructure,
      where: t.report_date == ^date,
      select: %{
        "lusaka_atm" => fragment("JSON_VALUE(?, '$.atm')", t.lusaka),
        "lusaka_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.lusaka),
        "lusaka_ib" => fragment("JSON_VALUE(?, '$.ib')", t.lusaka),
        "lusaka_mb" => fragment("JSON_VALUE(?, '$.mb')", t.lusaka),
        "lusaka_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.lusaka),
        "lusaka_others" => fragment("JSON_VALUE(?, '$.others')", t.lusaka),
        "lusaka_pos" => fragment("JSON_VALUE(?, '$.pos')", t.lusaka),
        "central_atm" => fragment("JSON_VALUE(?, '$.atm')", t.central),
        "central_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.central),
        "central_ib" => fragment("JSON_VALUE(?, '$.ib')", t.central),
        "central_mb" => fragment("JSON_VALUE(?, '$.mb')", t.central),
        "central_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.central),
        "central_others" => fragment("JSON_VALUE(?, '$.others')", t.central),
        "central_pos" => fragment("JSON_VALUE(?, '$.pos')", t.central),
        "copperbelt_atm" => fragment("JSON_VALUE(?, '$.atm')", t.copperbelt),
        "copperbelt_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.copperbelt),
        "copperbelt_ib" => fragment("JSON_VALUE(?, '$.ib')", t.copperbelt),
        "copperbelt_mb" => fragment("JSON_VALUE(?, '$.mb')", t.copperbelt),
        "copperbelt_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.copperbelt),
        "copperbelt_others" => fragment("JSON_VALUE(?, '$.others')", t.copperbelt),
        "copperbelt_pos" => fragment("JSON_VALUE(?, '$.pos')", t.copperbelt),
        "western_atm" => fragment("JSON_VALUE(?, '$.atm')", t.western),
        "western_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.western),
        "western_ib" => fragment("JSON_VALUE(?, '$.ib')", t.western),
        "western_mb" => fragment("JSON_VALUE(?, '$.mb')", t.western),
        "western_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.western),
        "western_others" => fragment("JSON_VALUE(?, '$.others')", t.western),
        "western_pos" => fragment("JSON_VALUE(?, '$.pos')", t.western),
        "luapula_atm" => fragment("JSON_VALUE(?, '$.atm')", t.luapula),
        "luapula_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.luapula),
        "luapula_ib" => fragment("JSON_VALUE(?, '$.ib')", t.luapula),
        "luapula_mb" => fragment("JSON_VALUE(?, '$.mb')", t.luapula),
        "luapula_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.luapula),
        "luapula_others" => fragment("JSON_VALUE(?, '$.others')", t.luapula),
        "luapula_pos" => fragment("JSON_VALUE(?, '$.pos')", t.luapula),
        "northern_atm" => fragment("JSON_VALUE(?, '$.atm')", t.northern),
        "northern_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.northern),
        "northern_ib" => fragment("JSON_VALUE(?, '$.ib')", t.northern),
        "northern_mb" => fragment("JSON_VALUE(?, '$.mb')", t.northern),
        "northern_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.northern),
        "northern_others" => fragment("JSON_VALUE(?, '$.others')", t.northern),
        "northern_pos" => fragment("JSON_VALUE(?, '$.pos')", t.northern),
        "north_western_atm" => fragment("JSON_VALUE(?, '$.atm')", t.north_western),
        "north_western_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.north_western),
        "north_western_ib" => fragment("JSON_VALUE(?, '$.ib')", t.north_western),
        "north_western_mb" => fragment("JSON_VALUE(?, '$.mb')", t.north_western),
        "north_western_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.north_western),
        "north_western_others" => fragment("JSON_VALUE(?, '$.others')", t.north_western),
        "north_western_pos" => fragment("JSON_VALUE(?, '$.pos')", t.north_western),
        "muchinga_atm" => fragment("JSON_VALUE(?, '$.atm')", t.muchinga),
        "muchinga_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.muchinga),
        "muchinga_ib" => fragment("JSON_VALUE(?, '$.ib')", t.muchinga),
        "muchinga_mb" => fragment("JSON_VALUE(?, '$.mb')", t.muchinga),
        "muchinga_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.muchinga),
        "muchinga_others" => fragment("JSON_VALUE(?, '$.others')", t.muchinga),
        "muchinga_pos" => fragment("JSON_VALUE(?, '$.pos')", t.muchinga),
        "eastern_atm" => fragment("JSON_VALUE(?, '$.atm')", t.eastern),
        "eastern_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.eastern),
        "eastern_ib" => fragment("JSON_VALUE(?, '$.ib')", t.eastern),
        "eastern_mb" => fragment("JSON_VALUE(?, '$.mb')", t.eastern),
        "eastern_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.eastern),
        "eastern_others" => fragment("JSON_VALUE(?, '$.others')", t.eastern),
        "eastern_pos" => fragment("JSON_VALUE(?, '$.pos')", t.eastern),
        "southern_atm" => fragment("JSON_VALUE(?, '$.atm')", t.southern),
        "southern_atmco" => fragment("JSON_VALUE(?, '$.atmco')", t.southern),
        "southern_ib" => fragment("JSON_VALUE(?, '$.ib')", t.southern),
        "southern_mb" => fragment("JSON_VALUE(?, '$.mb')", t.southern),
        "southern_mvb" => fragment("JSON_VALUE(?, '$.mvb')", t.southern),
        "southern_others" => fragment("JSON_VALUE(?, '$.others')", t.southern),
        "southern_pos" => fragment("JSON_VALUE(?, '$.pos')", t.southern)
        # Add more fields for other regions as needed
      }
    )
    |> MisReports.Repo.all()
  end

  alias MisReports.Utilities.WeeklyBozBalance

  @doc """
  Returns the list of weekly_boz_balance.

  ## Examples

      iex> list_weekly_boz_balance()
      [%WeeklyBozBalance{}, ...]

  """
  def list_weekly_boz_balance do
    Repo.all(WeeklyBozBalance)
  end

  def list_weekly_boz_balance(params) do
    WeeklyBozBalance
    |> preload([:checker, :maker])
    |> isearch_weekly_boz_balance_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  @doc """
  Gets a single weekly_boz_balance.

  Raises `Ecto.NoResultsError` if the Weekly boz balance does not exist.

  ## Examples

      iex> get_weekly_boz_balance!(123)
      %WeeklyBozBalance{}

      iex> get_weekly_boz_balance!(456)
      ** (Ecto.NoResultsError)

  """
  def get_weekly_boz_balance!(id), do: Repo.get!(WeeklyBozBalance, id)

  def get_weekly_boz_balance_reference!(reference),
    do: Repo.get_by!(WeeklyBozBalance, reference: reference)

  @doc """
  Creates a weekly_boz_balance.

  ## Examples

      iex> create_weekly_boz_balance(%{field: value})
      {:ok, %WeeklyBozBalance{}}

      iex> create_weekly_boz_balance(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_weekly_boz_balance(attrs \\ %{}) do
    %WeeklyBozBalance{}
    |> WeeklyBozBalance.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a weekly_boz_balance.

  ## Examples

      iex> update_weekly_boz_balance(weekly_boz_balance, %{field: new_value})
      {:ok, %WeeklyBozBalance{}}

      iex> update_weekly_boz_balance(weekly_boz_balance, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_weekly_boz_balance(%WeeklyBozBalance{} = weekly_boz_balance, attrs) do
    weekly_boz_balance
    |> WeeklyBozBalance.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a weekly_boz_balance.

  ## Examples

      iex> delete_weekly_boz_balance(weekly_boz_balance)
      {:ok, %WeeklyBozBalance{}}

      iex> delete_weekly_boz_balance(weekly_boz_balance)
      {:error, %Ecto.Changeset{}}

  """
  def delete_weekly_boz_balance(%WeeklyBozBalance{} = weekly_boz_balance) do
    Repo.delete(weekly_boz_balance)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking weekly_boz_balance changes.

  ## Examples

      iex> change_weekly_boz_balance(weekly_boz_balance)
      %Ecto.Changeset{data: %WeeklyBozBalance{}}

  """
  def change_weekly_boz_balance(%WeeklyBozBalance{} = weekly_boz_balance, attrs \\ %{}) do
    WeeklyBozBalance.changeset(weekly_boz_balance, attrs)
  end

  alias MisReports.Utilities.SubmissionDates

  @doc """
  Returns the list of tbl_submission_dates.

  ## Examples

      iex> list_tbl_submission_dates()
      [%SubmissionDates{}, ...]

  """
  def list_tbl_submission_dates do
    Repo.all(SubmissionDates)
  end

  def get_submission_dates do
    SubmissionDates
    # |> where([a], a.submission_date == ^date and a.status == "D")
    |> select([a], %{
      report_frequency: a.report_frequency,
      descript: a.descript,
      submission_date: a.submission_date,
      status: a.status
    })
    |> Repo.all()
  end

  def list_submission_dates(params) do
    SubmissionDates
    |> preload([:checker, :maker])
    |> submission_dates_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp submission_dates_filter(query, nil), do: query
  defp submission_dates_filter(query, ""), do: query

  defp submission_dates_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.descript, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.submission_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_frequency, ^search_term))
  end

  @doc """
  Gets a single submission_dates.

  Raises `Ecto.NoResultsError` if the Submission dates does not exist.

  ## Examples

      iex> get_submission_dates!(123)
      %SubmissionDates{}

      iex> get_submission_dates!(456)
      ** (Ecto.NoResultsError)

  """
  def get_submission_dates!(id), do: Repo.get!(SubmissionDates, id)

  @doc """
  Creates a submission_dates.

  ## Examples

      iex> create_submission_dates(%{field: value})
      {:ok, %SubmissionDates{}}

      iex> create_submission_dates(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_submission_dates(attrs \\ %{}) do
    %SubmissionDates{}
    |> SubmissionDates.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a submission_dates.

  ## Examples

      iex> update_submission_dates(submission_dates, %{field: new_value})
      {:ok, %SubmissionDates{}}

      iex> update_submission_dates(submission_dates, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_submission_dates(%SubmissionDates{} = submission_dates, attrs) do
    submission_dates
    |> SubmissionDates.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a submission_dates.

  ## Examples

      iex> delete_submission_dates(submission_dates)
      {:ok, %SubmissionDates{}}

      iex> delete_submission_dates(submission_dates)
      {:error, %Ecto.Changeset{}}

  """
  def delete_submission_dates(%SubmissionDates{} = submission_dates) do
    Repo.delete(submission_dates)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking submission_dates changes.

  ## Examples

      iex> change_submission_dates(submission_dates)
      %Ecto.Changeset{data: %SubmissionDates{}}

  """
  def change_submission_dates(%SubmissionDates{} = submission_dates, attrs \\ %{}) do
    SubmissionDates.changeset(submission_dates, attrs)
  end

  def get_exchange_rate_for_11d(date) do
    ExchangeRates
    |> where(
      [a],
      fragment("CONVERT(DATE, ?) = ?", a.month, ^date) and a.status == ^"A" and
        a.currency_code == "USD"
    )
    |> limit(1)
    |> select([a], %{
      exchange_rate_lcy: a.exchange_rate_lcy
    })
    |> Repo.one()
  end

  alias MisReports.Utilities.QuarterlyPublicationExport

  @doc """
  Returns the list of tbl_quarterly_publication_export.

  ## Examples

      iex> list_tbl_quarterly_publication_export()
      [%QuarterlyPublicationExport{}, ...]

  """
  def list_tbl_quarterly_publication_export do
    Repo.all(QuarterlyPublicationExport)
  end

  @doc """
  Gets a single quarterly_publication_export.

  Raises `Ecto.NoResultsError` if the Quarterly publication export does not exist.

  ## Examples

      iex> get_quarterly_publication_export!(123)
      %QuarterlyPublicationExport{}

      iex> get_quarterly_publication_export!(456)
      ** (Ecto.NoResultsError)

  """
  def get_quarterly_publication_export!(id), do: Repo.get!(QuarterlyPublicationExport, id)

  def get_quarterly_publication_report_status(status) do
    QuarterlyPublicationExport
    |> where([a], a.status == ^status)
    |> Repo.all()
  end




  @doc """
  Creates a quarterly_publication_export.

  ## Examples

      iex> create_quarterly_publication_export(%{field: value})
      {:ok, %QuarterlyPublicationExport{}}

      iex> create_quarterly_publication_export(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_quarterly_publication_export(attrs \\ %{}) do
    %QuarterlyPublicationExport{}
    |> QuarterlyPublicationExport.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a quarterly_publication_export.

  ## Examples

      iex> update_quarterly_publication_export(quarterly_publication_export, %{field: new_value})
      {:ok, %QuarterlyPublicationExport{}}

      iex> update_quarterly_publication_export(quarterly_publication_export, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_quarterly_publication_export(%QuarterlyPublicationExport{} = quarterly_publication_export, attrs) do
    quarterly_publication_export
    |> QuarterlyPublicationExport.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a quarterly_publication_export.

  ## Examples

      iex> delete_quarterly_publication_export(quarterly_publication_export)
      {:ok, %QuarterlyPublicationExport{}}

      iex> delete_quarterly_publication_export(quarterly_publication_export)
      {:error, %Ecto.Changeset{}}

  """
  def delete_quarterly_publication_export(%QuarterlyPublicationExport{} = quarterly_publication_export) do
    Repo.delete(quarterly_publication_export)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking quarterly_publication_export changes.

  ## Examples

      iex> change_quarterly_publication_export(quarterly_publication_export)
      %Ecto.Changeset{data: %QuarterlyPublicationExport{}}

  """
  def change_quarterly_publication_export(%QuarterlyPublicationExport{} = quarterly_publication_export, attrs \\ %{}) do
    QuarterlyPublicationExport.changeset(quarterly_publication_export, attrs)
  end

  alias MisReports.Utilities.QuarterlyPublicationReport

  @doc """
  Returns the list of tbl_quarterly_publication_report.

  ## Examples

      iex> list_tbl_quarterly_publication_report()
      [%QuarterlyPublicationReport{}, ...]

  """
  def list_tbl_quarterly_publication_report do
    Repo.all(QuarterlyPublicationReport)
  end

  @doc """
  Gets a single quarterly_publication_report.

  Raises `Ecto.NoResultsError` if the Quarterly publication report does not exist.

  ## Examples

      iex> get_quarterly_publication_report!(123)
      %QuarterlyPublicationReport{}

      iex> get_quarterly_publication_report!(456)
      ** (Ecto.NoResultsError)

  """
  def get_quarterly_publication_report!(id), do: Repo.get!(QuarterlyPublicationReport, id)

  def get_quarterly_publication_report(ref) do
    QuarterlyPublicationReport
    |> where([a], a.ref == ^ref)
    |> Repo.one()
  end

  def update_quarterly_publication_report(%QuarterlyPublicationReport{} = quarterly_publication_report, attrs) do
    quarterly_publication_report
    |> QuarterlyPublicationReport.changeset(attrs)
    |> Repo.update()
  end

  def get_publication_schedules(ref) do
    QuarterlyPublicationReport
    |> where([a], a.ref == ^ref)
    |> Repo.one()
  end

  def publication_list(params) do
    QuarterlyPublicationReport
    |> preload([:checker, :maker])
    |> publication_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp publication_isearch_filter(query, nil), do: query

  defp publication_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.year, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  @doc """
  Creates a quarterly_publication_report.

  ## Examples

      iex> create_quarterly_publication_report(%{field: value})
      {:ok, %QuarterlyPublicationReport{}}

      iex> create_quarterly_publication_report(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_quarterly_publication_report(attrs \\ %{}) do
    %QuarterlyPublicationReport{}
    |> QuarterlyPublicationReport.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a quarterly_publication_report.

  ## Examples

      iex> update_quarterly_publication_report(quarterly_publication_report, %{field: new_value})
      {:ok, %QuarterlyPublicationReport{}}

      iex> update_quarterly_publication_report(quarterly_publication_report, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_quarterly_publication_report(%QuarterlyPublicationReport{} = quarterly_publication_report, attrs) do
    quarterly_publication_report
    |> QuarterlyPublicationReport.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a quarterly_publication_report.

  ## Examples

      iex> delete_quarterly_publication_report(quarterly_publication_report)
      {:ok, %QuarterlyPublicationReport{}}

      iex> delete_quarterly_publication_report(quarterly_publication_report)
      {:error, %Ecto.Changeset{}}

  """
  def delete_quarterly_publication_report(%QuarterlyPublicationReport{} = quarterly_publication_report) do
    Repo.delete(quarterly_publication_report)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking quarterly_publication_report changes.

  ## Examples

      iex> change_quarterly_publication_report(quarterly_publication_report)
      %Ecto.Changeset{data: %QuarterlyPublicationReport{}}

  """
  def change_quarterly_publication_report(%QuarterlyPublicationReport{} = quarterly_publication_report, attrs \\ %{}) do
    QuarterlyPublicationReport.changeset(quarterly_publication_report, attrs)
  end



  alias MisReports.Utilities.WeeklyFileExport

  def get_weekly_report_status(status) do
    WeeklyFileExport
    |> where([w], w.status == ^status)
    |> order_by([w], [desc: w.inserted_at])
    |> Repo.all()
  end

  def get_weekly_report_by_reference(reference) do
    WeeklyFileExport
    |> where([w], w.reference == ^reference)
    |> Repo.one()
  end

  def get_weekly_report_data_by_reference(reference) do
    alias MisReports.SourceData.WeeklyReport

    WeeklyReport
    |> where([w], w.reference == ^reference)
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()
  end

  def update_weekly_report_status(report, status) do
    report
    |> WeeklyFileExport.changeset(%{status: status})
    |> Repo.update()
  end

  def get_weekly_schedules(date) do
    WeeklyFileExport
    |> where([w], w.report_date == ^date and w.status == "A")
    |> preload([:maker, :checker])
    |> Repo.all()
  end

  def create_weekly_export(attrs \\ %{}) do
    %WeeklyFileExport{}
    |> WeeklyFileExport.changeset(attrs)
    |> Repo.insert()
  end

  def update_weekly_file_export(%WeeklyFileExport{} = weekly_export, attrs) do
    weekly_export
    |> WeeklyFileExport.changeset(attrs)
    |> Repo.update()
  end

  def get_weekly_export!(id), do: Repo.get!(WeeklyFileExport, id)

  def get_weekly_export_by_reference(reference) do
    WeeklyFileExport
    |> where([w], w.reference == ^reference)
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()
  end

  def get_weekly_export_by_reference_and_type(reference, export_type) do
    WeeklyFileExport
    |> where([w], w.reference == ^reference and w.export_type == ^export_type)
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()
  end

  def get_weekly_exports_by_reference_and_type(reference, export_type) do
    WeeklyFileExport
    |> where([w], w.reference == ^reference and w.export_type == ^export_type)
    |> order_by([w], [desc: w.inserted_at])
    |> Repo.all()
  end

  def get_completed_weekly_export_by_reference(reference) do
    require Logger
    Logger.info("Looking for completed weekly export with reference: #{reference}")

    result = WeeklyFileExport
    |> where([w], w.reference == ^reference and w.status == "EXPORT_COMPLETE" and not is_nil(w.filename))
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()

    case result do
      nil ->
        Logger.info("No completed weekly export found for reference: #{reference}")
        nil
      export ->
        Logger.info("Found completed weekly export for reference: #{reference}, filename: #{export.filename || "nil"}, status: #{export.status}")
        export
    end
  end

  def get_all_completed_weekly_exports_by_reference(reference) do
    require Logger
    Logger.info("Looking for all completed weekly exports with reference: #{reference}")

    result = WeeklyFileExport
    |> where([w], w.reference == ^reference and w.status == "EXPORT_COMPLETE" and not is_nil(w.filename))
    |> order_by([w], [desc: w.inserted_at])
    |> Repo.all()

    case result do
      [] ->
        Logger.info("No completed weekly exports found for reference: #{reference}")
        []
      exports ->
        Logger.info("Found #{length(exports)} completed weekly exports for reference: #{reference}")
        exports
    end
  end

  def get_weekly_export_by_uuid(uuid) do
    WeeklyFileExport
    |> where([w], w.uuid == ^uuid)
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()
  end

  def get_weekly_export_by_reference_and_status(reference, status) do
    WeeklyFileExport
    |> where([w], w.reference == ^reference and w.status == ^status)
    |> Repo.one()
  end

  def get_latest_weekly_export_by_reference(reference) do
    require Logger
    Logger.info("Looking for latest weekly export with reference: #{reference}")

    result = WeeklyFileExport
    |> where([w], w.reference == ^reference)
    |> order_by([w], [desc: w.inserted_at])
    |> limit(1)
    |> Repo.one()

    case result do
      nil ->
        Logger.info("No weekly export found for reference: #{reference}")
        nil
      export ->
        Logger.info("Found weekly export for reference: #{reference}, status: #{export.status}, filename: #{export.filename || "nil"}, export_type: #{export.export_type || "unknown"}")
        export
    end
  end

  def weekly_export_list(params) do
    WeeklyFileExport
    |> preload([:checker, :maker])
    |> weekly_export_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp weekly_export_isearch_filter(query, nil), do: query

  defp weekly_export_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([w], fragment("lower(?) like lower(?)", w.status, ^search_term))
    |> or_where([w], fragment("lower(?) like lower(?)", w.report_date, ^search_term))
    |> or_where([w], fragment("lower(?) like lower(?)", w.process_id, ^search_term))
  end

end
