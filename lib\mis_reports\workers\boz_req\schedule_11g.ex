defmodule MisReports.Workers.BozReq.Schedule11g do

  def perform(item) do

    decoded_item =
      case item.schedule_11g do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
      
    decoded_item = format_map(decoded_item)

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-7CSCH11G7C002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{ "Code" => "1152_00001",
          "Value" => "#{format_number(decoded_item["C17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00002",
          "Value" => "#{format_number(decoded_item["D17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00003",
          "Value" => "#{format_number(decoded_item["E17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00004",
          "Value" => "#{format_number(decoded_item["F17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00005",
          "Value" => "#{format_number(decoded_item["G17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00006",
          "Value" => "#{format_number(decoded_item["H17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00007",
          "Value" => "#{format_number(decoded_item["I17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00008",
          "Value" => "#{format_number(decoded_item["C18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00009",
          "Value" => "#{format_number(decoded_item["D18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00010",
          "Value" => "#{format_number(decoded_item["E18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00011",
          "Value" => "#{format_number(decoded_item["F18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00012",
          "Value" => "#{format_number(decoded_item["G18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00013",
          "Value" => "#{format_number(decoded_item["H18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00014",
          "Value" => "#{format_number(decoded_item["I18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00015",
          "Value" => "#{format_number(decoded_item["C19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00016",
          "Value" => "#{format_number(decoded_item["D19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00017",
          "Value" => "#{format_number(decoded_item["E19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00018",
          "Value" => "#{format_number(decoded_item["F19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00019",
          "Value" => "#{format_number(decoded_item["G19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00020",
          "Value" => "#{format_number(decoded_item["H19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00021",
          "Value" => "#{format_number(decoded_item["I19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00022",
          "Value" => "#{format_number(decoded_item["C20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00023",
          "Value" => "#{format_number(decoded_item["D20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00024",
          "Value" => "#{format_number(decoded_item["E20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00025",
          "Value" => "#{format_number(decoded_item["F20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00026",
          "Value" => "#{format_number(decoded_item["G20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00027",
          "Value" => "#{format_number(decoded_item["H20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00028",
          "Value" => "#{format_number(decoded_item["I20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00029",
          "Value" => "#{format_number(decoded_item["C21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00030",
          "Value" => "#{format_number(decoded_item["D21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00031",
          "Value" => "#{format_number(decoded_item["E21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00032",
          "Value" => "#{format_number(decoded_item["F21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00033",
          "Value" => "#{format_number(decoded_item["G21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00034",
          "Value" => "#{format_number(decoded_item["H21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00035",
          "Value" => "#{format_number(decoded_item["I21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00036",
          "Value" => "#{format_number(decoded_item["C22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00037",
          "Value" => "#{format_number(decoded_item["D22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00038",
          "Value" => "#{format_number(decoded_item["E22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00039",
          "Value" => "#{format_number(decoded_item["F22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
          %{ "Code" => "1152_00040",
          "Value" => "#{format_number(decoded_item["G22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00041",
          "Value" => "#{format_number(decoded_item["H22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00042",
          "Value" => "#{format_number(decoded_item["I22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00043",
          "Value" => "#{format_number(decoded_item["C23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00044",
          "Value" => "#{format_number(decoded_item["D23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00045",
          "Value" => "#{format_number(decoded_item["E23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00046",
          "Value" => "#{format_number(decoded_item["F23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00047",
          "Value" => "#{format_number(decoded_item["G23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00048",
          "Value" => "#{format_number(decoded_item["H23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00049",
          "Value" => "#{format_number(decoded_item["I23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00050",
          "Value" => "#{format_number(decoded_item["C24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00051",
          "Value" => "#{format_number(decoded_item["D24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00052",
          "Value" => "#{format_number(decoded_item["E24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00053",
          "Value" => "#{format_number(decoded_item["F24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00054",
          "Value" => "#{format_number(decoded_item["G24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00055",
          "Value" => "#{format_number(decoded_item["H24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00056",
          "Value" => "#{format_number(decoded_item["I24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00057",
          "Value" => "#{format_number(decoded_item["C25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00058",
          "Value" => "#{format_number(decoded_item["D25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00059",
          "Value" => "#{format_number(decoded_item["E25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00060",
          "Value" => "#{format_number(decoded_item["F25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00061",
          "Value" => "#{format_number(decoded_item["G25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00062",
          "Value" => "#{format_number(decoded_item["H25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00063",
          "Value" => "#{format_number(decoded_item["I25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00064",
          "Value" => "#{format_number(decoded_item["C26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00065",
          "Value" => "#{format_number(decoded_item["D26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00066",
          "Value" => "#{format_number(decoded_item["E26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00067",
          "Value" => "#{format_number(decoded_item["F26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00068",
          "Value" => "#{format_number(decoded_item["G26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00069",
          "Value" => "#{format_number(decoded_item["H26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00070",
          "Value" => "#{format_number(decoded_item["I26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00071",
          "Value" => "#{format_number(decoded_item["C27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00072",
          "Value" => "#{format_number(decoded_item["D27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00073",
          "Value" => "#{format_number(decoded_item["E27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00074",
          "Value" => "#{format_number(decoded_item["F27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00075",
          "Value" => "#{format_number(decoded_item["G27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00076",
          "Value" => "#{format_number(decoded_item["H27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00077",
          "Value" => "#{format_number(decoded_item["I27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00078",
          "Value" => "0",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00079",
          "Value" => "0",
          "_dataType" => "NUMERIC"
          },
        %{ "Code" => "1152_00080",
          "Value" => "0",
          "_dataType" => "NUMERIC"
          },
        %{
          "Code" => "1152_00081",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00082",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00083",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00084",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00085",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00086",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00087",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00088",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00089",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00090",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00091",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00092",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00093",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00094",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00095",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00096",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00097",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00098",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00099",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00100",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00101",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00102",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00103",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00104",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00105",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00106",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00107",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00108",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00109",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00110",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00111",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00112",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00113",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00114",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00115",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00116",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00117",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00118",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00119",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00120",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00121",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00122",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00123",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00124",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00125",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00126",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00127",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00128",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00129",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00130",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00131",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00132",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00133",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00134",
          "Value" => "#{format_number(decoded_item["C27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00135",
          "Value" => "#{format_number(decoded_item["D27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00136",
          "Value" => "#{format_number(decoded_item["E27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00137",
          "Value" => "#{format_number(decoded_item["F27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00138",
          "Value" => "#{format_number(decoded_item["G27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00139",
          "Value" => "#{format_number(decoded_item["H27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00140",
          "Value" => "#{format_number(decoded_item["I27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00141",
          "Value" => "#{format_number(decoded_item["C40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00142",
          "Value" => "#{format_number(decoded_item["D40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00143",
          "Value" => "#{format_number(decoded_item["E40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00144",
          "Value" => "#{format_number(decoded_item["F40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00145",
          "Value" => "#{format_number(decoded_item["G40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00146",
          "Value" => "#{format_number(decoded_item["H40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1152_00147",
          "Value" => "#{format_number(decoded_item["I40"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        }
  ],
      "dynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
