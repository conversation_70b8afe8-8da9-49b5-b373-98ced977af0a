defmodule MisReports.Workers.BozReq.Schedule30b1 do
  @derive {Jason.Encoder, except: [:__struct__]}
  defstruct decoded_item: nil

  def perform(item) do
    decoded_item =
      case item.schedule_30b1 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # # Example usage
    # schedule = MisReports.Workers.BozReq.Schedule30c1.perform(item)

    # # Add range of cells
    # sum = MisReports.Workers.BozReq.Schedule30c1.sum_range(schedule.decoded_item, "A1:A5")

    # # Add two specific cells
    # sum = MisReports.Workers.BozReq.Schedule30c1.add_cells(schedule.decoded_item, "K15", "B62")

    # # Store decoded_item in module state
    schedule = %__MODULE__{decoded_item: decoded_item}

    # regulatory_capital = decoded_item["header"]

    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-1OSCH30B11O002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1183_00001", "Value" => add_cells(decoded_item, "B15", "B62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00002", "Value" => add_cells(decoded_item, "C15", "C62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00003", "Value" => add_cells(decoded_item, "D15", "D62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00004", "Value" => add_cells(decoded_item, "E15", "E62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00005", "Value" => add_cells(decoded_item, "F15", "F62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00006", "Value" => add_cells(decoded_item, "G15", "G62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00007", "Value" => add_cells(decoded_item, "H15", "H62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00008", "Value" => add_cells(decoded_item, "I15", "I62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00009", "Value" => add_cells(decoded_item, "J15", "J62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00010", "Value" => add_ranges(decoded_item, "B15:J15", "B62:J62") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00011", "Value" => add_cells(decoded_item, "B16", "B64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00012", "Value" => add_cells(decoded_item, "C16", "C64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00013", "Value" => add_cells(decoded_item, "D16", "D64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00014", "Value" => add_cells(decoded_item, "E16", "E64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00015", "Value" => add_cells(decoded_item, "F16", "F64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00016", "Value" => add_cells(decoded_item, "G16", "G64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00017", "Value" => add_cells(decoded_item, "H16", "H64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00018", "Value" => add_cells(decoded_item, "I16", "I64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00019", "Value" => add_cells(decoded_item, "J16", "J64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00020", "Value" => add_ranges(decoded_item, "B16:J16", "B64:J64") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00021", "Value" => add_cells(decoded_item, "B17", "B66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00022", "Value" => add_cells(decoded_item, "C17", "C66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00023", "Value" => add_cells(decoded_item, "D17", "D66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00024", "Value" => add_cells(decoded_item, "E17", "E66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00025", "Value" => add_cells(decoded_item, "F17", "F66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00026", "Value" => add_cells(decoded_item, "G17", "G66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00027", "Value" => add_cells(decoded_item, "H17", "H66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00028", "Value" => add_cells(decoded_item, "I17", "I66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00029", "Value" => add_cells(decoded_item, "J17", "J66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00030", "Value" => add_ranges(decoded_item, "B17:J17", "B66:J66") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00031", "Value" => add_cells(decoded_item, "B18", "B68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00032", "Value" => add_cells(decoded_item, "C18", "C68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00033", "Value" => add_cells(decoded_item, "D18", "D68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00034", "Value" => add_cells(decoded_item, "E18", "E68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00035", "Value" => add_cells(decoded_item, "F18", "F68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00036", "Value" => add_cells(decoded_item, "G18", "G68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00037", "Value" => add_cells(decoded_item, "H18", "H68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00038", "Value" => add_cells(decoded_item, "I18", "I68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00039", "Value" => add_cells(decoded_item, "J18", "J68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00040", "Value" => add_ranges(decoded_item, "B18:J18", "B68:J68") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00041", "Value" => add_cells(decoded_item, "B19", "B70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00042", "Value" => add_cells(decoded_item, "C19", "C70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00043", "Value" => add_cells(decoded_item, "D19", "D70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00044", "Value" => add_cells(decoded_item, "E19", "E70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00045", "Value" => add_cells(decoded_item, "F19", "F70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00046", "Value" => add_cells(decoded_item, "G19", "G70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00047", "Value" => add_cells(decoded_item, "H19", "H70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00048", "Value" => add_cells(decoded_item, "I19", "I70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00049", "Value" => add_cells(decoded_item, "J19", "J70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00050", "Value" => add_ranges(decoded_item, "B19:J19", "B70:J70") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00051", "Value" => add_cells(decoded_item, "B20", "B72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00052", "Value" => add_cells(decoded_item, "C20", "C72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00053", "Value" => add_cells(decoded_item, "D20", "D72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00054", "Value" => add_cells(decoded_item, "E20", "E72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00055", "Value" => add_cells(decoded_item, "F20", "F72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00056", "Value" => add_cells(decoded_item, "G20", "G72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00057", "Value" => add_cells(decoded_item, "H20", "H72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00058", "Value" => add_cells(decoded_item, "I20", "I72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00059", "Value" => add_cells(decoded_item, "J20", "J72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00060", "Value" => add_ranges(decoded_item, "B20:J20", "B72:J72") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00061", "Value" => add_cells(decoded_item, "B21", "B74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00062", "Value" => add_cells(decoded_item, "C21", "C74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00063", "Value" => add_cells(decoded_item, "D21", "D74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00064", "Value" => add_cells(decoded_item, "E21", "E74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00065", "Value" => add_cells(decoded_item, "F21", "F74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00066", "Value" => add_cells(decoded_item, "G21", "G74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00067", "Value" => add_cells(decoded_item, "H21", "H74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00068", "Value" => add_cells(decoded_item, "I21", "I74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00069", "Value" => add_cells(decoded_item, "J21", "J74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00070", "Value" => add_ranges(decoded_item, "B21:J21", "B74:J74") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00071", "Value" => add_cells(decoded_item, "B22", "B76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00072", "Value" => add_cells(decoded_item, "C22", "C76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00073", "Value" => add_cells(decoded_item, "D22", "D76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00074", "Value" => add_cells(decoded_item, "E22", "E76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00075", "Value" => add_cells(decoded_item, "F22", "F76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00076", "Value" => add_cells(decoded_item, "G22", "G76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00077", "Value" => add_cells(decoded_item, "H22", "H76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00078", "Value" => add_cells(decoded_item, "I22", "I76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00079", "Value" => add_cells(decoded_item, "J22", "J76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00080", "Value" => add_ranges(decoded_item, "B22:J22", "B76:J76") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00081", "Value" => add_cells(decoded_item, "B23", "B78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00082", "Value" => add_cells(decoded_item, "C23", "C78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00083", "Value" => add_cells(decoded_item, "D23", "D78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00084", "Value" => add_cells(decoded_item, "E23", "E78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00085", "Value" => add_cells(decoded_item, "F23", "F78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00086", "Value" => add_cells(decoded_item, "G23", "G78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00087", "Value" => add_cells(decoded_item, "H23", "H78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00088", "Value" => add_cells(decoded_item, "I23", "I78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00089", "Value" => add_cells(decoded_item, "J23", "J78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00090", "Value" => add_ranges(decoded_item, "B23:J23", "B78:J78") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00091", "Value" => add_cells(decoded_item, "B24", "B80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00092", "Value" => add_cells(decoded_item, "C24", "C80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00093", "Value" => add_cells(decoded_item, "D24", "D80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00094", "Value" => add_cells(decoded_item, "E24", "E80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00095", "Value" => add_cells(decoded_item, "F24", "F80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00096", "Value" => add_cells(decoded_item, "G24", "G80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00097", "Value" => add_cells(decoded_item, "H24", "H80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00098", "Value" => add_cells(decoded_item, "I24", "I80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00099", "Value" => add_cells(decoded_item, "J24", "J80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00100", "Value" => add_ranges(decoded_item, "B24:J24", "B80:J80") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00101", "Value" => add_cells(decoded_item, "B25", "B82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00102", "Value" => add_cells(decoded_item, "C25", "C82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00103", "Value" => add_cells(decoded_item, "D25", "D82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00104", "Value" => add_cells(decoded_item, "E25", "E82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00105", "Value" => add_cells(decoded_item, "F25", "F82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00106", "Value" => add_cells(decoded_item, "G25", "G82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00107", "Value" => add_cells(decoded_item, "H25", "H82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00108", "Value" => add_cells(decoded_item, "I25", "I82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00109", "Value" => add_cells(decoded_item, "J25", "J82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00110", "Value" => add_ranges(decoded_item, "B25:J25", "B82:J82") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00111", "Value" => add_cells(decoded_item, "B26", "B84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00112", "Value" => add_cells(decoded_item, "C26", "C84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00113", "Value" => add_cells(decoded_item, "D26", "D84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00114", "Value" => add_cells(decoded_item, "E26", "E84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00115", "Value" => add_cells(decoded_item, "F26", "F84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00116", "Value" => add_cells(decoded_item, "G26", "G84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00117", "Value" => add_cells(decoded_item, "H26", "H84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00118", "Value" => add_cells(decoded_item, "I26", "I84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00119", "Value" => add_cells(decoded_item, "J26", "J84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00120", "Value" => add_ranges(decoded_item, "B26:J26", "B84:J84") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00121", "Value" => sum_range(decoded_item, "B15:B26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00122", "Value" => sum_range(decoded_item, "C15:C26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00123", "Value" => sum_range(decoded_item, "D15:D26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00124", "Value" => sum_range(decoded_item, "E15:E26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00125", "Value" => sum_range(decoded_item, "F15:F26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00126", "Value" => sum_range(decoded_item, "G15:G26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00127", "Value" => sum_range(decoded_item, "H15:H26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00128", "Value" => sum_range(decoded_item, "I15:I26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00129", "Value" => sum_range(decoded_item, "J15:J26") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00130", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                                      {"B26:J26", "B84:J84"},
                                                                                      {"B25:J25", "B82:J82"},
                                                                                      {"B24:J24", "B80:J80"},
                                                                                      {"B23:J23", "B78:J78"},
                                                                                      {"B22:J22", "B76:J76"},
                                                                                      {"B21:J21", "B74:J74"},
                                                                                      {"B20:J20", "B72:J72"},
                                                                                      {"B19:J19", "B70:J70"},
                                                                                      {"B18:J18", "B68:J68"},
                                                                                      {"B17:J17", "B66:J66"},
                                                                                      {"B16:J16", "B64:J64"},
                                                                                      {"B15:J15", "B62:J62"},
                                                                                    ]) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00131", "Value" => add_cells(decoded_item, "B32:B34", "B91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00132", "Value" => add_cells(decoded_item, "C32:C34", "C91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00133", "Value" => add_cells(decoded_item, "D32:D34", "D91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00134", "Value" => add_cells(decoded_item, "E32:E34", "E91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00135", "Value" => add_cells(decoded_item, "F32:F34", "F91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00136", "Value" => add_cells(decoded_item, "G32:G34", "G91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00137", "Value" => add_cells(decoded_item, "H32:H34", "H91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00138", "Value" => add_cells(decoded_item, "I32:I34", "I91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00139", "Value" => add_cells(decoded_item, "J32:J34", "J91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00140", "Value" => add_range_pairs_across_columns(decoded_item, "B","J", "32:34", "91") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00141", "Value" => add_cells(decoded_item, "B32", "B93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00142", "Value" => add_cells(decoded_item, "C32", "C93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00143", "Value" => add_cells(decoded_item, "D32", "D93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00144", "Value" => add_cells(decoded_item, "E32", "E93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00145", "Value" => add_cells(decoded_item, "F32", "F93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00146", "Value" => add_cells(decoded_item, "G32", "G93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00147", "Value" => add_cells(decoded_item, "H32", "H93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00148", "Value" => add_cells(decoded_item, "I32", "I93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00149", "Value" => add_cells(decoded_item, "J32", "J93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00150", "Value" => add_ranges(decoded_item, "B32:J32", "B93:J93") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00151", "Value" => add_cells(decoded_item, "B33", "B95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00152", "Value" => add_cells(decoded_item, "C33", "C95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00153", "Value" => add_cells(decoded_item, "D33", "D95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00154", "Value" => add_cells(decoded_item, "E33", "E95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00155", "Value" => add_cells(decoded_item, "F33", "F95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00156", "Value" => add_cells(decoded_item, "G33", "G95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00157", "Value" => add_cells(decoded_item, "H33", "H95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00158", "Value" => add_cells(decoded_item, "I33", "I95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00159", "Value" => add_cells(decoded_item, "J33", "J95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00160", "Value" => add_ranges(decoded_item, "B33:J33", "B95:J95") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00161", "Value" => add_cells(decoded_item, "B34", "B97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00162", "Value" => add_cells(decoded_item, "C34", "C97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00163", "Value" => add_cells(decoded_item, "D34", "D97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00164", "Value" => add_cells(decoded_item, "E34", "E97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00165", "Value" => add_cells(decoded_item, "F34", "F97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00166", "Value" => add_cells(decoded_item, "G34", "G97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00167", "Value" => add_cells(decoded_item, "H34", "H97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00168", "Value" => add_cells(decoded_item, "I34", "I97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00169", "Value" => add_cells(decoded_item, "J34", "J97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00170", "Value" => add_ranges(decoded_item, "B34:J34", "B97:J97") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00171", "Value" => add_cells(decoded_item, "B35", "B99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00172", "Value" => add_cells(decoded_item, "C35", "C99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00173", "Value" => add_cells(decoded_item, "D35", "D99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00174", "Value" => add_cells(decoded_item, "E35", "E99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00175", "Value" => add_cells(decoded_item, "F35", "F99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00176", "Value" => add_cells(decoded_item, "G35", "G99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00177", "Value" => add_cells(decoded_item, "H35", "H99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00178", "Value" => add_cells(decoded_item, "I35", "I99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00179", "Value" => add_cells(decoded_item, "J35", "J99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00180", "Value" => add_ranges(decoded_item, "B35:J35", "B99:J99") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00181", "Value" => add_cells(decoded_item, "B36", "B101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00182", "Value" => add_cells(decoded_item, "C36", "C101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00183", "Value" => add_cells(decoded_item, "D36", "D101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00184", "Value" => add_cells(decoded_item, "E36", "E101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00185", "Value" => add_cells(decoded_item, "F36", "F101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00186", "Value" => add_cells(decoded_item, "G36", "G101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00187", "Value" => add_cells(decoded_item, "H36", "H101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00188", "Value" => add_cells(decoded_item, "I36", "I101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00189", "Value" => add_cells(decoded_item, "J36", "J101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00190", "Value" => add_ranges(decoded_item, "B36:J36", "B101:J101") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00191", "Value" => add_cells(decoded_item, "B37", "B103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00192", "Value" => add_cells(decoded_item, "C37", "C103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00193", "Value" => add_cells(decoded_item, "D37", "D103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00194", "Value" => add_cells(decoded_item, "E37", "E103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00195", "Value" => add_cells(decoded_item, "F37", "F103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00196", "Value" => add_cells(decoded_item, "G37", "G103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00197", "Value" => add_cells(decoded_item, "H37", "H103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00198", "Value" => add_cells(decoded_item, "I37", "I103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00199", "Value" => add_cells(decoded_item, "J37", "J103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00200", "Value" => add_ranges(decoded_item, "B37:J37", "B103:J103") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00201", "Value" => add_cells(decoded_item, "B38", "B105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00202", "Value" => add_cells(decoded_item, "C38", "C105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00203", "Value" => add_cells(decoded_item, "D38", "D105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00204", "Value" => add_cells(decoded_item, "E38", "E105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00205", "Value" => add_cells(decoded_item, "F38", "F105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00206", "Value" => add_cells(decoded_item, "G38", "G105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00207", "Value" => add_cells(decoded_item, "H38", "H105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00208", "Value" => add_cells(decoded_item, "I38", "I105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00209", "Value" => add_cells(decoded_item, "J38", "J105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00210", "Value" => add_ranges(decoded_item, "B38:J38", "B105:J105") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00211", "Value" => add_cells(decoded_item, "B39", "B107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00212", "Value" => add_cells(decoded_item, "C39", "C107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00213", "Value" => add_cells(decoded_item, "D39", "D107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00214", "Value" => add_cells(decoded_item, "E39", "E107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00215", "Value" => add_cells(decoded_item, "F39", "F107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00216", "Value" => add_cells(decoded_item, "G39", "G107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00217", "Value" => add_cells(decoded_item, "H39", "H107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00218", "Value" => add_cells(decoded_item, "I39", "I107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00219", "Value" => add_cells(decoded_item, "J39", "J107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00220", "Value" => add_ranges(decoded_item, "B39:J39", "B107:J107") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00221", "Value" => add_cells(decoded_item, "B40", "B109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00222", "Value" => add_cells(decoded_item, "C40", "C109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00223", "Value" => add_cells(decoded_item, "D40", "D109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00224", "Value" => add_cells(decoded_item, "E40", "E109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00225", "Value" => add_cells(decoded_item, "F40", "F109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00226", "Value" => add_cells(decoded_item, "G40", "G109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00227", "Value" => add_cells(decoded_item, "H40", "H109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00228", "Value" => add_cells(decoded_item, "I40", "I109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00229", "Value" => add_cells(decoded_item, "J40", "J109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00230", "Value" => add_ranges(decoded_item, "B40:J40", "B109:J109") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00231", "Value" => add_cells(decoded_item, "B41", "B111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00232", "Value" => add_cells(decoded_item, "C41", "C111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00233", "Value" => add_cells(decoded_item, "D41", "D111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00234", "Value" => add_cells(decoded_item, "E41", "E111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00235", "Value" => add_cells(decoded_item, "F41", "F111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00236", "Value" => add_cells(decoded_item, "G41", "G111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00237", "Value" => add_cells(decoded_item, "H41", "H111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00238", "Value" => add_cells(decoded_item, "I41", "I111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00239", "Value" => add_cells(decoded_item, "J41", "J111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00240", "Value" => add_ranges(decoded_item, "B41:J41", "B111:J111") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00241", "Value" => add_cells(decoded_item, "B42", "B113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00242", "Value" => add_cells(decoded_item, "C42", "C113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00243", "Value" => add_cells(decoded_item, "D42", "D113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00244", "Value" => add_cells(decoded_item, "E42", "E113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00245", "Value" => add_cells(decoded_item, "F42", "F113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00246", "Value" => add_cells(decoded_item, "G42", "G113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00247", "Value" => add_cells(decoded_item, "H42", "H113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00248", "Value" => add_cells(decoded_item, "I42", "I113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00249", "Value" => add_cells(decoded_item, "J42", "J113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00250", "Value" => add_ranges(decoded_item, "B42:J42", "B113:J113") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00251", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"B35", "B99"},
                                                                        {"B36", "B101"},
                                                                        {"B37", "B103"},
                                                                        {"B38", "B105"},
                                                                        {"B39", "B107"},
                                                                        {"B40", "B109"},
                                                                        {"B41", "B111"},
                                                                        {"B42", "B113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "B32:B34", "B91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00252", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"C35", "C99"},
                                                                        {"C36", "C101"},
                                                                        {"C37", "C103"},
                                                                        {"C38", "C105"},
                                                                        {"C39", "C107"},
                                                                        {"C40", "C109"},
                                                                        {"C41", "C111"},
                                                                        {"C42", "C113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "C32:C34", "C91"))
                                                                       |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00253", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"D35", "D99"},
                                                                        {"D36", "D101"},
                                                                        {"D37", "D103"},
                                                                        {"D38", "D105"},
                                                                        {"D39", "D107"},
                                                                        {"D40", "D109"},
                                                                        {"D41", "D111"},
                                                                        {"D42", "D113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "D32:D34", "D91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00254", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"E35", "E99"},
                                                                        {"E36", "E101"},
                                                                        {"E37", "E103"},
                                                                        {"E38", "E105"},
                                                                        {"E39", "E107"},
                                                                        {"E40", "E109"},
                                                                        {"E41", "E111"},
                                                                        {"E42", "E113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "E32:E34", "E91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00255", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"F35", "F99"},
                                                                        {"F36", "F101"},
                                                                        {"F37", "F103"},
                                                                        {"F38", "F105"},
                                                                        {"F39", "F107"},
                                                                        {"F40", "F109"},
                                                                        {"F41", "F111"},
                                                                        {"F42", "F113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "F32:F34", "F91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00256", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"G35", "G99"},
                                                                        {"G36", "G101"},
                                                                        {"G37", "G103"},
                                                                        {"G38", "G105"},
                                                                        {"G39", "G107"},
                                                                        {"G40", "G109"},
                                                                        {"G41", "G111"},
                                                                        {"G42", "G113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "G32:G34", "G91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00257", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"H35", "H99"},
                                                                        {"H36", "H101"},
                                                                        {"H37", "H103"},
                                                                        {"H38", "H105"},
                                                                        {"H39", "H107"},
                                                                        {"H40", "H109"},
                                                                        {"H41", "H111"},
                                                                        {"H42", "H113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "H32:H34", "H91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00258", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"I35", "I99"},
                                                                        {"I36", "I101"},
                                                                        {"I37", "I103"},
                                                                        {"I38", "I105"},
                                                                        {"I39", "I107"},
                                                                        {"I40", "I109"},
                                                                        {"I41", "I111"},
                                                                        {"I42", "I113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "I32:I34", "I91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00259", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"J35", "J99"},
                                                                        {"J36", "J101"},
                                                                        {"J37", "J103"},
                                                                        {"J38", "J105"},
                                                                        {"J39", "J107"},
                                                                        {"J40", "J109"},
                                                                        {"J41", "J111"},
                                                                        {"J42", "J113"}
                                                                      ])
                                                                      |> Decimal.add(add_cells(decoded_item, "J32:J34", "J91"))
                                                                      |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00260", "Value" => add_multiple_range_pairs(decoded_item, [
                                                                        {"K35", "K99"},
                                                                        {"K36", "K101"},
                                                                        {"K37", "K103"},
                                                                        {"K38", "K105"},
                                                                        {"K39", "K107"},
                                                                        {"K40", "K109"},
                                                                        {"K41", "K111"},
                                                                        {"K42", "K113"}
                                                                      ])
                                                                     |> Decimal.add(add_cells(decoded_item, "K32:K34", "K91"))
                                                                     |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00261", "Value" => calculate_differences(decoded_item, "B") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00262", "Value" => calculate_differences(decoded_item, "C") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00263", "Value" => calculate_differences(decoded_item, "D") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00264", "Value" => calculate_differences(decoded_item, "E") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00265", "Value" => calculate_differences(decoded_item, "F") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00266", "Value" => calculate_differences(decoded_item, "G") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00267", "Value" => calculate_differences(decoded_item, "H") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00268", "Value" => calculate_differences(decoded_item, "I") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00269", "Value" => calculate_differences(decoded_item, "J") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00270", "Value" => sum_column_differences(decoded_item, "B", "J") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00271", "Value" => calculate_differences(decoded_item, "B") |> to_string() , "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00272", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00273", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00274", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00275", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E"))
                                             |> Decimal.add(calculate_differences(decoded_item, "F")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00276", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E"))
                                             |> Decimal.add(calculate_differences(decoded_item, "F"))
                                             |> Decimal.add(calculate_differences(decoded_item, "G")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00277", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E"))
                                             |> Decimal.add(calculate_differences(decoded_item, "F"))
                                             |> Decimal.add(calculate_differences(decoded_item, "G"))
                                             |> Decimal.add(calculate_differences(decoded_item, "H")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00278", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E"))
                                             |> Decimal.add(calculate_differences(decoded_item, "F"))
                                             |> Decimal.add(calculate_differences(decoded_item, "G"))
                                             |> Decimal.add(calculate_differences(decoded_item, "H"))
                                             |> Decimal.add(calculate_differences(decoded_item, "I")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00279", "Value" => calculate_differences(decoded_item, "B")
                                             |> Decimal.add(calculate_differences(decoded_item, "C"))
                                             |> Decimal.add(calculate_differences(decoded_item, "D"))
                                             |> Decimal.add(calculate_differences(decoded_item, "E"))
                                             |> Decimal.add(calculate_differences(decoded_item, "F"))
                                             |> Decimal.add(calculate_differences(decoded_item, "G"))
                                             |> Decimal.add(calculate_differences(decoded_item, "H"))
                                             |> Decimal.add(calculate_differences(decoded_item, "I"))
                                             |> Decimal.add(calculate_differences(decoded_item, "J")) |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00280", "Value" => "", "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00281", "Value" => add_cells(decoded_item, "B50", "B125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00282", "Value" => add_cells(decoded_item, "C50", "C125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00283", "Value" => add_cells(decoded_item, "D50", "D125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00284", "Value" => add_cells(decoded_item, "E50", "E125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00285", "Value" => add_cells(decoded_item, "F50", "F125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00286", "Value" => add_cells(decoded_item, "G50", "G125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00287", "Value" => add_cells(decoded_item, "H50", "H125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00288", "Value" => add_cells(decoded_item, "I50", "I125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00289", "Value" => add_cells(decoded_item, "J50", "J125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00290", "Value" => add_ranges(decoded_item, "B50:J50", "B125:J125") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00291", "Value" => add_cells(decoded_item, "B51", "B127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00292", "Value" => add_cells(decoded_item, "C51", "C127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00293", "Value" => add_cells(decoded_item, "D51", "D127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00294", "Value" => add_cells(decoded_item, "E51", "E127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00295", "Value" => add_cells(decoded_item, "F51", "F127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00296", "Value" => add_cells(decoded_item, "G51", "G127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00297", "Value" => add_cells(decoded_item, "H51", "H127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00298", "Value" => add_cells(decoded_item, "I51", "I127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00299", "Value" => add_cells(decoded_item, "J51", "J127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00300", "Value" => add_ranges(decoded_item, "B51:J51", "B127:J127") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00301", "Value" => add_cells(decoded_item, "B54", "B131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00302", "Value" => add_cells(decoded_item, "C54", "C131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00303", "Value" => add_cells(decoded_item, "D54", "D131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00304", "Value" => add_cells(decoded_item, "E54", "E131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00305", "Value" => add_cells(decoded_item, "F54", "F131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00306", "Value" => add_cells(decoded_item, "G54", "G131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00307", "Value" => add_cells(decoded_item, "H54", "H131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00308", "Value" => add_cells(decoded_item, "I54", "I131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00309", "Value" => add_cells(decoded_item, "J54", "J131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00310", "Value" => add_ranges(decoded_item, "B54:J54", "B131:J131") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00311", "Value" => add_cells(decoded_item, "B55", "B133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00312", "Value" => add_cells(decoded_item, "C55", "C133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00313", "Value" => add_cells(decoded_item, "D55", "D133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00314", "Value" => add_cells(decoded_item, "E55", "E133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00315", "Value" => add_cells(decoded_item, "F55", "F133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00316", "Value" => add_cells(decoded_item, "G55", "G133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00317", "Value" => add_cells(decoded_item, "H55", "H133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00318", "Value" => add_cells(decoded_item, "I55", "I133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00319", "Value" => add_cells(decoded_item, "J55", "J133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00320", "Value" => add_ranges(decoded_item, "B55:J55", "B133:J133") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00321", "Value" => add_cells(decoded_item, "B56", "B135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00322", "Value" => add_cells(decoded_item, "C56", "C135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00323", "Value" => add_cells(decoded_item, "D56", "D135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00324", "Value" => add_cells(decoded_item, "E56", "E135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00325", "Value" => add_cells(decoded_item, "F56", "F135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00326", "Value" => add_cells(decoded_item, "G56", "G135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00327", "Value" => add_cells(decoded_item, "H56", "H135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00328", "Value" => add_cells(decoded_item, "I56", "I135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00329", "Value" => add_cells(decoded_item, "J56", "J135") |> to_string(), "_dataType" => "NUMERIC"},
        %{"Code" => "1183_00330", "Value" => add_ranges(decoded_item, "B56:J56", "B135:J135") |> to_string(), "_dataType" => "NUMERIC"},
       ] |> format_map(),
      "DynamicItemsList" => []
    }


  end


  # Sum cells vertically (e.g. A1:A5 sums down column A)
def sum_range_vertical(decoded_item, range) do
  case parse_range(range) do
    {start_col, start_row, end_col, end_row} ->
      # For vertical sum, we keep the column fixed and iterate through rows
      for row <- start_row..end_row do
        cell = "#{start_col}#{row}"
        val = decoded_item[cell] || "0"
        parse_decimal(val)
      end
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
  end
end
# Parse a range like "A1:B2" into {start_col, start_row, end_col, end_row}
defp parse_range(range) do
  case String.split(range, ":") do
    [start_cell, end_cell] ->
      {start_col, start_row} = parse_cell(start_cell)
      {end_col, end_row} = parse_cell(end_cell)
      {start_col, start_row, end_col, end_row}
  end
end
# Original horizontal sum (e.g. A1:E1 sums across row 1)
def sum_range_horizontal(decoded_item, range) do
  case parse_range(range) do
    {start_col, start_row, end_col, end_row} ->
      # For horizontal sum, we keep the row fixed and iterate through columns
      for col <- char_range(start_col, end_col) do
        cell = "#{col}#{start_row}"
        val = decoded_item[cell] || "0"
        parse_decimal(val)
      end
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
  end
end

# Smart sum that detects direction based on range
def sum_range(decoded_item, range) when is_binary(range) do
  case parse_range(range) do
    {start_col, start_row, end_col, end_row} = range_parts when start_col == end_col ->
      # Vertical sum (same column)
      sum_range_vertical(decoded_item, range)

    {start_col, start_row, end_col, end_row} = range_parts when start_row == end_row ->
      # Horizontal sum (same row)
      sum_range_horizontal(decoded_item, range)

    {start_col, start_row, end_col, end_row} ->
      # Grid sum (both directions)
      for col <- char_range(start_col, end_col),
          row <- start_row..end_row do
        cell = "#{col}#{row}"
        decoded_item |> Map.get(cell, "0") |> parse_decimal()
      end
      |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    _ ->
      # Invalid range format
      Decimal.new("0")
  end
end
# Parse a cell reference like "A1" into {column, row}
defp parse_cell(cell) do
  {col, row} = String.split_at(cell, 1)
  {col, String.to_integer(row)}
end

# Generate range of column letters (e.g. "A".."C" gives ["A", "B", "C"])
defp char_range(start_col, end_col) do
  for n <- String.to_charlist(start_col)..String.to_charlist(end_col),
      do: <<n::utf8>>
end

# Convert string value to Decimal, handling nil/invalid values
defp parse_decimal(value) when is_binary(value) do
  case Decimal.new(String.trim(value)) do
    {:ok, decimal} -> decimal
    _ -> Decimal.new("0")
  end
end

# Add cells in a vertical range within same column
def add_cells(decoded_item, range, target)
    when is_binary(range)
    and is_binary(target) do
  if String.contains?(range, ":") do
    # Handle range case
    case String.split(range, ":") do
      [start_cell, end_row] ->
        # Extract column and row info
        {col, start_row} = parse_cell(start_cell)
        # Get just the row number from end
        end_row = String.to_integer(end_row)

        # Sum all cells in the range
        sum1 = start_row..end_row
        |> Enum.map(fn row ->
          cell = "#{col}#{row}"
          val = decoded_item[cell] || "0"
          parse_decimal(val)
        end)
        |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

        # Add the target cell
        val2 = parse_decimal(decoded_item[target] || "0")
        Decimal.add(sum1, val2)
    end
  else
    # Handle single cell case
    val1 = parse_decimal(decoded_item[range] || "0")
    val2 = parse_decimal(decoded_item[target] || "0")
    Decimal.add(val1, val2)
  end
end

# Keep original add_cells function for two individual cells
def add_cells(decoded_item, cell1, cell2) do
  val1 = parse_decimal(decoded_item[cell1] || "0")
  val2 = parse_decimal(decoded_item[cell2] || "0")
  Decimal.add(val1, val2)
end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

def add_ranges(decoded_item, ranges) when is_list(ranges) do
  # Sum multiple ranges
  ranges
  |> Enum.map(fn range -> sum_range(decoded_item, range) end)
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
end

# Keep original function for backward compatibility
def add_ranges(decoded_item, range1, range2) do
  add_ranges(decoded_item, [range1, range2])
end

def add_range_pairs_across_columns(decoded_item, start_col, end_col, range_rows, target_row) do
  start_ascii = String.to_charlist(start_col) |> hd()
  end_ascii = String.to_charlist(end_col) |> hd()

  [range_start, range_end] = String.split(range_rows, ":")
  range_start = String.to_integer(range_start)
  range_end = String.to_integer(range_end)

  start_ascii..end_ascii
  |> Enum.map(&<<&1::utf8>>)
  |> Enum.map(fn col ->
    # First sum the vertical range (e.g. B35:B42)
    range_sum = range_start..range_end
    |> Enum.map(fn row ->
      cell = "#{col}#{row}"
      val = decoded_item[cell] || "0"
      parse_decimal(val)
    end)
    |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)

    # Add the target cell (e.g. B30)
    target_val = parse_decimal(decoded_item["#{col}#{target_row}"] || "0")
    Decimal.add(range_sum, target_val)
  end)
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
end

def sum_multiple_ranges(decoded_item, ranges_list) do
  ranges_list
  |> Enum.map(fn ranges -> add_ranges(decoded_item, ranges) end)
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
end

def sum_column_differences(decoded_item, start_col, end_col) do
  start_ascii = String.to_charlist(start_col) |> hd()
  end_ascii = String.to_charlist(end_col) |> hd()

  start_ascii..end_ascii
  |> Enum.map(&<<&1::utf8>>)
  |> Enum.map(fn col ->
    # First part: sum_range("X15:X26")
    sum1 = sum_range(decoded_item, "#{col}15:#{col}26")

    # Second part: add_multiple_range_pairs for X35:X113 pairs
    pairs = [
      {"#{col}35", "#{col}99"},
      {"#{col}36", "#{col}101"},
      {"#{col}37", "#{col}103"},
      {"#{col}38", "#{col}105"},
      {"#{col}39", "#{col}107"},
      {"#{col}40", "#{col}109"},
      {"#{col}41", "#{col}111"},
      {"#{col}42", "#{col}113"}
    ]
    sum2 = add_multiple_range_pairs(decoded_item, pairs)

    # Third part: add_cells("X32:X34", "X91")
    sum3 = add_cells(decoded_item, "#{col}32:#{col}34", "#{col}91")

    # Calculate difference: sum1 - (sum2 + sum3)
    Decimal.sub(sum1, Decimal.add(sum2, sum3))
  end)
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
end

def calculate_differences(decoded_item, column) do
  # First part: sum the range X15:X26
  sum1 = sum_range(decoded_item, "#{column}15:#{column}26")

  # Second part: create pairs for add_multiple_range_pairs
  pairs = for row <- 35..42 do
    target_row = row * 2 + 29  # Calculates 99,101,103,105,107,109,111,113
    {"#{column}#{row}", "#{column}#{target_row}"}
  end

  # Add cells X32:X34 + X91
  extra_sum = add_cells(decoded_item, "#{column}32:#{column}34", "#{column}91")

  # Final calculation: sum1 - (pairs_sum + extra_sum)
  sum1
  |> Decimal.sub(
    add_multiple_range_pairs(decoded_item, pairs)
    |> Decimal.add(extra_sum)
  )
end

def add_multiple_range_pairs(decoded_item, pairs) do
  # Take a list of tuple pairs and sum them all
  pairs
  |> Enum.map(fn {cell1, cell2} ->
    val1 = parse_decimal(decoded_item[cell1] || "0")
    val2 = parse_decimal(decoded_item[cell2] || "0")
    Decimal.add(val1, val2)
  end)
  |> Enum.reduce(Decimal.new("0"), &Decimal.add/2)
end
end
