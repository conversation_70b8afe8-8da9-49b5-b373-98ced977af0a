defmodule MisReports.Workers.BozReq.Schedule11f do

  def perform(item) do

    decoded_item =
      case item.schedule_11f do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-9ZSCH11F9Z002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1151_00001",
          "Value" => "#{format_number(decoded_item["C17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00002",
          "Value" => "#{format_number(decoded_item["D17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00003",
          "Value" => "#{format_number(decoded_item["E17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00004",
          "Value" => "#{format_number(decoded_item["F17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00005",
          "Value" => "#{format_number(decoded_item["G17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00006",
          "Value" => "#{format_number(decoded_item["H17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00007",
          "Value" => "#{format_number(decoded_item["I17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00008",
          "Value" => "#{format_number(decoded_item["C18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00009",
          "Value" => "#{format_number(decoded_item["D18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00010",
          "Value" => "#{format_number(decoded_item["E18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00011",
          "Value" => "#{format_number(decoded_item["F18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00012",
          "Value" => "#{format_number(decoded_item["G18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00013",
          "Value" => "#{format_number(decoded_item["H18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00014",
          "Value" => "#{format_number(decoded_item["I18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00015",
          "Value" => "#{format_number(decoded_item["C19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00016",
          "Value" => "#{format_number(decoded_item["D19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00017",
          "Value" => "#{format_number(decoded_item["E19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00018",
          "Value" => "#{format_number(decoded_item["F19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00019",
          "Value" => "#{format_number(decoded_item["G19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00020",
          "Value" => "#{format_number(decoded_item["H19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00021",
          "Value" => "#{format_number(decoded_item["I19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00022",
          "Value" => "#{format_number(decoded_item["C20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00023",
          "Value" => "#{format_number(decoded_item["D20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00024",
          "Value" => "#{format_number(decoded_item["E20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00025",
          "Value" => "#{format_number(decoded_item["F20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00026",
          "Value" => "#{format_number(decoded_item["G20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00027",
          "Value" => "#{format_number(decoded_item["H20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00028",
          "Value" => "#{format_number(decoded_item["I20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00029",
          "Value" => "#{format_number(decoded_item["C21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00030",
          "Value" => "#{format_number(decoded_item["D21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00031",
          "Value" => "#{format_number(decoded_item["E21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00032",
          "Value" => "#{format_number(decoded_item["F21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00033",
          "Value" => "#{format_number(decoded_item["G21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00034",
          "Value" => "#{format_number(decoded_item["H21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00035",
          "Value" => "#{format_number(decoded_item["I21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00036",
          "Value" => "#{format_number(decoded_item["C22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00037",
          "Value" => "#{format_number(decoded_item["D22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00038",
          "Value" => "#{format_number(decoded_item["E22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00039",
          "Value" => "#{format_number(decoded_item["F22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00040",
          "Value" => "#{format_number(decoded_item["G22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00041",
          "Value" => "#{format_number(decoded_item["H22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00042",
          "Value" => "#{format_number(decoded_item["I22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00043",
          "Value" => "#{format_number(decoded_item["C23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00044",
          "Value" => "#{format_number(decoded_item["D23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00045",
          "Value" => "#{format_number(decoded_item["E23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00046",
          "Value" => "#{format_number(decoded_item["F23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00047",
          "Value" => "#{format_number(decoded_item["G23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00048",
          "Value" => "#{format_number(decoded_item["H23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00049",
          "Value" => "#{format_number(decoded_item["I23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00050",
          "Value" => "#{format_number(decoded_item["C24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00051",
          "Value" => "#{format_number(decoded_item["D24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00052",
          "Value" => "#{format_number(decoded_item["E24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00053",
          "Value" => "#{format_number(decoded_item["F24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00054",
          "Value" => "#{format_number(decoded_item["G24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00055",
          "Value" => "#{format_number(decoded_item["H24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00056",
          "Value" => "#{format_number(decoded_item["I24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00057",
          "Value" => "#{format_number(decoded_item["C25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00058",
          "Value" => "#{format_number(decoded_item["D25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00059",
          "Value" => "#{format_number(decoded_item["E25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00060",
          "Value" => "#{format_number(decoded_item["F25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00061",
          "Value" => "#{format_number(decoded_item["G25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00062",
          "Value" => "#{format_number(decoded_item["H25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00063",
          "Value" => "#{format_number(decoded_item["I25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00064",
          "Value" => "#{format_number(decoded_item["C26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00065",
          "Value" => "#{format_number(decoded_item["D26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00066",
          "Value" => "#{format_number(decoded_item["E26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00067",
          "Value" => "#{format_number(decoded_item["F26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00068",
          "Value" => "#{format_number(decoded_item["G26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00069",
          "Value" => "#{format_number(decoded_item["H26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00070",
          "Value" => "#{format_number(decoded_item["I26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00071",
          "Value" => "#{format_number(decoded_item["C27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00072",
          "Value" => "#{format_number(decoded_item["D27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00073",
          "Value" => "#{format_number(decoded_item["E27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00074",
          "Value" => "#{format_number(decoded_item["F27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00075",
          "Value" => "#{format_number(decoded_item["G27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00076",
          "Value" => "#{format_number(decoded_item["H27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00077",
          "Value" => "#{format_number(decoded_item["I27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00078",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00079",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00080",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00081",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00082",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00083",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00084",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00085",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00086",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00087",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00088",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00089",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00090",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00091",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00092",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00093",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00094",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00095",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00096",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00097",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00098",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00099",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00100",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00101",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00102",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00103",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00104",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00105",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00106",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00107",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00108",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00109",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1151_00110",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{ "Code" => "1151_00111",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00112",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00113",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00114",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00115",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00116",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00117",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00118",
        "Value" =>"0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00119",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00120",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00121",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00122",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00123",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00124",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00125",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00126",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00127",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00128",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00129",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00130",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00131",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00132",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00133",
        "Value" => "0",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00134",
        "Value" => "#{format_number(decoded_item["C27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00135",
        "Value" => "#{format_number(decoded_item["D27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00136",
        "Value" => "#{format_number(decoded_item["E27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00137",
        "Value" => "#{format_number(decoded_item["F27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00138",
        "Value" => "#{format_number(decoded_item["G27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00139",
        "Value" => "#{format_number(decoded_item["H27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
        },
      %{ "Code" => "1151_00140",
        "Value" => "#{format_number(decoded_item["I27"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
        "_dataType" => "NUMERIC"
      }
      ],
      "dynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
