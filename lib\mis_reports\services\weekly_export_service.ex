defmodule MisReports.Services.WeeklyExportService do
  @moduledoc """
  Service module for managing weekly export operations.

  This module centralizes all weekly export-related functionality including:
  - Creating export records
  - Managing export statuses
  - Generating export filenames
  - Handling export metadata
  """

  require Logger
  alias MisReports.Utilities
  alias MisReportsWeb.UserController

  @doc """
  Creates a weekly export record for the given process_id and reference.

  This function handles the creation of weekly export records for Core Liquid Assets (CLA)
  and Forex Risk reports. It automatically determines the export type based on the process_id
  and generates appropriate metadata.

  ## Parameters

  - `process_id` - The process identifier (2000 for CLA, 3000 for Forex)
  - `reference` - The workflow reference number

  ## Returns

  - `{:ok, export}` - Successfully created export record
  - `{:ok, nil}` - Skipped creation (non-weekly process)
  - `{:error, changeset}` - Failed to create export record

  ## Examples

      iex> MisReports.Services.WeeklyExportService.create_export_record("2000", "REF123")
      {:ok, %WeeklyFileExport{}}

      iex> MisReports.Services.WeeklyExportService.create_export_record("1000", "REF123")
      {:ok, nil}
  """
  def create_export_record(process_id, reference) do
    # Convert process_id to string if it's an integer
    process_id_str = if is_integer(process_id), do: "#{process_id}", else: process_id

    Logger.debug("Creating weekly export for process_id: #{process_id_str}, reference: #{reference}")

    # Only create export records for weekly reports (process_id 2000 or 3000)
    if process_id_str in ["2000", "3000"] do
      report_date = Date.utc_today()
      export_type = determine_export_type(process_id_str)

      Logger.debug("Weekly export type: #{export_type}")

      # Get the current user ID (default to 1 if not available)
      maker_id = get_current_user_id()

      # Calculate week and year
      {week_num, year} = Timex.iso_week(report_date)
      week_str = "#{week_num}"
      year_str = "#{year}"

      # Generate a default filename with the correct format based on export type
      default_filename = generate_export_filename(export_type)

      Logger.debug("Creating weekly export with week: #{week_str}, year: #{year_str}, default filename: #{default_filename}")

      # Create the weekly export record with the default filename
      export_attrs = %{
        report_date: report_date,
        process_id: process_id_str,
        reference: reference,
        maker_id: maker_id,
        status: "PENDING",
        export_type: export_type,
        week: week_str,
        year: year_str,
        filename: default_filename
      }

      create_export_record_with_attrs(export_attrs, reference)
    else
      Logger.warning("Skipping weekly export creation for non-weekly process_id: #{process_id_str}")
      {:ok, nil}
    end
  end

  @doc """
  Determines the export type based on the process ID.

  ## Parameters

  - `process_id_str` - String representation of the process ID

  ## Returns

  - Export type string ("CORE_LIQUID_ASSETS", "FOREX_RISK", or "UNKNOWN")
  """
  def determine_export_type(process_id_str) do
    case process_id_str do
      "2000" -> "CORE_LIQUID_ASSETS"
      "3000" -> "FOREX_RISK"
      _ -> "UNKNOWN"
    end
  end

  @doc """
  Generates a standardized filename for the export based on the export type.

  ## Parameters

  - `export_type` - The type of export ("CORE_LIQUID_ASSETS", "FOREX_RISK", etc.)

  ## Returns

  - Formatted filename string
  """
  def generate_export_filename(export_type) do
    datetime = Timex.local() |> Timex.format!("%Y%m%d%H%M", :strftime)
    report_type = case export_type do
      "CORE_LIQUID_ASSETS" -> "CLA"
      "FOREX_RISK" -> "FX"
      _ -> "UNKNOWN"
    end
    "Weekly_#{report_type}_Report_#{datetime}.xlsx"
  end

  @doc """
  Updates the status of a weekly export record.

  ## Parameters

  - `export_record` - The export record to update
  - `new_status` - The new status to set
  - `additional_attrs` - Optional additional attributes to update

  ## Returns

  - `{:ok, updated_export}` - Successfully updated
  - `{:error, changeset}` - Failed to update
  """
  def update_export_status(export_record, new_status, additional_attrs \\ %{}) do
    attrs = Map.put(additional_attrs, :status, new_status)
    Utilities.update_weekly_file_export(export_record, attrs)
  end

  @doc """
  Retrieves export records by reference number.

  ## Parameters

  - `reference` - The workflow reference number

  ## Returns

  - Export record or nil if not found
  """
  def get_export_by_reference(reference) do
    Utilities.get_weekly_export_by_reference(reference)
  end

  @doc """
  Retrieves all completed export records by reference number.

  ## Parameters

  - `reference` - The workflow reference number

  ## Returns

  - List of completed export records
  """
  def get_completed_exports_by_reference(reference) do
    Utilities.get_all_completed_weekly_exports_by_reference(reference)
  end

  # Private helper functions

  defp get_current_user_id do
    # Default to user ID 1 if current user cannot be determined
    # In a real application, this should be passed as a parameter
    # or retrieved from the current session/context
    1
  end

  defp create_export_record_with_attrs(attrs, reference) do
    Utilities.create_weekly_export(attrs)
    |> case do
      {:ok, export} ->
        Logger.info("Created weekly export record with reference #{reference}")
        {:ok, export}
      {:error, changeset} ->
        error_msg = UserController.traverse_errors(changeset.errors) |> Enum.join("\r\n")
        Logger.error("Failed to create weekly export record: #{error_msg}")
        {:error, changeset}
    end
  end
end
