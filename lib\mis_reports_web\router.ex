defmodule MisReportsWeb.Router do
  # alias Mix.Dep.Loader
  use MisReportsWeb, :router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, {MisReportsWeb.LayoutView, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug(MisReportsWeb.Plugs.SetUser)
    # plug(MisReportsWeb.Plugs.SessionTimeout,
    #   timeout_after_seconds: Application.get_env(:mis_reports, :session_timeout)
    # )
    plug(MisReportsWeb.Plugs.SessionTimeout,
      timeout_after_seconds: 30_000_000_000_000
    )
  end

  pipeline :income_statement do
    plug(:fetch_session)
    plug :put_layout, false
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  pipeline :session do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_flash)
    plug :protect_from_forgery
    plug(:put_secure_browser_headers)
  end

  pipeline :change_pass_session do
    plug(:accepts, ["html"])
    plug(:fetch_session)
    plug(:fetch_flash)
    plug :protect_from_forgery
    plug(:put_secure_browser_headers)
    plug(MisReportsWeb.Plugs.SetUser)
  end

  scope "/", MisReportsWeb do
    pipe_through :browser
    get "/page", PageController, :index
    get "/income", PageController, :income
  end

  scope "/", MisReportsWeb do
    pipe_through :browser
    get "/create/user", UserController, :new
    post "/create/user", UserController, :create
    get("/list/users", UserController, :list_users)
    get("/update/user", UserController, :edit)
    post("/update/user", UserController, :update)
    #    get("/change/user/password", UserController, :new_password)
    # post("/change/user/password", UserController, :change_password)
    post("/reset/password", UserController, :reset_password)
    get("/user/activity/logs", UserController, :user_logs)
    post("/change/user/status", UserController, :update_status)
    get("/view/user/activities", UserController, :user_logs)
    get("/old-dashboard", UserController, :dashboard)
    post "/show/user", UserController, :show
  end

  scope "/", MisReportsWeb do
    pipe_through :browser
    get "/new/user/role", UserRoleController, :new
    post "/new/user/role", UserRoleController, :create
    get "/update/user/:id/role", UserRoleController, :edit
    post "/update/user/role", UserRoleController, :update
    get "/system/user/roles", UserRoleController, :index
    delete "/delete/user/role", UserRoleController, :delete
    post "/change/user/role/status", UserRoleController, :change_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:session])
    get("/", SessionController, :new)
    post("/", SessionController, :create)
    get("/forgort/password", UserController, :forgot_password)
    post("/confirmation/token", UserController, :token)
    get("/reset/password", UserController, :reset_password)
    get("/token/verification", SessionController, :token)
    post("/token/verification", SessionController, :confirm_token)
  end

  scope "/", MisReportsWeb do
    pipe_through([:change_pass_session])
    get("/change/password", UserController, :change_password)
    post("change/password", UserController, :update_password)
  end

  scope "/", MisReportsWeb do
    pipe_through :browser
    get "/loan-advance/bulk/upload", ReturnsController, :bulk_upload
    post "/loan-advance/bulk/upload", ReturnsController, :process_upload
    get "/loan-advance/list", ReturnsController, :loan_list
    post "/loan-advance/list", ReturnsController, :entry_lookup
    get "/loan-advance/returns", ReturnsController, :export_loan_returns
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    get("/signout", SessionController, :signout)
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/roles/new", UserRoleLive.Index, :new
    live "/roles/:id/edit", UserRoleLive.Index, :edit
    live "/roles", UserRoleLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/dashboard", DashboardLive.Index, :index
    live "/users/new", UserLive.Index, :new
    live "/users/:id/edit", UserLive.Index, :edit
    live "/users", UserLive.Index, :index
    live "/users/:id/audit-logs", UserLive.Index, :audit_log
    #  live "/uses/change/password", UserLive.Index, :change_password
  end

  # scope "/", MisReportsWeb do
  #   pipe_through([:session])
  #   live "/users/change/password", UserLive.Index, :change_password
  # end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/mappings/new-spec", MappingLive.Index, :new_spec
    live "/mappings/:id/edit-spec", MappingLive.Index, :edit_spec
    live "/mappings", MappingLive.Index, :index
    live "/bank/accounts/new-acc", MappingLive.Index, :new_bank_acc
    live "/bank/accounts/:id/edit-acc", MappingLive.Index, :edit_bank_acc
    live "/bank/accounts", MappingLive.Index, :list_bank_acc
    live "/gov/accounts/new", MappingLive.Index, :new_gov_acc
    live "/gov/accounts/:id/edit", MappingLive.Index, :edit_gov_acc
    live "/gl/mapping/new-gl-mapping", MappingLive.Index, :new_gl_mapping
    live "/gl/mapping/listing/:id/edit-gl-list", MappingLive.Index, :edit_gl_mapping
    live "/gl/mapping", MappingLive.Index, :gl_mapping_list
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/interest/rate/risks/new", InterestRateRiskLive.Index, :new
    live "/interest/rate/risks/:id/edit", InterestRateRiskLive.Index, :edit
    live "/interest/rate/risks", InterestRateRiskLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/customer/segementations/new", SourceDataLive.Index, :new
    live "/template/sample", SourceDataLive.Index, :new_temp
    live "/customer/segementations/:id/edit", SourceDataLive.Index, :edit
    live "/customer/segementations/:id/view", SourceDataLive.Index, :view
    live "/uploaded/files/list/", SourceDataLive.Index, :upload_list
    live "/trial/balance/list", SourceDataLive.Index, :trial_bal_list
    live "/customer/contribution/list", SourceDataLive.Index, :ccr_list
    live "/customer/segementations", SourceDataLive.Index, :gbm_list_items
    live "/trial/balance/upload", SourceDataLive.Index, :trial_bal_upload
    live "/all/deal/list", SourceDataLive.Index, :all_deal_list
    live "/fx/Cash/flow/list", SourceDataLive.Index, :fx_cash_flow_list
    live "/trial/balance/:id/entries", SourceDataLive.Index, :show_trial_bal
    live "/customer/segementations/audit-logs", SourceDataLive.Index, :audit_log
    live "/adjust/:id/contribution/", SourceDataLive.Index, :edit_ccr
    live "/customer/account/details", SourceDataLive.Index, :cust_acc_list
    live "/weekly/upload", SourceDataLive.Index, :weekly_upload
    live "/weekly/upload/list", SourceDataLive.Index, :weekly_upload_list
    live "/trial/balance/list", SourceDataLive.Index, :show_trial_bal
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/company/configurations", SettingsLive.Index, :company_settings
    live "/directories/configurations", SettingsLive.Index, :directories
    live "/gov/accounts", SettingsLive.Index, :gov_accounts
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/system/reports/new", ReportsLive.Index, :index
    live "/schedules/index", SchedulesLive.Index, :index
    live "/other/index", OtherSchedulesLive.Index, :index
    live "/income/index", IncomeStatementLive.Index, :index
    live "/balance/index", BalanceSheetLive.Index, :index
    live "/submit/:uuid/report", BalanceSheetLive.Index, :submit_report
    live "/prudential/report", BalanceSheetLive.Index, :prudential_report_list
    live "/loader/index", LoaderLive.Index, :index
    live "/Prudential/Generate", IncomeStatementLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/bussiness/units", BusinessUnitLive.Index, :index
    live "/loan/business/unit/new", BusinessUnitLive.Index, :new
    live "/loan_business_unit/:id/edit", BusinessUnitLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/debtors/book/analysis", DebtorsBookAnalysisLive.Index, :index
    live "/debtors/book/analysis/new", DebtorsBookAnalysisLive.Index, :new
    live "/debtors/book/analysis/:id/edit", DebtorsBookAnalysisLive.Index, :edit
    live "/debtors/book/analysis/approve", DebtorsBookAnalysisLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/branch/new", BranchLive.Index, :new
    live "/branches", BranchLive.Index, :index
    live "/branch/:id/edit", BranchLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/exchange_placement/new", ExchangePlacementLive.Index, :new
    live "/exchange_placement", ExchangePlacementLive.Index, :index
    live "/exchange_placement/:id/edit", ExchangePlacementLive.Index, :edit
    live "/exchange_placement/approve", ExchangePlacementLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/Prudential/Initial", PrudentialReportLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/institution_details/new", InstitutionDetailsLive.Index, :new
    live "/institution_details", InstitutionDetailsLive.Index, :index
    live "/institution_details/:id/edit", InstitutionDetailsLive.Index, :edit
    live "/institution_details/approve", InstitutionDetailsLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/insertion/new", InsertionLive.Index, :new
    live "/insertion", InsertionLive.Index, :index
    live "/insertion/:id/edit", InsertionLive.Index, :edit
    live "/insertion/approve", InsertionLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/tech_infra/new", TechInfraLive.Index, :new
    live "/tech_infra", TechInfraLive.Index, :index
    live "/tech_infra/:id/edit", TechInfraLive.Index, :edit
  end


  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/quarterly/new", QuarterlyLive.Index, :new
    live "/quarterly", QuarterlyLive.Index, :index
    live "/quarterly/download", QuarterlyLive.Index, :publication_list
    live "/quarterly/:id/edit", QuarterlyLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/govt/accounts/new", GovtAccountsLive.Index, :new
    live "/govt/accounts", GovtAccountsLive.Index, :index
    live "/govt/accounts/:id/edit", GovtAccountsLive.Index, :edit
    live "/govt/accounts/approve", GovtAccountsLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/is_trend", IncomeStatementTrendLive.Index, :index

  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/ifrs/review/trend", IncomeStmtIfrsTrendLive.Index, :index

  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/offblcSheet/sheet/new", OffBlcSheetLive.Index, :new
    live "/offblcSheet/:id/sheet", OffBlcSheetLive.Index, :edit
    live "/OffBlcSheet/sheets", OffBlcSheetLive.Index, :index
    live "/OffBlcSheet/sheets/approve", OffBlcSheetLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/WeeklyBozBlc/sheet/new", WeeklyBozBalanceLive.Index, :new
    live "/WeeklyBozBlc/:id/sheet", WeeklyBozBalanceLive.Index, :edit
    live "/WeeklyBozBlc/sheets", WeeklyBozBalanceLive.Index, :index
    live "/WeeklyBozBlc/approve", WeeklyBozBalanceLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/counterparty/securities/new", CounterpartySecuritiesLive.Index, :new
    live "/counterparty/securities/approve", CounterpartySecuritiesLive.Index, :update_status
    live "/counterparty/securities/:id/edit", CounterpartySecuritiesLive.Index, :edit
    live "/counterparty/securities/", CounterpartySecuritiesLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/ShareHolders/share/new", ShareHoldersLive.Index, :new
    live "/ShareHolders/shares/:id/edit", ShareHoldersLive.Index, :edit
    live "/ShareHolders/sheets", ShareHoldersLive.Index, :index
    live "/Share/Holders/approval", ShareHoldersLive.Index, :update_status
  end


  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/secureholdings/holdings/new", SecureHoldingsLive.Index, :new
    live "/SecureHoldings/:id/edit", SecureHoldingsLive.Index, :edit
    live "/SecureHoldings/secures", SecureHoldingsLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/Balance/domestic", BalanceDueDomesticLive.Index, :index
    live "/Balance/due/new", BalanceDueDomesticLive.Index, :new
    live "/Balance/:id/edit", BalanceDueDomesticLive.Index, :edit
    live "/bal/due/domestic/approve", BalanceDueDomesticLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/exchange/rate/new", ExchangeRateLive.Index, :new
    live "/exchange/rate/approve", ExchangeRateLive.Index, :update_status
    live "/exchange/:id/rate", ExchangeRateLive.Index, :edit
    live "/exchange/rates", ExchangeRateLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/config/workflow/step/new", WklStepLive.Index, :new
    live "/config/workflow/:id/step", WklStepLive.Index, :edit
    live "/config/workflow/step", WklStepLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/config/workflow/process/new", WklProcessLive.Index, :new
    live "/config/workflow/:id/process", WklProcessLive.Index, :edit
    live "/config/workflow/process", WklProcessLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/mis/report/new", MisReportLive.Index, :index
    live "/exchange/rates/report", MisReportLive.Index, :exchange_rate_report
    live "/loan/classifications/report", MisReportLive.Index, :loan_classifications_report
    live "/specific/provisions/movements/report", MisReportLive.Index, :specific_provisions_movement_report
    live "/sector/analysis/report", MisReportLive.Index, :sector_analysis_report
  end
  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/deposit/", DepositLive.Index, :index
  end
  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/loans/tobanks", LoanBanksLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/loans/due/banks", BalancesDueBanksLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/net/interest/income", NetInterestIncomeLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/cost/analysis", CostAnalysisLive.Index, :index
  end
  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/loans/adavances", LoanAdvancesLive.Index, :index
  end
  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/config/workflow/action/new", WklActionLive.Index, :new
    live "/config/workflow/:id/action", WklActionLive.Index, :edit
    live "/config/workflow/action", WklActionLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/config/workflow/step-action/new", WklActionRuleLive.Index, :new
    live "/config/workflow/:id/step-action", WklActionRuleLive.Index, :edit
    live "/config/workflow/step-action", WklActionRuleLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/workflow/pending-tasks/", WklPendingTasksLive.Index, :index
    live "/workflow/tasks/", WklTasksLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/repossed/properties/new", RepossessedPropertiesLive.Index, :new
    live "/repossed/properties/approve", RepossessedPropertiesLive.Index, :update_status
    live "/repossed/properties/:id/edit", RepossessedPropertiesLive.Index, :edit
    live "/repossed/properties", RepossessedPropertiesLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/securities", SecuritiesLive.Index, :index
    live "/Securities/view", SecuritiesLive.Index, :new
    live "/securities_unit/:id/edit", SecuritiesLive.Index, :edit
    live "/securities/approve", SecuritiesLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/allowance", AllowancesLive.Index, :index
    live "/allowances/view", AllowancesLive.Index, :new
    live "/allowances_unit/:id/edit", AllowancesLive.Index, :edit
    live "/allowances/approve", AllowancesLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/current/deferred", CurrentDefferredLive.Index, :index
    live "/tax/new", CurrentDefferredLive.Index, :new
    live "/tax/:id/edit", CurrentDefferredLive.Index, :edit
    live "/tax/approve", CurrentDefferredLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/reckon/index", ReckonLive.Index, :index
    # live "/reckon/:id/BS", ReckonLive.Index, :reckon_bs
    live "/reckon/IS", ReckonLive.Index, :reckon_is
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/finace/dashbord", FinanceLive.Index, :index
    live "/credit/dashbord", CreditLive.Index, :index
    live "/bs/dashboard", BsDashboardLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/isdashboard/dashboard", IsDashboardLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/employee/benefits/new", EmployeeBenefitLive.Index, :new
    live "/employee/benefits", EmployeeBenefitLive.Index, :index
    live "/employee/benefits/:id/edit", EmployeeBenefitLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/new/benefit", BenefitLive.Index, :new
    live "/benefit", BenefitLive.Index, :index
    live "/benefit/approve", BenefitLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/technological_infrastructure/new", TechnologicalInfrastructureLive.Index, :new
    live "/technological_infrastructure", TechnologicalInfrastructureLive.Index, :index
    live "/technological_infrastructure/approve", TechnologicalInfrastructureLive.Index, :update_status
    live "/technological_infrastructure/:id/edit", TechnologicalInfrastructureLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/employee/stats/new", EmployeeStatsLive.Index, :new
    live "/employee/stats", EmployeeStatsLive.Index, :index
    live "/employee/stats/:id/edit", EmployeeStatsLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/stats/new", StatsLive.Index, :new
    live "/stats", StatsLive.Index, :index
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/loan/classification/new", LoanClassificationLive.Index, :new
    live "/loan/classifications", LoanClassificationLive.Index, :index
    live "/loan/classification/:id/edit", LoanClassificationLive.Index, :edit
    live "/loan/classifications/approval", LoanClassificationLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/cmmp/products/new", CmmpProductLive.Index, :new
    live "/cmmp/products", CmmpProductLive.Index, :index
    live "/cmmp/products/:id/edit", CmmpProductLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/loan/products/new", LoanProductLive.Index, :new
    live "/list/loan/products/", LoanProductLive.Index, :index
    live "/loan/products/:id/edit", LoanProductLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/loan/sectors/new", LoanSectorLive.Index, :new
    live "/loan/sectors", LoanSectorLive.Index, :index
    live "/loan/sectors/:id/edit", LoanSectorLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/regulatory/capital", RegulatoryCapitalLive.Index, :index
    live "/regulatory/capital/new", RegulatoryCapitalLive.Index, :new
    live "/regulatory/capital/approve", RegulatoryCapitalLive.Index, :update_status
    live "/regulatory/capital/:id/edit", RegulatoryCapitalLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/loan/scheme/codes", LoanSchemeCodeLive.Index, :new
    live "/loan/scheme/codes/new", LoanSchemeCodeLive.Index, :index
    live "/loan/scheme/codes/:id/edit", LoanSchemeCodeLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/weekly/index", WeeklyLive.Index, :index
    # live "/loan/sytem", LoanSystemSectorLive.Index, :index
    # live "/view/sector", ViewLoanSystemSectorLive.Index, :index
    # live "/schem/code", SchemeCodeLive.Index, :index
    # live "/loan/products",LoanProductsLive.Index, :index
    # live "/view/loan/products",ViewLoanProductsLive.Index, :index
    # live "/view/loan/sector",ViewloansectorLive.Index, :index
    # live "/past/due/classification",PastDueClassificationLive.Index, :index
    get "/download/unmaintained/accounts", DocStorageController, :generate_csv
    post "/download/unmaintained/accounts", DocStorageController, :generate_csv

    get "/download/unmaintained/gls", DocStorageController, :generate_csv_gls
    post "/download/unmaintained/gls", DocStorageController, :generate_csv_gls
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/adjustments", AdjustmentsLive.Index, :index
    live "/adjustments/new", AdjustmentsLive.Index, :new
    live "/adjustments/:id/edit", AdjustmentsLive.Index, :edit
    live "/insertions/new", InsertionLive.Index, :new
    live "/insertions/:id/edit", InsertionLive.Index, :edit
    live "/adjustments/approve", AdjustmentsLive.Index, :update_status
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/submission/dates", SubmissionDatesLive.Index, :new
    live "/submitted/dates", SubmissionDatesLive.Index, :index
    live "/submission/dates/:id/edit", SubmissionDatesLive.Index, :edit
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/BSA/Pening/Submission", BsaReportsLive.Index, :pending_reports
    live "/BSA/Submited/Reports", BsaReportsLive.Index, :submited_reports
  end


  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/cmmp", CmmpLive.Index, :index
    live "/cmmp/reportlist", CmmpReportLive.Index, :index

  end


  scope "/", MisReportsWeb do
    pipe_through([:income_statement])

    live_session :default, root_layout: false do
      # or root_layout: {LayoutView, :custom_layout}
      live "/incomeStatement/", IncomeStatementLive.Index, :index
    end
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    get("/download/:filename", ReportsController, :download)
    get("/download/cmmp/:filename", ReportsController, :download_cmmp)
    get("/download/publication/:filename", ReportsController, :download_publication)
    # Deprecated weekly report download endpoint - will be removed in future
    get("/download/weekly/:filename", ReportsController, :download_weekly)
    get("/download/income/statement", ReportsController, :income_statement_pdf)
    # Direct download endpoints for weekly reports
    get("/weekly/download/:filename", ReportsController, :download_weekly_direct)
    get("/weekly/download/reference/:reference", ReportsController, :download_weekly_by_reference)
    # Specific download endpoints for FX and CLA reports
    get("/weekly/download/fx/:filename", ReportsController, :download_weekly_fx)
    get("/weekly/download/cla/:filename", ReportsController, :download_weekly_cla)
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])
    live "/debtors/book/analysis", DebtorsBookAnalysisLive.Index, :index
    live "/debtors/book/analysis/new", DebtorsBookAnalysisLive.Index, :new
    live "/debtors/book/analysis/:id/edit", DebtorsBookAnalysisLive.Index, :edit
    live "/debtors/book/analysis/approve", DebtorsBookAnalysisLive.Index, :update_status
    live "/cmmp/report/list", DebtorsBookAnalysisLive.Index, :cmmp_report_listing
  end

  scope "/", MisReportsWeb do
    pipe_through([:browser])

    live "/submission/dates", SubmissionDatesLive.Index, :new
    live "/submitted/dates", SubmissionDatesLive.Index, :index
    live "/submission/dates/:id/edit", SubmissionDatesLive.Index, :edit
  end

  scope "/api", MisReportsWeb do
    pipe_through :api

    get "/downloads/:token", DownloadController, :download
  end

  # Other scopes may use custom stacks.
  # scope "/api", MisReportsWeb do
  #   pipe_through :api
  # end
end
