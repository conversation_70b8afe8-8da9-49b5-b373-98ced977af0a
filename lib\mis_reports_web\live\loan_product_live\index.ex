defmodule MisReportsWeb.LoanProductLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  import Ecto.Query, warn: false
  alias MisReports.Repo
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.Prudentials.LoanProducts
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Prudentials
  alias MisReportsWeb.UserController
  alias MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000]
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do

      socket =
        socket
          |> assign(:process_id, params["process_id"])
          |> assign(:reference, params["reference"])
          |> assign(:step_id, params["step_id"])


      {:noreply,
       socket
       |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    loan_product = Prudentials.get_loan_product!(id)
    socket
    |> assign(:loan_product, loan_product)
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :loan_product, %LoanProducts{})
  end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        loan_product = Prudentials.get_loan_product_by_reference!(reference)

        socket
        |> assign(:loan_product, loan_product)
        |> assign(:changeset, Prudentials.change_loan_product(loan_product))
        # Explicitly reassign reference
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_loan_products(socket)

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_loan_products()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_loan_products()}
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_loan_products()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_by, "sort_dir" => sort_dir}, socket) do
    sort_by_atom = String.to_atom(sort_by)
    sort_dir_atom = String.to_atom(sort_dir)
    updated_socket = assign(socket, sort_by: {sort_dir_atom, sort_by_atom})
    {:noreply, list_loan_products(updated_socket)}
  end

  defp handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    loan_product = Prudentials.get_loan_product!(id)
    audit_msg = "changed status flag for loan product: \"#{loan_product.product}\", to: #{status}"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      LoanProducts.changeset(loan_product, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_product, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Successful!")
         |> push_redirect(to: Routes.loan_product_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_delete(params, socket) do
    id = params["id"]
    loan_product = Prudentials.get_loan_product!(id)
    audit_msg = "Deleted Loan Product: \"#{loan_product.product}\""
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_loan_product, loan_product)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_loan_product: _loan_product, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Loan Product Deleted successfully!")
         |> push_redirect(to: Routes.loan_product_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp list_loan_products(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Prudentials.list_loan_products(params)
        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, loan_products: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  defp put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"loan_product", "new"}

      act when act in ~w(edit)a ->
        {"loan_product", "edit"}

      act when act in ~w(update_status)a ->
        {"loan_product", "update_status"}

      act when act in ~w(delete)a ->
        {"loan_product", "delete"}

      act when act in ~w(index)a ->
        {"loan_product", "index"}

      _ ->
        {"loan_product", "unknown"}
    end
  end
end
