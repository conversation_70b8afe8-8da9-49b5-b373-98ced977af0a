defmodule MisReportsWeb.EmployeeBenefitLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.UserLiveAuth
  alias MisReports.{Employees , Repo }
  alias MisReportsWeb.LiveHelpers
  alias MisReports.Employees.EmployeeBenefit
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.UserController

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
     assign(socket,
       page: 1,
       page_size: 10,
       isearch: nil,
       sort_by: {:asc, :id},
       length_menu: [10, 25, 50, 100, 300, 500, 1000],
       action: nil
     )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    menu_opts = LiveHelpers.menu_opts(__MODULE__, socket.assigns.live_action, [:edit])
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      {:noreply,
        socket
        |> assign(menu_opts: menu_opts)
        |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :new, _params) do
    assign(socket, :benefit, %EmployeeBenefit{})
    |> assign(:position, "")
    |> assign(:values, nil)
  end

  defp apply_action(socket, :index, _params), do: list_benefits(socket)

  defp apply_action(socket, :edit, %{"id" => id}) do
    benefit = Employees.get_employee_benefit!(id)
    values = get_next_nil(benefit)

    socket
    |> assign(page: %{prev: "Source Mappings", current: "Edit specification"})
    |> assign(:benefit, benefit)
    |> assign(:values, values)
    |> assign(:position, "h")
  end

  @impl true
  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_benefits()}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_benefits()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_benefits()}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    sort_by =
      Enum.find_value([:descript, :inserted_at, :status], fn field ->
        if String.to_existing_atom(sort_field) == field do
          {String.to_existing_atom(dir), field}
        end
      end)

    {:noreply,
     socket
     |> assign(sort_by: sort_by)
     |> list_benefits()}
  end



  def get_next_nil(benefit) do
    Map.from_struct(benefit)
    |> Map.take([:exec_manag, :snr_manag, :managers, :gen_staff, :other_staff])
    |> MisReports.Workers.Utils.to_atomic_map()
  end




  defp list_benefits(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Employees.list_employee_benefits(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end


  def page_name(:new), do: "New Estimates"
  def page_name(:index), do: "Staff Benefits Estimates List"
  def page_name(:edit), do: "Staff Benefits Estimates Edit"

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    # if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    # else
    #   UserLiveAuth.unauthorized(socket)
    # end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    # if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    # else
    #   UserLiveAuth.unauthorized(socket)
    # end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    benefit = Employees.get_employee_benefit!(id)
    current_user = socket.assigns.current_user

    if current_user.id == benefit.maker_id do
      {:noreply,
       socket
       |> put_flash(:error, "You cannot approve your own submission. Please have another user review and activate it.")
       |> push_redirect(to: Routes.employee_benefit_index_path(socket, :index))}
    else
      audit_msg = "changed status for template employee stats to: #{status}"


    Ecto.Multi.new()
    |> Ecto.Multi.update(
    :update,
    EmployeeBenefit.changeset(benefit, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _loan_sector, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.employee_benefit_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
      end
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    benefit = Employees.get_employee_benefit!(id)
    audit_msg = "Deleted Employee Benefit:"
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_employee_benefit, benefit)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
    {:ok, %{del_employee_benefit: _loan_sector, audit_log: _audit_log}} ->
      {:noreply,
      socket
      |> put_flash(:info, "Employee Benefit Deleted successfully!")
      |> push_redirect(to: Routes.employee_benefit_index_path(socket, :index))}

    {:error, failed_value} ->
      {:error, failed_value}
    end
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"employee_benefit", "new"}

      act when act in ~w(edit)a ->
        {"employee_benefit", "edit"}

      act when act in ~w(update_status)a ->
        {"employee_benefit", "update_status"}

      act when act in ~w(delete)a ->
        {"employee_benefit", "delete"}

      act when act in ~w(index)a ->
        {"employee_benefit", "index"}

      _ ->
        {"employee_benefit", "unknown"}
    end
  end
end
