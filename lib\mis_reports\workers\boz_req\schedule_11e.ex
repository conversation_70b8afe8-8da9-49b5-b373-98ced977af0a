defmodule MisReports.Workers.BozReq.Schedule11e do
  alias MisReports.Workers.BalanceSheet

  def perform(item) do

    decoded_item =
      case item.schedule_11e do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end


    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-7OSCH11E7O002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{ "Code" => "1120_00001",
          "Value" => "#{decoded_item["C23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00002",
          "Value" => "#{decoded_item["D23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00003",
          "Value" => "#{decoded_item["E23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00004",
          "Value" => "#{decoded_item["F23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00005",
          "Value" => "#{decoded_item["G23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00006",
          "Value" => "#{decoded_item["H23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00007",
          "Value" => "#{decoded_item["I23"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00008",
          "Value" => "#{decoded_item["C18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00009",
          "Value" => "#{decoded_item["D18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00010",
          "Value" => "#{decoded_item["E18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00011",
          "Value" => "#{decoded_item["F18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00012",
          "Value" => "#{decoded_item["G18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00013",
          "Value" => "#{decoded_item["H18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00014",
          "Value" => "#{decoded_item["I18"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00015",
          "Value" => "#{decoded_item["C16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00016",
          "Value" => "#{decoded_item["D16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00017",
          "Value" => "#{decoded_item["E16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00018",
          "Value" => "#{decoded_item["F16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00019",
          "Value" => "#{decoded_item["G16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00020",
          "Value" => "#{decoded_item["H16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00021",
          "Value" => "#{decoded_item["I16"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00022",
          "Value" => "#{decoded_item["C19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00023",
          "Value" => "#{decoded_item["D19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00024",
          "Value" => "#{decoded_item["E19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00025",
          "Value" => "#{decoded_item["F19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00026",
          "Value" => "#{decoded_item["G19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00027",
          "Value" => "#{decoded_item["H19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00028",
          "Value" => "#{decoded_item["I19"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00029",
          "Value" => "#{decoded_item["C22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00030",
          "Value" => "#{decoded_item["D22"] || "0"}",
          "_dataType" => "NUMERIC" },
          %{ "Code" => "1120_00031",
          "Value" => "#{decoded_item["E22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00032",
          "Value" => "#{decoded_item["F22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00033",
          "Value" => "#{decoded_item["G22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00034",
          "Value" => "#{decoded_item["H22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00035",
          "Value" => "#{decoded_item["I22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00036",
          "Value" => "#{decoded_item["C24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00037",
          "Value" => "#{decoded_item["D24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00038",
          "Value" => "#{decoded_item["E24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00039",
          "Value" => "#{decoded_item["F24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00040",
          "Value" => "#{decoded_item["G24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00041",
          "Value" => "#{decoded_item["H24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00042",
          "Value" => "#{decoded_item["I24"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00043",
          "Value" => "#{decoded_item["C21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00044",
          "Value" => "#{decoded_item["D21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00045",
          "Value" => "#{decoded_item["E21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00046",
          "Value" => "#{decoded_item["F21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00047",
          "Value" => "#{decoded_item["G21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00048",
          "Value" => "#{decoded_item["H21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00049",
          "Value" => "#{decoded_item["I21"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00050",
          "Value" => "#{decoded_item["C17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00051",
          "Value" => "#{decoded_item["D17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00052",
          "Value" => "#{decoded_item["E22"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00053",
          "Value" => "#{decoded_item["F17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00054",
          "Value" => "#{decoded_item["G17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00055",
          "Value" => "#{decoded_item["H17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00056",
          "Value" => "#{decoded_item["I17"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00057",
          "Value" => "#{decoded_item["C20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00058",
          "Value" => "#{decoded_item["D20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00059",
          "Value" => "#{decoded_item["E20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00060",
          "Value" => "#{decoded_item["F20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00061",
          "Value" => "#{decoded_item["G20"] || "0"}",
          "_dataType" => "NUMERIC" },
          %{ "Code" => "1120_00062",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00063",
          "Value" => "#{decoded_item["I20"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00064",
          "Value" => "#{decoded_item["C25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00065",
          "Value" => "#{decoded_item["D25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00066",
          "Value" => "#{decoded_item["E25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00067",
          "Value" => "#{decoded_item["F25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00068",
          "Value" => "#{decoded_item["G25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00069",
          "Value" => "#{decoded_item["H25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00070",
          "Value" => "#{decoded_item["I25"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00071",
          "Value" => "#{decoded_item["C26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00072",
          "Value" => "#{decoded_item["D26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00073",
          "Value" => "#{decoded_item["E26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00074",
          "Value" => "#{decoded_item["F26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00075",
          "Value" => "#{decoded_item["G26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00076",
          "Value" => "#{decoded_item["H26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00077",
          "Value" => "#{decoded_item["I26"] || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00078",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00079",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00080",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00081",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00082",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00083",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00084",
          "Value" => "N/A",
          "_dataType" => "TEXT" },
        %{ "Code" => "1120_00085",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00086",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00087",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00088",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00089",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00090",
          "Value" => "0",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1120_00091",
          "Value" => "0",
          "_dataType" => "NUMERIC" }
      ],
          "DynamicItemsList" => [
        %{
          "Area" => 429,
          "_areaName" => "SUMMARY OF TIME DEPOSITS OVER K1 MILLION AND NON RESIDENT DEPOSIT LISTING",
          "DynamicItems" => [
            %{ "Code" => "1.1",
              "Value" => "N/A",
              "_dataType" => "TEXT" },
            %{ "Code" => "1.2",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.3",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.4",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.5",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.6",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.7",
              "Value" => "0",
              "_dataType" => "NUMERIC" },
            %{ "Code" => "1.8",
              "Value" => "0",
              "_dataType" => "NUMERIC" }
          ]
        },
          ]
    }

  end


  def total_outstanding_amt(items) do
    formatted_values =
      items
      |> Enum.map(fn item -> convert_to_number(item["amt_outstanding"]) end)

    Enum.reduce(formatted_values, 0.0, &BalanceSheet.get_sum/2)
  end


  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values like "0"
  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
  # If String.to_float fails, try integer conversion
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

 defp convert_to_number(_), do: 0.0
end
