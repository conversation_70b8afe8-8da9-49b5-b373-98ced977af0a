defmodule MisReports.Workers.BozReq.Schedule15 do

  def perform(item) do

    decoded_item =
      case item.schedule_15 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-VASCH15VA001",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
            %{
              "Code" => "1195_00001",
              "Value" => "#{decoded_item["C21"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00002",
              "Value" => "#{decoded_item["C22"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00003",
              "Value" => "#{decoded_item["C23"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00004",
              "Value" => "#{decoded_item["C24"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00005",
              "Value" => "#{decoded_item["C25"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00006",
              "Value" => "#{decoded_item["C26"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00007",
              "Value" => "#{decoded_item["C27"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00008",
              "Value" => "#{decoded_item["C28"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00009",
              "Value" => "#{decoded_item["C30"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00010",
              "Value" => "#{decoded_item["C31"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00011",
              "Value" => "#{decoded_item["C32"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00012",
              "Value" => "#{decoded_item["C33"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00013",
              "Value" => "#{decoded_item["C34"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00014",
              "Value" => "#{decoded_item["C35"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00015",
              "Value" => "#{decoded_item["C36"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00016",
              "Value" => "#{decoded_item["C37"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00017",
              "Value" => "#{decoded_item["C38"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00018",
              "Value" => "#{decoded_item["C39"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00019",
              "Value" => "#{decoded_item["C40"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00020",
              "Value" => "#{decoded_item["C42"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00021",
              "Value" => "#{decoded_item["C43"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00022",
              "Value" => "#{decoded_item["C44"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00023",
              "Value" => "#{decoded_item["C45"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00024",
              "Value" => "#{decoded_item["C46"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00025",
              "Value" => "#{decoded_item["C47"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00026",
              "Value" => "#{decoded_item["C49"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00027",
              "Value" => "#{decoded_item["C50"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00028",
              "Value" => "#{decoded_item["C51"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00029",
              "Value" => "#{decoded_item["C52"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00030",
              "Value" => "#{decoded_item["C53"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00031",
              "Value" => "#{decoded_item["C54"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00032",
              "Value" => "#{decoded_item["C55"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00033",
              "Value" => "#{decoded_item["C56"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00034",
              "Value" => "#{decoded_item["C58"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00035",
              "Value" => "#{decoded_item["C60"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00036",
              "Value" => "#{decoded_item["C61"] || "0"}",
              "_dataType" => "NUMERIC"
            },
            %{
              "Code" => "1195_00037",
              "Value" => "#{decoded_item["C62"] || "0"}",
              "_dataType" => "NUMERIC"
            }
          ],
          "dynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
