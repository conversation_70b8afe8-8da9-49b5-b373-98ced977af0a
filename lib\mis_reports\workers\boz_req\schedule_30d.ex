defmodule MisReports.Workers.BozReq.Schedule30d do

  def perform(item) do

    decoded_item =
      case item.schedule_30d do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    return_items = decoded_item["total"]
    dynamic_list = decoded_item["list"]

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-8LSCH30D8L002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1186_00003",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00004",
          "Value" => "#{format_number(return_items["forward_total"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00005",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00006",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00007",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00008",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00009",
          "Value" => "#{format_number(return_items["swap_total"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00010",
          "Value" => "0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00011",
          "Value" => "#{format_number(return_items["mtm_base"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00001",
          "Value" => "#{format_number(return_items["receive_amount"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1186_00002",
          "Value" => "#{format_number(return_items["mtm_base"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 459,
          "_areaName" => "DERIVATIVES POSITION",
          "DynamicItems" => map_data(dynamic_list)
        }
      ]
    }

  end

  def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->

        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => "#{map["product_type"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => "#{map["currency"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => "#{format_number(map["receive_amount"]) |> Decimal.new() |> Decimal.to_float() || "0"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.4", "Value" => "#{format_number(map["mtm_base"]) |> Decimal.new() |> Decimal.to_float() || "0"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.5", "Value" => "#{map["trade_date"] || "N/A"}", "_dataType" => "DATE"},
          %{"Code" => "#{index}.6", "Value" => "#{map["cash_flow_date"] || "N/A"}", "_dataType" => "DATE"},
          %{"Code" => "#{index}.7", "Value" => "#{map["counter_party"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.8", "Value" => "#{map["counter_party_type"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.9", "Value" => "N/A", "_dataType" => "TEXT"}

        ]
      end)
  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
