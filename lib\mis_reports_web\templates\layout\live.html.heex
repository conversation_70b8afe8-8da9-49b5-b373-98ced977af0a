<style>
  img, video {
  max-width: 191% !important;
  height: auto;
  }
  .dropdowns {
  position: relative;
  display: inline-block;
  }
  .dropdown-contents {
  display: none;
  position: absolute;
  background-color: #f9f9f9;
  margin-left:-98%;
  min-width: 200px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index:                         MIS Report<img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300' : ''" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />                       MIS Report<img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="ope                        Quaterly <img class                    Workflow Configuration <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300': ' '" src="https://img.icons8.com/metro/26/gary/expand-arrow.png" alt="expand-arrow" />"w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300' : ''" src="https://img.icons8.com/metro/26/gary/expand-arrow.png" alt="expand-arrow" /> ? '-rotate-180 duration-300' : ''" src="https://img.icons8.com/metro/26/gary/expand-arrow.png" alt="expand-arrow" />;
  }
  .dropdowns:hover .dropdown-contents {
  display: block;
  margin-left: 80%;
  }
  a:hover {
  background-color:#F3F4F6;
  }
  .class-name{

  }
  @keyframes animation-name{
  from{
  transform: translateX(100%);
  height: 0px;
  }

  }

  /* Style for schedule tags in comments */
  .schedule-tag {
    display: inline-block;
    font-weight: bold;
    color: #0066cc;
  
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 4px;
  }

  /* Style for schedule comments to ensure each starts on a new line */
  .schedule-comment {
    margin-bottom: 4px;
    display: block;
  }
</style>

<div>
  <div class="relative z-50 lg:hidden" role="dialog" aria-modal="true">
    <div aria-hidden="true"></div>

    <div class="fixed inset-0 flex">
      <div class="relative mr-16 flex w-full max-w-xs flex-1">
        <div class="absolute left-full top-0 flex w-16 justify-center pt-5"></div>
        <!-- Sidebar component, swap this element with another sidebar if you like -->
        <div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gradient-to-r from-[#0033a1] to-[#0062e1] px-6 pb-4">
          <div class="flex h-16 shrink-0 items-center">
            <img src="/images/12.png" alt="Probase" />
          </div>

          <nav class="flex flex-1 flex-col">
            <ul role="list" class="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" class="-mx-2 space-y-1">
                  <li x-data="{ open: false }">
                    <a href="#" @click="open = ! open" class="group active flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                        />
                      </svg>
                      Dashboard
                    </a>
                  </li>

                  <li>
                    <a href="#" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                        />
                      </svg>
                      Team
                    </a>
                  </li>

                  <li>
                    <a href="#" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z"
                        />
                      </svg>
                      Projects
                    </a>
                  </li>

                  <li>
                    <a href="#" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5A2.25 2.25 0 0121 11.25v7.5" />
                      </svg>
                      Calendar
                    </a>
                  </li>

                  <li>
                    <a href="#" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
                        />
                      </svg>
                      Documents
                    </a>
                  </li>

                  <li>
                    <a href="#" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-600 hover:bg-blue-200  hover:text-gray-600">
                      <svg class="h-6 w-6 shrink-0 text-gray-600 group-hover:text-gray-600" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
                        /> <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      Reports
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>
  </div>
  <!-- Static sidebar for desktop -->
  <div class="">
    <div class="hidden lg:fixed lg:inset-y-0 lg:z-1 lg:flex lg:w-50 lg:flex-col overflow-auto">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex grow flex-col gap-y-5  bg-gray-100  px-3 pb-3">
        <div class="flex justify-center">
          <img src="/images/p2.png" alt="Probase" style="width: 47%" />
        </div>

        <nav class="flex flex-1 flex-col " id="sidebar">
          <ul role="list" class="flex flex-1 flex-col gap-y-7">
            <li>
              <ul role="list" class="-mx-2 space-y-1">
                <li>
                  <!-- Current: "hover:bg-blue-200  text-gray-600", Default: "text-gray-600 hover:text-gray-600 hover:bg-blue-200 " -->
                  <a href={Routes.dashboard_index_path(@socket, :index)} class=" group  text-blue-700 flex gap-x-3 rounded-sm hover:bg-blue-200 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6  ">
                    <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                      />
                    </svg>
                    Home
                  </a>
                </li>

                <li>
                  <!-- Current: "hover:bg-blue-200  text-gray-600", Default: "text-gray-600 hover:text-gray-600 hover:bg-blue-200 " -->
                  <a href={Routes.wkl_pending_tasks_index_path(@socket, :index)} class="relative group text-blue-700 flex items-center gap-x-3 rounded-sm hover:bg-blue-100 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6 transition-all duration-200">
                    <div class="flex items-center gap-x-3">
                      <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                       <span class="whitespace-nowrap">Your Pending Tasks</span>
                    </div>

                    <%= if  MisReports.Workflow.get_pending_tasks_count(@current_user.id) > 0 do %>
                      <div class=" -top-2 -right-2">
                        <div class="relative flex h-5 w-5 items-center justify-center">
                          <span class="absolute inline-flex h-full w-full bg-blue-200 opacity-50"></span>
                          <span class="relative inline-flex bg-blue-700 text-white text-xs font-semibold h-5 w-5 items-center justify-center shadow-lg shadow-blue-500/50">
                            <%= MisReports.Workflow.get_pending_tasks_count(@current_user.id) %>
                          </span>
                        </div>
                      </div>
                    <% end %>
                  </a>
                </li>

                <li>
                  <!-- Current: "hover:bg-blue-200  text-gray-600", Default: "text-gray-600 hover:text-gray-600 hover:bg-blue-200 " -->
                  <a href={Routes.wkl_tasks_index_path(@socket, :index)} class=" group  text-blue-700 flex gap-x-3 rounded-sm hover:bg-blue-200 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6  ">
                    <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M8 4H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-2" /> <path stroke-linecap="round" stroke-linejoin="round" d="M15 2H9a1 1 0 00-1 1v2a1 1 0 001 1h6a1 1 0 001-1V3a1 1 0 00-1-1z" />
                      <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4" />
                    </svg>
                    Task History
                  </a>
                </li>

                <li>
                  <!-- Current: "hover:bg-blue-200  text-gray-600", Default: "text-gray-600 hover:text-gray-600 hover:bg-blue-200 " -->
                  <a href={Routes.balance_sheet_index_path(@socket, :prudential_report_list)} class=" group  text-blue-700 flex gap-x-3 rounded-sm hover:bg-blue-200 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6  ">
                    <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 002.25 2.25h5.25a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V19.5a2.25 2.25 0 002.25 2.25h5.25a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08"
                      />
                    </svg>
                    Completed Returns
                  </a>
                </li>

                <li>
                  <!-- Current: "hover:bg-blue-200  text-gray-600", Default: "text-gray-600 hover:text-gray-600 hover:bg-blue-200 " -->
                  <a href={Routes.bsa_reports_index_path(@socket, :submited_reports)} class=" group  text-blue-700 flex gap-x-3 rounded-sm hover:bg-blue-200 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6  ">
                    <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path 
                        stroke-linecap="round" 
                        stroke-linejoin="round" 
                        d="M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 002.25-2.25V6a2.25 2.25 0 00-2.25-2.25H6A2.25 2.25 0 003.75 6v2.25A2.25 2.25 0 006 10.5zm0 9.75h2.25A2.25 2.25 0 0010.5 18v-2.25a2.25 2.25 0 00-2.25-2.25H6a2.25 2.25 0 00-2.25 2.25V18A2.25 2.25 0 006 20.25zm9.75-9.75H18a2.25 2.25 0 002.25-2.25V6A2.25 2.25 0 0018 3.75h-2.25A2.25 2.25 0 0013.5 6v2.25a2.25 2.25 0 002.25 2.25z"
                      />
                    </svg>
                    BSA Submitted Returns
                  </a>
                </li>

                <li>
                  <a href={Routes.user_index_path(@socket, :audit_log, @current_user.id)} class="group text-blue-700 flex gap-x-3 rounded-sm hover:bg-blue-200 border-r-4 border-blue-700 w-full p-2 text-sm font-semibold leading-6">
                    <svg class="h-6 w-6 shrink-0 text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
                      /> <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Audit Logs
                  </a>
                </li>

                <nav x-data="{ activeMenu: null }" class="w-full">
                  <ul class="space-y-1">
                    <li x-data="{ open: false }">                      <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200  hover:text-blue-700">
                        <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"
                          /> <circle cx="12" cy="12" r="9" /> />
                        </svg>
                        User Maintenance<img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300': ' '" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                      </a>




    <div x-show="open" x-cloak class="mt-1 pl-6">
  <div class="space-y-2 text-sm font-semibold text-gray-600">
    <!-- Manage Users -->
    <div x-data="{ open: false }" class="flex flex-col font-semibold p-2 border-l-4 transition duration-500 ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 border-white bg-blue-200 rounded-lg shadow-md cursor-pointer">
      <button @click="open = !open" class="text-left">Manage Users</button>

      <div x-show="open" @click.outside="open = false" x-cloak class="space-y-2 pl-4">



    <.link
    :if={MisReports.Accounts.User.has_role?(@user_role.role_str, "user", "index")}
    navigate={Routes.user_index_path(@socket, :index)}
    class="transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300 text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100"
    role="menuitem"
    tabindex="-1"
    id="menu-item-0">
    View Entries
  </.link>

  <.link
    :if={MisReports.Accounts.User.has_role?(@user_role.role_str, "user", "new")}
    navigate={Routes.user_index_path(@socket, :new)}
    class="transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 hover:bg-white duration-300 text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100"
    role="menuitem"
    tabindex="-1"
    id="menu-item-0">
    Add Entries
  </.link>
</div>

    </div>
    <!-- Manage Role -->
    <div x-data="{ open: false }" class="flex flex-col p-2 border-l-4 transition duration-500 ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 border-white bg-blue-200 rounded-lg shadow-md cursor-pointer">
      <button @click="open = !open" class="text-left w-full">Manage Role</button>



      <div x-show="open" @click.outside="open = false" x-cloak>
         <.link
    :if={MisReports.Accounts.User.has_role?(@user_role.role_str, "user_role", "index")}
    navigate={Routes.user_role_index_path(@socket, :index)}
    class="text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100"
    role="menuitem"
    tabindex="-1"
    id="menu-item-0">
    View Entries
  </.link>

  <.link
    :if={MisReports.Accounts.User.has_role?(@user_role.role_str, "user_role", "new")}
    navigate={Routes.user_role_index_path(@socket, :new)}
    class="text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100"
    role="menuitem"
    tabindex="-1"
    id="menu-item-0">
    Add Entries
  </.link>

</div>

    </div>
  </div>
</div>

      </li>

  <div  x-data="{ open: false }" >                      <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                        <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round" 
                            d="M12 3v3m0 0l1.5-1.5M12 6L10.5 4.5M4.93 4.93l2.12 2.12m0 0l1.41-1.41M7.05 7.05l-1.41 1.41M3 12h3m0 0l-1.5 1.5M6 12L4.5 10.5M19.07 19.07l-2.12-2.12m0 0l-1.41 1.41M16.95 16.95l1.41-1.41M21 12h-3m0 0l1.5-1.5M18 12l1.5 1.5M12 21v-3m0 0l-1.5 1.5M12 18l1.5-1.5M4.93 19.07l2.12-2.12m0 0l-1.41-1.41M7.05 16.95l-1.41 1.41M19.07 4.93l-2.12 2.12m0 0l1.41 1.41M16.95 7.05l1.41-1.41"
                          />
                        </svg>
                        MIS Report<img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? 'rotate-180 duration-300' : ''" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                      </a>
                      <!-- Added space-y-2 to add vertical spacing between links -->
                      <div x-show="open" @click.outside="open = false" class="space-y-2  text-[0.9rem] ">
                        <.link navigate={Routes.mis_report_index_path(@socket, :index)} class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2   rounded-lg shadow-md text-grey-600 bg-blue-200  text-left" role="menuitem">
                          Profit and Loss Analysis
                        </.link>
                          
                        <.link
                          navigate={Routes.cost_analysis_index_path(@socket, :index)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 rounded-lg shadow-md bg-blue-200  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 text-left"
                          role="menuitem"
                        >
                          Cost Analysis
                        </.link>
  
                        <.link navigate={Routes.deposit_index_path(@socket, :index)} class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md bg-blue-200  text-left" role="menuitem">
                          Deposits
                        </.link>

                        <.link
                          navigate={Routes.loan_advances_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 rounded-lg shadow-md bg-blue-200  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 text-left"
                          role="menuitem"
                        >
                          Loans & Advances
                        </.link>

                        <.link
                          navigate={Routes.loan_banks_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md bg-blue-200  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105  text-left"
                          role="menuitem"
                        >
                          Balances With Banks
                        </.link>
                      
                        <.link
                          navigate={Routes.balances_due_banks_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md bg-blue-200  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 text-left"
                          role="menuitem"
                        >
                          Balances Due To Banks
                        </.link>

                        <.link
                          navigate={Routes.mis_report_index_path(@socket, :exchange_rate_report)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200"
                        >
                          Exchange Rate
                        </.link>
                  
                        <.link
                          navigate={Routes.mis_report_index_path(@socket, :loan_classifications_report)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 "
                        >
                          Loan Classifications
                        </.link>
                
                        <.link
                          navigate={Routes.mis_report_index_path(@socket, :specific_provisions_movement_report)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 bg-blue-200  text-left"
                          role="menuitem"
                        >
                          Specfic Provisions
                        </.link>


                  
                     <.link
                          navigate={Routes.mis_report_index_path(@socket,:sector_analysis_report)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 "
                        >
                          Sector Analysis
                        </.link>

                        <.link
                          navigate={Routes.income_statement_trend_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 "
                        >
                          Income Statement Trend
                        </.link>
                  
                        <.link
                          navigate={Routes.reckon_index_path(@socket, :reckon_is)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 rounded-lg shadow-md  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 bg-blue-200  text-left"
                          role="menuitem"
                        >
                          Income Statement Reckon
                        </.link>

                        <.link
                          navigate={Routes.reckon_index_path(@socket, :index)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 bg-blue-200  text-left"
                          role="menuitem"
                        >
                          Balance Sheet Reckon
                        </.link>

                        <.link
                          navigate={Routes.income_stmt_ifrs_trend_index_path(@socket, :index)}
                          class="  divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2  rounded-lg shadow-md  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 bg-blue-200  text-left"
                          role="menuitem"
                        >
                          Income Statement IFRS Trend
                        </.link>
                </div>
              </div>                    <div x-data="{ open: false } ">
                      <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                        <svg class="h-6 w-4.5 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 12.75l-1.5 1.5-6-6 1.5-1.5a3.75 3.75 0 015.25 5.25zM19.5 10.5L21 12m-3.5-2.5L18 9m-.25 3.25l-3-3m.75-5.25a3 3 0 10-4.5 4.5m.75 4.5l-4 4m6-6a2.25 2.25 0 11-3-3l.75-.75" /> <circle cx="12" cy="12" r="9" />
                        </svg>
                        Quaterly <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? 'rotate-180 duration-300' : ''" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                      </a>

                      <div x-show="open" @click.outside="open = false" x-cloak class="mt-3 space-y-2 transition ease-in-out delay-10 hover:-translate-y-1 hover:scale-110">
                        <.link
                          navigate={Routes.cmmp_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200"
                        >
                          CMMP
                        </.link>
                        <.link
                          navigate={Routes.debtors_book_analysis_index_path(@socket,:cmmp_report_listing)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200"
                        >
                          Download CMMP
                        </.link>

                        <.link
                          navigate={Routes.quarterly_index_path(@socket, :index)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200"
                        >
                          Publication
                        </.link>
                        <.link
                          navigate={Routes.quarterly_index_path(@socket,:publication_list)}
                          class=" divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200"
                        >
                          Download Publication
                        </.link>
                      </div>
                    </div>
                    <!-- Maintenance Section -->
                    
                  </ul>
                </nav>                <div x-data="{ open: false } ">
                  <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                    <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M6 12a2 2 0 1 0 4 0a2 2 0 1 0-4 0M12 12a2 2 0 1 0 4 0a2 2 0 1 0-4 0M18 12a2 2 0 1 0 4 0a2 2 0 1 0-4 0M8 12h3.5M13 12h3.5M11.5 12l-0.5-0.5M11.5 12l-0.5 0.5M16.5 12l-0.5-0.5M16.5 12l-0.5 0.5M7.5 8c0-2.5 9-2.5 9 0M16.5 8l-0.5 0.5M16.5 8l0.5 0.5"
                      />
                    </svg>
                    Workflow Configuration <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? 'rotate-180 duration-300': ' '" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                  </a>

                  <div x-show="open" class="ms-4 mt- space-y-2  border-l-4 ">
                    <div class="space-y-2">
                      <.link navigate={Routes.wkl_process_index_path(@socket, :new)} class="block px-4 py-2  border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        Add Process
                      </.link>

                      <.link navigate={Routes.wkl_process_index_path(@socket, :index)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        View Processes
                      </.link>
                    </div>

                    <div class="space-y-2">
                      <.link navigate={Routes.wkl_step_index_path(@socket, :new)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        Add Steps
                      </.link>

                      <.link navigate={Routes.wkl_step_index_path(@socket, :index)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        View Steps
                      </.link>
                    </div>



                    <div class="space-y-2">
                      <.link navigate={Routes.wkl_action_index_path(@socket, :new)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        Add Action
                      </.link>

                      <.link navigate={Routes.wkl_action_index_path(@socket, :index)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        View Actions
                      </.link>
                    </div>

                    <div class="space-y-2">
                      <.link navigate={Routes.wkl_action_rule_index_path(@socket, :new)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        Add workflow Map
                      </.link>

                      <.link navigate={Routes.wkl_action_rule_index_path(@socket, :index)} class="block px-4 py-2 text-sm border-l-4 border-white bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                        View workflow Maps
                      </.link>
                    </div>
                  </div>
                </div>

                <div x-data="{ uploadsOpen: false, filesOpen: false, entriesOpen: false }">
                  <!-- Main View Uploads Section -->
                  <div class="mb-2">                    <a href="javascript:void(0)" @click.prevent="uploadsOpen = !uploadsOpen" class="group flex items-center justify-between rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                      <div class="flex items-center gap-x-3">
                        <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 12.75l-1.5 1.5-6-6 1.5-1.5a3.75 3.75 0 015.25 5.25zM19.5 10.5L21 12m-3.5-2.5L18 9m-.25 3.25l-3-3m.75-5.25a3 3 0 10-4.5 4.5m.75 4.5l-4 4m6-6a2.25 2.25 0 11-3-3l.75-.75" />
                        </svg>
                        <span>View Uploads</span>
                      </div>
                    <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? 'rotate-360 duration-300': ' '" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                    </a>

                    <!-- Nested content when "View Uploads" is clicked -->
                    <div x-show="uploadsOpen" class="mt-2 space-y-2 pl-4">

                      <!-- View Upload Files Submenu -->
                      <div>                        <a href="javascript:void(0)" @click.prevent="filesOpen = !filesOpen" class="group flex items-center justify-between rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                          <div class="flex items-center gap-x-3">
                            <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                            </svg>
                            <span>View Upload Files</span>
                          </div>

                        </a>

                        <div x-show="filesOpen" class="mt-2 space-y-2 pl-8">
                          <.link
                            navigate={Routes.source_data_index_path(@socket, :upload_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                          monthy
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :weekly_upload_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            Weekly
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :trial_bal_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            Trial Balance
                          </.link>
                        </div>
                      </div>

                      <!-- View Upload Entries Submenu -->
                      <div>                        <a href="javascript:void(0)" @click.prevent="entriesOpen = !entriesOpen" class="group flex items-center justify-between rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200 hover:text-blue-700">
                          <div class="flex items-center gap-x-3">
                            <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 12h16.5m-16.5 3.75h16.5M3.75 19.5h16.5M5.625 4.5h12.75a1.875 1.875 0 010 3.75H5.625a1.875 1.875 0 010-3.75z" />
                            </svg>
                            <span>View Upload Entries</span>
                          </div>

                        </a>

                        <div x-show="entriesOpen" class="mt-2 space-y-2 pl-8">
                          <.link
                            navigate={Routes.source_data_index_path(@socket, :show_trial_bal)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            Trial Balance Entries
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :ccr_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                          CCR Entries
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :gbm_list_items)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            GBM Entries
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :fx_cash_flow_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            FX Cash Flow Entries
                          </.link>

                          <.link
                            navigate={Routes.source_data_index_path(@socket, :all_deal_list)}
                            class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-300"
                          >
                            All Deal Entries
                          </.link>
                        </div>
                      </div>

                    </div>
                  </div>
                </div>                <div x-data="{ open: false }">
                  <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-sm p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200  hover:text-blue-700">
                    <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75"
                      />
                    </svg>
                    System Mapping <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300': ' '" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                  </a>

                  <div x-show="open" @click.outside="open = false" x-cloak class="mt-3">
                    <div class="mt-3 space-y-1">
                      <!-- File Specifications -->
                      <div x-data="{ open: false }" class="divide-y divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 bg-blue-200 p-2 text-sm font-semibold leading-6  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 rounded-md">
                        <button @click="open = !open" class="w-full rounded-md text-left">
                          File Specifications
                        </button>

                        <div x-show="open" @click.outside="open = false" x-cloak class="mt-2">
                          <.link navigate={Routes.mapping_index_path(@socket, :new_spec)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            Add Entries
                          </.link>

                          <.link navigate={Routes.mapping_index_path(@socket, :index)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            View Entries
                          </.link>
                        </div>
                      </div>
                      <!-- GL Accounts -->
                      <div x-data="{ open: false }" class="divide-y divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 bg-blue-200 p-2 text-sm font-semibold leading-6  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 rounded-md">
                        <button @click="open = !open" class="w-full rounded-md text-left">
                          GL Accounts
                        </button>

                        <div x-show="open" @click.outside="open = false" x-cloak class="mt-2">
                          <.link navigate={Routes.mapping_index_path(@socket, :new_bank_acc)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            Add Entries
                          </.link>

                          <.link navigate={Routes.mapping_index_path(@socket, :list_bank_acc)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            View Entries
                          </.link>
                        </div>
                      </div>
                      <!-- GL Mappings -->
                      <div x-data="{ open: false }" class="divide-y divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 bg-blue-200 p-2 text-sm font-semibold leading-6 transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 rounded-md">
                        <button @click="open = !open" class="w-full rounded-md text-left">
                          GL Mappings
                        </button>

                        <div x-show="open" @click.outside="open = false" x-cloak class="mt-2">
                          <.link navigate={Routes.mapping_index_path(@socket, :new_gl_mapping)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            Add Entries
                          </.link>

                          <.link navigate={Routes.mapping_index_path(@socket, :gl_mapping_list)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            View Entries
                          </.link>
                        </div>
                      </div>

                      <div x-data="{ open: false }" class="divide-y divide-slate-200 border-l-4 border-l-2 border-[#ffff] group ms-8 bg-blue-200 p-2 text-sm font-semibold leading-6  transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-110 rounded-md">
                        <button @click="open = !open" class="w-full rounded-md text-left">
                          Submission Dates
                        </button>

                        <div x-show="open" @click.outside="open = false" x-cloak class="mt-2">
                          <.link navigate={Routes.submission_dates_index_path(@socket, :new)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            Add Entries
                          </.link>

                          <.link navigate={Routes.submission_dates_index_path(@socket, :index)} class="block px-4 py-2 text-sm bg-blue-200 rounded-md cursor-pointer transition duration-300 ease-in-out hover:-translate-y-1 hover:scale-105 hover:bg-blue-200">
                            View Entries
                          </.link>
                        </div>
                      </div>
                    </div>

                    <div x-data="{ open: false }" class=" mt-3 divide-y divide-slate-200 border-l-4 border-l-2 class-name transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110  border-[#ffff] group ms-8 bg-blue-200  p-2 text-sm font-semibold leading-6 ">
                      <button @click="open = ! open">
                        CMMP Allowance for Losses
                      </button>

                      <div x-show="open" @click.outside="open = false" x-cloak class="mt-3 class-name transition ease-in-out delay-10 hover:-translate-y-1 hover:scale-110">
                        <.link navigate={Routes.debtors_book_analysis_index_path(@socket, :new)} class="text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100 " role="menuitem" tabindex="-1" id="menu-item-0">Add Entries</.link>
                        <.link navigate={Routes.debtors_book_analysis_index_path(@socket, :index)} class="text-gray-600 block px-4 py-2 text-sm hover:bg-gray-100" role="menuitem" tabindex="-1" id="menu-item-2">View Entries</.link>
                      </div>
                    </div>
                  </div>
                </div>

                <li x-data="{ open: false }">                  <a href="#" @click="open = ! open" class="group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-blue-700 hover:bg-blue-200  hover:text-blue-700">
                    <svg class="h-6 w-6 shrink-0 text-blue-700 group-hover:text-blue-700" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z"
                      /> <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    System Settings <img class="w-3 h-3.5 mx-6 my-1 ml-auto extra-margin-right" x-bind:class="open ? '-rotate-180 duration-300': ' '" src="https://img.icons8.com/metro/26/0066FF/expand-arrow.png" alt="expand-arrow" />
                  </a>

                  <div x-show="open" @click.outside="open = false" x-cloak class="mt-3">
                    <div x-data="{ open: false }" class=" divide-y divide-slate-200 border-l-4 border-l-2 class-name transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110  border-[#ffff] group ms-8 bg-blue-200  p-2 text-sm font-semibold leading-6 ">
                      <div>
                        <.link navigate={Routes.settings_index_path(@socket, :company_settings)} class=" block px-4 py-2 text-sm " role="menuitem" tabindex="-1" id="menu-item-2"> Settings</.link>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>

  <div class="lg:pl-72">
    <div>
      <!-- Separator -->
      <div class="h-6 w-px bg-gray-900/10" aria-hidden="true"></div>

      <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        <form class="relative flex flex-1" action="#" method="GET"></form>

        <div class="dropdowns">
          <span class="inline-block h-10 w-10 overflow-hidden rounded-full bg-gray-100">
            <img src="/images/9.png" alt="Probase" style="width: 100%" />
          </span>

          <div class="dropdown-contents" style="margin-left:-300%;">
            <.link navigate={Routes.user_path(@socket, :change_password)} class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="user-menu-item-0">Change Password</.link>
            <a href={Routes.session_path(@socket, :signout)} class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="user-menu-item-2">Sign out</a>
          </div>
        </div>

        <div class="flex items-center gap-x-4 lg:gap-x-6">
          <button type="button" class="-m-2.5 p-2.5 text-gray-600 hover:text-gray-500">
            <span class="sr-only">View notifications</span>
          </button>

          <div class="relative"></div>
        </div>
      </div>
    </div>

    <main>
      <div>
        <div id="notification-alert" phx-hook="Notification"></div>
         <%= @inner_content %>
      </div>
    </main>
  </div>
</div>
