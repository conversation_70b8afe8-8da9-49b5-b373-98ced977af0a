<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
  <div class="mt-5 font-semibold text-xl">BALANCES DUE TO DOMESTIC AND FOREIGN BANKS AND INSTITUTIONS</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">Add New Record</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Entries </div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">View Entries </div>
    <% end %><br>

    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />

    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.BalanceDueDomesticView, "balances_due_domestic.html", assigns) %>
    <% end %>

    <%= if @live_action == :update_status do %>  
      <.live_component 
      module={MisReportsWeb.BalanceDueDomesticLive.BalanceDueDomesticComponent}
      id="balance_due_domestic"
      current_user={@current_user} 
      balance_due_domestic={@balance_due_domestic} 
      changeset={@changeset}
      process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
      action={@live_action} />

    <% end %>

    <.live_component :if={@live_action in [:new, :edit]} 
    module={MisReportsWeb.BalanceDueDomesticLive.BalanceDueDomesticComponent} 
    id="balance_due_domestic" 
    current_user={@current_user} 
    balance_due_domestic={@balance_due_domestic} 
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id}
    action={@live_action} />

</div>

<.confirm_modal />

<.info_notification />

<.error_notification />

