defmodule MisReports.Utilities.ShareHolders do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_shareholders_equity" do
      field :status, :string, default: "D"
      field :c22, :decimal, default: 0
      field :d22, :decimal, default: 0
      field :e22, :decimal, default: 0
      field :f22, :decimal, default: 0
      field :g22, :decimal, default: 0
      field :c24, :decimal, default: 0
      field :d24, :decimal, default: 0
      field :e24, :decimal, default: 0
      field :f24, :decimal, default: 0
      field :g24, :decimal, default: 0
      field :c26, :decimal, default: 0
      field :d26, :decimal, default: 0
      field :e26, :decimal, default: 0
      field :f26, :decimal, default: 0
      field :g26, :decimal, default: 0
      field :c28, :decimal, default: 0
      field :d28, :decimal, default: 0
      field :e28, :decimal, default: 0
      field :f28, :decimal, default: 0
      field :g28, :decimal, default: 0
      field :c31, :decimal, default: 0
      field :d31, :decimal, default: 0
      field :e31, :decimal, default: 0
      field :f31, :decimal, default: 0
      field :g31, :decimal, default: 0
      field :c33, :decimal, default: 0
      field :d33, :decimal, default: 0
      field :e33, :decimal, default: 0
      field :f33, :decimal, default: 0
      field :g33, :decimal, default: 0
      field :c35, :decimal, default: 0
      field :d35, :decimal, default: 0
      field :e35, :decimal, default: 0
      field :f35, :decimal, default: 0
      field :g35, :decimal, default: 0
      field :c37, :decimal, default: 0
      field :d37, :decimal, default: 0
      field :e37, :decimal, default: 0
      field :f37, :decimal, default: 0
      field :g37, :decimal, default: 0
      field :report_date, :date
      field :reference, :string
      belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
      belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(share_holders, attrs) do
    share_holders
    |> cast(attrs, [
       :c22,
       :d22,
       :e22,
       :f22,
       :g22,
       :c24,
       :d24,
       :e24,
       :f24,
       :g24,
       :c26,
       :d26,
       :e26,
       :f26,
       :g26,
       :c28,
       :d28,
       :e28,
       :f28,
       :g28,
       :c31,
       :d31,
       :e31,
       :f31,
       :g31,
       :c33,
       :d33,
       :e33,
       :f33,
       :g33,
       :c35,
       :d35,
       :e35,
       :f35,
       :g35,
       :c37,
       :d37,
       :e37,
       :f37,
       :g37,
       :status,
       :report_date,
       :maker_id,
       :checker_id,
       :reference])
    # |> validate_required([:retain_blc_month, :retain_net_income_month, :retain_period_adjust, :retain_fair_com_income, :retain_cash_net_tax, :retain_tranfers_retained, :retain_divedends, :retain_oth_incre_month, :fair_blc_month, :fair_net_income_month, :fair_period_adjust, :fair_fair_com_income, :fair_cash_net_tax, :fair_tranfers_retained, :fair_divedends, :fair_oth_incre_month, :rev_blc_month, :rev_net_income_month, :rev_period_adjust, :rev_fair_com_income, :rev_cash_net_tax, :rev_tranfers_retained, :rev_divedends, :rev_oth_incre_month, :statu_blc_month, :statu_net_income_month, :statu_period_adjust, :statu_fair_com_income, :statu_cash_net_tax, :statu_tranfers_retained, :statu_divedends, :statu_oth_incre_month, :oth_blc_month, :oth_net_income_month, :oth_period_adjust, :oth_fair_com_income, :oth_cash_net_tax, :oth_tranfers_retained, :oth_divedends, :oth_oth_incre_month, :report_date, :maker_id, :checker_id])
  end
end
