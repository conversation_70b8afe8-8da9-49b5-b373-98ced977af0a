defmodule MisReports.Workers.BozReq.Schedule31c do

  def perform(item) do

    decoded_item =
      case item.schedule_31c do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    return_items = Map.get(decoded_item, "total", %{})
    dynamic_list = Map.get(decoded_item, "list", [])

      # Find the first item that contains "rate"
    rate_item = Enum.find(dynamic_list, fn item -> Map.has_key?(item, "rate") end) || %{}
    rate = Map.get(rate_item, "rate", %{})  # Ensure rate is always a map
    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-0LSCH31C0L002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1187_00001",
          "Value" => "#{convert_to_number(rate) || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1187_00002",
          "Value" => "#{format_number(return_items["total_zmw"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "area" => 460,
          "_areaName" => "BALANCES WITH DOMESTIC AND FOREIGN BANKS AND OTHER FINANCIAL INSTITUTIONS",
          "DynamicItems" => map_data(dynamic_list)
        }
      ]
    }

  end

  def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->

      # IO.inspect(map, label: "===============================MAP IN 31C====================")
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => "#{map["institution_type"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => "#{map["relationship"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => "#{map["type_of_Financial_institution"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => "#{map["account_name"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => "#{map["cur_classification"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.6", "Value" => "#{map["new_trade_cur"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.7", "Value" => "#{format_number(map["converted_kwacha_amt"]) || "0"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.8", "Value" => "#{format_number(map["new_clean_settlement"]) || "0"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.9", "Value" => "#{map["balance_type"]}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.10", "Value" => "#{map["new_interest"]}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.11", "Value" => "#{map["deal_date"]}", "_dataType" => "DATE"},
          %{"Code" => "#{index}.12", "Value" => "#{map["maturity_date"]}", "_dataType" => "DATE"},
          %{"Code" => "#{index}.13", "Value" => "#{map["further_details"]}", "_dataType" => "TEXT"},

        ]
      end)
  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values like "0"
  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
  # If String.to_float fails, try integer conversion
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

 defp convert_to_number(_), do: 0.0
end
