defmodule MisReportsWeb.AdjustmentsLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  import Ecto.Query, warn: false
  alias MisReports.Repo
  on_mount MisReportsWeb.UserLiveAuth
  import MisReportsWeb.Router.Helpers
  alias MisReports.Utilities.Adjustments
  alias MisReportsWeb.LiveHelpers
  alias MisReportsWeb.UserController
  alias MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReports.Utilities
  alias MisReports.Mappings.{FileSpec, DataFields}
  alias MisReports.Workflow

  @impl true
  def mount(_params, _session, socket) do
    debit_options = fetch_gl_codes()

    assigns = [
      active_tab: "adjustments",
      adjustment: %Adjustments{},
      debit_options: debit_options,
      columns: [],
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000],
      debit_name: "",
      debit_value: "",
      credit_name: "",
      credit_value: "",
      form_data: %{debit_value: "", debit_name: "", credit_value: "", credit_name: ""},
      cur_cat_credit_state: {@cur_cat_credit_state},
      business_unit_credit_state: {@business_unit_credit_state},
      cur_cat_debit_state: {@cur_cat_debit_state},
      business_unit_debit_state: {@business_unit_debit_state},
      process_id: nil,
      # reference: nil,
      step_id: socket.assigns[:step_id],
      page_title: "Adjustments",
      reference: socket.assigns[:reference]
    ]

    {:ok, assign(socket, assigns)}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do
      reference = params["reference"]
      process_id = params["process_id"]
      step_id = params["step_id"]

      socket =
        socket
        |> assign(:process_id, process_id)
        |> assign(:reference, reference)
        |> assign(:step_id, step_id)

      {:noreply, socket |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp initialize_adjustment_assigns(socket) do
    adjustment = %Adjustments{}

    socket
    |> assign(:adjustment, adjustment)
    |> assign(:changeset, Utilities.change_adjustments(adjustment))
    |> assign(:columns, [])
    |> assign(:cur_cat_credit_state, false)
    |> assign(:business_unit_credit_state, false)
    |> assign(:cur_cat_debit_state, false)
    |> assign(:business_unit_debit_state, false)
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    socket =
      socket
      |> assign(:active_tab, tab)

    {:noreply, socket}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    socket =
      socket
      |> assign(:active_tab, tab)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:tab_switched, "adjustments"}, socket) do
    IO.inspect(socket.assigns.step_id, label: "============step_id===========")

    case socket.assigns.step_id do
      # APPROVAL step
      "67" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :update_status,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}

      # APPROVAL step
      "10079" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :update_status,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "66" ->
        # # Initialize a new adjustment record
        adjustment = %Adjustments{}

        {:noreply,
         socket
         |> assign(:adjustment, adjustment)
         #  |> assign(:changeset, Utilities.change_adjustments(adjustment))
         |> push_redirect(
           to:
             Routes.adjustments_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}
    end
  end

  @impl true
  def handle_info({:tab_switched, "insertions"}, socket) do
    case socket.assigns.step_id do
      # APPROVAL step
      "67" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :update_status,
               process_id: socket.assigns.process_id,
               reference: socket.assigns.reference,
               step_id: socket.assigns.step_id
             )
         )}
# APPROVAL step
      "10079" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :update_status,
               process_id: socket.assigns.process_id,
               reference: socket.assigns.reference,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "66" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}

      # SUBMISSION step or others
      "10078" ->
        {:noreply,
         socket
         |> push_redirect(
           to:
             Routes.insertion_index_path(socket, :new,
               reference: socket.assigns.reference,
               process_id: socket.assigns.process_id,
               step_id: socket.assigns.step_id
             )
         )}
    end
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> initialize_adjustment_assigns()
  end

  def create_initial_record(report_date, user, previous_reference) do
    current_user_id = to_string(user.id)
    user_id = user.id
    IO.inspect(user.type, label: "============USER TYPE===========")
    IO.inspect(user_id, label: "============USER ID===========")
    # Determine process_id based on user type
    {process_id, adjustment_type} =
      case user.type do
        "FINANCE" -> {1100, "FINANCE"}
        "CREDIT" -> {1110, "CREDIT"}
        "OTHER" -> {1110, "FINANCE"}
        _ -> {nil, nil}
      end

    IO.inspect(process_id, label: "============PROCESS ID===========")

    # Return error if user type is invalid
    if is_nil(process_id) do
      {:error,
       %{
         status: :invalid_user_type,
         message: "User must be of type 'finance' or 'credit'"
       }}
    else
      audit_msg = "Created first #{adjustment_type} Adjustment record"

      {:ok, sub_reference} = Workflow.ensure_unique_reference()

      # Create INITIAL_RECORD with adjustment type
      params = %{
        "report_date" => report_date,
        "status" => "INITIAL_RECORD",
        "adjustment_type" => adjustment_type
      }

      IO.inspect(params, label: "============PARAMS===========")

      multi =
        Ecto.Multi.new()
        |> Ecto.Multi.insert(
          :adjustment,
          MisReports.Utilities.Adjustments.changeset(
            %MisReports.Utilities.Adjustments{maker_id: user_id},
            params
          )
        )
        |> MisReportsWeb.UserController.audit_log(user_id, audit_msg)

      multi
      |> Repo.transaction()
      |> case do
        {:ok, %{adjustment: adjustment}} ->
          case MisReports.Workflow.call_workflow(
                 sub_reference,
                 # Using dynamic process_id based on user type
                 process_id,
                 current_user_id,
                 81,
                 current_user_id,
                 "",
                 "Submission of the initial #{String.downcase(adjustment_type)} adjustment",
                 previous_reference
               ) do
            {:ok, reference_number} ->
              case Utilities.update_adjustments(adjustment, %{reference: reference_number}) do
                {:ok, updated_adjustment} ->
                  {:ok,
                   %{
                     status: :success,
                     adjustment: updated_adjustment,
                     reference_number: reference_number,
                     message:
                       "The initial #{String.downcase(adjustment_type)} adjustment record has been successfully created with, Reference Number: #{reference_number}"
                   }}

                {:error, changeset} ->
                  {:error,
                   %{
                     status: :adjustment_update_failed,
                     changeset: changeset,
                     message: "Failed to update adjustments reference"
                   }}
              end

            {:error, reason} ->
              {:error,
               %{
                 status: :workflow_failed,
                 adjustment: adjustment,
                 message:
                   "Initial #{String.downcase(adjustment_type)} adjustment record created, but the workflow failed: #{reason}"
               }}
          end

        {:error, changeset} ->
          {:error,
           %{
             status: :transaction_failed,
             changeset: changeset,
             message: "Failed to create the initial adjustment record."
           }}
      end
    end
  end

  defp parse_adjustment_lines(adjustment_lines) do
    case adjustment_lines do
      nil ->
        []

      "" ->
        []

      data when is_binary(data) ->
        try do
          Jason.decode!(data)
          |> Enum.map(fn i ->
            %{
              amount: i["amount"],
              credit: i["credit"],
              credit_col: i["credit_col"],
              debit: i["debit"],
              debit_col: i["debit_col"],
              type_credit: i["type_credit"],
              cur_cat_credit: i["cur_cat_credit"],
              business_unit_credit: i["business_unit_credit"],
              business_unit_credit_col: i["business_unit_credit_col"],
              cur_cat_credit_col: i["cur_cat_credit_col"],
              type_debit: i["type_debit"],
              cur_cat_debit: i["cur_cat_debit"],
              business_unit_debit: i["business_unit_debit"],
              business_unit_debit_col: i["business_unit_debit_col"],
              cur_cat_debit_col: i["cur_cat_debit_col"]
            }
          end)
        rescue
          _ -> []
        end

      _ ->
        []
    end
  end

  # defp apply_action(socket, :edit, %{"id" => id}) do
  #   adjustment = Utilities.get_adjustments!(id)
  #   columns = parse_adjustment_lines(adjustment.adjustment_lines)

  #   socket
  #   |> assign(page: %{prev: "adjustment", current: "Edit adjustment"})
  #   |> assign(:adjustment, adjustment)
  #   |> assign(:columns, columns)
  #   |> assign(:cur_cat_credit_state, false)
  #   |> assign(:business_unit_credit_state, false)
  #   |> assign(:cur_cat_debit_state, false)
  #   |> assign(:business_unit_debit_state, false)
  # end

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference

    case Utilities.get_adjustments_reference!(reference) do
      nil ->
        socket
        |> put_flash(:error, "Adjustment not found")
        |> push_redirect(to: Routes.adjustments_index_path(socket, :index))

      adjustments when is_list(adjustments) ->
        # Initialize with empty columns, will be populated when an adjustment is selected
        socket
        |> assign(:adjustments, adjustments)
        |> assign(:columns, [])
        |> assign(:selected_adjustment, nil)
        |> assign(:page, %{prev: "adjustment", current: "Review adjustments"})
        |> assign(:cur_cat_credit_state, false)
        |> assign(:business_unit_credit_state, false)
        |> assign(:cur_cat_debit_state, false)
        |> assign(:business_unit_debit_state, false)
        |> assign(:reference, reference)

      adjustment ->
        # Single adjustment case (fallback)
        columns = parse_adjustment_lines(adjustment.adjustment_lines)

        socket
        |> assign(:adjustments, [adjustment])
        |> assign(:selected_adjustment, adjustment)
        |> assign(:columns, columns)
        |> assign(:page, %{prev: "adjustment", current: "Review adjustment"})
        |> assign(:cur_cat_credit_state, false)
        |> assign(:business_unit_credit_state, false)
        |> assign(:cur_cat_debit_state, false)
        |> assign(:business_unit_debit_state, false)
        |> assign(:reference, reference)
    end
  end

  defp apply_action(socket, :index, _params), do: list_adjustments(socket)

  @impl true
  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :delete]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("get_file_sec", %{"value" => file_spec}, socket) do
    changeset = DataFields.gen_columns(%FileSpec{}, String.to_atom(file_spec))

    {:noreply,
     socket
     |> assign(file_spec_cols: changeset.columns)}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_adjustments()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_adjustments()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_adjustments()}
  end

  @impl true
  def handle_event("select_debit", %{"value" => value, "name" => name}, socket) do
    form_data =
      socket.assigns.form_data
      |> Map.put(:debit_name, name)
      |> Map.put(:debit_value, value)

    {:noreply, socket |> assign(form_data: form_data)}
  end

  @impl true
  def handle_event("select_credit", %{"value" => value, "name" => name}, socket) do
    form_data =
      socket.assigns.form_data
      |> Map.put(:credit_name, name)
      |> Map.put(:credit_value, value)

    {:noreply, socket |> assign(form_data: form_data)}
  end

  @impl true
  def handle_event("select_adjustment", %{"id" => adjustment_id}, socket) do
    case Enum.find(socket.assigns.adjustments, &(&1.id == String.to_integer(adjustment_id))) do
      nil ->
        {:noreply, socket}

      adjustment ->
        columns = parse_adjustment_lines(adjustment.adjustment_lines)

        {:noreply,
         socket
         |> assign(:selected_adjustment, adjustment)
         |> assign(:columns, columns)}
    end
  end

  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    adjustment = Utilities.get_adjustments!(id)

    audit_msg =
      "changed status for Adjustments for report date \"#{adjustment.report_date}\" to status \"#{status}\""

    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Adjustments.changeset(adjustment, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _adjustment, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Operation Succesfull!")
         |> push_redirect(to: Routes.adjustments_index_path(socket, :index))}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    adjustment = Utilities.get_adjustments!(id)
    audit_msg = "Deleted adjustments for report date \"#{adjustment.report_date}\" "
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_adjustment, adjustment)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{del_adjustment: _adjustment, audit_log: _audit_log}} ->
        {:noreply,
         socket
         |> put_flash(:info, "Adjustments Deleted successfully!")
         |> push_redirect(to: Routes.adjustments_index_path(socket, :index))}

      {:error, failed_value} ->
        {:error, failed_value}
    end
  end

  defp list_adjustments(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          Utilities.list_adjustments(params, "DOUBLE")

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, adjustments: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

  defp fetch_gl_codes do
    MisReports.Mappings.authorized_accounts()
  end

  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def handle_event("switch_view", %{"view" => view}, socket) do
    if view == "adjustments", do: "adjustments", else: "insertions"

    {:noreply,
     socket
     |> assign(:current_view, view)}
  end

  def authorize(socket) do
    user_type = socket.assigns.current_user.type

    adjustment_type =
      case user_type do
        "FINANCE" -> "finance_adjustments"
        "CREDIT" -> "credit_adjustments"
        "OTHER" -> "finance_adjustments"
        _ -> "unknown"
      end

    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {adjustment_type, "new"}

      act when act in ~w(edit)a ->
        {adjustment_type, "edit"}

      act when act in ~w(update_status)a ->
        {adjustment_type, "update_status"}

      act when act in ~w(delete)a ->
        {adjustment_type, "delete"}

      act when act in ~w(index)a ->
        {adjustment_type, "index"}

      _ ->
        {adjustment_type, "unknown"}
    end
  end
end
