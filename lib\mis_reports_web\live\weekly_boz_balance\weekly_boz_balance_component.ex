defmodule MisReportsWeb.WeeklyBozBalanceLive.WeeklyBozBalanceComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.WeeklyBozBalance
  alias MisReportsWeb.WeeklyBozBalanceController
  alias MisReportsWeb.UserController

  import MisReportsWeb.UserLive.Index, only: [put_conn_user: 1]

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.WeeklyBozBalanceView, "weekly_boz_balance.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.WeeklyBozBalanceView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{weekly_boz_blc: weekly_boz_blc} = assigns, socket) do
    changeset = Utilities.change_weekly_boz_balance(weekly_boz_blc)
    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)
    {:ok,
      socket
      |> assign(assigns)
      |> assign(:process_id, process_id)
      |> assign(:reference, reference)
      |> assign(:step_id, step_id)
      |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"weekly_boz_blc" => params}, socket) do
    changeset =
      socket.assigns.weekly_boz_blc
      |> WeeklyBozBalance.changeset(params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  @impl true
  def handle_event("save", %{"weekly_boz_blc" => weekly_boz_blc}, socket) do
    save_weekly_boz_blc(socket, socket.assigns.action, weekly_boz_blc)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      # Reject action
      "96" ->
        save_weekly_boz_blc(socket, :reject, params)

      # Approve action
      "97" ->
        save_weekly_boz_blc(socket, :approve, params)

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_weekly_boz_blc(socket, :new, params) do
    audit_msg = "Created new Weekly BOZ Balance"
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :weekly_boz_blc,
      WeeklyBozBalance.changeset(%WeeklyBozBalance{maker_id: user.id}, params)
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{weekly_boz_blc: weekly_boz_blc}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Weekly BOZ Balance Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_weekly_boz_balance(weekly_boz_blc, %{
                   reference: reference_number
                 }) do
              {:ok, updated_weekly_boz_blc} ->
                {:noreply,
                 socket
                 |> put_flash(
                   :info,
                   "Weekly BOZ Balance created successfully. Reference: #{reference_number}"
                 )
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Weekly BOZ Balance created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  defp save_weekly_boz_blc(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Weekly BOZ Balance Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(
           :info,
           "Weekly BOZ Balance rejected successfully. Reference: #{reference_number}"
         )
         |> push_redirect(
           to:
             Routes.wkl_pending_tasks_index_path(socket, :update_status, %{
               reference: reference_number
             })
         )}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject Weekly BOZ Balance: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_weekly_boz_blc(socket, :approve, params) do
    weekly_boz_blc = socket.assigns.weekly_boz_blc
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Weekly BOZ Balance Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      WeeklyBozBalance.changeset(weekly_boz_blc, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_weekly_boz_blc}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(
               :info,
               "Weekly BOZ Balance approved successfully. Reference: #{reference_number}"
             )
             |> push_redirect(
               to:
                 Routes.wkl_pending_tasks_index_path(socket, :index, %{
                   reference: reference_number
                 })
             )}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Weekly BOZ Balance approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve Weekly BOZ Balance")
         |> assign(:changeset, %{weekly_boz_blc.changeset | errors: failed_value.errors})}
    end
  end

  defp save_weekly_boz_blc(socket, :edit, params) do
    weekly_boz_blc = socket.assigns.weekly_boz_blc

    socket
    |> handle_update(params, weekly_boz_blc)
    |> case do
      {:ok, weekly_boz_blc} ->
        {:noreply,
         socket
         |> put_flash(:info, "Weekly BOZ Balance record updated successfully")
         |> push_redirect(to: Routes.weekly_boz_balance_index_path(socket, :new))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, weekly_boz_blc) do
    audit_msg = " Updated Weekly Boz Balance"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :weekly_boz_blc,
      WeeklyBozBalance.changeset(
        weekly_boz_blc,
        Map.merge(params, %{"status" => "D", "checker_id" => nil})
      )
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{weekly_boz_blc: weekly_boz_blc, audit_log: _user_log}} ->
        {:ok, weekly_boz_blc}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end
end
