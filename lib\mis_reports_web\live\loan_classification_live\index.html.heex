<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
   <div class="mt-5 font-semibold text-xl">Loan Classifications</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">New Loan Classification</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Loan Classification</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">View Loan Classifications </div>
    <% end %><br>
   
   <.info :if={live_flash(@flash, :info)} flash={@flash} />
   <.error :if={live_flash(@flash, :error)} flash={@flash} />
   
   <%= if @live_action == :index do %>
       <%= Phoenix.View.render(MisReportsWeb.LoanClassificationView, "loan_classifications.html", assigns) %>
   <% end %>

   <%= if @live_action == :update_status do %> 
   
   <.live_component 
    module={ MisReportsWeb.LoanClassificationLive.LoanClassificationComponent} 
    id="new-loan-classification" 
    current_user={@current_user} 
    loan_classification={@loan_classification} 
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id}
    action={@live_action} />

   <% end %>

    <.live_component :if={@live_action in [:new, :edit]} 
    module={ MisReportsWeb.LoanClassificationLive.LoanClassificationComponent} 
    id="new-loan-classification" 
    current_user={@current_user} 
    loan_classification={@loan_classification} 
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id}
    action={@live_action} />
   
 </div>
   
<.confirm_modal />

<.info_notification />

<.error_notification />