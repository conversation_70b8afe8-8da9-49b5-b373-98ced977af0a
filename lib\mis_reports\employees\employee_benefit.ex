defmodule MisReports.Employees.EmployeeBenefit do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_employee_benefit" do
    field :descript, :string
    field :month, :string
    field :year, :string
    field :exec_manag, :map
    field :snr_manag, :map
    field :managers, :map
    field :gen_staff, :map
    field :other_staff, :map
    field :status, :string, default: "PROGRESSING"
    field :maker_date, :naive_datetime
    field :checker_date, :naive_datetime
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(employee_benefit, attrs) do
    employee_benefit
    |> cast(attrs, [
      :descript,
      :month,
      :year,
      :exec_manag,
      :snr_manag,
      :managers,
      :gen_staff,
      :other_staff,
      :status,
      :maker_date,
      :checker_date,
      :maker_id,
      :checker_id,
      :reference
    ])
    |> validate_required([
      :descript,
      :status
    ])
  end
end
