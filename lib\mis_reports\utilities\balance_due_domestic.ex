
defmodule MisReports.Utilities.BalanceDueDomestic do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_balance_due_domestic" do
    field :a16, :string
    field :b16, :string
    field :c16, :string
    field :d16, :string
    field :e16, :string
    field :f16, :string
    field :g16, :decimal
    field :h16, :decimal
    field :i16, :string
    field :j16, :string
    field :k16, :date
    field :l16, :date
    field :m16, :string
    field :report_dt, :date
    field :status, :string, default: "D"
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(balance_due_domestic, attrs) do
    balance_due_domestic
    |> cast(attrs, [:a16, :b16, :c16, :d16, :e16, :report_dt,  :f16, :g16, :h16, :i16, :j16, :k16, :l16, :m16, :checker_id, :status, :reference])
    # |> validate_required([:a16, :b16, :c16, :d16, :e16, :f16, :g16, :h16, :i16, :j16, :k16, :l16, :m16])
  end
end
