defmodule MisReports.Workers.BsaReturns do
  require HTTPoison
  require Logger

  alias MisReports.Prudentials.BsaMaster
  alias MisReports.Repo

  def send(ref) do
    item = MisReports.Prudentials.get_prud_report_select_by_ref(ref)
    settings = MisReports.Utilities.get_comapany_settings_params()
    # IO.inspect(settings, label: "====================settings=======================")

    case authenticate(settings) do
      {:ok, token, _req} -> parepare_scheduele(item, settings, token)
      response -> response
    end

    # update table that
  end

  defp authenticate(settings) do
    req =
      %{
        "useruser" => "#{settings.username}",
        "userpass" => "#{settings.password}"
      }
      |> Poison.encode!()

    headers = [{"Content-Type", "application/json"}]
    IO.inspect(HTTPoison.post("#{settings.login_url}", req, headers), label: "++++++++++++")

    case HTTPoison.post("#{settings.login_url}", req, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        get_token(body, req)

      # IO.inspect(body, label: "====================body=======================")

      {:error, reason} ->
        {:error, reason, req}

      _ ->
        {:error, "Unknown error occurred", req}
    end
  end

  defp get_token(body, req) do
    case Poison.decode!(body) do
      %{"authenticated" => true, "accessToken" => token} -> {:ok, token, req}
      _ -> {:error, "Authentication failed", req}
    end
  end

  def parepare_scheduele(item, settings, token) do
    item
    |> Map.take([
    #   :bal_sheet,
    #   :income_stmt,
    #   :schedule_01c,
    #   :schedule_01d,
    #   :schedule_01e,
    #   :schedule_01f,
    #   :schedule_02a1,
    #   :schedule_02c,
    #   :schedule_02d,
    #   :schedule_02e,
    #   :schedule_02f,
    #   :schedule_02g,
    #   :schedule_02h,
    #   :schedule_02j,
    #   :schedule_03a,
    #   :schedule_03b,
    #   :schedule_04b,
    #   :schedule_04c,
    #   :schedule_04d,
    #   :schedule_05b,
    #   :schedule_05c,
    #   :schedule_05d,
    #   :schedule_06a,
    #   :schedule_06b,
    #   :schedule_07a,
      #:schedule_08a,
    #   :schedule_08b,
    #   :schedule_09a,
    #   :schedule_10c,
    #   :schedule_11d,
    #   :schedule_11e,
    #   :schedule_11f,
    #   :schedule_11g,
    #   :schedule_11h,
    #   :schedule_11j,
    #   :schedule_11k,
    #   :schedule_12,
    #   :schedule_13,
    #   :schedule_14,
    #   :schedule_15,
    #   :schedule_17b,
    #   :schedule_17c,
    # :schedule_17e,
     # :schedule_18b,
    #   :schedule_18c,
    #    :schedule_18d,
    #    :schedule_19,
    #   :schedule_20a,
    #   :schedule_21b,
      #
       #:schedule_21c,
    #   :schedule_22a,
    #   :schedule_22b,
    #   :schedule_23a,
    #   :schedule_23b,
    #   :schedule_24,
    #   :schedule_25,
    #    :schedule_26,

    #  :schedule_27,
    #    :schedule_27a,
    #   :schedule_28a,
       #:schedule_28b,
      # :schedule_29a,
      # :schedule_30b,
      # :schedule_30b1,
      # :schedule_30c,
      # :schedule_30c1,
      # :schedule_30d,
      # :schedule_31c,
      # :schedule_31d,
      # :schedule_31e,
      # :schedule_31f,
       :schedule_32a
#:schedule_18a,

    ])
    |> Enum.reduce([], fn {key, value}, acc ->
      result =
        try do
          schedule_data =
            case key do
              :bal_sheet -> MisReports.Workers.BozReq.BalanceSheet.perform(item)
              :income_stmt -> MisReports.Workers.BozReq.IncomeStatement.perform(item)
              :schedule_02a1 -> MisReports.Workers.BozReq.Schedule2a1.perform(item)
              :schedule_02c -> MisReports.Workers.BozReq.Schedule2c.perform(item)
              :schedule_02g -> MisReports.Workers.BozReq.Schedule02g.perform(item)
              :schedule_02h -> MisReports.Workers.BozReq.Schedule2h.perform(item)
              :schedule_03a -> MisReports.Workers.BozReq.Schedule03a.perform(item)
              :schedule_05b -> MisReports.Workers.BozReq.Schedule5b.perform(item)
              :schedule_07a -> MisReports.Workers.BozReq.Schedule7a.perform(item)
              :schedule_13 -> MisReports.Workers.BozReq.Schedule13.perform(item)
              :schedule_14 -> MisReports.Workers.BozReq.Schedule14.perform(item)
              :schedule_17b -> MisReports.Workers.BozReq.Schedule17b.perform(item)
              :schedule_04b -> MisReports.Workers.BozReq.Schedule04b.perform(item)
              :schedule_04d -> MisReports.Workers.BozReq.Schedule04d.perform(item)
              :schedule_12 -> MisReports.Workers.BozReq.Schedule12.perform(item)
              :schedule_15 -> MisReports.Workers.BozReq.Schedule15.perform(item)
              :schedule_30b -> MisReports.Workers.BozReq.Schedule30b.perform(item)
              :schedule_30d -> MisReports.Workers.BozReq.Schedule30d.perform(item)
              :schedule_31c -> MisReports.Workers.BozReq.Schedule31c.perform(item)
              :schedule_31d -> MisReports.Workers.BozReq.Schedule31d.perform(item)
              :schedule_31f -> MisReports.Workers.BozReq.Schedule31f.perform(item)
              :schedule_11d -> MisReports.Workers.BozReq.Schedule11d.perform(item)
              :schedule_11e -> MisReports.Workers.BozReq.Schedule11e.perform(item)
              :schedule_11f -> MisReports.Workers.BozReq.Schedule11f.perform(item)
              :schedule_11j -> MisReports.Workers.BozReq.Schedule11j.perform(item)
              :schedule_11g -> MisReports.Workers.BozReq.Schedule11g.perform(item)
              :schedule_01c -> MisReports.Workers.BozReq.Schedule01c.perform(item)
              :schedule_01e -> MisReports.Workers.BozReq.Schedule01e.perform(item)
              :schedule_01f -> MisReports.Workers.BozReq.Schedule01f.perform(item)
              :schedule_29a -> MisReports.Workers.BozReq.Schedule29a.perform(item)
              :schedule_18d -> MisReports.Workers.BozReq.Schedule18d.perform(item)
              :schedule_32a -> MisReports.Workers.BozReq.Schedule32a.perform(item)
              :schedule_21c -> MisReports.Workers.BozReq.Schedule21c.perform(item)
              :schedule_28b -> MisReports.Workers.BozReq.Schedule28b.perform(item)
              :schedule_27a -> MisReports.Workers.BozReq.Schedule27a.perform(item)
              :schedule_26 -> MisReports.Workers.BozReq.Schedule26.perform(item)
              :schedule_19 -> MisReports.Workers.BozReq.Schedule19.perform(item)
              :schedule_17e -> MisReports.Workers.BozReq.Schedule17e.perform(item)
             :schedule_18a -> MisReports.Workers.BozReq.Schedule17e.perform(item)
              _ -> %{}
            end

          case post_data(schedule_data, token, settings) do
            {response, response_body, json} ->
              save_schedule_result(key, response, response_body, json, item.id)

            error ->
              Logger.error("Failed to post schedule #{key}: #{inspect(error)}")
              {:error, %{schedule: key, message: "Failed to process schedule", error: error}}
          end
        rescue
          e ->
            Logger.error("Error processing schedule #{key}: #{Exception.message(e)}")
            {:error, %{schedule: key, message: "Runtime error", error: e.message}}
        catch
          kind, reason ->
            Logger.error(
              "Unexpected error in schedule #{key}: #{inspect(kind)} - #{inspect(reason)}"
            )

            {:error,
             %{schedule: key, message: "Unexpected error", error: "#{kind}: #{inspect(reason)}"}}
        end

      [result | acc]
    end)
    |> Enum.reverse()
  end

  # Helper function to handle the database save operation
  defp save_schedule_result(key, response, response_body, json, prudential_id) do
    changeset =
      BsaMaster.changeset(%BsaMaster{}, %{
        schedule: Atom.to_string(key),
        content: json,
        start_date:
          if(is_map(response_body), do: Map.get(response_body, "startDate", nil), else: nil),
        end_date:
          if(is_map(response_body), do: Map.get(response_body, "endDate", nil), else: nil),
        filename:
          if(is_map(response_body), do: Map.get(response_body, "filename", nil), else: nil),
        status: if(is_map(response_body), do: Map.get(response_body, "status", nil), else: nil),
        auth_status: "PENDING",
        prudential_id: prudential_id,
        response: Poison.encode!(response)
      })

    case Repo.insert(changeset) do
      {:ok, _result} ->
        %{message: "Record inserted successfully for schedule #{key}"}

      {:error, changeset_error} ->
        Logger.error("Database error for schedule #{key}: #{inspect(changeset_error.errors)}")
        %{message: "Database error for schedule #{key}", errors: changeset_error.errors}
    end
  end

  defp post_data(item, token, settings) do
    headers = [
      {"Content-Type", "application/json"},
      {"Authorization", "Bearer #{token}"},
      timeout: 199_600_000
    ]

    req = Poison.encode!(item)

    IO.inspect(HTTPoison.post("#{settings.post_url}", req, headers), label: "REQUEST=========")

    case HTTPoison.post("#{settings.post_url}", req, headers) do
      {:ok, %HTTPoison.Response{status_code: 200, body: body}} ->
        {:ok, Poison.decode!(body), req}

      {:ok, %HTTPoison.Response{status_code: 404, body: _body}} ->
        {:error, "Not found: does not exist", req}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 400..499 ->
        {:error, "Client error: #{status_code}", req}

      {:ok, %HTTPoison.Response{status_code: status_code}} when status_code in 500..599 ->
        {:error, "Server error: #{status_code}", req}

      {:error, %HTTPoison.Error{reason: reason}} ->
        {:error, reason, req}

      {:error, %HTTPoison.Response{status_code: status_code}} ->
        {:error, "HTTP error: #{status_code}", req}

      _err ->
        IO.inspect(req, label: "REQ")
        {:error, "Unknown error occurred", req}
    end
    |> handle_response()
  end

  defp handle_response({:error, rep, req}) do
    {%{response: "Unknown error occurred", status: "FAILED"}, rep, req}
  end

  defp handle_response({_atom, %FunctionClauseError{module: Decimal} = error, req}) do
    error_message = %{
      response: "Invalid decimal value",
      status: "FAILED",
      # Added message key
      message: "Invalid decimal value",
      details: %{
        module: error.module,
        function: error.function,
        arity: error.arity,
        args: error.args,
        error_type: :decimal_conversion
      }
    }

    {error_message, error, req}
  end

  defp handle_response({_atom, error = %FunctionClauseError{}, req}) do
    error_message = %{
      response: "Function clause error",
      status: "FAILED",
      # Added message key
      message: "Function clause error",
      details: %{
        module: error.module,
        function: error.function,
        arity: error.arity,
        error_type: :function_clause
      }
    }

    {error_message, error, req}
  end

  # Add a new catch-all handler
  defp handle_response({_atom, error, req}) when is_map(error) do
    error_message =
      Map.merge(
        %{
          message: "Error occurred",
          response: "Unknown error",
          status: "FAILED"
        },
        error
      )

    {error_message, error, req}
  end

  def extract_error_message(%FunctionClauseError{} = error) do
    %{
      response: "Function clause error in #{error.module}.#{error.function}/#{error.arity}",
      status: "FAILED"
    }
  end

  def extract_error_message(error) when is_exception(error) do
    %{
      response: Exception.message(error),
      status: "FAILED"
    }
  end

  def extract_error_message(error_map) do
    case error_map do
      %{"errors" => errors} when is_map(errors) ->
        Enum.map(errors, fn {key, messages} -> "#{key}: #{Enum.join(messages, ", ")}" end)
        |> Enum.join("\n")
        |> prepare_reponse_params("FAILED")

      %{"errors" => [%{"errorMessage" => response}]} ->
        prepare_reponse_params(response, "FAILED")

      %{"" => %{"errors" => _errors}} = errors ->
        Enum.flat_map(errors, fn {_key, value} -> value["errors"] end)
        |> Enum.map(fn error -> error["errorMessage"] end)
        |> Enum.join(", ")
        |> prepare_reponse_params("FAILED")

      %{"returnKey" => _resp, "filename" => filename} ->
        prepare_reponse_params(filename, "SUCCESS")

      _ ->
        prepare_reponse_params("Unknown error format", "FAILED")
    end
  end

  defp prepare_reponse_params(response, status) do
    %{response: response, status: status}
  end

  def test() do
    map = %{"bal_sheet" => 1, "income_stmt" => 2, "nil" => "nil"}

    Enum.map(map, fn {key, value} ->
      # result =
      case key do
        "bal_sheet" -> "balance sheet #{value}"
        # Replace with appropriate logic if needed
        "income_stmt" -> "income statement #{value}"
        # Fallback for unmatched keys
        _ -> nil
      end

      # result
      # |> post_data(token, settings)
    end)
  end
end
