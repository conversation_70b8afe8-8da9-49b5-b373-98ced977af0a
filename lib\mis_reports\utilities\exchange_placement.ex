defmodule MisReports.Utilities.ExchangePlacement do
  use Ecto.Schema
  import Ecto.Changeset

  schema "tbl_fcy_exchange_placement_inst" do
    field :institution_name, :string
    field :maximum, :string
    field :rating, :string
    field :rating_agency, :string
    field :report_date, :date
    field :status, :string, default: "D"
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(exchange_placement, attrs) do
    exchange_placement
    |> cast(attrs, [:institution_name, :rating, :rating_agency, :maximum, :report_date, :status, :checker_id, :maker_id, :reference])
    |> validate_required([:institution_name, :rating, :rating_agency, :maximum, :report_date])
  end
end
