defmodule MisReports.Workers.BozReq.BalanceSheet do

  def perform(item) do

    decoded_item =
    case item.bal_sheet do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    decoded_item = format_map(decoded_item)
    # IO.inspect(decoded_item, label: "===============================", limit: :infinity)

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    # %{l
    #   "ReturnKey" => "DEMOBSO1001",
    #   "InstCode" => "#{settings.institution_code}",
    #   "FinYear" => 2024,
    #   "StartDate" => "2024-01-01",
    #   "EndDate" => "2024-01-31",
    #   "ReturnItemsList" => [
    #     %{
    #       "Code" => "BSO1001_00001",
    #       "Value" => "6",
    #       "_dataType" => "NUMERIC"
    #     },
    #     %{
    #       "Code" => "BSO1001_00002",
    #       "Value" => "4",
    #       "_dataType" => "NUMERIC"
    #     }
    #     # ... (rest of the return items would follow the same pattern)
    #   ]
    # }

    %{
      "ReturnKey" => "ZM-7VBS7V002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => format_list(decoded_item) |> replace_empty_values(),
      "DynamicItemsList" => []
    }
  end
  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  defp format_list(decoded_item) do
    [
      %{
        "Code" => "1128_00001",
        "Value" => "#{decoded_item["B11"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00002",
        "Value" => "#{decoded_item["B12"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00003",
        "Value" => "#{decoded_item["B13"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00004",
        "Value" => "#{decoded_item["B14"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00005",
        "Value" => "#{decoded_item["B15"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00006",
        "Value" => "#{decoded_item["B16"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00007",
        "Value" => "#{decoded_item["B17"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00008",
        "Value" => "#{decoded_item["B18"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00009",
        "Value" => "#{decoded_item["B19"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00010",
        "Value" => "#{decoded_item["B20"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00011",
        "Value" => "#{decoded_item["B21"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00012",
        "Value" => "#{decoded_item["B22"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00013",
        "Value" => "#{decoded_item["B23"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00014",
        "Value" => "#{decoded_item["B24"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00015",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00016",
        "Value" => "#{decoded_item["B26"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00017",
        "Value" => "#{decoded_item["B27"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00018",
        "Value" => "#{decoded_item["B28"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00019",
        "Value" => "#{decoded_item["B29"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00020",
        "Value" => "#{decoded_item["B30"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00021",
        "Value" => "#{decoded_item["B31"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00022",
        "Value" => "#{decoded_item["B32"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00023",
        "Value" => "#{decoded_item["B33"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00024",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00025",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00026",
        "Value" => "#{decoded_item["B37"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00027",
        "Value" => "#{decoded_item["B38"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00028",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00029",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00030",
        "Value" => "#{decoded_item["B41"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00031",
        "Value" => "#{decoded_item["B42"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00032",
        "Value" => "#{decoded_item["B43"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00033",
        "Value" => "#{decoded_item["B44"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00034",
        "Value" => "#{decoded_item["B45"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00035",
        "Value" => "#{decoded_item["B46"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00036",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00037",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00038",
        "Value" => "#{decoded_item["B49"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00039",
        "Value" => "#{decoded_item["B50"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00040",
        "Value" => "#{decoded_item["B51"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00041",
        "Value" => "#{decoded_item["B52"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00042",
        "Value" => "#{decoded_item["B53"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00043",
        "Value" => "#{decoded_item["B54"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00044",
        "Value" => "#{decoded_item["B55"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00045",
        "Value" => "#{decoded_item["B56"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00046",
        "Value" => "#{decoded_item["B57"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00047",
        "Value" => "#{decoded_item["B58"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00048",
        "Value" => "#{decoded_item["B59"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00049",
        "Value" => "#{decoded_item["B60"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00050",
        "Value" => "#{decoded_item["B63"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00051",
        "Value" => "#{decoded_item["B64"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00052",
        "Value" => "#{decoded_item["B65"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00053",
        "Value" => "#{decoded_item["B66"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00054",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00055",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00056",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00057",
      #   "Value" => "#{decoded_item["B70"]}",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00058",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00059",
        "Value" => "#{decoded_item["B72"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00060",
        "Value" => "#{decoded_item["B73"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00061",
        "Value" => "#{decoded_item["B74"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00062",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00063",
        "Value" => "#{decoded_item["B76"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00064",
        "Value" => "#{decoded_item["B77"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00065",
        "Value" => "#{decoded_item["B78"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00066",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00067",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00068",
        "Value" => "#{decoded_item["B81"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00069",
        "Value" => "#{decoded_item["B82"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00070",
        "Value" => "#{decoded_item["B83"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00071",
        "Value" => "#{decoded_item["B84"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00072",
        "Value" => "#{decoded_item["B85"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00073",
        "Value" => "#{decoded_item["B86"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00074",
        "Value" => "#{decoded_item["B87"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00075",
        "Value" => "#{decoded_item["B88"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00076",
        "Value" => "#{decoded_item["B89"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00077",
        "Value" => "#{decoded_item["B90"]}",
        "_dataType" => "NUMERIC"
      },

      %{
        "Code" => "1128_00078",
        "Value" => "#{decoded_item["B91"]}",
        "_dataType" => "NUMERIC"
      },

      %{
        "Code" => "1128_00080",
        "Value" => "#{decoded_item["B92"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00081",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00082",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00082",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00083",
        "Value" => "#{decoded_item["B96"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00084",
        "Value" => "#{decoded_item["B97"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_00085",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00086",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00087",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00089",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_00090",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      %{
        "Code" => "1128_00091",
        "Value" => "#{decoded_item["B100"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00092",
        "Value" => "#{decoded_item["B101"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00093",
        "Value" => "#{decoded_item["B102"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00094",
        "Value" => "#{decoded_item["B103"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00095",
        "Value" => "#{decoded_item["B104"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00096",
        "Value" => "#{decoded_item["B105"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00097",
        "Value" => "#{decoded_item["B107"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00098",
        "Value" => "#{decoded_item["B108"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00099",
        "Value" => "#{decoded_item["B109"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1128_00100",
        "Value" => "#{decoded_item["B110"]}",
        "_dataType" => "NUMERIC"
      },
      # %{
      #   "Code" => "1128_000101",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000102",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000103",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000104",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000105",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000106",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # },
      # # %{
      # #   "Code" => "1128_00107",
      # #   "Value" => "#{decoded_item["B117"]}",
      # #   "_dataType" => "NUMERIC"
      # },
      # %{
      #   "Code" => "1128_000108",
      #   "Value" => "0",
      #   "_dataType" => "NUMERIC"
      # }
    ]
  end

  def replace_empty_values(list_of_maps) do
    Enum.map(list_of_maps, fn map ->
      if map["Value"] == "" do
        Map.put(map, "Value", "0")
      else
        map
      end
    end)
  end

end
