<div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
  <div class="mt-5 font-semibold text-xl">Branch Maintenance</div>
  <%= if @live_action == :new do %>
    <div class="text-sm">New Branch </div>
  <% end %>
  <%= if @live_action == :edit do %>
    <div class="text-sm">Edit Branch </div>
  <% end %>
  <%= if @live_action == :index do %>
    <div class="text-sm">View Branches </div>
  <% end %>
  <div class="mt-5">
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} /> 
    
    <%= if @live_action == :update_status do %>
      <.live_component 
        module={MisReportsWeb.BranchLive.BranchComponent} 
        id={@branch.id}
        current_user={@current_user} 
        branch={@branch}
        changeset={@changeset}
        provinces={@provinces}
        process_id={@process_id}
        reference={@reference}
        step_id={@step_id}
        action={@live_action}
      />
    <% end %>

    <.live_component 
      :if={@live_action in [:new, :edit]} 
      module={MisReportsWeb.BranchLive.BranchComponent} 
      id={@branch.id || :new}
      current_user={@current_user} 
      branch={@branch}
      provinces={@provinces}
      process_id={@process_id}
      reference={@reference}
      step_id={@step_id}
      action={@live_action} 
    />
    
    <%= if @live_action == :index do %>
      <%= Phoenix.View.render(MisReportsWeb.BranchView, "branches.html", assigns) %>
    <% end %>

    <.confirm_modal /> <.info_notification /> <.error_notification />
  </div>
</div>





























