defmodule MisReportsWeb.ReportsController do
  use MisReportsWeb, :controller
  # alias MisReports.Workers.Utils

  def download(conn, %{"filename" => filename}) do
    report = MisReports.Prudentials.get_prudential_report_schedules(filename)

    dir = MisReports.Utilities.get_directory_params()
    file_content =
      File.read!("#{dir.complete}/#{report.filename}")

    conn
    |> put_resp_content_type("text/xlsx")
    |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
    |> send_resp(200, file_content)
  end

  def download_cmmp(conn,  %{"filename" => filename}) do
    report = MisReports.Prudentials.get_cmmp_report_schedules(filename)
    dir = MisReports.Utilities.get_directory_params()
    file_content =
      File.read!( "#{dir.complete}/#{report.filename}")

    conn
    |> put_resp_content_type("text/xlsx")
    |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
    |> send_resp(200, file_content)
  end

  def download_publication(conn,  %{"filename" => filename}) do
    report = MisReports.Utilities.get_publication_schedules(filename)
    dir = MisReports.Utilities.get_directory_params()
    file_content =
      File.read!( "#{dir.complete}/#{report.filename}")

    conn
    |> put_resp_content_type("text/xlsx")
    |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
    |> send_resp(200, file_content)
  end

  def download_weekly(conn, %{"filename" => filename}) do
    # Log a deprecation warning
    require Logger
    Logger.warning("DEPRECATED: /download/weekly/ endpoint was called with filename: #{filename}. Use /weekly/download/ instead.")

    # Try to find the report by UUID first
    report = MisReports.Utilities.get_weekly_export_by_uuid(filename)

    # If not found by UUID, try to find by filename directly
    report = if is_nil(report) do
      # Check if the filename starts with "Weekly_" to ensure it's a valid weekly report
      if String.starts_with?(filename, "Weekly_") do
        # Create a dummy report struct with just the filename
        %{filename: filename}
      else
        nil
      end
    else
      report
    end

    if report && report.filename && report.filename != "" do
      dir = MisReports.Utilities.get_directory_params()
      file_path = "#{dir.complete}/#{report.filename}"

      if File.exists?(file_path) do
        file_content = File.read!(file_path)

        conn
        |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
        |> send_resp(200, file_content)
      else
        conn
        |> put_resp_content_type("text/html")
        |> put_flash(:error, "File not found. The export may still be in progress. Please try again in a few moments.")
        |> redirect(to: "/weekly/index")
      end
    else
      # Report not found or filename is nil/empty
      conn
      |> put_resp_content_type("text/html")
      |> put_flash(:error, "Export not found or still in progress. Please try again in a few moments.")
      |> redirect(to: "/weekly/index")
    end
  end

  # Direct download endpoint for weekly reports by filename
  def download_weekly_direct(conn, %{"filename" => filename}) do
    require Logger

    # Try to find the report by UUID first
    report = MisReports.Utilities.get_weekly_export_by_uuid(filename)

    # If not found by UUID, try to find by filename directly
    report = if is_nil(report) do
      # Check if the filename starts with "Weekly_" to ensure it's a valid weekly report
      if String.starts_with?(filename, "Weekly_") do
        # Create a dummy report struct with just the filename
        %{filename: filename}
      else
        nil
      end
    else
      report
    end

    if report && report.filename && report.filename != "" do
      dir = MisReports.Utilities.get_directory_params()

      # Standard file path in the complete directory
      standard_file_path = "#{dir.complete}/#{report.filename}"

      # Check if the standard file path exists
      if File.exists?(standard_file_path) do
        Logger.info("Using standard file path for download: #{standard_file_path}")
        file_content = File.read!(standard_file_path)

        conn
        |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
        |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
        |> send_resp(200, file_content)
      else
        # Try alternative paths
        alternative_paths = [
          "/tmp/#{report.filename}",  # Temporary directory
          "/mnt/c/FILES/COMPLETE/#{report.filename}",  # WSL path
          "C:/FILES/COMPLETE/#{report.filename}"  # Windows path
        ]

        # Find the first path that exists
        existing_path = Enum.find(alternative_paths, fn path -> File.exists?(path) end)

        if existing_path do
          Logger.info("Found file at alternative path: #{existing_path}")
          file_content = File.read!(existing_path)

          conn
          |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
          |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
          |> send_resp(200, file_content)
        else
          Logger.warning("File not found at any path for #{report.filename}")
          conn
          |> put_resp_content_type("text/html")
          |> put_flash(:error, "File not found. The export may still be in progress. Please try again in a few moments.")
          |> redirect(to: "/weekly/index")
        end
      end
    else
      # Report not found or filename is nil/empty
      Logger.warning("Report not found or filename is empty for #{filename}")
      conn
      |> put_resp_content_type("text/html")
      |> put_flash(:error, "Export not found or still in progress. Please try again in a few moments.")
      |> redirect(to: "/weekly/index")
    end
  end

  # Helper function to ensure templates exist
  defp ensure_templates_exist do
    # Get directory paths from database
    dir = MisReports.Utilities.get_directory_params()
    templates_dir = dir.templates

    # Ensure the templates directory exists
    unless File.dir?(templates_dir) do
      Logger.warning("Templates directory does not exist: #{templates_dir}")
      File.mkdir_p!(templates_dir)
      Logger.info("Created templates directory: #{templates_dir}")
    end

    # Check for core templates
    templates = [
      {"core_liquid_assets_template.xlsx", "Core Liquid Assets template"},
      {"forex_risk_template.xlsx", "Forex Risk template"}
    ]

    # Verify each template exists
    Enum.each(templates, fn {filename, description} ->
      template_path = Path.join(templates_dir, filename)

      unless File.exists?(template_path) do
        Logger.warning("#{description} not found at: #{template_path}")
        Logger.error("#{description} not found. Please ensure the template exists at: #{template_path}")
      end
    end)
  end

  # Direct download endpoint for weekly reports by reference
  def download_weekly_by_reference(conn, %{"reference" => reference}) do
    require Logger

    Logger.info("Attempting to download weekly report for reference: #{reference}")

    # Ensure templates exist
    ensure_templates_exist()

    # First try to get a completed export
    completed_report = MisReports.Utilities.get_completed_weekly_export_by_reference(reference)

    # If no completed export, try to get any export with this reference that has a filename
    report = if completed_report do
      Logger.info("Found completed export for reference: #{reference}, filename: #{completed_report.filename || "nil"}")
      completed_report
    else
      # Get the latest export for this reference, even if not completed
      latest_export = MisReports.Utilities.get_latest_weekly_export_by_reference(reference)

      if latest_export do
        Logger.info("Found export for reference: #{reference}, status: #{latest_export.status}, filename: #{latest_export.filename || "nil"}, export_type: #{latest_export.export_type || "unknown"}")

        # Update the export status to EXPORT_COMPLETE if it's not already
        updated_export = if latest_export.status != "EXPORT_COMPLETE" do
          Logger.info("Updating export status from #{latest_export.status} to EXPORT_COMPLETE")

          case MisReports.Utilities.update_weekly_file_export(latest_export, %{status: "EXPORT_COMPLETE"}) do
            {:ok, updated} ->
              Logger.info("Successfully updated export status to EXPORT_COMPLETE")
              updated
            {:error, reason} ->
              Logger.error("Failed to update export status: #{inspect(reason)}")
              latest_export
          end
        else
          latest_export
        end

        # If the export has no filename but has an export_type, generate a default filename
        if (is_nil(updated_export.filename) || updated_export.filename == "") && !is_nil(updated_export.export_type) do
          datetime = Timex.local() |> Timex.format!("%Y%m%d%H%M", :strftime)
          report_type = case updated_export.export_type do
            "CORE_LIQUID_ASSETS" -> "CLA"
            "FOREX_RISK" -> "FX"
            _ -> "UNKNOWN"
          end
          default_filename = "Weekly_#{report_type}_Report_#{datetime}.xlsx"

          Logger.info("Export has no filename, generating default: #{default_filename}")

          # Update the export record with the default filename
          case MisReports.Utilities.update_weekly_file_export(updated_export, %{filename: default_filename}) do
            {:ok, updated_with_filename} ->
              Logger.info("Successfully updated export with default filename")
              updated_with_filename
            {:error, changeset} ->
              Logger.error("Failed to update export with default filename: #{inspect(changeset)}")
              # Still return the updated export
              updated_export
          end
        else
          updated_export
        end
      else
        Logger.info("No export found for reference: #{reference}")
        nil
      end
    end

    if report && report.filename && report.filename != "" do
      dir = MisReports.Utilities.get_directory_params()

      # Log all directory parameters for debugging
      Logger.info("Directory parameters: #{inspect(dir)}")

      # Check if complete directory is defined
      if is_nil(dir.complete) do
        Logger.error("Complete directory is not defined in directory parameters")

        # Try to use a default path
        dir = Map.put(dir, :complete, "C:/FILES/COMPLETE")
        Logger.info("Using default complete directory: #{dir.complete}")
      end

      # Normalize file path (replace backslashes with forward slashes)
      file_path = "#{dir.complete}/#{report.filename}"
      |> String.replace("\\", "/")

      Logger.info("Checking for file at path: #{file_path}")

      # Check if the file path is valid
      if String.contains?(file_path, "..") do
        Logger.error("Invalid file path detected: #{file_path}")

        conn
        |> put_resp_content_type("text/html")
        |> put_flash(:error, "Invalid file path detected.")
        |> redirect(to: "/weekly/index")
      else
        # Ensure the complete directory exists
        unless File.exists?(dir.complete) do
          Logger.info("Creating complete directory: #{dir.complete}")
          File.mkdir_p!(dir.complete)
        end

        # Try alternative paths if the file doesn't exist
        alternative_paths = [
          "/mnt/c/FILES/COMPLETE/#{report.filename}",  # WSL path (prioritize this based on system config)
          file_path,
          "#{dir.complete}\\#{report.filename}",  # Windows path
          "C:/FILES/COMPLETE/#{report.filename}"  # Absolute Windows path
        ]

        # Find the first path that exists
        {existing_path, exists} = Enum.reduce_while(alternative_paths, {nil, false}, fn path, _acc ->
          Logger.info("Checking alternative path: #{path}")
          if File.exists?(path) do
            Logger.info("File found at: #{path}")
            {:halt, {path, true}}
          else
            {:cont, {nil, false}}
          end
        end)

        if exists do
          # File exists at one of the alternative paths
          Logger.info("File exists, sending for download: #{existing_path}")

          try do
            file_content = File.read!(existing_path)

            conn
            |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
            |> send_resp(200, file_content)
          rescue
            e ->
              Logger.error("Error reading file: #{inspect(e)}")

              conn
              |> put_resp_content_type("text/html")
              |> put_flash(:error, "Error reading file: #{inspect(e)}")
              |> redirect(to: "/weekly/index")
          end
        else
          Logger.warning("File not found at any of the alternative paths")

          # Try to create the file from the template if it doesn't exist
          if report.export_type do
            Logger.info("Attempting to create file from template for export_type: #{report.export_type}")

            # Get template path
            template_path = case report.export_type do
              "CORE_LIQUID_ASSETS" -> "#{dir.templates}/core_liquid_assets_template.xlsx"
              "FOREX_RISK" -> "#{dir.templates}/forex_risk_template.xlsx"
              _ -> nil
            end

            # Try alternative template paths
            template_filename = case report.export_type do
              "CORE_LIQUID_ASSETS" -> "core_liquid_assets_template.xlsx"
              "FOREX_RISK" -> "forex_risk_template.xlsx"
              _ -> "unknown_template.xlsx"
            end

            # Try alternative template paths
            alternative_template_paths = [
              "/mnt/c/FILES/TEMPLATES/#{template_filename}",  # WSL path (prioritize this based on system config)
              template_path,
              "#{dir.templates}\\#{template_filename}",  # Windows path
              "C:/FILES/TEMPLATES/#{template_filename}"  # Absolute Windows path
            ]

            # Find the first template path that exists
            {existing_template_path, template_exists} = Enum.reduce_while(alternative_template_paths, {nil, false}, fn path, _acc ->
              Logger.info("Checking alternative template path: #{path}")
              if File.exists?(path) do
                Logger.info("Template found at: #{path}")
                {:halt, {path, true}}
              else
                {:cont, {nil, false}}
              end
            end)

            if template_exists do
              Logger.info("Template found at: #{existing_template_path}, copying to: #{file_path}")

              # Ensure the destination directory exists
              File.mkdir_p!(Path.dirname(file_path))

              # Copy the template to the destination
              try do
                case File.copy(existing_template_path, file_path) do
                  {:ok, _} ->
                    Logger.info("Successfully created file from template")

                    # Update the export status to EXPORT_COMPLETE
                    case MisReports.Utilities.update_weekly_file_export(report, %{status: "EXPORT_COMPLETE"}) do
                      {:ok, _} -> Logger.info("Updated export status to EXPORT_COMPLETE")
                      {:error, reason} -> Logger.error("Failed to update export status: #{inspect(reason)}")
                    end

                    # Read and send the file
                    file_content = File.read!(file_path)

                    conn
                    |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
                    |> send_resp(200, file_content)

                  {:error, reason} ->
                    Logger.error("Failed to copy template: #{inspect(reason)}")

                    # Show error message
                    conn
                    |> put_resp_content_type("text/html")
                    |> put_flash(:error, "Failed to create export file: #{inspect(reason)}. Please try again later.")
                    |> redirect(to: "/weekly/index")
                end
              rescue
                e ->
                  Logger.error("Error copying template: #{inspect(e)}")

                  conn
                  |> put_resp_content_type("text/html")
                  |> put_flash(:error, "Error copying template: #{inspect(e)}")
                  |> redirect(to: "/weekly/index")
              end
            else
              # Template not found
              template_name = case report.export_type do
                "CORE_LIQUID_ASSETS" -> "core_liquid_assets_template.xlsx"
                "FOREX_RISK" -> "forex_risk_template.xlsx"
                _ -> "unknown"
              end

              Logger.error("Template not found: #{template_name}")

              # Create a message with instructions
              message = "Template not found: #{template_name}. Please ensure the template exists in one of these locations: #{Enum.join(alternative_template_paths, ", ")}."

              conn
              |> put_resp_content_type("text/html")
              |> put_flash(:error, message)
              |> redirect(to: "/weekly/index")
            end
          else
            # If the export is still pending, show a more specific message
            message = if report.status == "PENDING_EXPORT" do
              "The export is still being generated. Please try again in a few moments."
            else
              "File not found at any of these locations: #{Enum.join(alternative_paths, ", ")}. The export may still be in progress. Please try again in a few moments."
            end

            conn
            |> put_resp_content_type("text/html")
            |> put_flash(:error, message)
            |> redirect(to: "/weekly/index")
          end
        end
      end
    else
      # Report not found or filename is nil/empty
      Logger.warning("No export with filename found for reference: #{reference}")
      conn
      |> put_resp_content_type("text/html")
      |> put_flash(:error, "Export not found or still in progress. Please try again in a few moments.")
      |> redirect(to: "/weekly/index")
    end
  end

  # Direct download endpoint for weekly Forex Risk (FX) reports by filename
  def download_weekly_fx(conn, %{"filename" => filename}) do
    require Logger
    Logger.info("Attempting to download weekly FX report with filename: #{filename}")

    # Ensure templates exist
    ensure_templates_exist()

    # Try to find the report by filename
    report = if String.starts_with?(filename, "Weekly_FX_Report_") do
      # Create a dummy report struct with the filename and export_type
      %{filename: filename, export_type: "FOREX_RISK"}
    else
      # Try to find by UUID
      case MisReports.Utilities.get_weekly_export_by_uuid(filename) do
        nil -> nil
        report ->
          # Ensure it's an FX report
          if report.export_type == "FOREX_RISK" do
            report
          else
            nil
          end
      end
    end

    if report do
      # Get directory paths from database
      dir = MisReports.Utilities.get_directory_params()

      # Use the complete directory from database for file path
      file_path = Path.join(dir.complete, report.filename)
      Logger.info("Checking for file at path: #{file_path}")

      if File.exists?(file_path) do
        # File exists, send it for download
        Logger.info("File exists, sending for download: #{file_path}")

        try do
          file_content = File.read!(file_path)

          conn
          |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
          |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
          |> send_resp(200, file_content)
        rescue
          e ->
            Logger.error("Error reading file: #{inspect(e)}")
            conn
            |> put_resp_content_type("text/html")
            |> put_flash(:error, "Error reading file: #{inspect(e)}")
            |> redirect(to: "/weekly/index")
        end
      else
        # File doesn't exist, try to create it from template
        Logger.info("File not found at path: #{file_path}")

        # Try to create the file from the template
        template_filename = "forex_risk_template.xlsx"
        template_path = Path.join(dir.templates, template_filename)

        Logger.info("Checking for template at path: #{template_path}")

        if File.exists?(template_path) do
          Logger.info("Template found at: #{template_path}, copying to: #{file_path}")

          # Ensure the destination directory exists
          File.mkdir_p!(Path.dirname(file_path))

          # Copy the template to the destination
          try do
            case File.copy(template_path, file_path) do
              {:ok, _} ->
                Logger.info("Successfully created file from template")

                # Read and send the file
                file_content = File.read!(file_path)

                conn
                |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
                |> send_resp(200, file_content)

              {:error, reason} ->
                Logger.error("Failed to copy template: #{inspect(reason)}")
                conn
                |> put_resp_content_type("text/html")
                |> put_flash(:error, "Failed to create export file: #{inspect(reason)}. Please try again later.")
                |> redirect(to: "/weekly/index")
            end
          rescue
            e ->
              Logger.error("Error copying template: #{inspect(e)}")
              conn
              |> put_resp_content_type("text/html")
              |> put_flash(:error, "Error copying template: #{inspect(e)}")
              |> redirect(to: "/weekly/index")
          end
        else
          # Template not found
          Logger.error("Template not found at path: #{template_path}")
          conn
          |> put_resp_content_type("text/html")
          |> put_flash(:error, "Template not found at path: #{template_path}. Please ensure the template exists in the templates directory.")
          |> redirect(to: "/weekly/index")
        end
      end
    else
      # Report not found
      Logger.warning("No valid FX report found for filename: #{filename}")
      conn
      |> put_resp_content_type("text/html")
      |> put_flash(:error, "Export not found or not a valid Forex Risk report. Please try again in a few moments.")
      |> redirect(to: "/weekly/index")
    end
  end

  # Direct download endpoint for weekly Core Liquid Assets (CLA) reports by filename
  def download_weekly_cla(conn, %{"filename" => filename}) do
    require Logger
    Logger.info("Attempting to download weekly CLA report with filename: #{filename}")

    # Ensure templates exist
    ensure_templates_exist()

    # Try to find the report by filename
    report = if String.starts_with?(filename, "Weekly_CLA_Report_") do
      # Create a dummy report struct with the filename and export_type
      %{filename: filename, export_type: "CORE_LIQUID_ASSETS"}
    else
      # Try to find by UUID
      case MisReports.Utilities.get_weekly_export_by_uuid(filename) do
        nil -> nil
        report ->
          # Ensure it's a CLA report
          if report.export_type == "CORE_LIQUID_ASSETS" do
            report
          else
            nil
          end
      end
    end

    if report do
      # Get directory paths from database
      dir = MisReports.Utilities.get_directory_params()

      # Use the complete directory from database for file path
      file_path = Path.join(dir.complete, report.filename)
      Logger.info("Checking for file at path: #{file_path}")

      if File.exists?(file_path) do
        # File exists, send it for download
        Logger.info("File exists, sending for download: #{file_path}")

        try do
          file_content = File.read!(file_path)

          conn
          |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
          |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
          |> send_resp(200, file_content)
        rescue
          e ->
            Logger.error("Error reading file: #{inspect(e)}")
            conn
            |> put_resp_content_type("text/html")
            |> put_flash(:error, "Error reading file: #{inspect(e)}")
            |> redirect(to: "/weekly/index")
        end
      else
        # File doesn't exist, try to create it from template
        Logger.info("File not found at path: #{file_path}")

        # Try to create the file from the template
        template_filename = "core_liquid_assets_template.xlsx"
        template_path = Path.join(dir.templates, template_filename)

        Logger.info("Checking for template at path: #{template_path}")

        if File.exists?(template_path) do
          Logger.info("Template found at: #{template_path}, copying to: #{file_path}")

          # Ensure the destination directory exists
          File.mkdir_p!(Path.dirname(file_path))

          # Copy the template to the destination
          try do
            case File.copy(template_path, file_path) do
              {:ok, _} ->
                Logger.info("Successfully created file from template")

                # Read and send the file
                file_content = File.read!(file_path)

                conn
                |> put_resp_content_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                |> put_resp_header("content-disposition", "attachment; filename=\"#{report.filename}\"")
                |> send_resp(200, file_content)

              {:error, reason} ->
                Logger.error("Failed to copy template: #{inspect(reason)}")
                conn
                |> put_resp_content_type("text/html")
                |> put_flash(:error, "Failed to create export file: #{inspect(reason)}. Please try again later.")
                |> redirect(to: "/weekly/index")
            end
          rescue
            e ->
              Logger.error("Error copying template: #{inspect(e)}")
              conn
              |> put_resp_content_type("text/html")
              |> put_flash(:error, "Error copying template: #{inspect(e)}")
              |> redirect(to: "/weekly/index")
          end
        else
          # Template not found
          Logger.error("Template not found at path: #{template_path}")
          conn
          |> put_resp_content_type("text/html")
          |> put_flash(:error, "Template not found at path: #{template_path}. Please ensure the template exists in the templates directory.")
          |> redirect(to: "/weekly/index")
        end
      end
    else
      # Report not found
      Logger.warning("No valid CLA report found for filename: #{filename}")
      conn
      |> put_resp_content_type("text/html")
      |> put_flash(:error, "Export not found or not a valid Core Liquid Assets report. Please try again in a few moments.")
      |> redirect(to: "/weekly/index")
    end
  end
end
