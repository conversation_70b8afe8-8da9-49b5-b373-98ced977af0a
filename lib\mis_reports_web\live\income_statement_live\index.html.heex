<style>
     .overflow{
     width: 300px;
     height: 55%;
     overflow: auto;
     max-height: calc(100vh - 200px); /* Adjust based on your needs */
     overflow-y: auto;
     overflow-x: hidden;
     padding-right: 0.5rem;
     scrollbar-width: thin;
     scrollbar-color: #e5e7eb transparent;
     }
     .overflow::-webkit-scrollbar {
     width: 6px;
     }
     .overflow::-webkit-scrollbar-track {
     background: transparent;
     }
     .overflow::-webkit-scrollbar-thumb {
     background-color: #e5e7eb;
     border-radius: 3px;
     }
    .linkselector{
       color:#0550C4;
    }
     .dropbtn {
     background-color: none;
     color: gray;
     padding: 5px;
     font-size: 16px;
     border: none;
     }
     .dropdown {
     position: relative;
     display: inline-block;
     }
     .dropdown-content {
     display: none;
     position: absolute;
     background-color: #fff;
     min-width: 160px;
     box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
     z-index: 100;
     }
     .dropdown-content a {
     color: black;
     padding: 8px 8px;
     text-decoration: none;
     display: block;
     }
     .dropdown-content a:hover {background-color: #ddd;}
     .dropdown:hover .dropdown-content {display: block;}
     .dropdown:hover .dropbtn {background-color: none;}
     .mains {
     border-bottom: 1px solid gray;
    }
    .linkselector{
       color:#0550C4;
    }
    .approval-buttons {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background-color: white;
      padding: 1rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      z-index: 50;
    }

    .content-wrapper {
      padding-bottom: 80px; /* Make space for fixed buttons */
    }

    .tab-navigation {
      border-bottom: 1px solid #e5e7eb;
      margin-bottom: 1rem;
    }

    .tab-button {
      padding: 0.75rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-bottom: 2px solid transparent;
      margin-right: 1rem;
      transition: all 0.3s;
    }

    .tab-button.active {
      border-bottom: 2px solid #4f46e5;
      color: #4f46e5;
    }

    .tab-button:hover:not(.active) {
      color: #6b7280;
    }

    .schedule-container {
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      padding: 1.5rem;
      position: relative;
      padding-top: 4rem;  /* Make space for the action buttons */
      scrollbar-width: none;  /* Firefox */
      -ms-overflow-style: none;  /* IE and Edge */
    }

    .schedule-container::-webkit-scrollbar {
      display: none;  /* Chrome, Safari, Opera */
    }

    .fullscreen-btn {
      padding: 0.5rem;
      border-radius: 0.375rem;
      background-color: #f3f4f6;
      color: #374151;
      transition: all 0.2s;
    }

    .fullscreen-btn:hover {
      background-color: #e5e7eb;
    }

    .exit-fullscreen-btn {

      padding: 0.5rem;
      border-radius: 0.375rem;
      background-color: #f3f4f6;
      color: #374151;
      z-index: 51;
    }

    .approval-buttons {
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: white;
      padding: 1rem;
      border-radius: 0.5rem;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      z-index: 50;
    }

    .actions-header {
      position: absolute;
      top: 1rem;
      right: 1rem;
      display: flex;
      gap: 0.75rem;
      z-index: 10;
    }

    .actions-container {
      position: absolute;
      top: 1rem;
      right: 1rem;
      display: flex;
      align-items: center;
      z-index: 50;
    }

    .button-group {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .fullscreen-btn {
      display: inline-flex;

      background-color: #f3f4f6;
      color: #374151;
      transition: all 0.2s;
      border: 1px solid #e5e7eb;

    }

    .fullscreen-btn:hover {
      background-color: #e5e7eb;
      color: #111827;
    }

    .btn-icon {
      width: 1.25rem;
      height: 1.25rem;
    }

    .btn-text {
      font-size: 0.875rem;
      font-weight: 500;
    }

    .comment-card {
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 1.5rem;
      margin-top: 1rem;
      position: sticky;
      bottom: 0;
      z-index: 40;
    }
  /* Add to your existing style block */
  html {
    scroll-behavior: smooth;
  }

  .scroll-mt-24 {
    scroll-margin-top: 6rem;
  }
    .comment-textarea {
      width: 100%;
      min-height: 80px;
      border: 1px solid #e5e7eb;
      border-radius: 0.375rem;
      padding: 0.75rem;
      margin-bottom: 1rem;
      resize: vertical;
    }

    .comment-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
    }

    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(50%);
      }
      40% {
        transform: translateY(-10px) translateX(50%);
      }
      60% {
        transform: translateY(-5px) translateX(50%);
      }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .fixed.inset-0.bg-gray-800.bg-opacity-50 {
      z-index: 99999;
    }

    /* Style for schedule tags in comments */
    .schedule-tag {
      display: inline-block;
      font-weight: bold;
      color: #0066cc;

      padding: 2px 6px;
      border-radius: 4px;
      margin-right: 4px;
    }

    .loader-overlay {
      position: fixed;
      inset: 0;
      background-color: rgba(255, 255, 255, 0.8);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 50;
    }

    .loader-spinner {
      width: 3rem;
      height: 3rem;
      border: 3px solid #e5e7eb;
      border-radius: 50%;
      border-top-color: #4f46e5;
      animation: spin 1s linear infinite;
    }

    .loader-text {
      margin-top: 1rem;
      color: #4f46e5;
      font-size: 0.875rem;
      font-weight: 500;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.2);
        opacity: 0.7;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translate(-50%, -40%);
      }
      to {
        opacity: 1;
        transform: translate(-50%, -50%);
      }
    }
  .helper-message {
    position: fixed;
    left: 50%;
    bottom: 2rem;
    transform: translateX(-50%);
    background: white;
    color: #4f46e5;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 50;
    text-decoration: none;
    transition: all 0.2s ease;
    border: 1px solid #e5e7eb;
  }

  .helper-message:hover {
    transform: translate(-50%, -2px);
    box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
    background: #f9fafb;
  }

  .helper-message svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #4f46e5;
  }

  .helper-message span {
    font-weight: 500;
    color: #374151;
  }

  .helper-message-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
    color: white;
    border: none;
  }

  .helper-message-primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #3730a3 100%);
  }

  .helper-message-primary svg,
  .helper-message-primary span {
    color: white;
  }
  @keyframes bounce {
    0%, 100% {
      transform: translateX(-50%) translateY(0);
    }
    50% {
      transform: translateX(-50%) translateY(-10px);
    }
  }
  .actions-container {
    position: absolute;
    top: 1rem;
    right: 1rem;
    display: flex;
    align-items: center;
    z-index: 50;
  }

  .button-group {
    display: flex;
    align-items: center;
    gap: 0.25rem; /* Reduced gap between buttons */
  }

  .action-btn, .fullscreen-btn {
   /* height: 32px;  Smaller height */
    display: inline-flex;
  }

  .btn-icon {
    width: 1rem; /* Smaller icons */
    height: 1rem;
  }

  .btn-text {
    font-size: 0.75rem;
    font-weight: 500;
  }

    .fullscreen {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      z-index: 9999 !important;
      margin: 0 !important;
      padding: 2rem !important;
      background: white !important;
      overflow-y: auto !important;
    }

    .schedule-container {
      position: relative;
      transition: all 0.3s ease;
    }

    .schedule-container.fullscreen {
      border-radius: 0;
      box-shadow: none;
    }
</style>

<%= if @show_helper and not @view_only do %>
  <% step_name = MisReports.Workflow.get_wklstep_rule!(@step_id).step_name %>
  <%= case step_name do %>
    <% step when step in ["Generate Finance Report Stage", "Generate Credit Report Stage"] -> %>
      <%= if @report_type != "" && !@helper_clicked do %>
        <a href="#submit-button" class="helper-message helper-message-primary" phx-click="helper_clicked">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
           <span>Click to go to the Submit Section</span>
        </a>
      <% end %>
    <% step when step in ["Credit Review Stage","Finance Review Level 1", "Review Level 2", "Final Report Approval"] -> %>
      <%= if @report_type != "" && !@helper_clicked do %>
        <a href="#comment-section" class="helper-message" phx-click="helper_clicked">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
          </svg>
           <span>Click to go to the Review Section</span>
        </a>
      <% end %>
  <% end %>
<% end %>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 p-6">
  <div class="lg:col-span-3">
    <div class={"schedule-container #{if @is_fullscreen, do: 'fullscreen'}"} id="schedule-content">
      <%= if @report_type == "" && MisReports.Workflow.get_wklstep_rule!(@step_id).step_name in  ["Generate Finance Report Stage", "Generate Credit Report Stage"] do %>
        <%= Phoenix.View.render(
          if(@current_view == "income_statement",
            do: MisReportsWeb.IncomeStatementView,
            else: MisReportsWeb.BalanceSheetView
          ),
          "report_filter.html",
          assigns
        ) %>
      <% else %>
        <!-- Action Buttons -->
        <div class="actions-container">
          <%= if @report_type != "" do %>
            <div class="row button-group">
              <%= if not @view_only do %>
                <% step = MisReports.Workflow.get_wklstep_rule!(@step_id).step_name %>
                <%= if Enum.member?(["Credit Review Stage", "Generate Finance Report Stage", "Generate Credit Report Stage", "Finance Review Level 1"], step) do %>
                  <div class="col">
                    <button type="button" phx-click="create_adjustment" class="action-btn rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500" title="Make Adjustment">
                      <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      <span class="btn-text">Make Adjustments</span>
                    </button>
                  </div>
                <% end %>

                <%= if Enum.member?(["Credit Review Stage", "Generate Finance Report Stage", "Generate Credit Report Stage", "Finance Review Level 1", "Review Level 2", "Final Report Approval"], step) do %>
                  <div class="col">
                    <a
                      href={Routes.adjustments_index_path(@socket, :update_status, %{
                        reference: @reference,
                        process_id: @process_id,
                        step_id: @step_id
                      })}
                      target="_blank"
                      rel="noopener noreferrer"
                      class="action-btn rounded-md bg-blue-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-700 inline-flex items-center"
                      title="View Adjustment"
                    >
                      <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      <span class="btn-text">View Adjustments</span>
                    </a>
                  </div>
                <% end %>
              <% end %>

              <div class="col">
                <button type="button" phx-click="toggle_fullscreen" class="fullscreen-btn" title={if @is_fullscreen, do: "Exit Fullscreen", else: "Fullscreen"}>
                  <%= if @is_fullscreen do %>
                    <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V9m0 10H5m4 0l-4-4m11-1V5m0 10h4m-4 0l4-4" />
                    </svg>
                     <span class="btn-text">Exit</span>
                  <% else %>
                    <svg class="btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 10h4m-4 0l-5-5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                     <span class="btn-text">Fullscreen</span>
                  <% end %>
                </button>
              </div>
            </div>
          <% end %>
        </div>
        <!-- Tab Navigation - Only show after report type is loaded -->
        <nav class="tab-navigation flex mb-6">
          <button phx-click="switch_view" phx-value-view="income_statement" class={"tab-button #{if @current_view == "income_statement", do: "active"}"}>
            Income Statement
          </button>

          <button phx-click="switch_view" phx-value-view="balance_sheet" class={"tab-button #{if @current_view == "balance_sheet", do: "active"}"}>
            Balance Sheet
          </button>

          <button phx-click="switch_view" phx-value-view="other_schedules" class={"tab-button #{if @current_view == "other_schedules", do: "active"}"}>
            Other Schedules
          </button>
        </nav>
        <!-- Content Area -->
        <%= case get_template(@current_view, @report_type, @balance_sheet_schedules, @income_statement_schedules, @other_schedules, @step_id) do %>
          <% nil -> %>
            <div class="loader-overlay">
              <div class="loader-spinner"></div>

              <p class="loader-text">Loading saved report data...</p>

              <p class="text-sm text-gray-500 mt-2">Please wait...</p>
            </div>
          <% template -> %>
           <%!-- <%= Phoenix.View.render(
             case @current_view do
                  "income_statement" -> MisReportsWeb.IncomeStatementView
                  "balance_sheet" -> MisReportsWeb.BalanceSheetView
                  "other_schedules" -> MisReportsWeb.OtherSchedulesView
                  _ -> MisReportsWeb.IncomeStatementView
                end,
              template,
              Map.merge(assigns, %{
                data: if @report_type in ["income_statement", "blc_sheet"] do
                    case @current_view do
                      "income_statement" -> @income_statement_data
                      "balance_sheet" -> @balance_sheet_data
                      "other_schedules" -> @other_schedules_data
                      _ -> @income_statement_data
                    end
                  else
                    # Use cached schedule data for specific schedules
                    get_in(@cached_schedules, [@report_type])
                  end
              })
            ) %> --%>
            <% debug_result = debug_template_render(@current_view, template, assigns,
              if(@report_type in ["income_statement", "blc_sheet"]) do
                case @current_view do
                  "income_statement" -> @income_statement_data
                  "balance_sheet" -> @balance_sheet_data
                  "other_schedules" -> @other_schedules_data
                  _ -> @income_statement_data
                end
              else
                get_in(@cached_schedules, [@report_type])
              end
            ) %>

            <%= debug_result %>
        <% end %>
      <% end %>
    </div>
  </div>
  <!-- Right Column - Schedule Navigation -->
  <%= if @report_type != "" do %>
    <div class="lg:col-span-1">
      <div class="schedule-container">
        <div class="mb-4">
          <h3 class="font-medium text-gray-900">
            <%= case @current_view do %>
              <% "balance_sheet" -> %>
                Balance Sheet Schedules
              <% "other_schedules" -> %>
                Other Schedules
              <% _ -> %>
                Income Statement Schedules
            <% end %>
          </h3>
        </div>

        <div class="overflow">
          <%= render_navigation_menu(%{
            report_type: @report_type,
            view_only: @view_only,
            current_view: @current_view
          }) %>
        </div>
      </div>
    </div>
  <% end %>
  <!-- Comment Card -->
  <%= if @report_type != "" do %>
    <div class="lg:col-span-4">
      <%= if MisReports.Workflow.get_wklstep_rule!(@step_id).step_name in ["Credit Review Stage", "Review Level 2","Finance Review Level 1", "Final Report Approval"] and @report_type != "" do %>
        <div class="comments-history mb-4">
          <div class="bg-white rounded-lg shadow p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center gap-2">
              <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
              </svg>
              Review History
            </h3>

            <%= if length(@comments) > 0 do %>
              <div class="space-y-4">
                <%= for %{action_id: action_id, comments: comments, created_at: created_at, step_id: step_id, user_name: user_name} <- @comments do %>
                  <div class="flex gap-4 p-3 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors">
                    <div class="flex-shrink-0">
                      <div class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                        <span class="text-sm font-medium text-indigo-600">
                          <%= String.slice(user_name || "", 0..1) %>
                        </span>
                      </div>
                    </div>

                    <div class="flex-1">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-sm font-medium text-gray-900">
                          <%= user_name %>
                        </span>

                        <span class="text-xs text-gray-500">
                          <%= NaiveDateTime.to_string(created_at) %>
                        </span>
                      </div>

                      <p class="text-sm text-gray-600"><%= format_comment_with_bold_schedules(comments) %></p>

                      <div class="mt-2 flex items-center gap-2">
                        <span class={"text-xs px-2 py-1 rounded-full #{
                              case action_id do
                                81 -> "bg-blue-100 text-blue-700"
                                80 -> "bg-yellow-100 text-yellow-700"
                                97 -> "bg-green-100 text-green-700"
                                96 -> "bg-red-100 text-red-700"
                                _ -> "bg-gray-100 text-gray-700"
                              end
                            }"}>
                          <%= case action_id do
                            81 -> "Initiated"
                            80 -> "Submitted"
                            97 -> "Approved"
                            96 -> "Rejected"
                            _ -> "Action #{action_id}"
                          end %>
                        </span>
                         <span class="text-xs text-gray-500">Step <%= step_id %></span>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-sm text-gray-500 text-center py-4">No comments history available</p>
            <% end %>
          </div>
        </div>
      <% end %>

      <div id="comment-section" class="comment-card scroll-mt-24 scroll-target">
        <%= if not @view_only do %>
          <%= case MisReports.Workflow.get_wklstep_rule!(@step_id).step_name  do %>
            <% step when step in ["Generate Finance Report Stage", "Generate Credit Report Stage"] -> %>
              <div class="flex justify-end">
                <button id="submit-button" type="submit" phx-click="submit_report" class="scroll-target rounded-md bg-indigo-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500">
                  Submit Report
                </button>
              </div>
            <% step when step in ["Credit Review Stage","Finance Review Level 1", "Review Level 2", "Final Report Approval"]-> %>
              <form phx-submit="save" class="comment-form">
                <div class="mb-4">
                  <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">
                    Review Comments
                    <%= if @comment && String.trim(@comment) != "" do %>
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        <%= count_comments(@comment) %> schedule comment(s) included
                      </span>
                    <% end %>
                  </label>                  <div class="relative">
                    <textarea
                      id="comment"
                      name="comment"
                      class="comment-textarea shadow-sm block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                      placeholder="Schedule-specific comments will appear here automatically. You can add additional review comments..."
                      rows="8"
                      required
                      phx-hook="CommentTextarea"
                      data-comment={@comment || ""}
                      phx-update="ignore"
                    ><%= @comment || "" %></textarea>

                    <!-- Comment status indicator -->
                    <div class="absolute top-0 right-0 mt-2 mr-2">
                      <%= if @comment && String.trim(@comment) != "" do %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <%= count_comments(@comment) %> schedule comment(s)
                        </span>
                      <% end %>
                    </div>

                    <!-- Debug info -->
                    <%!-- <div class="mt-1 text-xs text-gray-500">
                      Debug: Comment length = <%= String.length(@comment || "") %> characters
                      <%= if @comment && String.trim(@comment) != "" do %>
                        | Preview: <%= String.slice(@comment, 0, 50) %>...
                      <% end %>
                    </div> --%>
                    <%= if @comment && String.trim(@comment) != "" do %>
                      <div class="absolute top-2 right-2">
                        <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                          Auto-aggregated
                        </span>
                      </div>
                    <% end %>
                  </div>
                  <%= if @comment && String.trim(@comment) != "" do %>
                    <div class="mt-2 text-xs text-gray-600">
                      <strong>Note:</strong> This field contains automatically aggregated comments from individual schedules.
                      You can add additional review comments above the existing content.
                    </div>
                  <% end %>
                </div>

                <div class="comment-actions">
                  <button type="submit" name="action" value="97" class="rounded-md bg-green-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-green-500">
                    Approve
                  </button>

                  <button type="submit" name="action" value="96" class="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500">
                    Reject
                  </button>
                </div>
              </form>
          <% end %>
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<%= if @loader do %>
  <div class="loader-overlay">
    <div class="loader-spinner"></div>

    <p class="loader-text">Loading schedule...</p>

    <p class="text-sm text-gray-500 mt-2">
      <%= cond do %>
        <% @report_type == "" -> %>
          Please wait...
        <% true -> %>
          Loading <%= String.replace(@report_type, "_", " ") %>...
      <% end %>
    </p>
  </div>
<% end %>
 <.error_notification />

<script>
  function hideCommentModal() {
    const modal = document.getElementById('comment-modal');
    if (modal) {
      modal.style.display = 'none';
      modal.style.visibility = 'hidden';
      modal.classList.add('hidden');
    }
    return true; // Allow form submission to continue
  }

  // Add debugging function for the page
  function debugModal() {
    console.log('=== MODAL DEBUG FROM PRUDENTIAL PAGE ===');
    const modal = document.getElementById('comment-modal');
    console.log('Modal element:', modal);
    if (modal) {
      console.log('Modal computed style:', window.getComputedStyle(modal));
      console.log('Modal display:', modal.style.display);
      console.log('Modal visibility:', modal.style.visibility);
    }

    const commentButtons = document.querySelectorAll('[phx-hook="CommentAuth"]');
    console.log('Comment buttons:', commentButtons.length);

    console.log('=== END MODAL DEBUG ===');
  }

  // Make debug function available globally
  window.debugModal = debugModal;

  // Listen for the hide-comment-modal event from LiveView
  window.addEventListener('phx:hide-comment-modal', function(e) {
    console.log('Received hide-comment-modal event');
    hideCommentModal();
  });
  // **AUTOMATIC MAIN COMMENT FIELD UPDATE - ENHANCED**
  // Listen for comment updates from LiveView
  window.addEventListener('phx:update-main-comment', function(e) {
    console.log('PRUDENTIAL: Received update-main-comment event:', e.detail);
    const mainCommentField = document.getElementById('comment');
    if (mainCommentField && e.detail.comment) {
      // Force update the textarea value and content
      mainCommentField.value = e.detail.comment;
      mainCommentField.textContent = e.detail.comment;
      mainCommentField.innerHTML = e.detail.comment;
      mainCommentField.dataset.comment = e.detail.comment;

      // Store for future reference
      window.lastAggregatedComment = e.detail.comment;
      
      // Update DOM visual feedback
      mainCommentField.style.backgroundColor = '#f0f9ff';
      mainCommentField.style.border = '2px solid #3b82f6';
      setTimeout(() => {
        mainCommentField.style.backgroundColor = '';
        mainCommentField.style.border = '';
      }, 2000);

      // Notify about comment update
      mainCommentField.dispatchEvent(new Event('input', { bubbles: true }));
      mainCommentField.dispatchEvent(new Event('change', { bubbles: true }));
      console.log('PRUDENTIAL: Main comment field updated automatically with aggregated comments:', e.detail.comment);

      // Trigger a visual indication that the field was updated
      mainCommentField.style.backgroundColor = '#f0f9ff';
      mainCommentField.style.border = '2px solid #3b82f6';
      setTimeout(() => {
        mainCommentField.style.backgroundColor = '';
        mainCommentField.style.border = '';
      }, 2000);

      // Dispatch a custom event to notify other parts of the page
      mainCommentField.dispatchEvent(new Event('input', { bubbles: true }));
      mainCommentField.dispatchEvent(new Event('change', { bubbles: true }));
    }
  });

  // Also listen for the custom event
  document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('comment-modal');
    if (modal) {
      // Listen for form submission to hide modal
      const form = modal.querySelector('#comment-form');
      if (form) {
        form.addEventListener('submit', function(e) {
          console.log('Form submitted, hiding modal in 100ms');
          setTimeout(() => {
            hideCommentModal();
          }, 100);
        });
      }
    }

    // **REAL-TIME COMMENT SYNCHRONIZATION**
    // Listen for LiveView updates to sync the main comment field
    window.addEventListener('phx:update', function() {
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField && window.lastAggregatedComment) {
        // Update the main comment field with aggregated comments
        mainCommentField.value = window.lastAggregatedComment;
        console.log('Main comment field updated with aggregated comments');
      }
    });
  });

    // Initialize pending comments container 
    window.pendingComments = window.pendingComments || {};

    // Function to handle comment synchronization
    window.syncComments = function() {
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField && window.lastAggregatedComment) {
        mainCommentField.value = window.lastAggregatedComment;
        mainCommentField.dataset.comment = window.lastAggregatedComment;
        mainCommentField.dispatchEvent(new Event('input', { bubbles: true }));
      }
    };

    // **REAL-TIME COMMENT SYNCHRONIZATION**
    window.addEventListener('phx:update', function() {
      syncComments();
    });

    // Listen for the hide-comment-modal event 
    window.addEventListener('phx:hide-comment-modal', function() {
      const modal = document.getElementById('comment-modal');
      if (modal) {
        modal.style.display = 'none';
        modal.style.visibility = 'hidden';
        modal.classList.add('hidden');
      }
    });

    // Listen for main comment field updates
    window.addEventListener('phx:update-main-comment', function(e) {
      console.log('PRUDENTIAL: Received update-main-comment event:', e.detail);
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField && e.detail.comment) {
        // Update field and visual feedback
        mainCommentField.value = e.detail.comment;
        mainCommentField.textContent = e.detail.comment;
        mainCommentField.innerHTML = e.detail.comment;
        mainCommentField.dataset.comment = e.detail.comment;
        window.lastAggregatedComment = e.detail.comment;

        // Visual feedback
        mainCommentField.style.backgroundColor = '#f0f9ff';
        mainCommentField.style.border = '2px solid #3b82f6';
        setTimeout(() => {
          mainCommentField.style.backgroundColor = '';
          mainCommentField.style.border = '';
        }, 2000);

        // Trigger events
        mainCommentField.dispatchEvent(new Event('input', { bubbles: true }));
        mainCommentField.dispatchEvent(new Event('change', { bubbles: true }));
      }
    });

    // Handle comment form submission
    const commentForm = document.getElementById('comment-form');
    if (commentForm) {
      commentForm.addEventListener('submit', function(e) {
        // The server will handle saving the comment
        setTimeout(() => {
          window.dispatchEvent(new Event('phx:hide-comment-modal'));
        }, 100);
      });
    }

    // **COMMENT PERSISTENCE HELPERS**
    window.addEventListener('phx:clear-pending-comments', function() {
      window.pendingComments = {};
      window.lastAggregatedComment = '';
      const mainCommentField = document.getElementById('comment');
      if (mainCommentField) {
        mainCommentField.value = '';
        mainCommentField.dataset.comment = '';
      }
    });
  </script>

  <style>
    .comment-textarea {
      @apply shadow-sm block w-full rounded-md border-gray-300 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm;
      min-height: 120px;
      line-height: 1.5;
    }

    .schedule-comment {
      @apply py-1;
    }

    .schedule-tag {
      @apply font-semibold text-gray-900 mr-1;
    }

    #comment-modal {
      @apply fixed inset-0 z-50 overflow-y-auto;
    }

    .modal-backdrop {
      @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
    }

    .modal-content {
      @apply relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6;
    }
  </style>

<div
  id="comment-modal"
  class={"fixed inset-0 z-50 overflow-y-auto #{if @show_comment_modal, do: "", else: "hidden"}"}
  aria-labelledby="modal-title"
  role="dialog"
  aria-modal="true"
  data-reference={@reference}
>
  <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

    <!-- Modal panel -->
    <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
            <!-- Comment icon -->
            <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
          </div>
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
               Comment on <%= @current_schedule_name || "Schedule" %>
            </h3>
            <div class="mt-4">
              <form id="comment-form" phx-submit="save_comment">
                <div>
                  <label for="comment" class="block text-sm font-medium text-gray-700">
                    Comment for <%= @current_schedule_name || "Schedule" %>
                  </label>
                  <div class="mt-1">
                    <textarea
                      id="modal-comment"
                      name="comment"
                      rows="4"
                      class="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Enter your comment here..."
                      value={@modal_comment}
                      required
                    ></textarea>
                  </div>
                  <%= if @comment && String.trim(@comment) != "" do %>
                    <div class="mt-2 p-2 bg-gray-50 rounded-md">
                      <p class="text-xs text-gray-600 mb-1">
                        Existing comments (<%= count_comments(@comment) %> total):
                      </p>
                      <div class="text-xs text-gray-700 max-h-20 overflow-y-auto">
                        <%= for line <- String.split(@comment, "\n") do %>
                          <%= if String.trim(line) != "" do %>
                            <div class="mb-1"><%= line %></div>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
                <div class="mt-4 flex justify-end">
                  <button
                    type="button"
                    class="mr-3 inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
                    phx-click="close_comment_modal"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
                    id="save-comment-button"
                  >
                    Save Comment
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
