defmodule MisReports.Workers.BozReq.Schedule2a1 do
  def perform(item) do
    # exchange_rate = MisReports.Prudentials.usd_rate(item.end_date) |> Decimal.to_float() || Decimal.new("1") |> Decimal.to_float()
    decoded_item =
      case item.schedule_02a1 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    # regulatory_capital = decoded_item["header"]
    dynamic_list = decoded_item["list"]
    cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-8TSCH2A18T001",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1018_00001", "Value" => "#{cost_of_funds}", "_dataType" => "NUMERIC"}
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 397,
          "_areaName" => "REPORTING REQUIREMENTS FOR THE TARGETED MEDIUM-TERM REFINANCING FACILITY",
          "DynamicItems" => map_data(dynamic_list) |> format_values()
        }
      ]
    }



  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


  def map_data(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => map["A"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => map["B"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => map["C"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => map["D"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => map["E"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.6", "Value" => map["F"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.7", "Value" => map["G"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.8", "Value" => map["H"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.9", "Value" => convert_decimal_map_to_decimal(map["I"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.10", "Value" => convert_decimal_map_to_decimal(map["J"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.11", "Value" => map["K"], "_dataType" => "DATE"},
          %{"Code" => "#{index}.12", "Value" => convert_decimal_map_to_decimal(map["L"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.13", "Value" => convert_decimal_map_to_decimal(map["M"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.14", "Value" => convert_decimal_map_to_decimal(map["N"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.15", "Value" => convert_decimal_map_to_decimal(map["O"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.16", "Value" => convert_decimal_map_to_decimal(map["P"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.17", "Value" => map["Q"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.18", "Value" => map["R"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.19", "Value" => map["S"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.20", "Value" => convert_decimal_map_to_decimal(map["T"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.21", "Value" => map["U"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.22", "Value" => map["V"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.23", "Value" => map["W"], "_dataType" => "TEXT"}
        ]
      end)
  end

  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  def convert_exponent(%{"coef" => coef, "exp" => exp, "sign" => sign}) do
    Decimal.new(sign, coef, exp) |> Decimal.to_float()
  end

  def convert_exponent(value), do: value

  def convert_decimal_map_to_decimal(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end


end
