<div>
    <h1 class="sr-only">Page title</h1>

    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />
    <%= if @live_action == :publication_list do %>
        <div class="mt-28 max-w-7xl mx-auto px-4 pt-10">
    
            <%= Phoenix.View.render(MisReportsWeb.QuarterlyView, "publication_list.html", assigns) %>
        </div>
    <% end %>

    <!-- Main 3 column grid -->
    <div class="grid grid-cols-1 gap-1 lg:col-span-2 mx-auto px-4 mt-8">
        <section aria-labelledby="section-1-title">
            <h2 class="sr-only" id="section-1-title">Section title</h2>
            <div class="overflow-hidden rounded-lg max-w-7xl mx-auto">
                <!-- Left column -->
                <%= cond do %>
                    <% @report_type == "" -> %>
                        <%= Phoenix.View.render(MisReportsWeb.QuarterlyView, "report_filter.html", assigns) %>
                    <% @report_type == "quarterly" -> %>
                        <%= Phoenix.View.render(MisReportsWeb.QuarterlyView, "quarterly.html", assigns) %>
                    <% true -> %>
                        <!-- Empty for other report types like quarterly_list -->
                <% end %>
            </div>
        </section>
    </div>
</div>





