defmodule MisReports.Workers.BozReq.Schedule5b do

  def perform(item) do

    decoded_item =
      case item.schedule_05b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    return_items = decoded_item["total"]
    dynamic_list = decoded_item["list"]
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()


    %{
      "ReturnKey" => "ZM-5QSCH5B5Q001",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1239_00001", "Value" => "#{format_number(return_items["SUM_N"]) |> Decimal.new() |> Decimal.to_float()}", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00002", "Value" => "#{format_number(return_items["SUM_P"]) |> Decimal.new() |> Decimal.to_float()}", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00003", "Value" => "0", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00004", "Value" => "0", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00005", "Value" => "0", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00006", "Value" => "0", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00007", "Value" => "#{format_number(return_items["SUM_Y"]) |> Decimal.new() |> Decimal.to_float()}", "_dataType" => "NUMERIC"},
        %{"Code" => "1239_00008", "Value" => "#{format_number(return_items["SUM_Z"]) |> Decimal.new() |> Decimal.to_float()}", "_dataType" => "NUMERIC"}
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 474,
          "_areaName" => "PAST DUE AND NON-PERFORMING LOANS MONTHLY RETURN INPUT SCHEDULE",
          "DynamicItems" => map_data(dynamic_list) |> format_values()
        }
      ]
    }


  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

 def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => map["A"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.2", "Value" => map["B"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.3", "Value" => map["C"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => map["D"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.5", "Value" => map["E"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.6", "Value" => map["F"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.7", "Value" => map["G"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.8", "Value" => map["H"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.9", "Value" => if(map["I"] in ["", nil], do: "Other", else: map["I"]), "_dataType" => "TEXT"},
          %{"Code" => "#{index}.10", "Value" => map["J"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.11", "Value" => map["K"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.12", "Value" => format_amount(map["L"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.13", "Value" => map["M"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.14", "Value" => format_amount(map["N"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.15", "Value" => map["O"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.16", "Value" => format_amount(map["P"]), "_dataType" => "NUMERIC"},
          # %{"Code" => "#{index}.17", "Value" => map["Q"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.18", "Value" => format_amount(map["R"]), "_dataType" => "NUMERIC"},
          # %{"Code" => "#{index}.19", "Value" => map["S"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.20", "Value" => format_amount(map["T"]), "_dataType" => "NUMERIC"},
          # %{"Code" => "#{index}.21", "Value" => map["U"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.22", "Value" => format_amount(map["V"]), "_dataType" => "NUMERIC"},
          # %{"Code" => "#{index}.23", "Value" => map["W"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.24", "Value" => format_amount(map["X"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.25", "Value" => format_amount(map["Y"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.26", "Value" => format_amount(map["Z"]), "_dataType" => "NUMERIC"},
          # %{"Code" => "#{index}.27", "Value" => map["AA"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.28", "Value" => map["AB"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.29", "Value" => map["AC"], "_dataType" => "TEXT"},
          # %{"Code" => "#{index}.30", "Value" => map["AD"], "_dataType" => "TEXT"}
        ]
      end)
  end


  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  # defp compare_keys(a, b) do
  #   case {String.length(a), String.length(b)} do
  #     {1, 1} -> a <= b  # Compare single-letter keys normally (A-Z)
  #     {1, _} -> true     # Single-letter keys (A-Z) always come before double-letter keys (AA, AB, ...)
  #     {_, 1} -> false    # Double-letter keys come after single-letter keys
  #     _ -> a <= b        # Compare multi-letter keys lexicographically (AA, AB, AC, AD...)
  #   end
  # end

  def convert_exponent(%{"coef" => coef, "exp" => exp, "sign" => sign}) do
    Decimal.new(sign, coef, exp) |> Decimal.to_float()
  end

  def convert_exponent(value), do: value

  def format_amount(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end

  # def make_map do
  #  [1..5] |> Enum.with_index(1) |> Enum.each(fn({index, value}) ->
  #               %{"Code" => "#{index}.1", "Value" => "Name of Borrower", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.2", "Value" => "Major shareholder/ Employer", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.3", "Value" => "Nationality", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.4", "Value" => "Classification Type", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.5", "Value" => "Gender", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.6", "Value" => "Facility Category", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.7", "Value" => "Economic Sector", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.8", "Value" => "Economic Sub-sector", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.9", "Value" => "Type of Facility", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.10", "Value" => "Loan Classification", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.11", "Value" => "Days past due", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.12", "Value" => "Amount Outstanding", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.13", "Value" => "Currency of Borrowing", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.14", "Value" => "Amount Outstanding K'000", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.15", "Value" => "Collateral Held Type 1", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.16", "Value" => "Collateral Type 1 Value", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.17", "Value" => "Collateral Held Type 2", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.18", "Value" => "Collateral Type 2 Value", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.19", "Value" => "Collateral Held Type 3", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.20", "Value" => "Collateral Type 3 Value", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.21", "Value" => "Collateral Held Type 4", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.22", "Value" => "Collateral Type 4 Value", "_idataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.23", "Value" => "Collateral Held Type 5", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.24", "Value" => "Collateral Type 5 Value", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.25", "Value" => "Actual Specific Provisions", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.26", "Value" => "Interest in Suspense", "_dataType" => "NUMERIC"},
  #               %{"Code" => "#{index}.27", "Value" => "Reason for Non-Performance", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.28", "Value" => "If Reason is Government Related", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.29", "Value" => "Government Department", "_dataType" => "TEXT"},
  #               %{"Code" => "#{index}.30", "Value" => "If Other Reason Provide Details", "_dataType" => "TEXT"}
  #   end)
  # end
  def make_map do
    list_of_maps = [
      %{"name" => "Alice", "hobbies" => ["reading", "hiking"]},
      %{"name" => "Bob", "hobbies" => ["coding", "gaming", "music"]},
      %{"name" => "Charlie", "hobbies" => ["drawing"]}
    ]

    Enum.flat_map(Enum.with_index(list_of_maps), fn {map, index} ->
      index = index + 1
      [
        %{"Code" => "#{index}.1", "Value" => map["name"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.2", "Value" => map["hobbies"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.3", "Value" => "Nationality", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.4", "Value" => "Classification Type", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.5", "Value" => "Gender", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.6", "Value" => "Facility Category", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.7", "Value" => "Economic Sector", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.8", "Value" => "Economic Sub-sector", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.9", "Value" => "Type of Facility", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.10", "Value" => "Loan Classification", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.11", "Value" => "Days past due", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.12", "Value" => "Amount Outstanding", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.13", "Value" => "Currency of Borrowing", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.14", "Value" => "Amount Outstanding K'000", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.15", "Value" => "Collateral Held Type 1", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.16", "Value" => "Collateral Type 1 Value", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.17", "Value" => "Collateral Held Type 2", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.18", "Value" => "Collateral Type 2 Value", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.19", "Value" => "Collateral Held Type 3", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.20", "Value" => "Collateral Type 3 Value", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.21", "Value" => "Collateral Held Type 4", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.22", "Value" => "Collateral Type 4 Value", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.23", "Value" => "Collateral Held Type 5", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.24", "Value" => "Collateral Type 5 Value", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.25", "Value" => "Actual Specific Provisions", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.26", "Value" => "Interest in Suspense", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.27", "Value" => "Reason for Non-Performance", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.28", "Value" => "If Reason is Government Related", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.29", "Value" => "Government Department", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.30", "Value" => "If Other Reason Provide Details", "_dataType" => "TEXT"}
      ]
    end)
  end


end
