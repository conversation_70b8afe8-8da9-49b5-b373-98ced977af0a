defmodule MisReportsWeb.BusinessUnitLive.BusinessUnitComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Repo}
  alias MisReports.{Prudentials, Prudentials.BusinessUnits}
  alias MisReportsWeb.UserController
  require Logger

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.BusinessUnitView, "business_unit.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.BusinessUnitView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{business_unit: business_unit} = assigns, socket) do
    changeset = Prudentials.change_business_unit(business_unit)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    # IO.inspect(reference, label("=============reference==============="))

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"business_unit" => business_unit_params}, socket) do
    changeset =
      socket.assigns.business_unit
      |> BusinessUnits.changeset(business_unit_params)
      |> Map.put(:action, :validate)


    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"business_unit" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    current_user_id = to_string(socket.assigns.current_user.id)
    user_id = socket.assigns.current_user.id
    audit_msg = "Created Business Unit \"#{params["ccr_business_unit"]}\" Successfully!"

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:business_unit, BusinessUnits.changeset(%BusinessUnits{maker_id: user_id}, params))
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{business_unit: business_unit}} ->
        # Spawn a task to handle workflow and reference update
        Task.start(fn ->
          case MisReports.Workflow.call_workflow(
            socket.assigns.reference,
            socket.assigns.process_id,
            current_user_id,
            80,
            "",
            "",
            "Submission of Business Unit Creation"
          ) do
            {:ok, reference_number} ->
              Prudentials.update_business_unit(business_unit, %{reference: reference_number})
            {:error, reason} ->
              Logger.error("""
              [BusinessUnitComponent] Workflow update failed:
              Business Unit ID: #{business_unit.id}
              Process ID: #{socket.assigns.process_id}
              Reference: #{socket.assigns.reference}
              Error: #{inspect(reason)}
              """)
              nil
          end
        end)

        # Return success immediately
        {:noreply,
         socket
         |> put_flash(:info, "Business unit created successfully")
         |> push_redirect(to: Routes.business_unit_index_path(socket, :new))}

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    loan_business_unit = socket.assigns.business_unit
    socket
    |> handle_update(params, loan_business_unit)
    |> case do
      {:ok, loan_business_unit} ->
        {:noreply,
          socket
          |> put_flash(:info, "Business unit updated successfully")
          |> push_redirect(to: Routes.business_unit_index_path(socket, :edit, loan_business_unit))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Business Unit Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Business unit rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject business unit: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :approve, params) do
    business_unit = socket.assigns.business_unit
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Business Unit Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      BusinessUnits.changeset(business_unit, %{
        status: "A",
        checker_id: current_user.id,
        checker_date: NaiveDateTime.utc_now()
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_business_unit}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Business unit approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Business unit approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve business unit")
         |> assign(:changeset, %{business_unit.changeset | errors: failed_value.errors})}
    end
  end

  def handle_update(socket, params, loan_business_unit) do
    audit_msg = "Updated Business unit for \"#{loan_business_unit.business_unit}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:loan_business_unit, BusinessUnits.changeset(loan_business_unit, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{loan_business_unit: loan_business_unit, audit_log: _user_log}} ->
        {:ok, loan_business_unit}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def traverse_errors(errors) do
    for {key, {msg, opts}} <- errors, into: %{} do
      msg =
        Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
          opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
        end)

      {key, msg}
    end
  end
end
