defmodule MisReports.Workers.BozReq.Schedule01e do
   def perform(item) do

    decoded_item =
      case item.schedule_01e do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    # regulatory_capital = decoded_item["header"]

    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()
    %{
      "ReturnKey" => "ZM-2JSCH1E2J002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1131_00001", "Value" => "#{decoded_item["B14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00002", "Value" => "#{decoded_item["C14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00003", "Value" => "#{decoded_item["D14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00004", "Value" => "#{decoded_item["E14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00005", "Value" => "#{decoded_item["F14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00006", "Value" => "#{decoded_item["G14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00007", "Value" => "#{decoded_item["H14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00008", "Value" => "#{decoded_item["I14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00009", "Value" => "#{decoded_item["J14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00010", "Value" => "#{decoded_item["K14"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00011", "Value" => "#{decoded_item["B15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00012", "Value" => "#{decoded_item["C15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00013", "Value" => "#{decoded_item["D15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00014", "Value" => "#{decoded_item["E15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00015", "Value" => "#{decoded_item["F15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00016", "Value" => "#{decoded_item["G15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00017", "Value" => "#{decoded_item["H15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00018", "Value" => "#{decoded_item["I15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00019", "Value" => "#{decoded_item["J15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00020", "Value" => "#{decoded_item["K15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00021", "Value" => "#{decoded_item["B16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00022", "Value" => "#{decoded_item["C16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00023", "Value" => "#{decoded_item["D16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00024", "Value" => "#{decoded_item["E16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00025", "Value" => "#{decoded_item["F16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00026", "Value" => "#{decoded_item["G16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00027", "Value" => "#{decoded_item["H16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00028", "Value" => "#{decoded_item["I16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00029", "Value" => "#{decoded_item["J16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00030", "Value" => "#{decoded_item["K16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00031", "Value" => "#{decoded_item["B17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00032", "Value" => "#{decoded_item["C17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00033", "Value" => "#{decoded_item["D17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00034", "Value" => "#{decoded_item["E17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00035", "Value" => "#{decoded_item["F17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00036", "Value" => "#{decoded_item["G17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00037", "Value" => "#{decoded_item["H17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00038", "Value" => "#{decoded_item["I17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00039", "Value" => "#{decoded_item["J17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00040", "Value" => "#{decoded_item["K17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00041", "Value" => "#{decoded_item["B18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00042", "Value" => "#{decoded_item["C18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00043", "Value" => "#{decoded_item["D18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00044", "Value" => "#{decoded_item["E18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00045", "Value" => "#{decoded_item["F18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00046", "Value" => "#{decoded_item["G18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00047", "Value" => "#{decoded_item["H18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00048", "Value" => "#{decoded_item["I18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00049", "Value" => "#{decoded_item["J18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00050", "Value" => "#{decoded_item["K18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00051", "Value" => "#{decoded_item["B19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00052", "Value" => "#{decoded_item["C19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00053", "Value" => "#{decoded_item["D19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00054", "Value" => "#{decoded_item["E19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00055", "Value" => "#{decoded_item["F19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00056", "Value" => "#{decoded_item["G19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00057", "Value" => "#{decoded_item["H19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00058", "Value" => "#{decoded_item["I19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00059", "Value" => "#{decoded_item["J19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00060", "Value" => "#{decoded_item["K19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00061", "Value" => "#{decoded_item["B20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00062", "Value" => "#{decoded_item["C20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00063", "Value" => "#{decoded_item["D20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00064", "Value" => "#{decoded_item["E20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00065", "Value" => "#{decoded_item["F20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00066", "Value" => "#{decoded_item["G20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00067", "Value" => "#{decoded_item["H20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00068", "Value" => "#{decoded_item["I20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00069", "Value" => "#{decoded_item["J20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00070", "Value" => "#{decoded_item["K20"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00071", "Value" => "#{decoded_item["B21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00072", "Value" => "#{decoded_item["C21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00073", "Value" => "#{decoded_item["D21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00074", "Value" => "#{decoded_item["E21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00075", "Value" => "#{decoded_item["F21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00076", "Value" => "#{decoded_item["G21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00077", "Value" => "#{decoded_item["H21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00078", "Value" => "#{decoded_item["I21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00079", "Value" => "#{decoded_item["J21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00080", "Value" => "#{decoded_item["K21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00081", "Value" => "#{decoded_item["B22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00082", "Value" => "#{decoded_item["C22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00083", "Value" => "#{decoded_item["D22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00084", "Value" => "#{decoded_item["E22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00085", "Value" => "#{decoded_item["F22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00086", "Value" => "#{decoded_item["G22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00087", "Value" => "#{decoded_item["H22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00088", "Value" => "#{decoded_item["I22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00089", "Value" => "#{decoded_item["J22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00090", "Value" => "#{decoded_item["K22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00091", "Value" => "#{decoded_item["B23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00092", "Value" => "#{decoded_item["C23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00093", "Value" => "#{decoded_item["D23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00094", "Value" => "#{decoded_item["E23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00095", "Value" => "#{decoded_item["F23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00096", "Value" => "#{decoded_item["G23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00097", "Value" => "#{decoded_item["H23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00098", "Value" => "#{decoded_item["I23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00099", "Value" => "#{decoded_item["J23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00100", "Value" => "#{decoded_item["K23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00101", "Value" => "#{decoded_item["B24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00102", "Value" => "#{decoded_item["C24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00103", "Value" => "#{decoded_item["D24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00104", "Value" => "#{decoded_item["E24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00105", "Value" => "#{decoded_item["F24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00106", "Value" => "#{decoded_item["G24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00107", "Value" => "#{decoded_item["H24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00108", "Value" => "#{decoded_item["I24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00109", "Value" => "#{decoded_item["J24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00110", "Value" => "#{decoded_item["K24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00111", "Value" => "#{decoded_item["B25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00112", "Value" => "#{decoded_item["C25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00113", "Value" => "#{decoded_item["D25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00114", "Value" => "#{decoded_item["E25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00115", "Value" => "#{decoded_item["F25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00116", "Value" => "#{decoded_item["G25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00117", "Value" => "#{decoded_item["H25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00118", "Value" => "#{decoded_item["I25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00119", "Value" => "#{decoded_item["J25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00120", "Value" => "#{decoded_item["K25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00121", "Value" => "#{decoded_item["B26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00122", "Value" => "#{decoded_item["C26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00123", "Value" => "#{decoded_item["D26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00124", "Value" => "#{decoded_item["E26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00125", "Value" => "#{decoded_item["F26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00126", "Value" => "#{decoded_item["G26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00127", "Value" => "#{decoded_item["H26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00128", "Value" => "#{decoded_item["I26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00129", "Value" => "#{decoded_item["J26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00130", "Value" => "#{decoded_item["K26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00131", "Value" => "#{decoded_item["B27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00132", "Value" => "#{decoded_item["C27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00133", "Value" => "#{decoded_item["D27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00134", "Value" => "#{decoded_item["E27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00135", "Value" => "#{decoded_item["F27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00136", "Value" => "#{decoded_item["G27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00137", "Value" => "#{decoded_item["H27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00138", "Value" => "#{decoded_item["I27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00139", "Value" => "#{decoded_item["J27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00140", "Value" => "#{decoded_item["K27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00141", "Value" => "#{decoded_item["B28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00142", "Value" => "#{decoded_item["C28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00143", "Value" => "#{decoded_item["D28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00144", "Value" => "#{decoded_item["E28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00145", "Value" => "#{decoded_item["F28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00146", "Value" => "#{decoded_item["G28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00147", "Value" => "#{decoded_item["H28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00148", "Value" => "#{decoded_item["I28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00149", "Value" => "#{decoded_item["J28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00150", "Value" => "#{decoded_item["K28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00151", "Value" => "#{decoded_item["B29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00152", "Value" => "#{decoded_item["C29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00153", "Value" => "#{decoded_item["D29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00154", "Value" => "#{decoded_item["E29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00155", "Value" => "#{decoded_item["F29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00156", "Value" => "#{decoded_item["G29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00157", "Value" => "#{decoded_item["H29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00158", "Value" => "#{decoded_item["I29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00159", "Value" => "#{decoded_item["J29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00160", "Value" => "#{decoded_item["K29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00161", "Value" => "#{decoded_item["B30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00162", "Value" => "#{decoded_item["C30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00163", "Value" => "#{decoded_item["D30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00164", "Value" => "#{decoded_item["E30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00165", "Value" => "#{decoded_item["F30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00166", "Value" => "#{decoded_item["G30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00167", "Value" => "#{decoded_item["H30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00168", "Value" => "#{decoded_item["I30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00169", "Value" => "#{decoded_item["J30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00170", "Value" => "#{decoded_item["K30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00171", "Value" => "#{decoded_item["B31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00172", "Value" => "#{decoded_item["C31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00173", "Value" => "#{decoded_item["D31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00174", "Value" => "#{decoded_item["E31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00175", "Value" => "#{decoded_item["F31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00176", "Value" => "#{decoded_item["G31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00177", "Value" => "#{decoded_item["H31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00178", "Value" => "#{decoded_item["I31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00179", "Value" => "#{decoded_item["J31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00180", "Value" => "#{decoded_item["K31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00181", "Value" => "#{decoded_item["B32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00182", "Value" => "#{decoded_item["C32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00183", "Value" => "#{decoded_item["D32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00184", "Value" => "#{decoded_item["E32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00185", "Value" => "#{decoded_item["F32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00186", "Value" => "#{decoded_item["G32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00187", "Value" => "#{decoded_item["H32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00188", "Value" => "#{decoded_item["I32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00189", "Value" => "#{decoded_item["J32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00190", "Value" => "#{decoded_item["K32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00191", "Value" => "#{decoded_item["B33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00192", "Value" => "#{decoded_item["C33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00193", "Value" => "#{decoded_item["D33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00194", "Value" => "#{decoded_item["E33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00195", "Value" => "#{decoded_item["F33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00196", "Value" => "#{decoded_item["G33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00197", "Value" => "#{decoded_item["H33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00198", "Value" => "#{decoded_item["I33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00199", "Value" => "#{decoded_item["J33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00200", "Value" => "#{decoded_item["K33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00201", "Value" => "#{decoded_item["B34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00202", "Value" => "#{decoded_item["C34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00203", "Value" => "#{decoded_item["D34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00204", "Value" => "#{decoded_item["E34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00205", "Value" => "#{decoded_item["F34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00206", "Value" => "#{decoded_item["G34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00207", "Value" => "#{decoded_item["H34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00208", "Value" => "#{decoded_item["I34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00209", "Value" => "#{decoded_item["J34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00210", "Value" => "#{decoded_item["K34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00211", "Value" => "#{decoded_item["B35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00212", "Value" => "#{decoded_item["C35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00213", "Value" => "#{decoded_item["D35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00214", "Value" => "#{decoded_item["E35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00215", "Value" => "#{decoded_item["F35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00216", "Value" => "#{decoded_item["G35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00217", "Value" => "#{decoded_item["H35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00218", "Value" => "#{decoded_item["I35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00219", "Value" => "#{decoded_item["J35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00220", "Value" => "#{decoded_item["K35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00221", "Value" => "#{decoded_item["B36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00222", "Value" => "#{decoded_item["C36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00223", "Value" => "#{decoded_item["D36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00224", "Value" => "#{decoded_item["E36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00225", "Value" => "#{decoded_item["F36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00226", "Value" => "#{decoded_item["G36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00227", "Value" => "#{decoded_item["H36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00228", "Value" => "#{decoded_item["I36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00229", "Value" => "#{decoded_item["J36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00230", "Value" => "#{decoded_item["K36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00231", "Value" => "#{decoded_item["B37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00232", "Value" => "#{decoded_item["C37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00233", "Value" => "#{decoded_item["D37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00234", "Value" => "#{decoded_item["E37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00235", "Value" => "#{decoded_item["F37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00236", "Value" => "#{decoded_item["G37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00237", "Value" => "#{decoded_item["H37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00238", "Value" => "#{decoded_item["I37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00239", "Value" => "#{decoded_item["J37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00240", "Value" => "#{decoded_item["K37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00241", "Value" => "#{decoded_item["B38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00242", "Value" => "#{decoded_item["C38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00243", "Value" => "#{decoded_item["D38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00244", "Value" => "#{decoded_item["E38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00245", "Value" => "#{decoded_item["F38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00246", "Value" => "#{decoded_item["G38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00247", "Value" => "#{decoded_item["H38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00248", "Value" => "#{decoded_item["I38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00249", "Value" => "#{decoded_item["J38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00250", "Value" => "#{decoded_item["K38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00251", "Value" => "#{decoded_item["B39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00252", "Value" => "#{decoded_item["C39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00253", "Value" => "#{decoded_item["D39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00254", "Value" => "#{decoded_item["E39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00255", "Value" => "#{decoded_item["F39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00256", "Value" => "#{decoded_item["G39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00257", "Value" => "#{decoded_item["H39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00258", "Value" => "#{decoded_item["I39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00259", "Value" => "#{decoded_item["J39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00260", "Value" => "#{decoded_item["K39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00261", "Value" => "#{decoded_item["B40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00262", "Value" => "#{decoded_item["C40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00263", "Value" => "#{decoded_item["D40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00264", "Value" => "#{decoded_item["E40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00265", "Value" => "#{decoded_item["F40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00266", "Value" => "#{decoded_item["G40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00267", "Value" => "#{decoded_item["H40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00268", "Value" => "#{decoded_item["I40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00269", "Value" => "#{decoded_item["J40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00270", "Value" => "#{decoded_item["K40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00271", "Value" => "#{decoded_item["B41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00272", "Value" => "#{decoded_item["C41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00273", "Value" => "#{decoded_item["D41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00274", "Value" => "#{decoded_item["E41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00275", "Value" => "#{decoded_item["F41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00276", "Value" => "#{decoded_item["G41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00277", "Value" => "#{decoded_item["H41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00278", "Value" => "#{decoded_item["I41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00279", "Value" => "#{decoded_item["J41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00280", "Value" => "#{decoded_item["K41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00281", "Value" => "#{decoded_item["B42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00282", "Value" => "#{decoded_item["C42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00283", "Value" => "#{decoded_item["D42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00284", "Value" => "#{decoded_item["E42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00285", "Value" => "#{decoded_item["F42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00286", "Value" => "#{decoded_item["G42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00287", "Value" => "#{decoded_item["H42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00288", "Value" => "#{decoded_item["I42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00289", "Value" => "#{decoded_item["J42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00290", "Value" => "#{decoded_item["K42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00291", "Value" => "#{decoded_item["B43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00292", "Value" => "#{decoded_item["C43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00293", "Value" => "#{decoded_item["D43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00294", "Value" => "#{decoded_item["E43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00295", "Value" => "#{decoded_item["F43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00296", "Value" => "#{decoded_item["G43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00297", "Value" => "#{decoded_item["H43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00298", "Value" => "#{decoded_item["I43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00299", "Value" => "#{decoded_item["J43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00300", "Value" => "#{decoded_item["K43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00301", "Value" => "#{decoded_item["B44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00302", "Value" => "#{decoded_item["C44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00303", "Value" => "#{decoded_item["D44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00304", "Value" => "#{decoded_item["E44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00305", "Value" => "#{decoded_item["F44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00306", "Value" => "#{decoded_item["G44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00307", "Value" => "#{decoded_item["H44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00308", "Value" => "#{decoded_item["I44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00309", "Value" => "#{decoded_item["J44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00310", "Value" => "#{decoded_item["K44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00311", "Value" => "#{decoded_item["B45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00312", "Value" => "#{decoded_item["C45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00313", "Value" => "#{decoded_item["D45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00314", "Value" => "#{decoded_item["E45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00315", "Value" => "#{decoded_item["F45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00316", "Value" => "#{decoded_item["G45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00317", "Value" => "#{decoded_item["H45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00318", "Value" => "#{decoded_item["I45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00319", "Value" => "#{decoded_item["J45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00320", "Value" => "#{decoded_item["K45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00321", "Value" => "#{decoded_item["B46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00322", "Value" => "#{decoded_item["C46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00323", "Value" => "#{decoded_item["D46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00324", "Value" => "#{decoded_item["E46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00325", "Value" => "#{decoded_item["F46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00326", "Value" => "#{decoded_item["G46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00327", "Value" => "#{decoded_item["H46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00328", "Value" => "#{decoded_item["I46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00329", "Value" => "#{decoded_item["J46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00330", "Value" => "#{decoded_item["K46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00331", "Value" => "#{decoded_item["B47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00332", "Value" => "#{decoded_item["C47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00333", "Value" => "#{decoded_item["D47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00334", "Value" => "#{decoded_item["E47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00335", "Value" => "#{decoded_item["F47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00336", "Value" => "#{decoded_item["G47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00337", "Value" => "#{decoded_item["H47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00338", "Value" => "#{decoded_item["I47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00339", "Value" => "#{decoded_item["J47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00340", "Value" => "#{decoded_item["K47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00341", "Value" => "#{decoded_item["B48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00342", "Value" => "#{decoded_item["C48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00343", "Value" => "#{decoded_item["D48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00344", "Value" => "#{decoded_item["E48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00345", "Value" => "#{decoded_item["F48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00346", "Value" => "#{decoded_item["G48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00347", "Value" => "#{decoded_item["H48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00348", "Value" => "#{decoded_item["I48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00349", "Value" => "#{decoded_item["J48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00350", "Value" => "#{decoded_item["K48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00351", "Value" => "#{decoded_item["B49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00352", "Value" => "#{decoded_item["C49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00353", "Value" => "#{decoded_item["D49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00354", "Value" => "#{decoded_item["E49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00355", "Value" => "#{decoded_item["F49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00356", "Value" => "#{decoded_item["G49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00357", "Value" => "#{decoded_item["H49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00358", "Value" => "#{decoded_item["I49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00359", "Value" => "#{decoded_item["J49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00360", "Value" => "#{decoded_item["K49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00361", "Value" => "#{decoded_item["B50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00362", "Value" => "#{decoded_item["C50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00363", "Value" => "#{decoded_item["D50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00364", "Value" => "#{decoded_item["E50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00365", "Value" => "#{decoded_item["F50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00366", "Value" => "#{decoded_item["G50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00367", "Value" => "#{decoded_item["H50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00368", "Value" => "#{decoded_item["I50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00369", "Value" => "#{decoded_item["J50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00370", "Value" => "#{decoded_item["K50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00371", "Value" => "#{decoded_item["B51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00372", "Value" => "#{decoded_item["C51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00373", "Value" => "#{decoded_item["D51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00374", "Value" => "#{decoded_item["E51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00375", "Value" => "#{decoded_item["F51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00376", "Value" => "#{decoded_item["G51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00377", "Value" => "#{decoded_item["H51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00378", "Value" => "#{decoded_item["I51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00379", "Value" => "#{decoded_item["J51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00380", "Value" => "#{decoded_item["K51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00381", "Value" => "#{decoded_item["B52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00382", "Value" => "#{decoded_item["C52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00383", "Value" => "#{decoded_item["D52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00384", "Value" => "#{decoded_item["E52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00385", "Value" => "#{decoded_item["F52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00386", "Value" => "#{decoded_item["G52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00387", "Value" => "#{decoded_item["H52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00388", "Value" => "#{decoded_item["I52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00389", "Value" => "#{decoded_item["J52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00390", "Value" => "#{decoded_item["K52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00391", "Value" => "#{decoded_item["B53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00392", "Value" => "#{decoded_item["C53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00393", "Value" => "#{decoded_item["D53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00394", "Value" => "#{decoded_item["E53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00395", "Value" => "#{decoded_item["F53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00396", "Value" => "#{decoded_item["G53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00397", "Value" => "#{decoded_item["H53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00398", "Value" => "#{decoded_item["I53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00399", "Value" => "#{decoded_item["J53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1131_00400", "Value" => "#{decoded_item["K53"]}", "_dataType" => "NUMERIC"},

      ] |> format_map(),
      "DynamicItemsList" => []
    }


  end

 def format_map(list_of_maps) do
    Enum.map(list_of_maps, fn map ->
      Enum.map(map, fn {key, value} ->
        {key, format_number(value)}
      end)
      |> Map.new()
    end)
  end

  defp format_number(string) do
    string
    |> String.replace(",", "")
    |> case do
      "" -> "0"
      value -> value
    end
  end


  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

end
