defmodule MisReports.Workers.Jobs.SaveMonthyPrudential do
  require Logger
  alias MisReports.Utilities
  alias MisReports.Prudentials.PrudReport
  alias MisReports.Utilities.FileExport
  alias MisReportsWeb.UserController

  def perform do
    IO.inspect("=========================PRUDENTIAL SAVE CRONJOB=============================")

    Utilities.get_prudential_report_status("PENDING")
    |> Enum.take(1)
    |> Task.

    async_stream(&handle_schedule_save(&1),
      max_concurrency: 1,
      timeout: :timer.hours(8),
      on_timeout: :kill_task
    )
    |> Stream.run()
  end

  def handle_schedule_save(item) do
    params = prepare_schedule_params(item)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:report, PrudReport.changeset(%PrudReport{}, params))
    |> Ecto.Multi.update(:update, FileExport.changeset(item, %{status: "PENDING_EXPORT"}))
    |> MisReports.Repo.transaction(
      timeout: :timer.minutes(50),
      isolation_level: :read_uncommitted
    )
    |> case do
      {:ok, _report} ->
        IO.inspect("=================== PRUDENTIAL SAVED ===============")

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        Logger.error(error_msg)
    end
  end

  def prepare_schedule_params(item) do
    get_month =
      if byte_size("#{item.report_date.month}") == 1,
        do: "0#{item.report_date.month}",
        else: "#{item.report_date.month}"

    start_date = "#{item.report_date.year}-#{get_month}-#{item.report_date.day}"
    start_date_iso = start_date |> Date.from_iso8601!()
    month = String.replace(start_date, "-", "") |> String.slice(0..5)
    prev_date = Timex.shift(start_date_iso, months: -1) |> Timex.end_of_month()
    date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

    usd_rate =
      Utilities.get_exchange_rate_by_date_and_code(start_date, "USD")[:exchange_rate_lcy] || "1"

    data = MisReports.SourceData.gbm_by_month(month)
    adjustments = Utilities.get_adjustments(start_date)
    income = MisReports.Workers.IS.generate_display(data, start_date, adjustments)
    prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)
    shd13 = MisReports.Workers.Sh13.generate_display(income, prev_income, start_date)

    bal_sheet =
      MisReports.Workers.BalanceSheet.generate_display(
        shd13,
        data,
        start_date,
        start_date,
        usd_rate,
        adjustments
      )

    depositors = MisReports.SourceData.number_depositors_data_18b(start_date)
    loans_entry = MisReports.Workers.LoansAdvances.Sh18b.generate_display(start_date)
    sh18b = MisReports.Workers.Sh18b.generate_display(data, loans_entry, depositors, start_date)
    sh31c = MisReports.Workers.Sh31c.generate_display(start_date, usd_rate, adjustments)

    %{
      start_date: start_date,
      end_date: start_date,
      type: "MONTHLY",
      status: "PENDING_EXPORT",
      ref: item.uuid,
      reference: item.reference,
      maker_id: item.maker_id,
      month: String.replace(start_date, "-", "") |> String.slice(4..5),
      year: String.replace(start_date, "-", "") |> String.slice(0..3),
      income_stmt: Poison.encode!(income),
      bal_sheet: Poison.encode!(bal_sheet),
      schedule_01c:
        Poison.encode!(MisReports.Workers.Sh01c.generate_display(start_date, start_date)),
      schedule_01e:
        Poison.encode!(MisReports.Workers.Sh01e.generate_display(start_date, start_date)),
      schedule_01f:
        Poison.encode!(MisReports.Workers.Sh01f.generate_display(start_date, start_date)),
      schedule_02a1:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh2a1.generate_export(start_date, adjustments)
        ),
      schedule_02c:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh02c.generate_export(start_date, adjustments)
        ),
      schedule_02g:
        Poison.encode!(MisReports.Workers.Sh02g.generate_export(start_date, adjustments)),
      schedule_02h:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh02h.generate_export(start_date, adjustments)
        ),
      schedule_03a:
        Poison.encode!(MisReports.Workers.LoansAdvances.Sh03a.generate_export(start_date)),
      schedule_04b:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh4b.generate_export(start_date, adjustments)
        ),
      schedule_04d:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh4d.generate_export(
            bal_sheet,
            start_date,
            adjustments
          )
        ),
      schedule_05b:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh5b.generate_export(start_date, adjustments)
        ),
      schedule_06a:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh6a.generate_export(start_date, adjustments)
        ),
      schedule_07a:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh7a.generate_export(start_date, adjustments)
        ),
      schedule_08a:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh8a.generate_export(start_date, adjustments)
        ),
      schedule_09a:
        Poison.encode!(MisReports.Workers.Sh09a.generate_display(start_date, start_date)),
      schedule_11d: Poison.encode!(get_all_deposits(start_date, usd_rate, adjustments)),
      schedule_11e: Poison.encode!(MisReports.Workers.Sh11e.generate_display(start_date)),
      # schedule_11f: Poison.encode!(MisReports.Workers.Sh11fAndsh11g.generate_display(start_date, "11F")),
      # schedule_11g: Poison.encode!(MisReports.Workers.Sh11fAndsh11g.generate_display(start_date, "11G")),
      schedule_11j: Poison.encode!(MisReports.Workers.Sh11j.generate_display(start_date,usd_rate, adjustments)),
      schedule_12: Poison.encode!(MisReports.Workers.Sh12.generate_display(start_date, start_date)),
      schedule_14: Poison.encode!(MisReports.Workers.Sh14.generate_display(bal_sheet, start_date, adjustments)),
      schedule_13: Poison.encode!(shd13),
      schedule_15:
        Poison.encode!(
          MisReports.Workers.Sh15.generate_display(shd13, bal_sheet, start_date, adjustments)
        ),
      schedule_17b:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh17b.generate_export(start_date, adjustments)
        ),
      schedule_17e:
        Poison.encode!(
          MisReports.Workers.Shd17e.generate_display(start_date,usd_rate, adjustments)
        ),

      schedule_18a: Poison.encode!(MisReports.Workers.Sh18a.generate_display(start_date)),
      schedule_18b: Poison.encode!(sh18b),
      schedule_18c: Poison.encode!(MisReports.Workers.Sh18c.generate_export()),
      schedule_18d: Poison.encode!(MisReports.Workers.Sh18d.generate_display()),
      schedule_19:
        Poison.encode!(MisReports.Workers.Sh19.generate_display(start_date, adjustments)),
      schedule_20a: Poison.encode!(MisReports.Workers.Sh20a.generate_display(start_date)),
      schedule_21c:
        Poison.encode!(
          MisReports.Workers.Sh21c.generate_display(start_date, start_date, adjustments)
        ),
      schedule_22a:
        Poison.encode!(
          MisReports.Workers.LoansAdvances.Sh22a.generate_export(start_date, adjustments)
        ),
      schedule_23a:
        Poison.encode!(MisReports.Workers.Sh23a.generate_display(start_date, usd_rate)),
      schedule_24: Poison.encode!(MisReports.Workers.Sh24.generate_export(start_date)),
      schedule_25:
        Poison.encode!(
          MisReports.Workers.Sh25.generate_display(start_date, start_date, adjustments)
        ),
      schedule_26:
        Poison.encode!(
          MisReports.Workers.Sh26.generate_display(start_date, start_date, adjustments)
        ),
      schedule_27:
        Poison.encode!(
          MisReports.Workers.Sh27.generate_display(start_date, start_date, adjustments)
        ),
      schedule_27a:
        Poison.encode!(
          MisReports.Workers.Sh27a.generate_display(start_date, start_date, adjustments)
        ),
      schedule_28a:
        Poison.encode!(
          MisReports.Workers.Sh28a.generate_display(start_date, start_date, adjustments)
        ),
      schedule_28b:
        Poison.encode!(
          MisReports.Workers.Sh28b.generate_display(start_date, start_date, adjustments)
        ),
      schedule_30d:
        Poison.encode!(
          MisReports.Workers.Sh30d.generate_display(start_date, start_date, adjustments)
        ),
      schedule_31c: Poison.encode!(sh31c),
      schedule_31d:
        Poison.encode!(
          MisReports.Workers.Sh31d.generate_display(start_date, usd_rate, adjustments)
        ),
      schedule_31f:
        Poison.encode!(MisReports.Workers.Sh31f.generate_display(start_date, adjustments)),
      schedule_29a: Poison.encode!(MisReports.Workers.Sh29a.generate_display(start_date)),
      schedule_30b:
        Poison.encode!(MisReports.Workers.Sh30b.generate_display(start_date, usd_rate)),
      schedule_30b1:
        Poison.encode!(MisReports.Workers.Sh30b.generate_display(start_date, usd_rate)),
      schedule_30c:
        Poison.encode!(MisReports.Workers.Sh30c.generate_display(start_date, usd_rate)),
      schedule_30c1:
        Poison.encode!(MisReports.Workers.Sh30c.generate_display(start_date, usd_rate)),
      schedule_32a:
        Poison.encode!(MisReports.Workers.Sh32a.generate_display(start_date, start_date))
    }
  end

  defp get_all_deposits(date, usd_rate, adjustments) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    MisReports.Workers.Sh11d.get_all_deposits(year, month, usd_rate, adjustments)
    |> Enum.map(fn {_group, [cust_seg | _]} -> cust_seg end)
  end
end
