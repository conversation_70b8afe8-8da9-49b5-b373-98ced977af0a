defmodule MisReportsWeb.InstitutionDetailsLive.InstitutionDetailsComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReportsWeb.UserController
  alias MisReports.Utilities.InstitutionDetails
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.InstitutionDetailsView, "institution_detail.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.InstitutionDetailsView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{institution_details: institution_details} = assigns, socket) do
    changeset = Utilities.change_institution_details(institution_details)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"institution_details" => institution_details}, socket) do
    changeset =
      socket.assigns.institution_details
      |> InstitutionDetails.changeset(institution_details)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"institution_details" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_approval(socket, :reject, params)
      "97" -> handle_approval(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg = "Created new Institution \"#{params["institution_name"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :institution_details,
      InstitutionDetails.changeset(%InstitutionDetails{maker_id: user.id}, params)
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{institution_details: institution_details}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Institution details Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_institution_details(institution_details, %{reference: reference_number}) do
              {:ok, updated_institution_details} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Institution details created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update Institution details reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Institution details created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    institution_details = socket.assigns.institution_details

    socket
    |> handle_update(params, institution_details)
    |> case do
      {:ok, institution_details} ->
        {:noreply,
         socket
         |> put_flash(:info, "Institution updated successfully")
         |> push_redirect(to: Routes.institution_details_index_path(socket, :index))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, institution_details) do
    audit_msg = "Updated Exchange Placement for Institution \"#{params["institution_name"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :institution_details,
      InstitutionDetails.changeset(
        institution_details,
        Map.merge(params, %{"status" => "D", "checker_id" => nil})
      )
    )
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{institution_details: institution_details, audit_log: _user_log}} ->
        {:ok, institution_details}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_approval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Institution details Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Institution details rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject Institution details: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_approval(socket, :approve, params) do
    institution_details = socket.assigns.institution_details
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Institution details Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      InstitutionDetails.changeset(institution_details, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_institution_details}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Institution details approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Institution details approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve Institution details")
         |> assign(:changeset, %{institution_details.changeset | errors: failed_value.errors})}
    end
  end
end
