
<style>
    .chileshe{
    border-radius:20px
    }
    </style>
    <.form :let={f} for={@changeset} as={:role} id="user-role-form" phx-submit="save"  phx-target={@myself} phx-hook="UserRoleCheckboxes">
        <div class="grid grid-cols-4 gap-2 mt-10 bg-white">
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > User roles </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][user_role][new]" value="N">
                    <input  name="role[role_str][user_role][new]" type="checkbox" value="Y" data-select-val={@role.role_str["user_role"]["new"]} id="new-user-role"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-user-role" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add user role </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user_role][delete]" value="N">
                    <input  name="role[role_str][user_role][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["user_role"]["delete"]} id="del-user-role"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-user-role" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete user role</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user_role][edit]" value="N">
                    <input  name="role[role_str][user_role][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["user_role"]["edit"]} id="edit-user-role"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-user-role" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit user role</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user_role][update_status]" value="N">
                    <input name="role[role_str][user_role][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["user_role"]["update_status"]}  id="update-user-role"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-user-role" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable user role </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user_role][index]" value="N">
                    <input name="role[role_str][user_role][index]" type="checkbox" value="Y" data-select-val={@role.role_str["user_role"]["index"]} id="user-role-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="user-role-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View user roles </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Users </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][user][new]" value="N">
                    <input  name="role[role_str][user][new]" type="checkbox" value="Y" data-select-val={@role.role_str["user"]["new"]}  id="new-user"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-user" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add user </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user][edit]" value="N">
                    <input  name="role[role_str][user][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["user"]["edit"]} id="edit-user"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-user" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit user </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user][update_status]" value="N">
                    <input name="role[role_str][user][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["user"]["update_status"]} id="update-user"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-user" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable user </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user][audit_log]" value="N">
                    <input name="role[role_str][user][audit_log]" type="checkbox" value="Y" data-select-val={@role.role_str["user"]["audit_log"]} id="user-audit-log"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="user-audit-log" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View user logs </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][user][index]" value="N">
                    <input name="role[role_str][user][index]" type="checkbox" value="Y" data-select-val={@role.role_str["user"]["index"]} id="user-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="user-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View users </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > File Spec </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][mapping][new_spec]" value="N">
                    <input  name="role[role_str][mapping][new_spec]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["new_spec"]}  id="new-file-spec"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-file-spec" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add file spec </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][delete_file_spec]" value="N">
                    <input  name="role[role_str][mapping][delete_file_spec]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["delete_file_spec"]}  id="del-file-spec"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-file-spec" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete file spec </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][edit_spec]" value="N">
                    <input  name="role[role_str][mapping][edit_spec]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["edit_spec"]} id="edit-file-spec"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-file-spec" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit file spec </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][file_spec_update_status]" value="N">
                    <input name="role[role_str][mapping][file_spec_update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["file_spec_update_status"]} id="update-file-spec"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-file-spec" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable file specs </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][index]" value="N">
                    <input name="role[role_str][mapping][index]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["index"]} id="list-file-specs"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="list-file-specs" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View file specs </label>
                </div>
            </div>
                    <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%"> GL Account </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][mapping][new_bank_acc]" value="N">
                    <input  name="role[role_str][mapping][new_bank_acc]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["new_bank_acc"]}  id="new-bank-Acc"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-bank-Acc" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add GL Account </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][delete_bank_acc]" value="N">
                    <input  name="role[role_str][mapping][delete_bank_acc]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["delete_bank_acc"]}  id="del-bank-Acc"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-bank-Acc" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete GL Account </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][edit_bank_acc]" value="N">
                    <input  name="role[role_str][mapping][edit_bank_acc]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["edit_bank_acc"]} id="edit-bank-Acc"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-bank-Acc" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit GL Account </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][update_bank_acc_stat]" value="N">
                    <input name="role[role_str][mapping][update_bank_acc_stat]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["update_bank_acc_stat"]} id="update-bank-Acc"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-bank-Acc" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable GL Accounts </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][list_bank_acc]" value="N">
                    <input name="role[role_str][mapping][list_bank_acc]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["list_bank_acc"]} id="list-bank-Accs"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="list-bank-Accs" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View  </label>
                </div>
            </div>
            
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > GL Mappings </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][mapping][new_gl_mapping]" value="N">
                    <input  name="role[role_str][mapping][new_gl_mapping]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["new_gl_mapping"]}  id="new-gl-mapping"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-gl-mapping" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add GL Mapping </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][delete_gl_mapping]" value="N">
                    <input  name="role[role_str][mapping][delete_gl_mapping]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["delete_gl_mapping"]}  id="del-gl-mapping"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-gl-mapping" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete GL Mapping </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][edit_gl_mapping]" value="N">
                    <input  name="role[role_str][mapping][edit_gl_mapping]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["edit_gl_mapping"]} id="edit-gl-mapping"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-gl-mapping" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit GL Mapping </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][update_gl_mapping_status]" value="N">
                    <input name="role[role_str][mapping][update_gl_mapping_status]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["update_gl_mapping_status"]} id="update-gl-mapping"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-gl-mapping" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable GL Mappings </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][mapping][gl_mapping_list]" value="N">
                    <input name="role[role_str][mapping][gl_mapping_list]" type="checkbox" value="Y" data-select-val={@role.role_str["mapping"]["gl_mapping_list"]} id="list-gl-mappings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="list-gl-mappings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View GL Mappings </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >Settings </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][settings][new_directories]" value="N">
                    <input  name="role[role_str][settings][new_directories]" type="checkbox" value="Y" data-select-val={@role.role_str["settings"]["new_directories"]} id="new-directories"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-directories" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add directories </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][settings][list_directories]" value="N">
                    <input  name="role[role_str][settings][list_directories]" type="checkbox" value="Y" data-select-val={@role.role_str["settings"]["list_directories"]} id="list-directories"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="list-directories" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View directories </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][settings][new_company_settings]" value="N">
                    <input  name="role[role_str][settings][new_company_settings]" type="checkbox" value="Y" data-select-val={@role.role_str["settings"]["new_company_settings"]} id="new-company-settings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="new-company-settings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Institution Settings </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][settings][view_company_settings]" value="N">
                    <input name="role[role_str][settings][view_company_settings]" type="checkbox" value="Y" data-select-val={@role.role_str["settings"]["new_company_settings"]} id="view-company-settings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="view-company-settings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Institution Settings </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Branches </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][branch][new]" value="N">
                    <input  name="role[role_str][branch][new]" type="checkbox" value="Y" data-select-val={@role.role_str["branch"]["new"]} id="new-branch"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-branch" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Branch </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][branch][delete]" value="N">
                    <input  name="role[role_str][branch][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["branch"]["delete"]} id="del-branch"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-branch" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Branch</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][branch][edit]" value="N">
                    <input  name="role[role_str][branch][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["branch"]["edit"]} id="edit-branch"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-branch" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Branch</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][branch][update_status]" value="N">
                    <input name="role[role_str][branch][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["branch"]["update_status"]} id="update-branch"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-branch" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable Branch </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][branch][index]" value="N">
                    <input name="role[role_str][branch][index]" type="checkbox" value="Y" data-select-val={@role.role_str["branch"]["index"]} id="branch-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="branch-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Branches </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >Business units </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][business_unit][new]" value="N">
                    <input  name="role[role_str][business_unit][new]" type="checkbox" value="Y" data-select-val={@role.role_str["business_unit"]["new"]} id="new-business-unit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-business-unit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add business unit </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][business_unit][delete]" value="N">
                    <input  name="role[role_str][business_unit][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["business_unit"]["delete"]} id="del-business-unit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-business-unit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete business unit</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][business_unit][edit]" value="N">
                    <input  name="role[role_str][business_unit][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["business_unit"]["edit"]} id="edit-business-unit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-business-unit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit business unit</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][business_unit][update_status]" value="N">
                    <input name="role[role_str][business_unit][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["business_unit"]["update_status"]} id="update-business-unit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-business-unit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable business unit </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][business_unit][index]" value="N">
                    <input name="role[role_str][business_unit][index]" type="checkbox" value="Y" data-select-val={@role.role_str["business_unit"]["index"]} id="business-unit-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="business-unit-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View business units </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Employee benefit </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][employee_benefit][new]" value="N">
                    <input  name="role[role_str][employee_benefit][new]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_benefit"]["new"]} id="new-employee-benefit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-employee-benefit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add employee benefit </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_benefit][delete]" value="N">
                    <input  name="role[role_str][employee_benefit][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_benefit"]["delete"]} id="del-employee-benefit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-employee-benefit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete employee benefit</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_benefit][edit]" value="N">
                    <input  name="role[role_str][employee_benefit][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_benefit"]["edit"]} id="edit-employee-benefit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-employee-benefit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit employee benefit</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_benefit][update_status]" value="N">
                    <input name="role[role_str][employee_benefit][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_benefit"]["update_status"]} id="update-employee-benefit"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-employee-benefit" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable employee benefit </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_benefit][index]" value="N">
                    <input name="role[role_str][employee_benefit][index]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_benefit"]["index"]} id="employee-benefit-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="employee-benefit-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View employee benefits </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >Employee stats </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][employee_stats][new]" value="N">
                    <input  name="role[role_str][employee_stats][new]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_stats"]["new"]} id="new-employee-stats"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-employee-stats" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add employee stats </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_stats][delete]" value="N">
                    <input  name="role[role_str][employee_stats][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_stats"]["delete"]} id="del-employee-stats"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-employee-stats" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete employee stats</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_stats][edit]" value="N">
                    <input  name="role[role_str][employee_stats][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_stats"]["edit"]} id="edit-employee-stats"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-employee-stats" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit employee stats</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_stats][update_status]" value="N">
                    <input name="role[role_str][employee_stats][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_stats"]["update_status"]}  id="update-employee-stats"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-employee-stats" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable employee stats </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][employee_stats][index]" value="N">
                    <input name="role[role_str][employee_stats][index]" type="checkbox" value="Y" data-select-val={@role.role_str["employee_stats"]["index"]} id="employee-stats-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="employee-stats-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View employee stats </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > exchange rates </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][exchange_rate][new]" value="N">
                    <input  name="role[role_str][exchange_rate][new]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_rate"]["new"]} id="new-exchange-rate"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-exchange-rate" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add exchange rate </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_rate][delete]" value="N">
                    <input  name="role[role_str][exchange_rate][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_rate"]["delete"]} id="del-exchange-rate"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-exchange-rate" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete exchange rate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_rate][edit]" value="N">
                    <input  name="role[role_str][exchange_rate][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_rate"]["edit"]} id="edit-exchange-rate"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-exchange-rate" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit exchange rate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_rate][update_status]" value="N">
                    <input name="role[role_str][exchange_rate][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_rate"]["update_status"]} id="update-exchange-rate"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-exchange-rate" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable exchange rate </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_rate][index]" value="N">
                    <input name="role[role_str][exchange_rate][index]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_rate"]["index"]} id="exchange-rate-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="exchange-rate-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View exchange rates </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Regulatory capital </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][regulatory_capital][new]" value="N">
                    <input  name="role[role_str][regulatory_capital][new]" type="checkbox" value="Y" data-select-val={@role.role_str["regulatory_capital"]["new"]} id="new-regulatory-capital"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-regulatory-capital" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add regulatory capital </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][regulatory_capital][delete]" value="N">
                    <input  name="role[role_str][regulatory_capital][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["regulatory_capital"]["delete"]} id="del-regulatory-capital"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-regulatory-capital" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete regulatory capital</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][regulatory_capital][edit]" value="N">
                    <input  name="role[role_str][regulatory_capital][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["regulatory_capital"]["edit"]} id="edit-regulatory-capital"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-regulatory-capital" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit regulatory capital</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][regulatory_capital][update_status]" value="N">
                    <input name="role[role_str][regulatory_capital][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["regulatory_capital"]["update_status"]} id="update-regulatory-capital"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-regulatory-capital" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable regulatory capital </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][regulatory_capital][index]" value="N">
                    <input name="role[role_str][regulatory_capital][index]" type="checkbox" value="Y" data-select-val={@role.role_str["regulatory_capital"]["index"]} id="regulatory-capital-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="regulatory-capital-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View regulatory capital </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >Loan classifications </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][loan_classification][new]" value="N">
                    <input  name="role[role_str][loan_classification][new]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_classification"]["new"]} id="new-loan-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-loan-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add loan classification </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_classification][delete]" value="N">
                    <input  name="role[role_str][loan_classification][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_classification"]["delete"]} id="del-loan-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-loan-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete loan classification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_classification][edit]" value="N">
                    <input  name="role[role_str][loan_classification][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_classification"]["edit"]} id="edit-loan-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-loan-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit loan classification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_classification][update_status]" value="N">
                    <input name="role[role_str][loan_classification][update_status]" type="checkbox"  value="Y" data-select-val={@role.role_str["loan_classification"]["update_status"]} id="update-loan-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-loan-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable loan classification </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_classification][index]" value="N">
                    <input name="role[role_str][loan_classification][index]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_classification"]["index"]} id="loan-classification-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="loan-classification-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View loan classifications </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Loan sectors </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][loan_sector][new]" value="N">
                    <input  name="role[role_str][loan_sector][new]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_sector"]["new"]} id="new-loan-sector"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-loan-sector" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add loan sector </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_sector][delete]" value="N">
                    <input  name="role[role_str][loan_sector][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_sector"]["delete"]} id="del-loan-sector"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-loan-sector" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete loan sector</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_sector][edit]" value="N">
                    <input  name="role[role_str][loan_sector][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_sector"]["edit"]} id="edit-loan-sector"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-loan-sector" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit loan sector</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_sector][update_status]" value="N">
                    <input name="role[role_str][loan_sector][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_sector"]["update_status"]}  id="update-loan-sector"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-loan-sector" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable loan sector </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_sector][index]" value="N">
                    <input name="role[role_str][loan_sector][index]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_sector"]["index"]} id="loan-sector-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="loan-sector-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View loan sectors </label>
                </div>
            </div>
    
           <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > loan products </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][loan_product][new]" value="N">
                    <input  name="role[role_str][loan_product][new]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_product"]["new"]} id="new-loan-product"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-loan-product" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add loan product </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_product][delete]" value="N">
                    <input  name="role[role_str][loan_product][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_product"]["delete"]} id="del-loan-product"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-loan-product" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete loan product</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_product][edit]" value="N">
                    <input  name="role[role_str][loan_product][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_product"]["edit"]} id="edit-loan-product"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-loan-product" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit loan product</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_product][update_status]" value="N">
                    <input name="role[role_str][loan_product][update_status]" type="checkbox"  value="Y" data-select-val={@role.role_str["loan_product"]["update_status"]} id="update-loan-product"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-loan-product" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable loan product </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][loan_product][index]" value="N">
                    <input name="role[role_str][loan_product][index]" type="checkbox" value="Y" data-select-val={@role.role_str["loan_product"]["index"]} id="loan-product-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="loan-product-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View loan products </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Past due classifications </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][past_due_classification][new]" value="N">
                    <input  name="role[role_str][past_due_classification][new]" type="checkbox" value="Y" data-select-val={@role.role_str["past_due_classification"]["new"]} id="new-past-due-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-past-due-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add past due classification </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][past_due_classification][delete]" value="N">
                    <input  name="role[role_str][past_due_classification][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["past_due_classification"]["delete"]} id="del-past-due-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-past-due-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete past due classification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][past_due_classification][edit]" value="N">
                    <input  name="role[role_str][past_due_classification][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["past_due_classification"]["edit"]} id="edit-past-due-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-past-due-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit past due classification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][past_due_classification][update_status]" value="N">
                    <input name="role[role_str][past_due_classification][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["past_due_classification"]["update_status"]} id="update-past-due-classification"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-past-due-classification" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable past due classification </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][past_due_classification][index]" value="N">
                    <input name="role[role_str][past_due_classification][index]" type="checkbox" value="Y" data-select-val={@role.role_str["past_due_classification"]["index"]} id="past-due-classification-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="past-due-classification-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View past due classifications </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Scheme codes </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][scheme_code][new]" value="N">
                    <input  name="role[role_str][scheme_code][new]" type="checkbox" value="Y" data-select-val={@role.role_str["scheme_code"]["new"]} id="new-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add scheme code </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][scheme_code][delete]" value="N">
                    <input  name="role[role_str][scheme_code][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["scheme_code"]["delete"]} id="del-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete scheme code</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][scheme_code][edit]" value="N">
                    <input  name="role[role_str][scheme_code][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["scheme_code"]["edit"]} id="edit-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit scheme code</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][scheme_code][update_status]" value="N">                                                                     
                    <input name="role[role_str][scheme_code][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["scheme_code"]["update_status"]} id="update-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable scheme code </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][scheme_code][index]" value="N">
                    <input name="role[role_str][scheme_code][index]" type="checkbox" value="Y" data-select-val={@role.role_str["scheme_code"]["index"]} id="scheme-code-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="scheme-code-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View scheme codes </label>
                </div>
            </div>
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Weekly BOZ Balance </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][weekly_boz_balance][new]" value="N">
                    <input  name="role[role_str][weekly_boz_balance][new]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_boz_balance"]["new"]} id="new-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Weekly BOZ Balance</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_boz_balance][delete]" value="N">
                    <input  name="role[role_str][weekly_boz_balance][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_boz_balance"]["delete"]} id="del-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Weekly BOZ Balance</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_boz_balance][edit]" value="N">
                    <input  name="role[role_str][weekly_boz_balance][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_boz_balance"]["edit"]} id="edit-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Weekly BOZ Balance</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_boz_balance][update_status]" value="N">                                                                     
                    <input name="role[role_str][weekly_boz_balance][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_boz_balance"]["update_status"]} id="update-scheme-code"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-scheme-code" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/disable Weekly BOZ Balance </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_boz_balance][index]" value="N">
                    <input name="role[role_str][weekly_boz_balance][index]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_boz_balance"]["index"]} id="scheme-code-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="scheme-code-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Weekly BOZ Balance </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >BOZ Prudential Report </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][prudential][new_pru]" value="N">
                    <input  name="role[role_str][prudential][new_pru]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["new_pru"]} id="new-pru"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-pru" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Create New Prudential </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_fin_gen]" value="N">
                    <input  name="role[role_str][prudential][pru_fin_gen]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_fin_gen"]} id="pru_fin_gen"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru-fin-ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Generate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_cr_gen]" value="N">
                    <input  name="role[role_str][prudential][pru_cr_gen]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_cr_gen"]} id="pru_cr_gen"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru-fin-ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Generate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_fin_ver]" value="N">
                    <input  name="role[role_str][prudential][pru_fin_ver]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_fin_ver"]} id="pru-fin-ver"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru-fin-ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Verification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_cr_ver]" value="N">
                    <input  name="role[role_str][prudential][pru_cr_ver]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_cr_ver"]} id="pru_cr_ver"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru_cr_ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Verification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_fin_apvl]" value="N">                                                                     
                    <input name="role[role_str][prudential][pru_fin_apvl]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_fin_apvl"]} id="pru_fin_apvl"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru_fin_apvl" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Approval</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_cr_apvl]" value="N">
                    <input name="role[role_str][prudential][pru_cr_apvl]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_cr_apvl"]} id="pru_cr_apvl"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru_cr_apvl" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Approval</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][prudential][pru_auth]" value="N">
                    <input name="role[role_str][prudential][pru_auth]" type="checkbox" value="Y" data-select-val={@role.role_str["prudential"]["pru_auth"]} id="pru_auth"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="pru_auth" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Authorisation</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" >Weekly Returns Report </span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][weekly_returns][new_weekly]" value="N">
                    <input  name="role[role_str][weekly_returns][new_weekly]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["new_weekly"]} id="new-weekly"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-pru" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Create New Weekly Return</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_fin_gen]" value="N">
                    <input  name="role[role_str][weekly_returns][weekly_fin_gen]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_fin_gen"]} id="weekly_fin_gen"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_fin_gen" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Generate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_cr_gen]" value="N">
                    <input  name="role[role_str][weekly_returns][weekly_cr_gen]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_cr_gen"]} id="weekly_cr_gen"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_cr_gen" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Generate</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_fin_ver]" value="N">
                    <input  name="role[role_str][weekly_returns][weekly_fin_ver]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_fin_ver"]} id="weekly_fin_ver"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_fin_ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Verification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_cr_ver]" value="N">
                    <input  name="role[role_str][weekly_returns][weekly_cr_ver]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_cr_ver"]} id="weekly_cr_ver"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_cr_ver" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Verification</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_fin_apvl]" value="N">                                                                     
                    <input name="role[role_str][weekly_returns][weekly_fin_apvl]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_fin_apvl"]} id="weekly_fin_apvl"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_fin_apvl" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Finance Approval</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_cr_apvl]" value="N">
                    <input name="role[role_str][weekly_returns][weekly_cr_apvl]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_cr_apvl"]} id="weekly_cr_apvl"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_cr_apvl" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Credit Approval</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_cfo_auth]" value="N">
                    <input name="role[role_str][weekly_returns][weekly_cfo_auth]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_cfo_auth"]} id="weekly_cfo_auth"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_cfo_auth" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">CFO Authorisation</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_returns][weekly_ceo_auth]" value="N">
                    <input name="role[role_str][weekly_returns][weekly_ceo_auth]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_returns"]["weekly_ceo_auth"]} id="weekly_ceo_auth"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_ceo_auth" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">CEO Authorisation</label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%"> Securities </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][securities][new]" value="N">
                    <input  name="role[role_str][securities][new]" type="checkbox" value="Y" data-select-val={@role.role_str["securities"]["new"]} id="new-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Security </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][securities][delete]" value="N">
                    <input  name="role[role_str][securities][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["securities"]["delete"]} id="del-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Security</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][securities][edit]" value="N">
                    <input  name="role[role_str][securities][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["securities"]["edit"]} id="edit-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Security</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][securities][update_status]" value="N">
                    <input name="role[role_str][securities][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["securities"]["update_status"]} id="update-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Security </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][securities][index]" value="N">
                    <input name="role[role_str][securities][index]" type="checkbox" value="Y" data-select-val={@role.role_str["securities"]["index"]} id="securities-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="securities-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Securities </label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%"> Finance Uploads </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][finance_uploads][new]" value="N">
                    <input  name="role[role_str][finance_uploads][new]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_uploads"]["new"]} id="new-finance_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-finance_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Finance Upload </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][finance_uploads][delete]" value="N">
                    <input  name="role[role_str][finance_uploads][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_uploads"]["delete"]} id="del-finance_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-finance_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete finance uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][finance_uploads][edit]" value="N">
                    <input  name="role[role_str][finance_uploads][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_uploads"]["edit"]} id="edit-finance_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-finance_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit finance uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][finance_uploads][update_status]" value="N">
                    <input name="role[role_str][finance_uploads][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_uploads"]["update_status"]} id="update-finance_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-finance_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable finance uploads </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][finance_uploads][index]" value="N">
                    <input name="role[role_str][finance_uploads][index]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_uploads"]["index"]} id="finance_uploads-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="finance_uploads-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View finance uploads </label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%"> Credit Uploads </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][credit_uploads][new]" value="N">
                    <input  name="role[role_str][credit_uploads][new]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_uploads"]["new"]} id="new-credit_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-credit_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Credit Upload </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][credit_uploads][delete]" value="N">
                    <input  name="role[role_str][credit_uploads][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_uploads"]["delete"]} id="del-credit_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-credit_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Credit uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][credit_uploads][edit]" value="N">
                    <input  name="role[role_str][credit_uploads][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_uploads"]["edit"]} id="edit-credit_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-credit_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Credit uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][credit_uploads][update_status]" value="N">
                    <input name="role[role_str][credit_uploads][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_uploads"]["update_status"]} id="update-credit_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-credit_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Credit uploads </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][credit_uploads][index]" value="N">
                    <input name="role[role_str][credit_uploads][index]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_uploads"]["index"]} id="credit_uploads-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="credit_uploads-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Credit uploads </label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%"> Weekly Uploads </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][weekly_uploads][new]" value="N">
                    <input  name="role[role_str][weekly_uploads][new]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_uploads"]["new"]} id="new-weekly_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-weekly_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Weekly Upload </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_uploads][delete]" value="N">
                    <input  name="role[role_str][weekly_uploads][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_uploads"]["delete"]} id="del-weekly_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-weekly_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Weekly uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_uploads][edit]" value="N">
                    <input  name="role[role_str][weekly_uploads][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_uploads"]["edit"]} id="edit-weekly_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-weekly_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Weekly uploads</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_uploads][update_status]" value="N">
                    <input name="role[role_str][weekly_uploads][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_uploads"]["update_status"]} id="update-weekly_uploads"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-weekly_uploads" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Weekly uploads </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][weekly_uploads][index]" value="N">
                    <input name="role[role_str][weekly_uploads][index]" type="checkbox" value="Y" data-select-val={@role.role_str["weekly_uploads"]["index"]} id="weekly_uploads-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="weekly_uploads-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Weekly uploads </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Allowance for the loan losess </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][allowances][new]" value="N">
                    <input  name="role[role_str][allowances][new]" type="checkbox" value="Y" data-select-val={@role.role_str["allowances"]["new"]} id="new-allowances"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-allowances" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Allowances</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][allowances][delete]" value="N">
                    <input  name="role[role_str][allowances][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["allowances"]["delete"]} id="del-allowances"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-allowances" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete allowances</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][allowances][edit]" value="N">
                    <input  name="role[role_str][allowances][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["allowances"]["edit"]} id="edit-allowances"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-allowances" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit allowances</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][allowances][update_status]" value="N">
                    <input name="role[role_str][allowances][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["allowances"]["update_status"]} id="update-allowances"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-allowances" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Security </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][allowances][index]" value="N">
                    <input name="role[role_str][allowances][index]" type="checkbox" value="Y" data-select-val={@role.role_str["allowances"]["index"]} id="allowances-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="allowances-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View allowances </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Off Balance Sheet </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][off_blc_sheet][new]" value="N">
                    <input  name="role[role_str][off_blc_sheet][new]" type="checkbox" value="Y" data-select-val={@role.role_str["off_blc_sheet"]["new"]} id="new-off-blc-sheet"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-off-blc-sheet" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add off Balance sheet</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][off_blc_sheet][delete]" value="N">
                    <input  name="role[role_str][off_blc_sheet][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["off_blc_sheet"]["delete"]} id="del-off-blc-sheet"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-off-blc-sheet" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete off Balance sheet</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][off_blc_sheet][edit]" value="N">
                    <input  name="role[role_str][off_blc_sheet][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["off_blc_sheet"]["edit"]} id="edit-off-blc-sheet"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-off-blc-sheet" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit off Balance sheet</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][off_blc_sheet][update_status]" value="N">
                    <input name="role[role_str][off_blc_sheet][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["off_blc_sheet"]["update_status"]} id="update-off-blc-sheet"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-off-blc-sheet" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Security </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][off_blc_sheet][index]" value="N">
                    <input name="role[role_str][off_blc_sheet][index]" type="checkbox" value="Y" data-select-val={@role.role_str["off_blc_sheet"]["index"]} id="off-blc-sheet-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="off-blc-sheet-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View off Balance sheet </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Share Holders </span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][share_holders][new]" value="N">
                    <input  name="role[role_str][share_holders][new]" type="checkbox" value="Y" data-select-val={@role.role_str["share_holders"]["new"]} id="new-share-holders"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-share-holders" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add off Share Holders</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][share_holders][delete]" value="N">
                    <input  name="role[role_str][share_holders][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["share_holders"]["delete"]} id="del-share-holders"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-share-holders" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete off Share Holders</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][share_holders][edit]" value="N">
                    <input  name="role[role_str][share_holders][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["share_holders"]["edit"]} id="edit-share-holders"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-share-holders" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit off Share Holders</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][share_holders][update_status]" value="N">
                    <input name="role[role_str][share_holders][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["share_holders"]["update_status"]} id="update-share-holders"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-share-holders" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Security </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][share_holders][index]" value="N">
                    <input name="role[role_str][share_holders][index]" type="checkbox" value="Y" data-select-val={@role.role_str["share_holders"]["index"]} id="share-holders-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="share-holders-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View off Share Holders </label>
                </div>
            </div>
    
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Securities Holdings</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][secure_holdings][new]" value="N">
                    <input  name="role[role_str][secure_holdings][new]" type="checkbox" value="Y" data-select-val={@role.role_str["secure_holdings"]["new"]} id="new-secure-holdings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-secure-holdings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add off Securities Holdings</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][secure_holdings][delete]" value="N">
                    <input  name="role[role_str][secure_holdings][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["secure_holdings"]["delete"]} id="del-secure-holdings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-secure-holdings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete off Securities Holdings</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][secure_holdings][edit]" value="N">
                    <input  name="role[role_str][secure_holdings][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["secure_holdings"]["edit"]} id="edit-secure-holdings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-secure-holdings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit off Securities Holdings</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][secure_holdings][update_status]" value="N">
                    <input name="role[role_str][secure_holdings][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["secure_holdings"]["update_status"]} id="update-secure-holdings"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-secure-holdings" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Securities Holdings </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][secure_holdings][index]" value="N">
                    <input name="role[role_str][secure_holdings][index]" type="checkbox" value="Y" data-select-val={@role.role_str["secure_holdings"]["index"]} id="secure-holdings-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="secure-holdings-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View off Securities Holdings</label>
                </div>
            </div>
   
            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Current & Deffered Tax</span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][tax][new]" value="N">
                    <input  name="role[role_str][tax][new]" type="checkbox" value="Y" data-select-val={@role.role_str["tax"]["new"]} id="new-tax"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-tax" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Current & Deffered</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][tax][delete]" value="N">
                    <input  name="role[role_str][tax][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["tax"]["delete"]} id="del-tax"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-tax" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][tax][edit]" value="N">
                    <input  name="role[role_str][tax][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["tax"]["edit"]} id="edit-tax"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-tax" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][tax][update_status]" value="N">
                    <input name="role[role_str][tax][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["tax"]["update_status"]} id="update-tax"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-tax" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Tax </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][tax][index]" value="N">
                    <input name="role[role_str][tax][index]" type="checkbox" value="Y" data-select-val={@role.role_str["tax"]["index"]} id="tax-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="tax-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Current & Deffered Tax</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Balances Due Domestic</span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][balance_due_domestic][new]" value="N">
                    <input  name="role[role_str][balance_due_domestic][new]" type="checkbox" value="Y" data-select-val={@role.role_str["balance_due_domestic"]["new"]} id="new-balance-due-domestic"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-balance-due-domestic" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Current & Deffered</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][balance_due_domestic][delete]" value="N">
                    <input  name="role[role_str][balance_due_domestic][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["balance_due_domestic"]["delete"]} id="del-balance-due-domestic"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-balance-due-domestic" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][balance_due_domestic][edit]" value="N">
                    <input  name="role[role_str][balance_due_domestic][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["balance_due_domestic"]["edit"]} id="edit-balance-due-domestic"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-balance-due-domestic" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][balance_due_domestic][update_status]" value="N">
                    <input name="role[role_str][balance_due_domestic][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["balance_due_domestic"]["update_status"]} id="update-balance-due-domestic"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-balance-due-domestic" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Tax </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][balance_due_domestic][index]" value="N">
                    <input name="role[role_str][balance_due_domestic][index]" type="checkbox" value="Y" data-select-val={@role.role_str["balance_due_domestic"]["index"]} id="balance-due-domestic-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="balance-due-domestic-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Current & Deffered Tax</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Repossessed Properties</span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][repossessed_properties][new]" value="N">
                    <input  name="role[role_str][repossessed_properties][new]" type="checkbox" value="Y" data-select-val={@role.role_str["repossessed_properties"]["new"]} id="new-repossessed-properties"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-repossessed-properties" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Current & Deffered</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][repossessed_properties][delete]" value="N">
                    <input  name="role[role_str][repossessed_properties][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["repossessed_properties"]["delete"]} id="del-repossessed-properties"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-repossessed-properties" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][repossessed_properties][edit]" value="N">
                    <input  name="role[role_str][repossessed_properties][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["repossessed_properties"]["edit"]} id="edit-repossessed-properties"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-repossessed-properties" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Current & Deffered Tax</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][repossessed_properties][update_status]" value="N">
                    <input name="role[role_str][repossessed_properties][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["repossessed_properties"]["update_status"]} id="update-repossessed-properties"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-repossessed-properties" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Tax </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][repossessed_properties][index]" value="N">
                    <input name="role[role_str][repossessed_properties][index]" type="checkbox" value="Y" data-select-val={@role.role_str["repossessed_properties"]["index"]} id="repossessed-properties-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="repossessed-properties-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Current & Deffered Tax</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Insertions</span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][insertion][new]" value="N">
                    <input  name="role[role_str][insertion][new]" type="checkbox" value="Y" data-select-val={@role.role_str["insertion"]["new"]} id="new-insertion"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-insertion" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Current & Insertion</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][insertion][delete]" value="N">
                    <input  name="role[role_str][insertion][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["insertion"]["delete"]} id="del-insertion"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-insertion" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Current & Insertion</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][insertion][edit]" value="N">
                    <input  name="role[role_str][insertion][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["insertion"]["edit"]} id="edit-insertion"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-insertion" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Current & Insertion</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][insertion][update_status]" value="N">
                    <input name="role[role_str][insertion][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["insertion"]["update_status"]} id="update-insertion"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-insertion" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Insertion</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][insertion][index]" value="N">
                    <input name="role[role_str][insertion][index]" type="checkbox" value="Y" data-select-val={@role.role_str["insertion"]["index"]} id="insertion-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="insertion-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Current & Insertion</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Submission Dates</span>
            
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][submission_dates][new]" value="N">
                    <input  name="role[role_str][submission_dates][new]" type="checkbox" value="Y" data-select-val={@role.role_str["submission_dates"]["new"]} id="new-submission-dates"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-submission-dates" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Submission Date</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][submission_dates][delete]" value="N">
                    <input  name="role[role_str][submission_dates][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["submission_dates"]["delete"]} id="del-submission-dates"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-submission-dates" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Submission Date</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][submission_dates][edit]" value="N">
                    <input  name="role[role_str][submission_dates][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["submission_dates"]["edit"]} id="edit-submission-dates"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-submission-dates" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Submission Date</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][submission_dates][update_status]" value="N">
                    <input name="role[role_str][submission_dates][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["submission_dates"]["update_status"]} id="update-submission-dates"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-submission-dates" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Submission Date</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][submission_dates][index]" value="N">
                    <input name="role[role_str][submission_dates][index]" type="checkbox" value="Y" data-select-val={@role.role_str["submission_dates"]["index"]} id="submission-dates-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="submission-dates-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Submission Dates</label>
                </div>
            </div>


            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Quarterly</span>
            
               
                
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][quarterly][index]" value="N">
                    <input name="role[role_str][quarterly][index]" type="checkbox" value="Y" data-select-val={@role.role_str["quarterly"]["index"]} id="quarterly-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="quarterly-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Current & Quarterly</label>
                </div>
            </div>

<!-- Credit Adjustments -->
<div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300">
    <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
        <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
    </span>
    <span class="mt-30" style="margin-top:20%">Credit Adjustments</span>

    <div class="flex items-center mt-2">
        <input type="hidden" name="role[role_str][credit_adjustments][new]" value="N">
        <input name="role[role_str][credit_adjustments][new]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_adjustments"]["new"]} id="new-credit-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 chileshe">
        <label for="new-credit-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Credit Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_adjustments][delete]" value="N">
        <input name="role[role_str][credit_adjustments][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_adjustments"]["delete"]} id="del-credit-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="del-credit-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Credit Adjustments</label> 
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_adjustments][edit]" value="N">
        <input name="role[role_str][credit_adjustments][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_adjustments"]["edit"]} id="edit-credit-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="edit-credit-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Credit Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_adjustments][update_status]" value="N">
        <input name="role[role_str][credit_adjustments][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_adjustments"]["update_status"]} id="update-credit-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="update-credit-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Credit Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_adjustments][index]" value="N">
        <input name="role[role_str][credit_adjustments][index]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_adjustments"]["index"]} id="credit-adjustments-index"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="credit-adjustments-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Credit Adjustments</label>
    </div>
</div>

<!-- Finance Insertions -->
<div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300">
    <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
        <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
    </span>
    <span class="mt-30" style="margin-top:20%">Finance Insertions</span>

    <div class="flex items-center mt-2">
        <input type="hidden" name="role[role_str][finance_insertions][new]" value="N">
        <input name="role[role_str][finance_insertions][new]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_insertions"]["new"]} id="new-finance-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 chileshe">
        <label for="new-finance-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Finance Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_insertions][delete]" value="N">
        <input name="role[role_str][finance_insertions][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_insertions"]["delete"]} id="del-finance-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="del-finance-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Finance Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_insertions][edit]" value="N">
        <input name="role[role_str][finance_insertions][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_insertions"]["edit"]} id="edit-finance-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="edit-finance-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Finance Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_insertions][update_status]" value="N">
        <input name="role[role_str][finance_insertions][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_insertions"]["update_status"]} id="update-finance-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="update-finance-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Finance Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_insertions][index]" value="N">
        <input name="role[role_str][finance_insertions][index]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_insertions"]["index"]} id="finance-insertions-index"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="finance-insertions-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Finance Insertions</label>
    </div>
</div>

<!-- Credit Insertions -->
<div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300">
    <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
        <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
    </span>
    <span class="mt-30" style="margin-top:20%">Credit Insertions</span>

    <div class="flex items-center mt-2">
        <input type="hidden" name="role[role_str][credit_insertions][new]" value="N">
        <input name="role[role_str][credit_insertions][new]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_insertions"]["new"]} id="new-credit-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 chileshe">
        <label for="new-credit-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Credit Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_insertions][delete]" value="N">
        <input name="role[role_str][credit_insertions][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_insertions"]["delete"]} id="del-credit-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="del-credit-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Credit Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_insertions][edit]" value="N">
        <input name="role[role_str][credit_insertions][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_insertions"]["edit"]} id="edit-credit-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="edit-credit-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Credit Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_insertions][update_status]" value="N">
        <input name="role[role_str][credit_insertions][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_insertions"]["update_status"]} id="update-credit-insertions"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="update-credit-insertions" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Credit Insertions</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][credit_insertions][index]" value="N">
        <input name="role[role_str][credit_insertions][index]" type="checkbox" value="Y" data-select-val={@role.role_str["credit_insertions"]["index"]} id="credit-insertions-index"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="credit-insertions-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Credit Insertions</label>
    </div>
</div>
            <!-- Finance Adjustments -->
<div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300">
    <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
        <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
    </span>
    <span class="mt-30" style="margin-top:20%">Finance Adjustments</span>

    <div class="flex items-center mt-2">
        <input type="hidden" name="role[role_str][finance_adjustments][new]" value="N">
        <input name="role[role_str][finance_adjustments][new]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_adjustments"]["new"]} id="new-finance-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 chileshe">
        <label for="new-finance-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Finance Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_adjustments][delete]" value="N">
        <input name="role[role_str][finance_adjustments][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_adjustments"]["delete"]} id="del-finance-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="del-finance-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Finance Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_adjustments][edit]" value="N">
        <input name="role[role_str][finance_adjustments][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_adjustments"]["edit"]} id="edit-finance-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="edit-finance-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Finance Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_adjustments][update_status]" value="N">
        <input name="role[role_str][finance_adjustments][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_adjustments"]["update_status"]} id="update-finance-adjustments"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="update-finance-adjustments" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Finance Adjustments</label>
    </div>
    <div class="flex items-center">
        <input type="hidden" name="role[role_str][finance_adjustments][index]" value="N">
        <input name="role[role_str][finance_adjustments][index]" type="checkbox" value="Y" data-select-val={@role.role_str["finance_adjustments"]["index"]} id="finance-adjustments-index"
            class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded chileshe">
        <label for="finance-adjustments-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Finance Adjustments</label>
    </div>
</div>

            <!-- Reckon -->

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">exchange Placement</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][exchange_placement][new]" value="N">
                    <input  name="role[role_str][exchange_placement][new]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_placement"]["new"]} id="new-exchange_placement"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-exchange_placement" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add exchange Placement</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_placement][delete]" value="N">
                    <input  name="role[role_str][exchange_placement][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_placement"]["delete"]} id="del-exchange_placement"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-exchange_placement" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete exchange Placement</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_placement][edit]" value="N">
                    <input  name="role[role_str][exchange_placement][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_placement"]["edit"]} id="edit-exchange_placement"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-exchange_placement" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit exchange Placement</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_placement][update_status]" value="N">
                    <input name="role[role_str][exchange_placement][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_placement"]["update_status"]} id="update-exchange_placement"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-exchange_placement" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable exchange Placement</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][exchange_placement][index]" value="N">
                    <input name="role[role_str][exchange_placement][index]" type="checkbox" value="Y" data-select-val={@role.role_str["exchange_placement"]["index"]} id="exchange_placement-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="exchange_placement-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View exchange Placement</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Technological Infrastructure</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][technological_infrastructure][new]" value="N">
                    <input  name="role[role_str][technological_infrastructure][new]" type="checkbox" value="Y" data-select-val={@role.role_str["technological_infrastructure"]["new"]} id="new-technological_infrastructure"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-technological_infrastructure" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add technological infrastructure</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][technological_infrastructure][delete]" value="N">
                    <input  name="role[role_str][technological_infrastructure][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["technological_infrastructure"]["delete"]} id="del-technological_infrastructure"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-technological_infrastructure" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete technological infrastructure</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][technological_infrastructure][edit]" value="N">
                    <input  name="role[role_str][technological_infrastructure][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["technological_infrastructure"]["edit"]} id="edit-technological_infrastructure"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-technological_infrastructure" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit technological infrastructure</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][technological_infrastructure][update_status]" value="N">
                    <input name="role[role_str][technological_infrastructure][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["technological_infrastructure"]["update_status"]} id="update-technological_infrastructure"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-technological_infrastructure" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable technological infrastructure</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][technological_infrastructure][index]" value="N">
                    <input name="role[role_str][technological_infrastructure][index]" type="checkbox" value="Y" data-select-val={@role.role_str["technological_infrastructure"]["index"]} id="technological_infrastructure-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="technological_infrastructure-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View technological infrastructure</label>
                </div>
            </div>

            <!-- ID-->

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Institution Details</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][institution_details][new]" value="N">
                    <input  name="role[role_str][institution_details][new]" type="checkbox" value="Y" data-select-val={@role.role_str["institution_details"]["new"]} id="new-institution-details"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-institution-details" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Institution Details</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][institution_details][delete]" value="N">
                    <input  name="role[role_str][institution_details][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["institution_details"]["delete"]} id="del-institution-details"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-institution-details" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Institution Details</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][institution_details][edit]" value="N">
                    <input  name="role[role_str][institution_details][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["institution_details"]["edit"]} id="edit-institution_details"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-institution-details" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Institution Details</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][institution_details][update_status]" value="N">
                    <input name="role[role_str][institution_details][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["institution_details"]["update_status"]} id="update-institution-details"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-institution-details" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Institution Details</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][institution_details][index]" value="N">
                    <input name="role[role_str][institution_details][index]" type="checkbox" value="Y" data-select-val={@role.role_str["institution_details"]["index"]} id="institution-details-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="institution-details-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Institution Details</label>
                </div>
            </div>

            <!-- ID-->

            <!-- Counterparty securities -->

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Counterparty Securities</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][counterparty_securities][new]" value="N">
                    <input  name="role[role_str][counterparty_securities][new]" type="checkbox" value="Y" data-select-val={@role.role_str["counterparty_securities"]["new"]} id="new-counterparty-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-counterparty-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add Counterparty Securities</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][counterparty_securities][delete]" value="N">
                    <input  name="role[role_str][counterparty_securities][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["counterparty_securities"]["delete"]} id="del-counterparty-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-counterparty-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete Counterparty Securities</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][counterparty_securities][edit]" value="N">
                    <input  name="role[role_str][counterparty_securities][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["counterparty_securities"]["edit"]} id="edit-counterparty-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-counterparty-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit Counterparty Securities</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][counterparty_securities][update_status]" value="N">
                    <input name="role[role_str][counterparty_securities][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["counterparty_securities"]["update_status"]} id="update-counterparty-securities"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-counterparty-securities" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable Counterparty Securities</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][counterparty_securities][index]" value="N">
                    <input name="role[role_str][counterparty_securities][index]" type="checkbox" value="Y" data-select-val={@role.role_str["counterparty_securities"]["index"]} id="counterparty-securities-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="counterparty-securities-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Counterparty Securities</label>
                </div>
            </div>

            <!-- Counterparty securities -->



            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Government Accounts</span>
    
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][govt_account][new]" value="N">
                    <input  name="role[role_str][govt_account][new]" type="checkbox" value="Y" data-select-val={@role.role_str["govt_account"]["new"]} id="new-govt_account"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-govt_account" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add government account</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][govt_account][delete]" value="N">
                    <input  name="role[role_str][govt_account][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["govt_account"]["delete"]} id="del-govt_account"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-govt_account" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete government account</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][govt_account][edit]" value="N">
                    <input  name="role[role_str][govt_account][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["govt_account"]["edit"]} id="edit-govt_account"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-govt_account" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit government account</label>
                </div>

                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][govt_account][update_status]" value="N">
                    <input name="role[role_str][govt_account][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["govt_account"]["update_status"]} id="update-govt-account"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-govt-account" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable goverment accounts </label>
                </div>
                
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][govt_account][index]" value="N">
                    <input name="role[role_str][govt_account][index]" type="checkbox" value="Y" data-select-val={@role.role_str["govt_account"]["index"]} id="govt-account-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="govt-account-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View goverment account</label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%" > Debtors Book Analysis </span>
                <div class="flex items-center mt-2">
                    <input type="hidden" name="role[role_str][debtors_book_analysis][new]" value="N">
                    <input  name="role[role_str][debtors_book_analysis][new]" type="checkbox" value="Y" data-select-val={@role.role_str["debtors_book_analysis"]["new"]} id="new-debtors-book-analysis"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300  focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe" >
                    <label for="new-debtors-book-analysis" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Add debtors book analysis</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][debtors_book_analysis][delete]" value="N">
                    <input  name="role[role_str][debtors_book_analysis][delete]" type="checkbox" value="Y" data-select-val={@role.role_str["debtors_book_analysis"]["delete"]} id="del-debtors-book-analysis"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="del-debtors-book-analysis" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Delete debtors book analysis</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][debtors_book_analysis][edit]" value="N">
                    <input  name="role[role_str][debtors_book_analysis][edit]" type="checkbox" value="Y" data-select-val={@role.role_str["debtors_book_analysis"]["edit"]} id="edit-debtors-book-analysis"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="edit-debtors-book-analysis" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Edit debtors book analysis</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][debtors_book_analysis][update_status]" value="N">
                    <input name="role[role_str][debtors_book_analysis][update_status]" type="checkbox" value="Y" data-select-val={@role.role_str["debtors_book_analysis"]["update_status"]} id="update-debtors-book-analysis"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="update-debtors-book-analysis" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">Enable/Disable debtors book analysis </label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][debtors_book_analysis][index]" value="N">
                    <input name="role[role_str][debtors_book_analysis][index]" type="checkbox" value="Y" data-select-val={@role.role_str["debtors_book_analysis"]["index"]} id="debtors-book-analysis-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="debtors-book-analysis-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View debtors book analysis </label>
                </div>
            </div>

            <div class="shadow p-5 rounded rounded-medium bg-white transition ease-in-out delay-150 bg-gray-150 hover:-translate-y-1 hover:scale-110 hover:bg-gray-150 duration-300  ">
                <span class="inline-block h-4 w-4 overflow-hidden rounded-full bg-gray-100">
                    <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </span>
                <span class="mt-30" style="margin-top:20%">Reckon</span>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][reckon][index]" value="N">
                    <input name="role[role_str][reckon][index]" type="checkbox" value="Y" data-select-val={@role.role_str["reckon"]["index"]} id="reckon-index"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="reckon-index" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View balance sheet</label>
                </div>
                <div class="flex items-center">
                    <input type="hidden" name="role[role_str][reckon][reckon_is]" value="N">
                    <input name="role[role_str][reckon][reckon_is]" type="checkbox" value="Y" data-select-val={@role.role_str["reckon"]["reckon_is"]} id="reckon-reckon_is"
                        class="w-4 h-4 text-blue-600 bg-white-100 border-white-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-white-600 chileshe">
                    <label for="reckon-reckon_is" class="ml-2 text-sm font-normal text-white-900 dark:text-white-300">View Income Statement</label>
                </div>
            </div>

            <div class="sm:col-span-2 mt-5 ml-4 mb-8 ">
                <label for="Role Description" class="block text-sm font-medium leading-6 text-gray-900">Role Description</label>
                <div class="mb-2">
                <%= text_input(
                    f,
                    :role_desc,
                    autocomplete: "Role Description",
                    placeholder: "Role Description",
                    class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                    required: "true"
                ) %> <%= error_tag(f, :role_desc) %>
                </div>
            </div>
    
        </div>
    
    
        <div class="mt-6 flex items-center justify-end gap-x-6">
            <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
        </div>

    </.form>
    
