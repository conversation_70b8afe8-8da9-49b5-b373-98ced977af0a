
defmodule MisReports.Workers.BozReq.Schedule19 do

  # Entry point for processing an item.
  def perform(item) do
    # Attempt to decode the JSON string contained in the schedule_19 field.
    decoded_item =
      case item.schedule_19 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # Retrieve company settings (e.g., institution code) needed for the report.
    settings = MisReports.Utilities.get_comapany_settings_params()

    # Construct and return the report map with all required fields.
    %{
      "ReturnKey" => "ZM-UTSCH19UT001",
      "InstCode" => "#{settings.institution_code}",
      #


"FinYear" => 2020,
      "StartDate" => "2020-01-01T00:00:00",
      "EndDate" => "2020-01-31T00:00:00",


      "ReturnItemsList" => [],
      "PeriodFrequency" => "Monthly",
      "DynamicItemsList" => [
        %{
          "Area" => 472,
          "_areaName" => "liquidity",

          "DynamicItems" => map_data(decoded_item) |> format_values()
        }
      ]
    }
  end

  # Iterates through a map, formatting each value by removing commas.
  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  # Removes commas from a numeric string; if nil, defaults to "0".
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  # Processes a list of records by generating a list of dynamic items with specific codes.
  def map_data(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->

      index = index + 1
      [
        %{"Code" => "#{index}.1", "Value" => map["counter_party"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.2", "Value" => map["limit"], "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.3", "Value" => map["currency"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.4", "Value" => map["clean_secured "], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.5", "Value" => map["securiy_type"], "_dataType" => "TEXT"}
      ]
    end)
  end

  # Formats each dynamic item based on its declared data type.
  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map) |> format_as_string()  # Add this line
        "TEXT" ->
          update_text_value(map)
        "DATE" ->
          update_text_value(map)
        _ ->
          map
      end
    end)
  end

  # For numeric values: if empty or nil, default to "0.00", or handle the {sign, exp, coef} map
  defp update_numeric_value(map) do
    value = Map.get(map, "Value")

    formatted_value =
      case value do
        nil -> "0.00"
        "" -> "0.00"
        %{sign: sign, exp: exp, coef: coef} ->  # Handle the special map case
          calculate_and_format_numeric(sign, exp, coef)  # Use a dedicated function

        _ when is_binary(value) ->   # Explicitly check if the value is a string
          case Float.parse(value) do
            {:ok, _} -> value
            {:error, _} -> "0.00"
          end

        _ ->  # If none of the above matched, return "0.00". This is a catch-all, which helps prevent further errors.
          "0.00"
      end

    Map.put(map, "Value", formatted_value)
  end

  # Calculate the numeric value from the {sign, exp, coef} map and format it as a string
  defp calculate_and_format_numeric(sign, exp, coef) do
    numeric_value = coef * :math.pow(10, exp) * sign
    Float.to_string(numeric_value)
  end


  # Ensure the Value is a string
  defp format_as_string(map) do
    value = Map.get(map, "Value")
    string_value = to_string(value)
    Map.put(map, "Value", string_value)
  end

  # For text values: if empty or nil, default to a single quote "'".
  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end
end
