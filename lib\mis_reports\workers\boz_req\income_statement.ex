defmodule MisReports.Workers.BozReq.IncomeStatement do

  def perform(item) do

    decoded_item =
    
      case item.income_stmt do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    decoded_item = format_map(decoded_item)

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()


    %{
      "returnKey" => "ZM-7UPL7U002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1127_00001",
          "Value" => "#{decoded_item["C11"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00002",
          "Value" => "#{decoded_item["C12"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00003",
          "Value" => "#{decoded_item["C13"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00004",
          "Value" => "#{decoded_item["C14"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00005",
          "Value" => "#{decoded_item["C15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00006",
          "Value" => "#{decoded_item["C16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00007",
          "Value" => "#{decoded_item["C17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00008",
          "Value" => "#{decoded_item["C18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00009",
          "Value" => "#{decoded_item["C19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00010",
          "Value" => "#{decoded_item["C20"] || "0"}",
          "_dataType" => "NUMERIC"
        },

        %{
          "Code" => "1127_00011",
          "Value" => "#{decoded_item["C21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00012",
          "Value" => "#{decoded_item["C22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00013",
          "Value" => "#{decoded_item["C23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00014",
          "Value" => "#{decoded_item["C24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00015",
          "Value" => "#{decoded_item["C25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00016",
          "Value" => "#{decoded_item["C26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00017",
          "Value" => "#{decoded_item["C27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00018",
          "Value" => "#{decoded_item["C28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00019",
          "Value" => "#{decoded_item["C29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00020",
          "Value" => "#{decoded_item["C30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00021",
          "Value" => "#{decoded_item["C31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00022",
          "Value" => "#{decoded_item["C32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00023",
          "Value" => "#{decoded_item["C33"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00024",
          "Value" => "#{decoded_item["C35"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00025",
          "Value" => "#{decoded_item["C36"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00026",
          "Value" => "#{decoded_item["C37"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00027",
          "Value" => "#{decoded_item["C38"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00028",
          "Value" => "#{decoded_item["C39"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00029",
          "Value" => "#{decoded_item["C40"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00030",
          "Value" => "#{decoded_item["C41"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00031",
          "Value" => "#{decoded_item["C42"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00032",
          "Value" => "#{decoded_item["C43"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00033",
          "Value" => "#{decoded_item["C44"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00034",
          "Value" => "#{decoded_item["C45"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00035",
          "Value" => "#{decoded_item["C46"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00036",
          "Value" => "#{decoded_item["C47"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00037",
          "Value" => "#{decoded_item["C48"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00038",
          "Value" => "#{decoded_item["C49"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00039",
          "Value" => "#{decoded_item["C50"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00040",
          "Value" => "#{decoded_item["C51"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00041",
          "Value" => "#{decoded_item["C52"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00042",
          "Value" => "#{decoded_item["C53"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00043",
          "Value" => "#{decoded_item["C54"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00044",
          "Value" => "#{decoded_item["C55"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00045",
          "Value" => "#{decoded_item["C56"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00046",
          "Value" => "#{decoded_item["C57"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00047",
          "Value" => "#{decoded_item["C58"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00048",
          "Value" => "#{decoded_item["C59"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00049",
          "Value" => "#{decoded_item["C60"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00050",
          "Value" => "#{decoded_item["C61"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00051",
          "Value" => "#{decoded_item["C64"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00052",
          "Value" => "#{decoded_item["C65"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00053",
          "Value" => "#{decoded_item["C66"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00054",
          "Value" => "#{decoded_item["C67"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00055",
          "Value" => "#{decoded_item["C68"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00056",
          "Value" => "#{decoded_item["C69"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00057",
          "Value" => "#{decoded_item["C71"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00058",
          "Value" => "#{decoded_item["C72"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00059",
          "Value" => "#{decoded_item["C73"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00060",
          "Value" => "#{decoded_item["C74"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00061",
          "Value" => "#{decoded_item["C75"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00062",
          "Value" => "#{decoded_item["C76"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00063",
          "Value" => "#{decoded_item["C77"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00064",
          "Value" => "#{decoded_item["C78"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00065",
          "Value" => "#{decoded_item["C79"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00066",
          "Value" => "#{decoded_item["C80"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00067",
          "Value" => "#{decoded_item["C81"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00068",
          "Value" => "#{decoded_item["C82"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00069",
          "Value" => "#{decoded_item["C84"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00070",
          "Value" => "#{decoded_item["C85"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00071",
          "Value" => "#{decoded_item["C86"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00072",
          "Value" => "#{decoded_item["C87"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00073",
          "Value" => "#{decoded_item["C88"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00074",
          "Value" => "#{decoded_item["C89"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00075",
          "Value" => "#{decoded_item["C90"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00076",
          "Value" => "#{decoded_item["C91"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00077",
          "Value" => "#{decoded_item["C92"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00078",
          "Value" => "#{decoded_item["C93"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00079",
          "Value" => "#{decoded_item["C94"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00080",
          "Value" => "#{decoded_item["C95"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00081",
          "Value" => "#{decoded_item["C96"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00082",
          "Value" => "#{decoded_item["C97"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1127_00083",
          "Value" => "#{decoded_item["C12"] || "0"}",
          "_dataType" => "NUMERIC"
        }

      ]
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

end
