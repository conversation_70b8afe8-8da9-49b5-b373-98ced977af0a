defmodule MisReports.Workers.BozReq.Schedule25 do

  def perform(item) do
    decoded_item =
      case item.schedule_25 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)
    IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    # %{
    #   "ReturnKey" => "DEMOBSO1001",
    #   "InstCode" => "#{settings.institution_code}",
    #   "FinYear" => 2024,
    #   "StartDate" => "2024-01-01",
    #   "EndDate" => "2024-01-31",
    #   "ReturnItemsList" => [
    #     %{
    #       "Code" => "BSO1001_00001",
    #       "Value" => "6",
    #       "_dataType" => "NUMERIC"
    #     },
    #     %{
    #       "Code" => "BSO1001_00002",
    #       "Value" => "4",
    #       "_dataType" => "NUMERIC"
    #     }
    #     # ... (rest of the return items would follow the same pattern)
    #   ]
    # }

    %{
      "ReturnKey" => "ZM-9ISCH259I002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => 2021,
      "StartDate" => "2021-11-01",
      "EndDate" => "2021-11-30",
      "ReturnItemsList" => [
        %{code: "1174_00001", value: "#{decoded_item["B15"]}", data_type: "NUMERIC"},
        %{code: "1174_00002", value: "#{decoded_item["B16"]}", data_type: "NUMERIC"},
        %{code: "1174_00003", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00004", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00005", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00006", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00007", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00008", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00009", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00010", value: "#{decoded_item["0"]}", data_type: "NUMERIC"},
        %{code: "1174_00011", value: "#{decoded_item["B25"]}", data_type: "NUMERIC"},
        %{code: "1174_00012", value: "#{decoded_item["B26"]}", data_type: "NUMERIC"},
        %{code: "1174_00020", value: "#{decoded_item["G20"]}", data_type: "NUMERIC"},
        %{code: "1174_00021", value: "#{decoded_item["G21"]}", data_type: "NUMERIC"},
        %{code: "1174_00013", value: "#{decoded_item["G14"]}", data_type: "NUMERIC"},
        %{code: "1174_00014", value: "#{decoded_item["G15"]}", data_type: "NUMERIC"},
        %{code: "1174_00015", value: "#{decoded_item["G16"]}", data_type: "NUMERIC"},
        %{code: "1174_00016", value: "#{decoded_item["G17"]}", data_type: "NUMERIC"},
        %{code: "1174_00017", value: "#{decoded_item["G18"]}", data_type: "NUMERIC"},
        %{code: "1174_00018", value: "#{decoded_item["G19"]}", data_type: "NUMERIC"},
        %{code: "1174_00019", value: "#{decoded_item["0"]}", data_type: "NUMERIC"}
      ],
      dynamic_items_list: [
        %{
          area: 452,
          area_name: "OTHER ASSETS",
          dynamic_items: [
            %{code: "1.1", value: "#{decoded_item["A32"]}", data_type: "TEXT"},
            %{code: "1.2", value: "#{decoded_item["B32"]}", data_type: "NUMERIC"},
            %{code: "1.3", value: "0", data_type: "NUMERIC"},
            %{code: "1.4", value: "0", data_type: "DATE"},
            %{code: "1.5", value: "0", data_type: "TEXT"},
            %{code: "1.6", value: "0", data_type: "NUMERIC"},
            %{code: "1.7", value: "0", data_type: "TEXT"},

            %{code: "2.1", value: "#{decoded_item["A33"]}", data_type: "TEXT"},
            %{code: "2.2", value: "#{decoded_item["B33"]}", data_type: "NUMERIC"},
            %{code: "2.3", value: "0", data_type: "NUMERIC"},
            %{code: "2.4", value: "0", data_type: "DATE"},
            %{code: "2.5", value: "0", data_type: "TEXT"},
            %{code: "2.6", value: "0", data_type: "NUMERIC"},
            %{code: "2.7", value: "0", data_type: "TEXT"},

            %{code: "4.1", value: "#{decoded_item["A34"]}", data_type: "TEXT"},
            %{code: "4.2", value: "#{decoded_item["B34"]}", data_type: "NUMERIC"},
            %{code: "4.3", value: "0", data_type: "NUMERIC"},
            %{code: "4.4", value: "0", data_type: "DATE"},
            %{code: "4.5", value: "0", data_type: "TEXT"},
            %{code: "4.6", value: "0", data_type: "NUMERIC"},
            %{code: "4.7", value: "0", data_type: "TEXT"},


            %{code: "5.1", value: "#{decoded_item["A35"]}", data_type: "TEXT"},
            %{code: "5.2", value: "#{decoded_item["B35"]}", data_type: "NUMERIC"},
            # %{code: "5.3", value: "0", data_type: "NUMERIC"},
            # %{code: "5.4", value: "0", data_type: "DATE"},
            # %{code: "5.5", value: "0", data_type: "TEXT"},
            # %{code: "5.6", value: "0", data_type: "NUMERIC"},
            # %{code: "5.7", value: "0", data_type: "TEXT"},

            %{code: "6.1", value: "#{decoded_item["A36"]}", data_type: "TEXT"},
            %{code: "6.2", value: "#{decoded_item["B36"]}", data_type: "NUMERIC"},
            # %{code: "6.3", value: "0", data_type: "NUMERIC"},
            # %{code: "6.4", value: "0", data_type: "DATE"},
            # %{code: "6.5", value: "0", data_type: "TEXT"},
            # %{code: "6.6", value: "0", data_type: "NUMERIC"},
            # %{code: "6.7", value: "0", data_type: "TEXT"},

            %{code: "7.1", value: "#{decoded_item["A37"]}", data_type: "TEXT"},
            %{code: "7.2", value: "#{decoded_item["B37"]}", data_type: "NUMERIC"},
            %{code: "7.3", value: "0", data_type: "NUMERIC"},
            %{code: "7.4", value: "0", data_type: "DATE"},
            %{code: "7.5", value: "0", data_type: "TEXT"},
            %{code: "7.6", value: "0", data_type: "NUMERIC"},
            %{code: "7.7", value: "0", data_type: "TEXT"},

            %{code: "8.1", value: "#{decoded_item["A38"]}", data_type: "TEXT"},
            %{code: "8.2", value: "#{decoded_item["B38"]}", data_type: "NUMERIC"},
            %{code: "8.3", value: "0", data_type: "NUMERIC"},
            %{code: "8.4", value: "0", data_type: "DATE"},
            %{code: "8.5", value: "0", data_type: "TEXT"},
            %{code: "8.6", value: "0", data_type: "NUMERIC"},
            %{code: "8.7", value: "0", data_type: "TEXT"},

            %{code: "9.1", value: "#{decoded_item["A39"]}", data_type: "TEXT"},
            %{code: "9.2", value: "#{decoded_item["B39"]}", data_type: "NUMERIC"},
            %{code: "9.3", value: "0", data_type: "NUMERIC"},
            %{code: "9.4", value: "0", data_type: "DATE"},
            %{code: "9.5", value: "0", data_type: "TEXT"},
            %{code: "9.6", value: "0", data_type: "NUMERIC"},
            %{code: "9.7", value: "0", data_type: "TEXT"},

            %{code: "10.1", value: "#{decoded_item["A40"]}", data_type: "TEXT"},
            %{code: "10.2", value: "#{decoded_item["B40"]}", data_type: "NUMERIC"},
            %{code: "10.3", value: "0", data_type: "NUMERIC"},
            %{code: "10.4", value: "0", data_type: "DATE"},
            %{code: "10.5", value: "0", data_type: "TEXT"},
            %{code: "10.6", value: "0", data_type: "NUMERIC"},
            %{code: "10.7", value: "0", data_type: "TEXT"},

            %{code: "11.1", value: "#{decoded_item["A41"]}", data_type: "TEXT"},
            %{code: "11.2", value: "#{decoded_item["B41"]}", data_type: "NUMERIC"},
            %{code: "11.3", value: "0", data_type: "NUMERIC"},
            %{code: "11.4", value: "0", data_type: "DATE"},
            %{code: "11.5", value: "0", data_type: "TEXT"},
            %{code: "11.6", value: "0", data_type: "NUMERIC"},
            %{code: "11.7", value: "0", data_type: "TEXT"},

            %{code: "12.1", value: "#{decoded_item["A42"]}", data_type: "TEXT"},
            %{code: "12.2", value: "#{decoded_item["B42"]}", data_type: "NUMERIC"},
            %{code: "12.3", value: "0", data_type: "NUMERIC"},
            %{code: "12.4", value: "0", data_type: "DATE"},
            %{code: "12.5", value: "0", data_type: "TEXT"},
            %{code: "12.6", value: "0", data_type: "NUMERIC"},
            %{code: "12.7", value: "0", data_type: "TEXT"},

            %{code: "13.1", value: "#{decoded_item["A43"]}", data_type: "TEXT"},
            %{code: "13.2", value: "#{decoded_item["B43"]}", data_type: "NUMERIC"},
            %{code: "13.3", value: "0", data_type: "NUMERIC"},
            %{code: "13.4", value: "0", data_type: "DATE"},
            %{code: "13.5", value: "0", data_type: "TEXT"},
            %{code: "13.6", value: "0", data_type: "NUMERIC"},
            %{code: "13.7", value: "0", data_type: "TEXT"},

            %{code: "14.1", value: "#{decoded_item["A44"]}", data_type: "TEXT"},
            %{code: "14.2", value: "#{decoded_item["B44"]}", data_type: "NUMERIC"},
            %{code: "14.3", value: "0", data_type: "NUMERIC"},
            %{code: "14.4", value: "0", data_type: "DATE"},
            %{code: "14.5", value: "0", data_type: "TEXT"},
            %{code: "14.6", value: "0", data_type: "NUMERIC"},
            %{code: "14.7", value: "0", data_type: "TEXT"},


            %{code: "15.1", value: "#{decoded_item["A45"]}", data_type: "TEXT"},
            %{code: "15.2", value: "#{decoded_item["B45"]}", data_type: "NUMERIC"},
            %{code: "15.3", value: "0", data_type: "NUMERIC"},
            %{code: "15.4", value: "0", data_type: "DATE"},
            %{code: "15.5", value: "0", data_type: "TEXT"},
            %{code: "15.6", value: "0", data_type: "NUMERIC"},
            %{code: "15.7", value: "0", data_type: "TEXT"},


            %{code: "16.1", value: "#{decoded_item["A46"]}", data_type: "TEXT"},
            %{code: "16.2", value: "#{decoded_item["B46"]}", data_type: "NUMERIC"},
            %{code: "16.3", value: "0", data_type: "NUMERIC"},
            %{code: "16.4", value: "0", data_type: "DATE"},
            %{code: "16.5", value: "0", data_type: "TEXT"},
            %{code: "16.6", value: "0", data_type: "NUMERIC"},
            %{code: "16.7", value: "0", data_type: "TEXT"},


            %{code: "17.1", value: "#{decoded_item["A47"]}", data_type: "TEXT"},
            %{code: "17.2", value: "#{decoded_item["B47"]}", data_type: "NUMERIC"},
            %{code: "17.3", value: "0", data_type: "NUMERIC"},
            %{code: "17.4", value: "0", data_type: "DATE"},
            %{code: "17.5", value: "0", data_type: "TEXT"},
            %{code: "17.6", value: "0", data_type: "NUMERIC"},
            %{code: "17.7", value: "0", data_type: "TEXT"},


            %{code: "18.1", value: "#{decoded_item["A48"]}", data_type: "TEXT"},
            %{code: "18.2", value: "#{decoded_item["B48"]}", data_type: "NUMERIC"},
            %{code: "18.3", value: "0", data_type: "NUMERIC"},
            %{code: "18.4", value: "0", data_type: "DATE"},
            %{code: "18.5", value: "0", data_type: "TEXT"},
            %{code: "18.6", value: "0", data_type: "NUMERIC"},
            %{code: "18.7", value: "0", data_type: "TEXT"},

            %{code: "19.1", value: "#{decoded_item["A49"]}", data_type: "TEXT"},
            %{code: "19.2", value: "#{decoded_item["B49"]}", data_type: "NUMERIC"},
            %{code: "19.3", value: "0", data_type: "NUMERIC"},
            %{code: "19.4", value: "0", data_type: "DATE"},
            %{code: "19.5", value: "0", data_type: "TEXT"},
            %{code: "19.6", value: "0", data_type: "NUMERIC"},
            %{code: "19.7", value: "0", data_type: "TEXT"},


            %{code: "20.1", value: "#{decoded_item["A50"]}", data_type: "TEXT"},
            %{code: "20.2", value: "#{decoded_item["B50"]}", data_type: "NUMERIC"},
            %{code: "20.3", value: "0", data_type: "NUMERIC"},
            %{code: "20.4", value: "0", data_type: "DATE"},
            %{code: "20.5", value: "0", data_type: "TEXT"},
            %{code: "20.6", value: "0", data_type: "NUMERIC"},
            %{code: "20.7", value: "0", data_type: "TEXT"},

            %{code: "21.1", value: "#{decoded_item["A51"]}", data_type: "TEXT"},
            %{code: "21.2", value: "#{decoded_item["B51"]}", data_type: "NUMERIC"},
            %{code: "21.3", value: "0", data_type: "NUMERIC"},
            %{code: "21.4", value: "0", data_type: "DATE"},
            %{code: "21.5", value: "0", data_type: "TEXT"},
            %{code: "21.6", value: "0", data_type: "NUMERIC"},
            %{code: "21.7", value: "0", data_type: "TEXT"},

            %{code: "22.1", value: "#{decoded_item["A52"]}", data_type: "TEXT"},
            %{code: "22.2", value: "#{decoded_item["B52"]}", data_type: "NUMERIC"},
            %{code: "22.3", value: "0", data_type: "NUMERIC"},
            %{code: "22.4", value: "0", data_type: "DATE"},
            %{code: "22.5", value: "0", data_type: "TEXT"},
            %{code: "22.6", value: "0", data_type: "NUMERIC"},
            %{code: "22.7", value: "0", data_type: "TEXT"},

            %{code: "23.1", value: "#{decoded_item["A53"]}", data_type: "TEXT"},
            %{code: "23.2", value: "#{decoded_item["B53"]}", data_type: "NUMERIC"},
            %{code: "23.3", value: "0", data_type: "NUMERIC"},
            %{code: "23.4", value: "0", data_type: "DATE"},
            %{code: "23.5", value: "0", data_type: "TEXT"},
            %{code: "23.6", value: "0", data_type: "NUMERIC"},
            %{code: "23.7", value: "0", data_type: "TEXT"},

            %{code: "24.1", value: "#{decoded_item["A54"]}", data_type: "TEXT"},
            %{code: "24.2", value: "#{decoded_item["B54"]}", data_type: "NUMERIC"},
            %{code: "24.3", value: "0", data_type: "NUMERIC"},
            %{code: "24.4", value: "0", data_type: "DATE"},
            %{code: "24.5", value: "0", data_type: "TEXT"},
            %{code: "24.6", value: "0", data_type: "NUMERIC"},
            %{code: "24.7", value: "0", data_type: "TEXT"},

            %{code: "25.1", value: "#{decoded_item["A55"]}", data_type: "TEXT"},
            %{code: "25.2", value: "#{decoded_item["B55"]}", data_type: "NUMERIC"},
            %{code: "25.3", value: "0", data_type: "NUMERIC"},
            %{code: "25.4", value: "0", data_type: "DATE"},
            %{code: "25.5", value: "0", data_type: "TEXT"},
            %{code: "25.6", value: "0", data_type: "NUMERIC"},
            %{code: "25.7", value: "0", data_type: "TEXT"},

            %{code: "26.1", value: "#{decoded_item["A56"]}", data_type: "TEXT"},
            %{code: "26.2", value: "#{decoded_item["B56"]}", data_type: "NUMERIC"},
            %{code: "26.3", value: "0", data_type: "NUMERIC"},
            %{code: "26.4", value: "0", data_type: "DATE"},
            %{code: "26.5", value: "0", data_type: "TEXT"},
            %{code: "26.6", value: "0", data_type: "NUMERIC"},
            %{code: "26.7", value: "0", data_type: "TEXT"},

            %{code: "27.1", value: "#{decoded_item["A57"]}", data_type: "TEXT"},
            %{code: "27.2", value: "#{decoded_item["B57"]}", data_type: "NUMERIC"},
            %{code: "27.3", value: "0", data_type: "NUMERIC"},
            %{code: "27.4", value: "0", data_type: "DATE"},
            %{code: "27.5", value: "0", data_type: "TEXT"},
            %{code: "27.6", value: "0", data_type: "NUMERIC"},
            %{code: "27.7", value: "0", data_type: "TEXT"},

            %{code: "28.1", value: "#{decoded_item["A58"]}", data_type: "TEXT"},
            %{code: "28.2", value: "#{decoded_item["B58"]}", data_type: "NUMERIC"},
            %{code: "28.3", value: "0", data_type: "NUMERIC"},
            %{code: "28.4", value: "0", data_type: "DATE"},
            %{code: "28.5", value: "0", data_type: "TEXT"},
            %{code: "28.6", value: "0", data_type: "NUMERIC"},
            %{code: "28.7", value: "0", data_type: "TEXT"},

            %{code: "29.1", value: "#{decoded_item["A59"]}", data_type: "TEXT"},
            %{code: "29.2", value: "#{decoded_item["B59"]}", data_type: "NUMERIC"},
            %{code: "29.3", value: "0", data_type: "NUMERIC"},
            %{code: "29.4", value: "0", data_type: "DATE"},
            %{code: "29.5", value: "0", data_type: "TEXT"},
            %{code: "29.6", value: "0", data_type: "NUMERIC"},
            %{code: "29.7", value: "0", data_type: "TEXT"},

            %{code: "30.1", value: "#{decoded_item["A60"]}", data_type: "TEXT"},
            %{code: "30.2", value: "#{decoded_item["B60"]}", data_type: "NUMERIC"},
            %{code: "30.3", value: "0", data_type: "NUMERIC"},
            %{code: "30.4", value: "0", data_type: "DATE"},
            %{code: "30.5", value: "0", data_type: "TEXT"},
            %{code: "30.6", value: "0", data_type: "NUMERIC"},
            %{code: "30.7", value: "0", data_type: "TEXT"},


            %{code: "31.1", value: "#{decoded_item["A61"]}", data_type: "TEXT"},
            %{code: "31.2", value: "#{decoded_item["B61"]}", data_type: "NUMERIC"},
            %{code: "31.3", value: "0", data_type: "NUMERIC"},
            %{code: "31.4", value: "0", data_type: "DATE"},
            %{code: "31.5", value: "0", data_type: "TEXT"},
            %{code: "31.6", value: "0", data_type: "NUMERIC"},
            %{code: "31.7", value: "0", data_type: "TEXT"},

            %{code: "32.1", value: "#{decoded_item["A62"]}", data_type: "TEXT"},
            %{code: "32.2", value: "#{decoded_item["B62"]}", data_type: "NUMERIC"},
            %{code: "32.3", value: "0", data_type: "NUMERIC"},
            %{code: "32.4", value: "0", data_type: "DATE"},
            %{code: "32.5", value: "0", data_type: "TEXT"},
            %{code: "32.6", value: "0", data_type: "NUMERIC"},
            %{code: "32.7", value: "0", data_type: "TEXT"},
          ]
        }
      ]
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
