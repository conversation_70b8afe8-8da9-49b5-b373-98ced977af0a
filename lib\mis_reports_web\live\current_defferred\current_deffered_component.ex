defmodule MisReportsWeb.CurrentDefferredLive.CurrentDefferredComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.Tax
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.CurrentTaxView, "current_tax.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.CurrentTaxView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{tax: tax} = assigns, socket) do
    changeset = Utilities.change_tax(tax)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"tax" => params}, socket) do
    changeset =
      socket.assigns.tax
      |> Tax.changeset(params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"tax" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    current_user_id = to_string(socket.assigns.current_user.id)
    user_id = socket.assigns.current_user.id
    audit_msg = "Created new current and deferred tax for period \"#{params["report_date"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:tax, Tax.changeset(%Tax{maker_id: user_id}, params))
    |> UserController.audit_log(user_id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{tax: tax}} ->
        case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Current Deferred Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_tax(tax, %{reference: reference_number}) do
              {:ok, updated_tax} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Current deferred created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update current deferred reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Current deferred created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, changeset} ->
        {:noreply, assign(socket, :changeset, changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    tax = socket.assigns.tax
    socket
    |> handle_update(params, tax)
    |> case do
      {:ok, tax} ->
        {:noreply,
         socket
         |> put_flash(:info, "Current deferred updated successfully")
         |> push_redirect(to: Routes.tax_index_path(socket, :edit, tax))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Current Deferred Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Current deferred rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject current deferred: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :approve, params) do
    tax = socket.assigns.tax
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Current Deferred Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      Tax.changeset(tax, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_tax}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Current deferred approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Current deferred approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve current deferred")
         |> assign(:changeset, %{tax.changeset | errors: failed_value.errors})}
    end
  end

  defp handle_constraint_error(socket, changeset) do
    if changeset.errors[:report_date] do
      {:noreply,
       socket
       |> put_flash(:error, "Report Date for This Month already exists")
       |> assign(changeset: changeset)}
    else
      {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, tax) do
    audit_msg = "Updated Current Deferred for \"#{tax.tax}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:tax, Tax.changeset(tax, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{tax: tax, audit_log: _user_log}} ->
        {:ok, tax}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  def traverse_errors(errors) do
    for {key, {msg, opts}} <- errors, into: %{} do
      msg =
        Regex.replace(~r"%{(\w+)}", msg, fn _, key ->
          opts |> Keyword.get(String.to_existing_atom(key), key) |> to_string()
        end)

      {key, msg}
    end
  end
end
