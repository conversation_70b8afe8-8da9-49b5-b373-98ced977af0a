defmodule MisReports.Workers.BozReq.Schedule21b do
  def perform(item) do
    decoded_item =
    case item.schedule_21b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)
    IO.inspect(decoded_item, label: "===============================")
    return_items = decoded_item["total"]
    dynamic_list = decoded_item["list"]
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    # %{
    #   "ReturnKey" => "DEMOBSO1001",
    #   "InstCode" => "#{settings.institution_code}",
    #   "FinYear" => 2024,
    #   "StartDate" => "2024-01-01",
    #   "EndDate" => "2024-01-31",
    #   "ReturnItemsList" => [
    #     %{
    #       "Code" => "BSO1001_00001",
    #       "Value" => "6",
    #       "_dataType" => "NUMERIC"
    #     },
    #     %{
    #       "Code" => "BSO1001_00002",
    #        "Value" => "#{decoded_item["B12"]}",
    #       "_dataType" => "NUMERIC"
    #     }
    #     # ... (rest of the return items would follow the same pattern)
    #   ]
    # }

    %{
      "ReturnKey" => "ZM-9KSCH21B9K002",
     "InstCode" => "#{settings.institution_code}",
      "FinYear" => 2021,
      "StartDate" => "2021-01-01T00:00:00",
      "EndDate" => "2021-01-31T00:00:00",
      "ReturnItemsList" => [
        %{code: "1123_00001", value: "#{decoded_item["B15"]}", data_type: :numeric},
        %{code: "1123_00002", value: "#{decoded_item["B16"]}", data_type: :numeric},
        %{code: "1123_00003", value: "0", data_type: :numeric},
        %{code: "1123_00004", value: "0", data_type: :numeric},
        %{code: "1123_00005", value: "0", data_type: :numeric},
        %{code: "1123_00006", value: "0", data_type: :numeric},
        %{code: "1123_00007", value: "#{decoded_item["B27"]}", data_type: :numeric},
        %{code: "1123_00008", value: "#{decoded_item["B22"]}", data_type: :numeric},
        %{code: "1123_00010", value: "0", data_type: :numeric}

      ],
          "DynamicItemsList" => [
            %{
              "Area" =>471,
              "_areaName" => "OTHER NON-INTEREST EXPENSES",
              "DynamicItems" => map_data(dynamic_list) |> format_values()
            }
          ]
        }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


def map_data(records) do
  Enum.flat_map(Enum.with_index(records), fn {map, index} ->
      index = index + 1
      [
        %{"Code" => "#{index}.1.1", "Value" => map["B28"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.1.2", "Value" => map["C25"], "_dataType" => "NUMERIC"},
      ]
    end)
  end
  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)
        "TEXT" ->
          update_text_value(map)
        _ ->
          map
      end

    end)
  end
  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end
  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end
end
