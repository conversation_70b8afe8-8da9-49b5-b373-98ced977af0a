defmodule MisReports.Workers.BozReq.Schedule17a do
  def perform(item) do
    decoded_item =
      case item.schedule_17a do
        nil ->
          %{}

        schedule when is_binary(schedule) ->
          case Poison.decode(schedule) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    decoded_item = format_scientific_values(decoded_item)
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-VBSCH17AVB001",
      "InstCode" =>  "#{settings.institution_code}",
     "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" =>
        Map.get(decoded_item, "return_list", default_return_items_list(decoded_item)),
      "DynamicItemsList" =>
        Map.get(decoded_item, "dynamic", default_dynamic_items_list(decoded_item))
    }
  end

  def format_scientific_values(map) do
    list = get_in(map, ["list"]) || []

    new_list =
      Enum.map(list, fn
        %{"value" => value} = item ->
          # Update only the "value" key in the map
          Map.put(item, "value", convert_to_number(value))

        other ->
          # If the structure doesn't match, leave it unchanged
          other
      end)

    # Return the original map updated with the new list for "list"
    Map.put(map, "list", new_list)
  end
  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

  defp convert_to_number(_), do: 0.0

  defp default_return_items_list(decoded_item) do
    [
      %{
        "Code" => "1196_00001",
        "Value" => "#{decoded_item["K13"]}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1191_00003",
        "Value" => "#{decoded_item["L13"]} TOTAL LENDING TO SHAREHOLDERS (ON AND OFF BALANCE SHEET)",
        "_dataType" => "NUMERIC"
      },

    ]
  end

  defp default_dynamic_items_list(decoded_item) do
    [
      %{
        "Area" => 465,
        "_areaName" => "Amount",
        "DynamicItems" => [
          %{
            "Code" => "1.1",
            "Value" => "#{decoded_item["A12"]} Shareholder\u0027s Name",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "1.2",
            "Value" => "#{decoded_item["D12"]}Amount",
            "_dataType" => "TEXT"
          },


        ]
      }
    ]
  end
end
