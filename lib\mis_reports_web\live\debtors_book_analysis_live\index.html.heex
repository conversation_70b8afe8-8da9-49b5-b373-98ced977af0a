<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    
     <%= if @live_action == :new do %>
      <div class="mt-5 font-semibold text-xl">Debtors Book Analysis</div>
      <div class="text-sm">New debtors book analysis</div>
     <% end %>
     <%= if @live_action == :edit do %>
        <div class="mt-5 font-semibold text-xl">Debtors Book Analysis</div>
        <div class="text-sm">Edit debtors book analysis</div>
     <% end %>
     <%= if @live_action == :index do %>
      <div class="mt-5 font-semibold text-xl">Debtors Book Analysis</div>
       <div class="text-sm">View debtors book analysis </div>
     <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />
    
    <%= if @live_action == :index do %>
         <%= Phoenix.View.render(MisReportsWeb.DebtorsBookAnalysisView, "debtors_book_analysis.html", assigns) %>
    <% end %>

    <%= if @live_action == :cmmp_report_listing do %>
         <%= Phoenix.View.render(MisReportsWeb.CmmpReportView, "cmmp_report.html", assigns) %>
    <% end %>

    <%= if @live_action == :update_status do %> 

    <.live_component
    module={ MisReportsWeb.DebtorsBookAnalysisLive.DebtorsBookAnalysisComponent} 
    id="new-debtors" 
    current_user={@current_user} 
    values={@values} 
    debtors_book_analysis={@debtors_book_analysis} 
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id}
    action={@live_action} />

    <% end %>
 
    <.live_component :if={@live_action in [:new, :edit]} 
    module={ MisReportsWeb.DebtorsBookAnalysisLive.DebtorsBookAnalysisComponent} 
    id="new-debtors" 
    current_user={@current_user} 
    values={@values} 
    debtors_book_analysis={@debtors_book_analysis}
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id} 
    action={@live_action} />
 </div>
    
 <.confirm_modal />
 
 <.info_notification />
 
 <.error_notification />