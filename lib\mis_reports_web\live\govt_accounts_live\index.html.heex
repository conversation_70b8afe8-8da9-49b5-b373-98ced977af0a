<div class="mx-auto max-w-7xl sm:px-6 lg:px-8 mt-5">
    <div class="mt-5 font-semibold text-xl">Government Accounts</div>
    <%= if @live_action == :new do %>
      <div class="text-sm">New Government Accounts</div>
    <% end %>
    <%= if @live_action == :edit do %>
      <div class="text-sm">Edit Government Accounts</div>
    <% end %>
    <%= if @live_action == :index do %>
      <div class="text-sm">View Government Accounts</div>
    <% end %><br>
    
    <.info :if={live_flash(@flash, :info)} flash={@flash} /> 
    <.error :if={live_flash(@flash, :error)} flash={@flash} />
   
    <%= if @live_action == :update_status do %>  
    <.live_component
    module={MisReportsWeb.GovtAccountsLive.GovtAccountsComponent} 
    id="new-govt-account" 
    current_user={@current_user} 
    govt_accounts={@govt_accounts}
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id} 
    action={@live_action} />
    <% end %>
    
    <.live_component :if={@live_action in [:new, :edit]} 
    module={MisReportsWeb.GovtAccountsLive.GovtAccountsComponent} 
    id="new-govt-account" 
    current_user={@current_user} 
    govt_accounts={@govt_accounts}
    process_id={@process_id}
    reference={@reference}
    step_id={@step_id} 
    action={@live_action} />
    
    <%= if @live_action == :index do %>
        <%= Phoenix.View.render(MisReportsWeb.GovtAccountsView, "govt_accounts.html", assigns) %>
    <% end %>
    
</div>
    
<.confirm_modal />

<.info_notification />

<.error_notification />