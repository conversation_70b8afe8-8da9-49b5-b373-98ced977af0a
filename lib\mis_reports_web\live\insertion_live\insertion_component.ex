defmodule  MisReportsWeb.InsertionLive.InsertionComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReportsWeb.UserController
  alias MisReports.{Utilities}
  alias MisReports.Utilities.Adjustments
  alias MisReports.Utilities.Adjustments
  alias MisReportsWeb.AdjustmentsLive.AdjustmentsComponent
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias Poison
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
    <%= case @action do %>
    <% action when action in [:new, :edit] -> %>
      <%= Phoenix.View.render(MisReportsWeb.InsertionView, "insertion.html", assigns) %>
      <% :update_status -> %>
        <%= Phoenix.View.render(MisReportsWeb.InsertionView, "approve_new.html", assigns) %>
      <% _ -> %>
        <div class="text-red-600">Invalid action</div>
    <% end %>
    </div>
    """
  end

  @impl true
  def update(%{insertion: _insertion} = assigns, socket) do
    insertion = case assigns[:reference] do
      nil ->
        # Create a new Adjustments struct if no reference
        %Adjustments{
          type: "SINGLE",
          maker_id: assigns.current_user.id,
          reference: nil
        }
      reference ->
        case Utilities.get_adjustments_reference!(reference) do
          [] ->
            # Create new if no existing adjustments found
            %Adjustments{
              type: "SINGLE",
              maker_id: assigns.current_user.id,
              reference: reference
            }
          [first | _] -> first
        end

    end


    changeset = Utilities.change_adjustments(insertion)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:insertion, [insertion])
     |> assign(:changeset, changeset)
     |> assign(:active_tab, "schedule_2c")
     |> assign(:selected_insertion, nil)
     |> assign(:schedule_content, nil)
     |> assign(:schedule_name, nil)
     |> assign(:columns, [])}
  end

  @impl true
  def update(assigns, socket) do
    insertions = case assigns[:reference] do
      nil ->
        # Create a new single insertion if no reference
        [%Adjustments{
          type: "SINGLE",
          maker_id: assigns.current_user.id,
          reference: nil
        }]

      reference ->
        # Get all adjustments with this reference
        case Utilities.get_adjustments_reference!(reference) do
          [] ->
            [%Adjustments{
              type: "SINGLE",
              maker_id: assigns.current_user.id,
              reference: reference
            }]
          adjustments when is_list(adjustments) -> adjustments
          adjustment -> [adjustment] # Handle single result
        end
    end

    IO.inspect(insertions, label: "============insertions===========")

    changeset = Utilities.change_adjustments(List.first(insertions))

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:insertion, insertions)  # Assign the list of insertions
     |> assign(:changeset, changeset)
     |> assign(:active_tab, "schedule_2c")
     |> assign(:selected_insertion, nil)
     |> assign(:schedule_content, nil)
     |> assign(:schedule_name, nil)
     |> assign(:columns, [])}
  end

  @impl true
  def handle_event("save", %{"insertion" => single} = params, socket) do
    # Prepare data in insertion format
    columns = prepare_columns(single)
    adjustment_lines = Poison.encode!(columns)


    adjustment_params = %{
      "adjustment_lines" => adjustment_lines,
      "schedule_content" => single,
      "report_date" => single["report_date"],
      "type" => "SINGLE",
      "schedule_name" => params["type"],
      "reference" => reference = socket.assigns.insertion |> List.first() |> Map.get(:reference),
      "comment" => (if single["single_comment"] in [nil, ""], do: "N/A", else: single["single_comment"])
    }

    step_id = Map.get(socket.assigns, :step_id)

    IO.inspect(step_id, label: "============step_id in insertions save===========")

    socket_with_assigns = socket
    |> assign(:reference, reference = socket.assigns.insertion |> List.first() |> Map.get(:reference))
    |> assign(:step_id, step_id)
    |> assign(:current_user, socket.assigns.current_user)

    # Delegate to AdjustmentsComponent
    AdjustmentsComponent.handle_save(socket_with_assigns, socket.assigns.action, adjustment_params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_save(socket, :reject, params)
      "97" -> handle_save(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_event("change_schedule", %{"type" => schedule}, socket) do

    case socket.assigns[:reference] do
      nil ->
        # If no reference exists, just update the active tab
        {:noreply, assign(socket, :active_tab, schedule)}

      reference ->
        # If reference exists, get adjustments and update state
        insertion = Utilities.get_adjustments_reference!(reference)
        changeset = Utilities.change_adjustments(List.first(insertion))

        {:noreply,
         socket
         |> assign(:active_tab, schedule)
         |> assign(:changeset, changeset)}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg =  "Created Adjustments for report date \"#{params["report_date"]}\""
    user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:adjustment, Adjustments.changeset(%Adjustments{maker_id: user.id, type: "SINGLE"}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
        {:ok, _buniness_unit} ->
          {:noreply,
            socket
            |> put_flash(:info, "Adjustments created successfully")
            |> push_redirect(to: Routes.insertion_index_path(socket, :new))}

        {:error, _failed_operation, failed_value, _changes_so_far} ->
          reason = traverse_errors(failed_value.errors) |> List.first()
          {:noreply,
            socket
            |> put_flash(:error, "#{reason}")
            |> push_redirect(to: Routes.insertion_index_path(socket, :new))}
      end
  end


  def prepare_columns(params) do
    case params["single_amount"] in [nil, ""] do
      false ->
        if String.contains?(params["single_amount"], "-") do
          [
            %{
              debit: params["single_gl_code"],
              credit: "",
              amount: params["single_amount"],
              type_credit: params["single_type_debit"],
              cur_cat_credit: params["single_cur_cat"],
              business_unit_credit: params["single_business_unit"],
              cur_cat_debit: params["single_cur_cat"],
              business_unit_debit: params["business_unit_debit"],
            }
          ]
        else
          [
            %{
              debit: "",
              credit: params["single_gl_code"],
              amount: params["single_amount"],
              type_credit: params["single_type_debit"],
              cur_cat_credit: params["single_cur_cat"],
              business_unit_credit: params["single_business_unit"],
              cur_cat_debit: params["single_cur_cat"],
              business_unit_debit: params["business_unit_debit"],
            }
          ]
        end
      true -> []
    end
  end

  @impl true
  def handle_event("select_insertion", %{"value" => adjustment_id}, socket) do
    case Enum.find(socket.assigns.insertion, &(&1.id == String.to_integer(adjustment_id))) do
      nil ->
        {:noreply, socket}

      adjustment ->
        # Parse adjustment lines
        lines = adjustment.adjustment_lines || "[]"
        decoded_lines =
          case Jason.decode(lines) do
            {:ok, parsed} -> parsed
            _ -> []
          end

        content = adjustment.schedule_content || %{}

        IO.inspect(content, label: "===========content=============")

        active_tab = adjustment.schedule_name

        # Map schedule fields based on input field names in approve_new.html.heex
        schedule_fields = case adjustment.schedule_name do


          "schedule_08a" -> %{
          "a" => content["a"],
          "b" => content["b"],
          "c" => content["c"],
          "d" => content["d"],
          "f" => content["f"],
          "h" => content["h"],
          "report_date" => content["report_date"]
        }

        "schedule_27" -> %{
          "account_name" => content["account_name"],
          "amount" => content["amount"],
          "description" => content["description"],
          "report_date" => content["report_date"],
          "single_gl_code" => content["gl_code"],
          "single_type_debit" => content["type"],
          "single_business_unit" => content["business_unit"],
          "single_cur_cat" => content["currency_category"],
          "single_comment" => content["comment"]
        }

        "schedule_31f" -> %{
          "actual_credit_balance_1" => content["amount_borrowed"],
          "currency_code" => content["currency"],
          "effective_credit_rate" => content["interest_rate"],
          "account_open_date" => content["deal_date"],
          "account_maturity_date" => content["maturity_date"],
          "actual_credit_balance" => content["outstanding_balance"],
          "actual_31f" => content["collateral"],
          "credit_balance" => content["collateral_value"],
          "report_date" => content["report_date"]
        }



          # Common fields for any other schedule
          _ -> %{}
        end

        # Always merge in common fields from form
        common_fields = %{
          "single_gl_code" => content["single_gl_code"],
          "single_type_debit" => content["single_type_debit"],
          "single_business_unit" => content["single_business_unit"],
          "single_cur_cat" => content["single_cur_cat"],
          "single_amount" => content["single_amount"],
          "single_comment" => content["single_comment"]
        }

        fields = Map.merge(schedule_fields, common_fields)

        # Create changeset with all fields
        changeset = Enum.reduce(fields, Utilities.change_adjustments(adjustment), fn {key, value}, acc ->
          try do
            field = String.to_existing_atom(key)
            Ecto.Changeset.put_change(acc, field, value)
          rescue
            ArgumentError -> acc
          end
        end)

        {:noreply,
         socket
         |> assign(:active_tab, active_tab)
         |> assign(:selected_insertion, adjustment)
         |> assign(:columns, decoded_lines)
         |> assign(:schedule_content, content)
         |> assign(:schedule_name, adjustment.schedule_name)
         |> assign(:changeset, changeset)}
    end
  end

  @impl true
  def handle_event("select_insertion", %{"_target" => ["single_adjustments"], "single_adjustments" => id}, socket) do
    handle_event("select_insertion", %{"value" => id}, socket)
  end


  def handle_save(socket, :reject, params) do
    reference = socket.assigns.reference
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Adjustments Rejected"
    process_id = socket.assigns.process_id

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           96,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Business unit rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject business unit: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :approve, params) do
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    comment = "Adjustments Approval"
    process_id = socket.assigns.process_id

    # Get all adjustments with the same reference number
    adjustments = Utilities.get_adjustments_reference!(reference)

    # Create a multi operation for each adjustment
    multi =
      Enum.reduce(adjustments, Ecto.Multi.new(), fn adjustment, multi ->
        Ecto.Multi.update(
          multi,
          {:update, adjustment.id},
          Adjustments.changeset(adjustment, %{
            status: "A",
            checker_id: current_user.id,
            checker_date: NaiveDateTime.utc_now()
          })
        )
      end)

    # Execute the transaction
    case Repo.transaction(multi) do
      {:ok, results} ->
        # Get all updated adjustments and invalidate their caches
        Enum.each(adjustments, fn adjustment ->
          MisReports.ScheduleLoader.invalidate_adjustments_cache(adjustment.report_date)
        end)

        # All updates successful, now call workflow
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               97,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "All adjustments approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Adjustments approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve adjustments: #{inspect(failed_operation)}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")

end
