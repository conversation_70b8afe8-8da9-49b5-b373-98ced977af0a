defmodule MisReportsWeb.IncomeStatementLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  on_mount MisReportsWeb.UserLiveAuth

  alias MisReportsWeb.LiveHelpers
  alias MisReports.Utilities
  alias MisReports.{Utilities, Repo, Prudentials.PrudReport, Utilities.FileExport}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Components.Shared.NavigationMenu
  alias MisReportsWeb.Components.CommentModalComponent

  import NavigationMenu

  require Logger

  defp normalize_step_name(step_name) when is_binary(step_name) do
    step_name
    |> String.trim()
    |> String.downcase()
    # Replace any whitespace with single underscore
    |> String.replace(~r/\s+/, "_")
    # Remove any special characters
    |> String.replace(~r/[^a-z0-9_]/, "")
  end

  defp normalize_step_name(_), do: ""

  @income_statement_schedules [
    "schedule_27A",
    "schedule_04D",
    "schedule_18A",
    "schedule_18B",
    "schedule_18C",
    "schedule_18D",
    "schedule_27",
    "schedule_24",
    "schedule_28A",
    "schedule_28B"
  ]

  @balance_sheet_schedules [
    "schedule_03A",
    "schedule_02D",
    "schedule_03B",
    "schedule_17C",
    "schedule_02C",
    "schedule_02G",
    "schedule_31E",
    "schedule_01c",
    "schedule_01D",
    "schedule_10C",
    "schedule_01E",
    "schedule_31C",
    "schedule_01F",
    "schedule_21B",
    "schedule_32A",
    "schedule_22B",
    "schedule_31F",
    "schedule_21C",
    "schedule_25",
    "schedule_02E",
    "schedule_02F",
    "schedule_31D",
    "schedule_30D",
    "schedule_04C",
    "schedule_31C",
    "schedule_12",
    "schedule_2H",
    "schedule_2A1",
    "schedule_5B",
    "schedule_5C",
    "schedule_6A",
    "schedule_6B",
    "schedule_13",
    "schedule_7A",
    "schedule_9A",
    "schedule_8A",
    "schedule_8B",
    "schedule_4B",
    "schedule_4D",
    "schedule_5D",
    "schedule_11D",
    "schedule_11F",
    "schedule_11L",
    "schedule_11H",
    "schedule_11G",
    "schedule_11J",
    "schedule_11E",
    "schedule_11K",
    "schedule_17A",
    "schedule_17B",
    "schedule_17E",
    "schedule_18A",
    "schedule_18B",
    "schedule_19",
    "schedule_15",
    "schedule_14",
    "schedule_26",
    "schedule_22A",
    "schedule_23A",
    "schedule_23B"
  ]

  @other_schedules [
    "schedule_29A",
    "schedule_20A",
    "schedule_30B",
    "schedule_30C"
  ]

  @review_steps [
    "generate_finance_report_stage",
    "finance_review_level_1",
    "credit_review_stage",
    "review_level_2",
    "final_report_approval"
  ]
  @impl true
  def mount(params, _session, socket) do
    socket =
      assign(socket,
        process_id: params["process_id"],
        reference: params["reference"],
        step_id: params["step_id"] || socket.assigns[:live_action],
        is_fullscreen: false,
        comment: "",
        report_type: "",
        income_statement_schedules: @income_statement_schedules,
        balance_sheet_schedules: @balance_sheet_schedules,
        income_statement_data: %{},
        balance_sheet_data: %{},
        other_schedules_data: %{},
        entries: [],
        adjustments: [],
        filter_params: %{},
        download_btn: false,
        page: %{current: "Reports", prev: "Financial Reports"},
        prud_status: "Pending",
        settings: init_settings(),
        loader: false,
        current_view: "income_statement",
        active_tab: "income_statement",
        show_helper: true,
        # Add these lines for comment persistence
        comments: [],
        pending_comments: %{},
        helper_clicked: false,
        other_schedules: @other_schedules,
        view_only: params["view"] == "true",
        # Add modal-related assigns
        show_comment_modal: false,
        current_schedule: nil,
        current_schedule_name: nil,
        modal_comment: ""
      )
      |> restore_comment_log()

    IO.inspect(
      normalize_step_name(MisReports.Workflow.get_wklstep_rule!(params["step_id"]).step_name) in @review_steps,
      label: "Review Step Check"
    )

    socket =
      if normalize_step_name(MisReports.Workflow.get_wklstep_rule!(params["step_id"]).step_name) in @review_steps do
        # Return the modified socket
        assign(socket, :loader, true)
      else
        # Return unmodified socket if condition not met
        socket
      end

    socket =
      if socket.assigns.step_id &&
           normalize_step_name(MisReports.Workflow.get_wklstep_rule!(params["step_id"]).step_name) in @review_steps and
           params["reference"] do
        case MisReports.Workflow.get_task_comments(params["reference"]) do
          nil ->
            socket

          comments ->
            assign(socket, :comments, comments)
        end
      else
        socket
      end

    # For review steps, automatically load the saved period

    if normalize_step_name(MisReports.Workflow.get_wklstep_rule!(params["step_id"]).step_name) in @review_steps do
      case MisReports.Workflow.get_report_period(params["reference"]) do
        nil ->
          socket
          |> put_flash(:error, "Report period not found")
          |> assign(loader: false)

        period ->
          filter_params = %{
            "start_date" => Date.to_iso8601(period.start_date),
            "end_date" => Date.to_iso8601(period.end_date)
          }

          Process.send_after(self(), {:process_filter, filter_params}, 100)
          assign(socket, loader: true)
      end
    else
      socket
    end

    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    # Increased timeout to 15 seconds to ensure visibility
    Process.send_after(self(), :hide_helper, 15000)

    process_id = params["process_id"] || socket.assigns.process_id
    reference = params["reference"] || socket.assigns.reference
    step_id = params["step_id"] || socket.assigns.step_id

    {:noreply,
     socket
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)}
  end

  @impl true
  def handle_info(:hide_helper, socket) do
    {:noreply, assign(socket, :show_helper, false)}
  end

  defp init_settings do
    %MisReports.Utilities.CompanySettings{
      username: "E0031000202401",
      tel_no: "**********",
      post_url: "https://bsatest.boz.zm/BSAAPI/api/submission/v2",
      password: "6U4}y3=sr2<G25P",
      institution_code: "0031000",
      company_name: "Stanbic Bank",
      bank_sort_code: "122225",
      acronym: "SC"
    }
  end

  @impl true
  def handle_event("view_page", %{"value" => schedule_type}, socket) do
    socket = assign(socket, :loader, true)

    case socket.assigns.cached_schedules do
      %{^schedule_type => data} when not is_nil(data) ->
        # Use cached data
        IO.puts("Loading FROM CACHE: #{schedule_type}")

        {:noreply,
         socket
         |> assign(:report_type, schedule_type)
         |> assign(:loader, false)
         |> push_event("get-pending-comments", %{})}

      _ ->
        # Load schedule if not cached

        new_prudential?(
          nil,
          schedule_type,
          socket.assigns.entries,
          socket.assigns.filter_params,
          get_usd_rate(socket.assigns.filter_params["start_date"]),
          socket.assigns.adjustments
        )

        {:noreply, socket}
    end
  end

  # Modify the filter-report handler for step 4
  def handle_event("filter-report", %{"report" => params}, socket) do
    step_name = MisReports.Workflow.get_wklstep_rule!(socket.assigns.step_id).step_name

    # Add thorough debugging

    normalized_step =
      step_name
      |> normalize_step_name()

    review_steps_normalized =
      @review_steps
      |> Enum.map(&normalize_step_name/1)

    cond do
      # Handle Generate Credit Report Stage
      normalize_step_name(step_name) == "generate_credit_report_stage" ->
        socket = assign(socket, loader: true)
        socket = assign(socket, :temp_period_params, params)
        Process.send_after(self(), {:process_filter, params}, 100)
        {:noreply, socket}

      # Handle Review Steps
      normalized_step in review_steps_normalized ->
        IO.inspect(normalized_step, label: "Matched: Review Step -")

        case MisReports.Workflow.get_report_period(socket.assigns.reference) do
          nil ->
            IO.inspect(socket.assigns.reference, label: "Reference:")

            {:noreply,
             socket
             |> put_flash(:error, "Report period not found")
             |> assign(loader: false)}

          period ->
            IO.inspect("Found period: #{inspect(period)}")

            params = %{
              "start_date" => Date.to_iso8601(period.start_date),
              "end_date" => Date.to_iso8601(period.end_date)
            }

            socket = assign(socket, loader: true)
            Process.send_after(self(), {:process_filter, params}, 100)
            {:noreply, socket}
        end

      # Default case
      true ->
        Logger.debug("No matching step found")
        {:noreply, socket}
    end
  end

  # Add this new handler for processing the filter
  def handle_info({:process_filter, params}, socket) do
    # Handle period creation/update first
    socket = handle_period_check(socket, params)

    # Use ScheduleLoader's cached functions
    case MisReports.ScheduleLoader.load_schedules(params) do
      {:ok, results} when is_map(results) ->
        # Get entries and adjustments from cache
        month = String.replace(params["start_date"], "-", "") |> String.slice(0..5)
        entries = MisReports.ScheduleLoader.get_cached_entries(month)
        settings = MisReports.Utilities.get_comapany_settings_params()
        adjustments = MisReports.ScheduleLoader.get_cached_adjustments(params["end_date"])

        handle_schedule_results(socket, results, entries, settings, params, adjustments)

      {:error, _reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to load schedules")
         |> assign(:loader, false)}
    end
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    report_type =
      case tab do
        "income_statement" -> "income_statement"
        "balance_sheet" -> "blc_sheet"
        _ -> ""
      end

    # Restore any pending comments from storage before switching tabs
    socket = restore_comment_log(socket)

    # Push events to sync comments
    {:noreply,
     socket
     |> assign(:active_tab, tab)
     |> assign(:report_type, report_type)
     |> push_event("update-main-comment", %{comment: socket.assigns.comment})
     |> push_event("get-pending-comments", %{})}
  end

  # Handle the event when client sends back the pending comments
  def handle_event("pending_comments_data", %{"comments" => comments_json}, socket) do
    # Parse the JSON string of comments
    case Jason.decode(comments_json) do
      {:ok, comments_map} ->
        # Combine all comments into one string for the main comment field
        combined_comments = combine_pending_comments(comments_map, socket.assigns.reference)

        {:noreply, assign(socket, :comment, combined_comments)}

      {:error, _} ->
        # If there's an error parsing the JSON, just continue without changing comments
        {:noreply, socket}
    end
  end

  # Helper function to combine all pending comments into one string
  defp combine_pending_comments(comments_map, reference) do
    # Filter comments that belong to the current reference
    reference_prefix = "#{reference}_"

    comments_map
    |> Enum.filter(fn {key, _} -> String.starts_with?(key, reference_prefix) end)
    |> Enum.map(fn {key, comment} ->
      # Extract schedule ID from the key
      schedule = String.replace_prefix(key, reference_prefix, "")
      # Format the schedule name
      schedule_name = format_schedule_name(schedule)
      # Format the comment
      "[#{schedule_name}] - #{comment}"
    end)
    |> Enum.join("\n")
  end

  def handle_event("switch_view", %{"view" => view}, socket) do
    default_report =
      case view do
        "income_statement" -> "income_statement"
        "balance_sheet" -> "blc_sheet"
        "other_schedules" -> "schedule_20A"
        _ -> ""
      end

    # Also restore comments when switching views
    {:noreply,
     socket
     |> assign(:current_view, view)
     |> assign(:report_type, default_report)
     |> push_event("get-pending-comments", %{})}
  end

  def handle_event("toggle_fullscreen", _, socket) do
    {:noreply, assign(socket, :is_fullscreen, !socket.assigns.is_fullscreen)}
  end

  def handle_event("update_comment", params, socket) do
    comment = params["comment"] || params["value"] || ""
    IO.inspect(comment, label: "Comment updated to")
    {:noreply, assign(socket, :comment, comment)}
  end

  defp get_template(
         current_view,
         report_type,
         balance_sheet_schedules,
         income_statement_schedules,
         other_schedules,
         step_id
       ) do
    if step_id &&
         normalize_step_name(MisReports.Workflow.get_wklstep_rule!(step_id).step_name) in @review_steps and
         report_type == "" do
      # Return nil to skip template rendering and show loader
      nil
    else
      cond do
        report_type == "blc_sheet" and current_view == "balance_sheet" ->
          "balance_sheet.html"

        report_type == "income_statement" and current_view == "income_statement" ->
          "income_statement.html"

        report_type == "other_schedules" and current_view == "other_schedules" ->
          "schedule_20A.html"

        report_type in balance_sheet_schedules ->
          "#{report_type}.html"

        report_type in income_statement_schedules ->
          "#{report_type}.html"

        report_type in other_schedules ->
          "#{report_type}.html"

        true ->
          "report_filter.html"
      end
    end
  end

  def clean_data(entries) do
    Enum.map(entries, fn e ->
      case String.length(e.sap_gl_acc_no) > 6 do
        true -> Map.put(e, :sap_gl_acc_no, String.slice(e.sap_gl_acc_no, 4..-1))
        false -> e
      end
    end)
  end

  def page_name(:index), do: "Income Statement"

  def gen_header(date) do
    date = get_month(date)

    %{
      inst_code: "0031000",
      inst_name: "Stanbic Bank",
      start_date: date.start,
      end_date: date.end
    }
  end

  def get_month(date) do
    [y, m, _d] = String.split(date, "-")

    case m do
      "01" -> %{start: "1 January #{y}", end: "30 January #{y}"}
      "02" -> %{start: "1 February #{y}", end: "28 February #{y}"}
      "03" -> %{start: "1 March #{y}", end: "30 March #{y}"}
      "04" -> %{start: "1 April #{y}", end: "30 April #{y}"}
      "05" -> %{start: "1 May #{y}", end: "30 May #{y}"}
      "06" -> %{start: "1 June #{y}", end: "30 June #{y}"}
      "07" -> %{start: "1 July #{y}", end: "30 July #{y}"}
      "08" -> %{start: "1 August #{y}", end: "30 August #{y}"}
      "09" -> %{start: "1 September #{y}", end: "30 Septermber #{y}"}
      "10" -> %{start: "1 October #{y}", end: "30 October #{y}"}
      _ -> %{start: "1 September #{y}", end: "30 Septermber #{y}"}
    end
  end

  def handle_info({_ref, {:load_schedule, result, filter_params, schedule_type}}, socket) do
    assigns = [
      report_type: schedule_type,
      loader: false,
      filter_params: filter_params,
      data: result,
      download_btn: false
    ]

    {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, _pid, :normal}, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_info({:save_schedule}, socket) do
    IO.inspect("====================== SAVING DATA =============================")

    report_date = socket.assigns.filter_params["start_date"]
    month = String.replace(report_date, "-", "") |> String.slice(4..5)
    year = String.replace(report_date, "-", "") |> String.slice(0..3)
    audit_msg = "Created New Prudential report for Month: #{month} and Year: #{year}"
    user = socket.assigns.current_user
    socket = assign(socket, loader: false)
    refence_number = socket.assigns.reference

    IO.inspect(refence_number, label: "=======Reference number IN handle_save============")

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :report,
      FileExport.changeset(%FileExport{maker_id: user.id}, %{
        report_date: report_date,
        reference: refence_number
      })
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{report: _report, audit_log: _user_log}} ->
        message = %{message: %{info: "Prudential report confirmation successful."}}

        {:noreply, push_event(socket, "notification", message)}

      {:error, _failed_operation, _failed_value, _changes_so_far} ->
        message = %{message: %{info: "Prudential report confirmation successful."}}

        {:noreply, push_event(socket, "notification", message)}
        # error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        # {:noreply, push_event(socket, "notification", %{message: %{error: error_msg}})}
    end
  end

  defp new_prudential?(nil, schedule_type, data, filter_params, usd_rate, adjustments) do
    Task.async(fn ->
      result =
        case schedule_type do
          "schedule_03A" ->
            MisReports.Workers.LoansAdvances.Sh03a.generate_display(filter_params["end_date"]).list

          "schedule_03B" ->
            MisReports.Workers.Sh03b.generate_display(data)

          "schedule_4D" ->
            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

            start_date = filter_params["end_date"] |> Date.from_iso8601!()
            prev_date = Timex.shift(start_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)

            shd13 =
              MisReports.Workers.Sh13.generate_display(
                income,
                prev_income,
                filter_params["end_date"]
              )

            balance_sheet =
              MisReports.Workers.BalanceSheet.generate_display(
                shd13,
                data,
                filter_params["start_date"],
                filter_params["end_date"],
                usd_rate,
                adjustments
              )

            MisReports.Workers.LoansAdvances.Sh4d.generate_display(
              balance_sheet,
              filter_params["end_date"],
              adjustments
            ).list

          "schedule_18A" ->
            MisReports.Workers.Sh18a.generate_display(filter_params["end_date"])

          "schedule_18B" ->
            depositors =
              MisReports.SourceData.number_depositors_data_18b(filter_params["end_date"])

            loans_entry =
              MisReports.Workers.LoansAdvances.Sh18b.generate_display(filter_params["end_date"])

            MisReports.Workers.Sh18b.generate_display(
              data,
              loans_entry,
              depositors,
              filter_params["end_date"]
            )

          "schedule_18C" ->
            MisReports.Workers.Sh18c.generate_display(data)

          "schedule_18D" ->
            MisReports.Workers.Sh18d.generate_display(data)

          "income_statement" ->
            MisReports.Workers.IS.generate_display(
              data,
              filter_params["end_date"],
              adjustments
            )

          "schedule_24" ->
            MisReports.Workers.Sh24.generate_display(filter_params["end_date"])

          "schedule_27" ->
            MisReports.Workers.Sh27.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_27A" ->
            MisReports.Workers.Sh27a.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_28A" ->
            MisReports.Workers.Sh28a.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_28B" ->
            MisReports.Workers.Sh28b.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_21B" ->
            MisReports.Workers.Sh21b.generate_display(data, adjustments)

          "schedule_01E" ->
            MisReports.Workers.Sh01e.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_01c" ->
            MisReports.Workers.Sh01c.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_21C" ->
            MisReports.Workers.Sh21c.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_12" ->
            MisReports.Workers.Sh12.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_13" ->
            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

            end_date = filter_params["end_date"] |> Date.from_iso8601!()
            prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)

            MisReports.Workers.Sh13.generate_display(
              income,
              prev_income,
              filter_params["end_date"]
            )

          _ ->
            []
        end

      send(self(), {:load_schedule, result, filter_params, schedule_type})
    end)
  end

  defp generate_balance_sheet(income_statement, entries, date, adjustments) do
    # Generate schedule 13 with the income statement data
    shd13 = MisReports.Workers.Sh13.generate_display(income_statement, income_statement, date)

    # Get USD exchange rate
    usd_rate =
      Utilities.get_exchange_rate_by_date_and_code(date, "USD")[:exchange_rate_lcy] || "1"

    # Generate balance sheet
    MisReports.Workers.BalanceSheet.generate_display(
      shd13,
      entries,
      date,
      date,
      usd_rate,
      adjustments
    )
  end

  defp get_usd_rate(date) do
    Utilities.get_exchange_rate_by_date_and_code(date, "USD")[:exchange_rate_lcy] || "1"
  end

  # Add these event handlers after your existing handle_event functions

  def handle_event("submit_report", _params, socket) do
    current_user_id = to_string(socket.assigns.current_user.id)
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           80,
           "",
           "",
           " Generated Prudential Report Submission"
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Report submitted successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to submit report: #{reason}")
         |> assign(:loader, false)}
    end
  end

  def handle_event("save", params, socket) do
    action = params["action"]
    comment = params["comment"]

    # **AUTOMATIC COMMENT AGGREGATION FOR WORKFLOW SUBMISSION**
    # Ensure all schedule-specific comments are included in the final comment
    final_comment = if String.trim(comment || "") == "" do
      # If no additional comment provided, use only aggregated schedule comments
      socket.assigns.comment || ""
    else
      # The comment field already contains aggregated schedule comments
      # The user may have added additional review comments at the top
      comment
    end

    if String.trim(final_comment) == "" do
      {:noreply,
       socket
       |> put_flash(:error, "Please add a comment or provide schedule-specific feedback before proceeding")
       |> assign(:loader, false)}
    else
      case action do
        "97" ->
          if normalize_step_name(
               MisReports.Workflow.get_wklstep_rule!(socket.assigns.step_id).step_name
             ) ==
               "final_report_approval" do
            handle_final_approval(socket, final_comment)
          else
            handle_approval(socket, final_comment)
          end

        "96" ->
          handle_rejection(socket, final_comment)

        _ ->
          {:noreply,
           socket
           |> put_flash(:error, "Invalid action")
           |> assign(:loader, false)}
      end
    end
  end

  # Add a JavaScript event to clear pending comments when approving or rejecting
  defp clear_pending_comments(socket) do
    push_event(socket, "clear-pending-comments", %{})
  end

  # Add these private functions for handling approvals and rejections

  defp handle_approval(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           97,
           "",
           "",
           comment
         ) do
      {:ok, _reference_number} ->
        {:noreply,
         socket
         |> clear_pending_comments()
         |> put_flash(:info, "Report approved successfully")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Approval failed: #{reason}")
         |> assign(:loader, false)}
    end
  end

  defp handle_final_approval(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    # First handle the save_schedule
    case handle_info({:save_schedule}, socket) do
      {:noreply, updated_socket} ->
        # Update report period status
        case MisReports.Workflow.update_report_period_status(socket.assigns.reference, "approved") do
          {:ok, _period} ->
            # Then call workflow
            case MisReports.Workflow.call_workflow(
                   socket.assigns.reference,
                   socket.assigns.process_id,
                   current_user_id,
                   97,
                   "",
                   "",
                   comment
                 ) do
              {:ok, _reference_number} ->
                {:noreply,
                 updated_socket
                 |> put_flash(:info, "Prudential Report Process completed successfully")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, reason} ->
                {:noreply,
                 updated_socket
                 |> put_flash(:error, "Final approval failed: #{reason}")
                 |> assign(:loader, false)}
            end

          {:error, reason} ->
            {:noreply,
             updated_socket
             |> put_flash(:error, "Failed to update report status: #{reason}")
             |> assign(:loader, false)}
        end

      error ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to save schedule: #{inspect(error)}")
         |> assign(:loader, false)}
    end
  end

  defp handle_rejection(socket, comment) do
    current_user_id = to_string(socket.assigns.current_user.id)

    case MisReports.Workflow.call_workflow(
           socket.assigns.reference,
           socket.assigns.process_id,
           current_user_id,
           96,
           "",
           "",
           comment
         ) do
      {:ok, _reference_number} ->
        {:noreply,
         socket
         |> clear_pending_comments()
         |> put_flash(:info, "Report rejected successfully")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Rejection failed: #{reason}")
         |> assign(:loader, false)}
    end
  end

  def handle_event("helper_clicked", _params, socket) do
    {:noreply, assign(socket, :helper_clicked, true)}
  end

  defp handle_period_check(socket, params) do
    if normalize_step_name(
         MisReports.Workflow.get_wklstep_rule!(socket.assigns.step_id).step_name
       ) ==
         "generate_credit_report_stage" do
      case MisReports.Workflow.get_report_period(socket.assigns.reference) do
        nil -> create_new_period(socket, params)
        existing -> update_existing_period(socket, existing, params)
      end
    else
      socket
    end
  end

  defp create_new_period(socket, params) do
    period_attrs = %{
      reference: socket.assigns.reference,
      start_date: Date.from_iso8601!(params["start_date"]),
      end_date: Date.from_iso8601!(params["end_date"]),
      report_type: "Prudential_Monthly",
      status: "pending"
    }

    case MisReports.Workflow.create_report_period(period_attrs) do
      {:ok, _period} ->
        socket

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to save report period")
        |> assign(loader: false)
    end
  end

  defp update_existing_period(socket, existing, params) do
    update_attrs = %{
      start_date: Date.from_iso8601!(params["start_date"]),
      end_date: Date.from_iso8601!(params["end_date"])
    }

    case MisReports.Workflow.update_report_period(existing, update_attrs) do
      {:ok, _updated_period} ->
        socket

      {:error, _} ->
        socket
        |> put_flash(:error, "Failed to update report period dates")
        |> assign(loader: false)
    end
  end

  defp handle_schedule_results(socket, results, entries, settings, params, adjustments) do
    case results do
      %{"income_statement" => income_statement, "blc_sheet" => balance_sheet} ->
        {:noreply,
         socket
         |> assign(:page, %{prev: "Financial Reports", current: "Reports"})
         |> assign(:entries, entries)
         |> assign(:settings, settings)
         |> assign(:filter_params, params)
         |> assign(:adjustments, adjustments)
         |> assign(:income_statement_data, income_statement)
         |> assign(:balance_sheet_data, balance_sheet)
         |> assign(:cached_schedules, results)
         |> assign(:report_type, "income_statement")
         |> assign(:active_tab, "income_statement")
         |> assign(:prud_status, "Pending")
         |> assign(:loader, false)}

      %{"income_statement" => income_statement} ->
        balance_sheet =
          generate_balance_sheet(income_statement, entries, params["end_date"], adjustments)

        {:noreply,
         socket
         |> assign(:page, %{prev: "Financial Reports", current: "Reports"})
         |> assign(:entries, entries)
         |> assign(:settings, settings)
         |> assign(:filter_params, params)
         |> assign(:adjustments, adjustments)
         |> assign(:income_statement_data, income_statement)
         |> assign(:balance_sheet_data, balance_sheet)
         |> assign(:cached_schedules, Map.put(results, "blc_sheet", balance_sheet))
         |> assign(:report_type, "income_statement")
         |> assign(:active_tab, "income_statement")
         |> assign(:prud_status, "Pending")
         |> assign(:loader, false)}

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to load reports data")
         |> assign(:loader, false)}
    end
  end

  # Add these event handlers
  def handle_event("create_adjustment", _params, socket) do
    report_date = socket.assigns.filter_params["end_date"]
IO.inspect(report_date, label: "Report Date")
    if is_nil(report_date) do
      {:noreply,
       socket
       |> put_flash(:error, "Please select a report period first")}
    else
      case MisReportsWeb.AdjustmentsLive.Index.create_initial_record(
             report_date,
             socket.assigns.current_user,
             socket.assigns.reference
           ) do
        {:ok, result} ->
          {:noreply,
           socket
           |> put_flash(:info, result.message)
           |> redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

        {:error, %{message: message}} ->
          {:noreply,
           socket
           |> put_flash(:error, message)}
      end
    end
  end

  def handle_event("view_adjustment", _params, socket) do
    {:noreply,
     socket
     |> redirect(to: Routes.adjustments_index_path(socket, :index))}
  end

  # Handle opening the comment modal using LiveView assigns
  @impl true
  def handle_event("open_comment_modal", %{"schedule" => schedule}, socket) do
    schedule_name = format_schedule_name(schedule)

    {:noreply,
     socket
     |> assign(:show_comment_modal, true)
     |> assign(:current_schedule, schedule)
     |> assign(:current_schedule_name, schedule_name)
     |> assign(:modal_comment, "")}
  end

  # Handle closing the comment modal
  @impl true
  def handle_event("close_comment_modal", _params, socket) do
    {:noreply,
     socket
     |> assign(:show_comment_modal, false)
     |> assign(:current_schedule, nil)
     |> assign(:current_schedule_name, nil)
     |> assign(:modal_comment, "")}
  end

  def handle_event("save_comment", %{"comment" => comment}, socket) do
    # Get schedule from socket assigns
    schedule = socket.assigns.current_schedule

    if is_nil(schedule) or String.trim(comment) == "" do
      {:noreply, socket |> put_flash(:error, "Please enter a comment")}
    else
      reference_number = socket.assigns.reference
      schedule_name = format_schedule_name(schedule)

      # Push event to store comment in browser storage
      {:noreply,
       socket
       |> push_event("store-schedule-comment", %{
         key: "#{reference_number}_#{schedule}",
         comment: comment
       })
       |> assign(:show_comment_modal, false)
       |> assign(:current_schedule, nil)
       |> assign(:current_schedule_name, nil)
       |> assign(:modal_comment, "")
       |> push_event("get-pending-comments", %{})}
    end
  end

  def handle_event("cancel_comment", _params, socket) do
    # Just return the socket, the JavaScript will handle hiding the modal
    {:noreply, socket}
  end

  # Helper function to format schedule ID into a readable name
  defp format_schedule_name(schedule) do
    case schedule do
      "income_statement" -> "Income Statement"
      "blc_sheet" -> "Balance Sheet"
      schedule when is_binary(schedule) ->
        # Convert "schedule_01A" to "Schedule 01A"
        schedule
        |> String.replace("schedule_", "Schedule ")
        |> String.upcase()
      _ -> "Unknown Schedule"
    end
  end

  # Helper function to count comments in the comment string
  defp count_comments(comment_string) when is_binary(comment_string) do
    comment_string
    |> String.split("\n")
    |> Enum.filter(fn line -> String.trim(line) != "" end)
    |> length()
  end
  defp count_comments(_), do: 0

  # Helper function to restore comment log from pending comments
  defp restore_comment_log(socket) do
    # Trigger client-side to send back pending comments
    socket
    |> push_event("get-pending-comments", %{})
  end

  # Helper function to clear comment log (only called on final approval/rejection)
  defp clear_comment_log(socket) do
    reference_number = socket.assigns.reference
    pending_comments = socket.assigns.pending_comments
    comment_log_key = "#{reference_number}_comment_log"

    updated_pending_comments = Map.delete(pending_comments, comment_log_key)

    socket
    |> assign(:comment, "")
    |> assign(:pending_comments, updated_pending_comments)
  end

  # Helper function to format comment text with bold schedule titles
  defp format_comment_with_bold_schedules(comment) do
    # Split the comment by newlines to handle existing line breaks
    lines = String.split(comment, "\n")

    # Process each line
    formatted_lines =
      Enum.map(lines, fn line ->
        # Check if this line contains a schedule reference
        if Regex.match?(~r/\[(.*?)\]/, line) do
          # Format the schedule reference with bold styling
          Regex.replace(~r/\[(.*?)\] - (.*)/, line, fn _, schedule, text ->
            "<div class=\"schedule-comment\"><strong class=\"schedule-tag\">#{schedule}</strong> #{text}</div>"
          end)
        else
          # Regular line without schedule reference
          "<div>#{line}</div>"
        end
      end)

    # Join the formatted lines and return as raw HTML
    Phoenix.HTML.raw(Enum.join(formatted_lines, ""))
  end

  #  Add this helper function
  defp debug_template_render(current_view, template, assigns, data) do
    IO.inspect(current_view, label: "Current View")
    IO.inspect(template, label: "Template Being Rendered")
    IO.inspect(data, label: "data Being Rendered")

    IO.inspect(get_in(data, [:gender_totals]), label: "Gender Totals in Data")
    IO.inspect(get_in(data, [:province_totals]), label: "Province Totals in Data")

    view_module =
      case current_view do
        "income_statement" -> MisReportsWeb.IncomeStatementView
        "balance_sheet" -> MisReportsWeb.BalanceSheetView
        "other_schedules" -> MisReportsWeb.OtherSchedulesView
        _ -> MisReportsWeb.IncomeStatementView
      end

    IO.inspect(view_module, label: "View Module")

    Phoenix.View.render(
      view_module,
      template,
      Map.merge(assigns, %{data: data})
    )
  end
end

# String.slice("0000317050", 3..10)
