defmodule MisReports.Workers.BozReq.Schedule28a do
  def perform(item) do
    decoded_item =
      case item.schedule_28a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)
    IO.inspect(decoded_item, label: "===============================")
    return_items = decoded_item["total"]
    dynamic_list = decoded_item["list"]
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    # %{
    #   "ReturnKey" => "DEMOBSO1001",
    #   "InstCode" => "#{settings.institution_code}",
    #   "FinYear" => 2024,
    #   "StartDate" => "2024-01-01",
    #   "EndDate" => "2024-01-31",
    #   "ReturnItemsList" => [
    #     %{
    #       "Code" => "BSO1001_00001",
    #       "Value" => "6",
    #       "_dataType" => "NUMERIC"
    #     },
    #     %{
    #       "Code" => "BSO1001_00002",
    #        "Value" => "#{decoded_item["B12"]}",
    #       "_dataType" => "NUMERIC"
    #     }
    #     # ... (rest of the return items would follow the same pattern)
    #   ]
    # }

    %{
      "ReturnKey" => "ZM-VYSCH28AVY001",
     "InstCode" => "#{settings.institution_code}",
      "FinYear" => 2021,
      "StartDate" => "2021-01-01T00:00:00",
      "EndDate" => "2021-01-31T00:00:00",
      "ReturnItemsList" => [
        %{
          "Code" => "1200_00001",
          "Value" => "#{decoded_item["@data.total"]}",
          "_dataType" => "NUMERIC"
        }
      ],
          "DynamicItemsList" => [
            %{
              "Area" =>468,
              "_areaName" => "OTHER NON-INTEREST EXPENSES",
              "DynamicItems" => map_data(dynamic_list) |> format_values()
            }
          ]
        }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


def map_data(records) do
  Enum.flat_map(Enum.with_index(records), fn {map, index} ->
      index = index + 1
      [
        %{"Code" => "#{index}.1", "Value" => map["Account Name"], "_dataType" => "TEXT"},
        %{"Code" => "#{index}.2", "Value" => "#{convert_to_number(Map.get(map, "Amount", 0))}", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.3", "Value" => map["Detail"], "_dataType" => "TEXT"},
      ]
    end)
  end
  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)
        "TEXT" ->
          update_text_value(map)
        _ ->
          map
      end

    end)
  end
  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end
  defp update_text_value(map)
 do
    if Map.get(map, "Value") in [nil,
 ""] do
      Map.put(map, "Value", "'")
    else

      map
    end
  end

  defp convert_to_number(value)
 when is_binary(value) do
    case Float.parse(value) do

      {number, _} ->
 number
      :error ->
 0
    end
  end
  defp convert_to_number(value)
 when is_number(value), do: value
defp convert_to_number(_), do: 0
end
