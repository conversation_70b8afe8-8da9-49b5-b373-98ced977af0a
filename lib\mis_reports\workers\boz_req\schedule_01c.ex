defmodule MisReports.Workers.BozReq.Schedule01c do
  def perform(item) do

    decoded_item =
      case item.schedule_01c do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    # regulatory_capital = decoded_item["header"]

    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()
    %{
      "ReturnKey" => "ZM-3ASCH1C3A002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1129_00001", "Value" => "#{decoded_item["B15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00002", "Value" => "#{decoded_item["C15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00003", "Value" => "#{decoded_item["D15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00004", "Value" => "#{decoded_item["E15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00005", "Value" => "#{decoded_item["F15"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00006", "Value" => "#{decoded_item["B16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00007", "Value" => "#{decoded_item["C16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00008", "Value" => "#{decoded_item["D16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00009", "Value" => "#{decoded_item["E16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00010", "Value" => "#{decoded_item["F16"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00011", "Value" => "#{decoded_item["B17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00012", "Value" => "#{decoded_item["C17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00013", "Value" => "#{decoded_item["D17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00014", "Value" => "#{decoded_item["E17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00015", "Value" => "#{decoded_item["F17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00016", "Value" => "#{decoded_item["B18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00017", "Value" => "#{decoded_item["C18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00018", "Value" => "#{decoded_item["D18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00019", "Value" => "#{decoded_item["E18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00020", "Value" => "#{decoded_item["F18"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00021", "Value" => "#{decoded_item["B19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00022", "Value" => "#{decoded_item["C19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00023", "Value" => "#{decoded_item["D19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00024", "Value" => "#{decoded_item["E19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00025", "Value" => "#{decoded_item["F19"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00026", "Value" => "#{decoded_item["B22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00027", "Value" => "#{decoded_item["C22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00028", "Value" => "#{decoded_item["D22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00029", "Value" => "#{decoded_item["E22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00030", "Value" => "#{decoded_item["F22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00031", "Value" => "#{decoded_item["B23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00032", "Value" => "#{decoded_item["C23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00033", "Value" => "#{decoded_item["D23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00034", "Value" => "#{decoded_item["E23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00035", "Value" => "#{decoded_item["F23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00036", "Value" => "#{decoded_item["B24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00037", "Value" => "#{decoded_item["C24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00038", "Value" => "#{decoded_item["D24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00039", "Value" => "#{decoded_item["E24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00040", "Value" => "#{decoded_item["F24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00041", "Value" => "#{decoded_item["B27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00042", "Value" => "#{decoded_item["C27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00043", "Value" => "#{decoded_item["D27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00044", "Value" => "#{decoded_item["E27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00045", "Value" => "#{decoded_item["F27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00046", "Value" => "#{decoded_item["B28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00047", "Value" => "#{decoded_item["C28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00048", "Value" => "#{decoded_item["D28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00049", "Value" => "#{decoded_item["E28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00050", "Value" => "#{decoded_item["F28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00051", "Value" => "#{decoded_item["B29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00052", "Value" => "#{decoded_item["C29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00053", "Value" => "#{decoded_item["D29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00054", "Value" => "#{decoded_item["E29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00055", "Value" => "#{decoded_item["F29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00056", "Value" => "#{decoded_item["B30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00057", "Value" => "#{decoded_item["C30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00058", "Value" => "#{decoded_item["D30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00059", "Value" => "#{decoded_item["E30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00060", "Value" => "#{decoded_item["F30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00061", "Value" => "#{decoded_item["B33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00062", "Value" => "#{decoded_item["C33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00063", "Value" => "#{decoded_item["D33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00064", "Value" => "#{decoded_item["E33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00065", "Value" => "#{decoded_item["F33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00066", "Value" => "#{decoded_item["B34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00067", "Value" => "#{decoded_item["C34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00068", "Value" => "#{decoded_item["D34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00069", "Value" => "#{decoded_item["E34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00070", "Value" => "#{decoded_item["F34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00071", "Value" => "#{decoded_item["B35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00072", "Value" => "#{decoded_item["C35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00073", "Value" => "#{decoded_item["D35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00074", "Value" => "#{decoded_item["E35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00075", "Value" => "#{decoded_item["F35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00076", "Value" => "#{decoded_item["B36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00077", "Value" => "#{decoded_item["C36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00078", "Value" => "#{decoded_item["D36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00079", "Value" => "#{decoded_item["E36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00080", "Value" => "#{decoded_item["F36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00081", "Value" => "#{decoded_item["B37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00082", "Value" => "#{decoded_item["C37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00083", "Value" => "#{decoded_item["D37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00084", "Value" => "#{decoded_item["E37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00085", "Value" => "#{decoded_item["F37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00086", "Value" => "#{decoded_item["B38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00087", "Value" => "#{decoded_item["C38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00088", "Value" => "#{decoded_item["D38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00089", "Value" => "#{decoded_item["E38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00090", "Value" => "#{decoded_item["F38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00091", "Value" => "#{decoded_item["B39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00092", "Value" => "#{decoded_item["C39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00093", "Value" => "#{decoded_item["D39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00094", "Value" => "#{decoded_item["E39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00095", "Value" => "#{decoded_item["F39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00096", "Value" => "#{decoded_item["B40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00097", "Value" => "#{decoded_item["C40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00098", "Value" => "#{decoded_item["D40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00099", "Value" => "#{decoded_item["E40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00100", "Value" => "#{decoded_item["F40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00101", "Value" => "#{decoded_item["B43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00102", "Value" => "#{decoded_item["C43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00103", "Value" => "#{decoded_item["D43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00104", "Value" => "#{decoded_item["E43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00105", "Value" => "#{decoded_item["F43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00106", "Value" => "#{decoded_item["B44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00107", "Value" => "#{decoded_item["C44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00108", "Value" => "#{decoded_item["D44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00109", "Value" => "#{decoded_item["E44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00110", "Value" => "#{decoded_item["F44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00111", "Value" => "#{decoded_item["B45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00112", "Value" => "#{decoded_item["C45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00113", "Value" => "#{decoded_item["D45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00114", "Value" => "#{decoded_item["E45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00115", "Value" => "#{decoded_item["F45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00116", "Value" => "#{decoded_item["B46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00117", "Value" => "#{decoded_item["C46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00118", "Value" => "#{decoded_item["D46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00119", "Value" => "#{decoded_item["E46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00120", "Value" => "#{decoded_item["F46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00121", "Value" => "#{decoded_item["B47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00122", "Value" => "#{decoded_item["C47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00123", "Value" => "#{decoded_item["D47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00124", "Value" => "#{decoded_item["E47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00125", "Value" => "#{decoded_item["F47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00126", "Value" => "#{decoded_item["B48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00127", "Value" => "#{decoded_item["C48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00128", "Value" => "#{decoded_item["D48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00129", "Value" => "#{decoded_item["E48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00130", "Value" => "#{decoded_item["F48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00131", "Value" => "#{decoded_item["B49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00132", "Value" => "#{decoded_item["C49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00133", "Value" => "#{decoded_item["D49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00134", "Value" => "#{decoded_item["E49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00135", "Value" => "#{decoded_item["F49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00136", "Value" => "#{decoded_item["B50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00137", "Value" => "#{decoded_item["C50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00138", "Value" => "#{decoded_item["D50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00139", "Value" => "#{decoded_item["E50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00140", "Value" => "#{decoded_item["F50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00141", "Value" => "#{decoded_item["B51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00142", "Value" => "#{decoded_item["C51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00143", "Value" => "#{decoded_item["D51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00144", "Value" => "#{decoded_item["E51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00145", "Value" => "#{decoded_item["F51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00146", "Value" => "#{decoded_item["B52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00147", "Value" => "#{decoded_item["C52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00148", "Value" => "#{decoded_item["D52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00149", "Value" => "#{decoded_item["E52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00150", "Value" => "#{decoded_item["F52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00151", "Value" => "#{decoded_item["B53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00152", "Value" => "#{decoded_item["C53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00153", "Value" => "#{decoded_item["D53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00154", "Value" => "#{decoded_item["E53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00155", "Value" => "#{decoded_item["F53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00156", "Value" => "#{decoded_item["B54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00157", "Value" => "#{decoded_item["C54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00158", "Value" => "#{decoded_item["D54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00159", "Value" => "#{decoded_item["E54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00160", "Value" => "#{decoded_item["F54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00161", "Value" => "#{decoded_item["B55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00162", "Value" => "#{decoded_item["C55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00163", "Value" => "#{decoded_item["D55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00164", "Value" => "#{decoded_item["E55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00165", "Value" => "#{decoded_item["F55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00166", "Value" => "#{decoded_item["B56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00167", "Value" => "#{decoded_item["C56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00168", "Value" => "#{decoded_item["D56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00169", "Value" => "#{decoded_item["E56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00170", "Value" => "#{decoded_item["F56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00171", "Value" => "#{decoded_item["B57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00172", "Value" => "#{decoded_item["C57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00173", "Value" => "#{decoded_item["D57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00174", "Value" => "#{decoded_item["E57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00175", "Value" => "#{decoded_item["F57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00176", "Value" => "#{decoded_item["B58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00177", "Value" => "#{decoded_item["C58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00178", "Value" => "#{decoded_item["D58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00179", "Value" => "#{decoded_item["E58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00180", "Value" => "#{decoded_item["F58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00181", "Value" => "#{decoded_item["B59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00182", "Value" => "#{decoded_item["C59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00183", "Value" => "#{decoded_item["D59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00184", "Value" => "#{decoded_item["E59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00185", "Value" => "#{decoded_item["F59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00186", "Value" => "#{decoded_item["B60"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00187", "Value" => "#{decoded_item["C60"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00188", "Value" => "#{decoded_item["D60"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00189", "Value" => "#{decoded_item["E60"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00190", "Value" => "#{decoded_item["F60"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00191", "Value" => "#{decoded_item["B61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00192", "Value" => "#{decoded_item["C61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00193", "Value" => "#{decoded_item["D61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00194", "Value" => "#{decoded_item["E61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00195", "Value" => "#{decoded_item["F61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00196", "Value" => "#{decoded_item["B62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00197", "Value" => "#{decoded_item["C62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00198", "Value" => "#{decoded_item["D62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00199", "Value" => "#{decoded_item["E62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00200", "Value" => "#{decoded_item["F62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00201", "Value" => "#{decoded_item["B64"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00202", "Value" => "#{decoded_item["C64"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00203", "Value" => "#{decoded_item["D64"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00204", "Value" => "#{decoded_item["E64"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00205", "Value" => "#{decoded_item["F64"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00206", "Value" => "#{decoded_item["B65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00207", "Value" => "#{decoded_item["C65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00208", "Value" => "#{decoded_item["D65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00209", "Value" => "#{decoded_item["E65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1129_00210", "Value" => "#{decoded_item["F65"]}", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00211", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Treasury bills_ COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00212", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Treasury bills_AMORTISED COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00213", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Treasury bills_FAIR VALUE THROUGH P\u0026L", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00214", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Treasury bills_FAIR VALUE THROUGH OCI", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00215", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Treasury bills_FACE/NOMINALVALUE", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00216", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Government bonds_ COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00217", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Government bonds_AMORTISED COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00218", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Government bonds_FAIR VALUE THROUGH P\u0026L", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00219", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Government bonds_FAIR VALUE THROUGH OCI", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00220", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Government bonds_FACE/NOMINALVALUE", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00221", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Foreign currency bonds_ COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00222", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Foreign currency bonds_AMORTISED COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00223", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Foreign currency bonds_FAIR VALUE THROUGH P\u0026L", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00224", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Foreign currency bonds_FAIR VALUE THROUGH OCI", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00225", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_Foreign currency bonds_FACE/NOMINALVALUE", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00226", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_TOTAL - ISSUED BY THE GOVERNMENT_ COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00227", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_TOTAL - ISSUED BY THE GOVERNMENT_AMORTISED COST", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00228", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_TOTAL - ISSUED BY THE GOVERNMENT_FAIR VALUE THROUGH P\u0026L", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00229", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_TOTAL - ISSUED BY THE GOVERNMENT_FAIR VALUE THROUGH OCI", "_dataType" => "NUMERIC"},
        # %{"Code" => "1129_00230", "Value" => "NON-RESIDENT INVESTMENTS_ISSUED BY THE GOVERNMENT_TOTAL - ISSUED BY THE GOVERNMENT_FACE/NOMINALVALUE", "_dataType" => "NUMERIC"}
      ] |> format_map(),
      "DynamicItemsList" => []
    }


  end

  def format_map(list_of_maps) do
    Enum.map(list_of_maps, fn map ->
      Enum.map(map, fn {key, value} ->
        {key, format_number(value)}
      end)
      |> Map.new()
    end)
  end

  defp format_number(string) do
    string
    |> String.replace(",", "")
    |> case do
      "" -> "0"
      value -> value
    end
  end




  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end


end
