defmodule MisReports.Prudentials do
  @moduledoc """
  The Prudentials context.
  """

  import Ecto.Query, warn: false
  alias MisReports.Repo
  alias MisReports.Prudentials.LoanAdvance
  alias MisReports.SourceData.CustContribution
  alias MisReports.Prudentials.Obddr
  alias MisReports.Prudentials.RegulatoryCapital
  alias MisReports.Prudentials.LoanInsiderLending
  alias MisReports.Prudentials.LargeLoans
  alias MisReports.Prudentials.BusinessUnits
  alias MisReports.Prudentials.CreditCards
  alias MisReports.Prudentials.LoanProducts
  alias MisReports.Prudentials.LoanSectors
  alias MisReports.Prudentials.LoanClassifications
  alias MisReports.Prudentials.LoanSchemeCodes
  alias MisReports.Prudentials.LoanInsiderLending
  alias MisReports.Prudentials.LoanInsiderLending
  alias MisReports.Utilities.ExchangeRates
  alias MisReports.Prudentials.ReliefList
  alias MisReports.Prudentials.GovernmentExposureList
  alias MisReports.Prudentials.ObddrSectors
  alias MisReports.Prudentials.DaysPastDue
  alias MisReports.Prudentials.CmmpFileExport
  alias MisReports.Prudentials.CmmpReport

  # alias MisReports.Prudentials.AccountDomicileBranch
  @doc """
  Returns the list of tbl_loans_advances.

  ## Examples

      iex> list_tbl_loans_advances()
      [%LoanAdvance{}, ...]

  """

  def tumone() do
    PrudReport
    |> where([a], not is_nil(a.filename))
    |> Repo.all()
  end
  def list_tbl_loans_advances do
    Repo.all(LoanAdvance)
  end

  def get_loan_acc_not_maintained(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    CustContribution
    |> where([a],
        a.month_period == ^month and a.year_period == ^year
        and a.product_code_source in ^loan_scheme_codes()
        and a.business_unit in ^business_units()
        and a.account_status in ["ACT", "DORMNT", "INACT"]
        and a.actual_debit_balance > 0
        and a.source_system == "Finacle"
    )
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> where([a, b],
      is_nil(b.account_no)
    )
    |> select([a, b],
      %{account_number: a.customer_number_local_cif})
    |> Repo.all()
  end


  def business_units do
    BusinessUnits
    |> where([a], a.status == "A")
    |> Repo.all()
    |> Enum.map(&[&1.ccr_business_unit])
    |> List.flatten()

  end


  def loans_advances_foreign(date) do
    # exchange_rate = Application.get_env(:mis_reports, :exchange_rate) |> Decimal.new()
    # exposure = Application.get_env(:mis_reports, :exposure) |> Decimal.new()
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate = usd_rate(date)
    CustContribution
    |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
    |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
    |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product_type)
    |> join(:left, [a, b, c, d], e in "tbl_customer_details", on: a.customer_number_local_cif == e.account_no)
    |> join(:left, [a, b, c, d, e], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
    |> where([a, b, c, d, e, f],
      a.off_balance_sheet_flag == "On"
      and a.product_code_source in ^loan_scheme_codes()
      and a.business_unit in ^business_units()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0
      and a.source_system == "Finacle"
      and d.schedule_type == "02G"
      and f.month == ^date
      and f.status == "A")
    |> where([a], a.month_period == ^month and a.year_period == ^year)
    |> where([a], a.currency_code != "ZMW")
    # |> group_by([a, b, c, d, e, f], [
    #   a.account_number
    # ])
    |> order_by([a, b, c, d, e, f], desc: a.actual_debit_balance)
    |> select([a, b, c, d, e, f],
        %{
            "account_no" => a.account_number,
            "A" => a.customer_name,
            "B" =>  d.product_type,
            "C" =>  "USD",
            "currency_code" => a.currency_code,
            "D" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", e.economic_sector, e.economic_sector) ,
            "E" => fragment("CASE WHEN ? is null THEN 'Other Sectors'  ELSE ? END", e.economic_sub_sector, e.economic_sub_sector),
            "F" => fragment("
                              CASE
                              WHEN ? = 'USD' THEN ?
                              ELSE ? END AS relationship",
                              a.currency_code,
                              a.actual_debit_balance,
                              a.actual_debit_balance
                            ),
            "G" => a.actual_debit_balance,
            "H" => fragment("
                      CASE
                      WHEN ? = 'USD' THEN ?
                      ELSE ? END AS relationship",
                      a.currency_code,
                      a.actual_debit_balance,
                      a.actual_debit_balance
                    ),
            "I" => d.type_of_security,
            "J" => fragment("
                      CASE
                      WHEN ? = 'USD' THEN ?
                      ELSE ? END AS relationship",
                      a.currency_code,
                      a.actual_debit_balance,
                      a.actual_debit_balance
                    ),
            "K" => a.effective_debit_rate,
            "L" => fragment("CASE WHEN ? = 'USD' THEN 'Yes'  ELSE 'No' END", a.currency_code),
            "M" => "",
            "N" => "0.00%",
            "ratio" => d.security_value_ratio
         }
    )
    |> Repo.all()

  end

  def government_exposure_past_due_2h(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate = usd_rate(date)

    GovernmentExposureList
    |> join(:left, [a], b in "tbl_cust_contribution", on: a.account_no == b.account_number)
    |> join(:left, [a, b], c in "tbl_loan_business_units", on: b.business_unit == c.ccr_business_unit)
    |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: b.currency_code == d.currency_code)
    |> join(:left, [a, b, c, d], e in "tbl_days_past_due", on: a.account_no == e.account_no)
    |> where([a, b, c, d, e], b.month_period == ^month and b.year_period == ^year)
    |> where([a, b, c, d, e],
      a.month == ^date
      and b.off_balance_sheet_flag == "On"
      and b.product_code_source in ^loan_scheme_codes()
      and b.business_unit in ^business_units()
      and b.account_status in ["ACT", "DORMNT", "INACT"]
      and b.actual_debit_balance > 0
      and b.source_system == "Finacle"
      and d.month == ^date
      and d.status == "A"
      and e.date == ^date
      )
    |> group_by([a, b, c, d, e], [
      c.facility_category, e.days_past_due, b.currency_code
    ])
    |> select([a, b, c, d, e],
        %{
          "facility_category" => c.facility_category,
          "days_past_due" => e.days_past_due,
          "currency" => b.currency_code,
          "D" => fragment("CASE WHEN ? = 'ZMW' THEN 'K'  ELSE 'USD' END", b.currency_code),
          "E" => fragment("
                              CASE
                              WHEN ? = 'ZMW'  THEN ? / 1000
                              WHEN ? = 'USD' THEN ?
                              ELSE ? END AS relationship",
                              b.currency_code,
                              sum(b.actual_debit_balance),
                              b.currency_code,
                              sum(b.actual_debit_balance),
                              sum(b.actual_debit_balance)
                            )
        }
      )
      |> Repo.all()
      # |> Enum.filter(fn data -> data["D"] == "USD"  end)
      # |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, "E", Decimal.new("0"))) end)
  end

  @doc """
  Gets a single loan_advance.

  Raises `Ecto.NoResultsError` if the Loan advance does not exist.

  ## Examples

      iex> get_loan_advance!(123)
      %LoanAdvance{}

      iex> get_loan_advance!(456)
      ** (Ecto.NoResultsError)

  """
  def get_loan_advance!(id), do: Repo.get!(LoanAdvance, id)
  @doc """
  Creates a loan_advance.

  ## Examples

      iex> create_loan_advance(%{field: value})
      {:ok, %LoanAdvance{}}

      iex> create_loan_advance(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_loan_advance(attrs \\ %{}) do
    %LoanAdvance{}
    |> LoanAdvance.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a loan_advance.

  ## Examples

      iex> update_loan_advance(loan_advance, %{field: new_value})
      {:ok, %LoanAdvance{}}

      iex> update_loan_advance(loan_advance, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_loan_advance(%LoanAdvance{} = loan_advance, attrs) do
    loan_advance
    |> LoanAdvance.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a loan_advance.

  ## Examples

      iex> delete_loan_advance(loan_advance)
      {:ok, %LoanAdvance{}}

      iex> delete_loan_advance(loan_advance)
      {:error, %Ecto.Changeset{}}

  """
  def delete_loan_advance(%LoanAdvance{} = loan_advance) do
    Repo.delete(loan_advance)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking loan_advance changes.

  ## Examples

      iex> change_loan_advance(loan_advance)
      %Ecto.Changeset{data: %LoanAdvance{}}

  """
  def change_loan_advance(%LoanAdvance{} = loan_advance, attrs \\ %{}) do
    LoanAdvance.changeset(loan_advance, attrs)
  end

  def loans_list(%{role: %{loans: %{loan_list: "Y"}}}, search_params, page, size) do
    LoanAdvance
    |> handle_filter(search_params)
    |> order_by([a], desc: a.id)
    |> compose_loans_select()
    |> Repo.paginate(page: page, page_size: size)
  end

  def loans_list(_user, _search_params, _page, _size), do: empty_scrivener()

  def loans_count do
    scheme_codes = List.flatten(Enum.map(MisReports.Prudentials.LoanSchemeCodes |> MisReports.Repo.all(), &[&1.scheme_code]))
    cleaned_list = for value <- scheme_codes, do: String.replace(value, ~r/\r\n/, "")
    CustContribution
    |> where([a], a.product_code_source in ^cleaned_list
      and a.business_unit in ^business_units()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0 and a.currency_code == "USD" )
    |> Repo.all()
    |> Enum.count()
  end
  def govenrnment_exposure_list(date) do
    MisReports.Prudentials.GovernmentExposureList
    |> where([a], a.month == ^date)
    |> Repo.all()
  end

  def relief_list(date) do
    MisReports.Prudentials.ReliefList
    |> where([a], a.date == ^date)
    |> Repo.all()
  end

  def credit_card_list(date) do
    CreditCards
    |> where([a], a.date == ^date and a.stage in ["stg1", "stg2", "stg3"] and a.utilization > 0)
    |> Repo.all()
    |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :utilization, Decimal.new("0"))) end)
    |> Decimal.div(Decimal.new("1000"))
  end
  def loan_returns_2c(date) do

    # usd_rate = usd_rate(date)
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    CustContribution
    |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
    |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
    |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product)
    |> join(:left, [a, b, c, d], e in "tbl_customer_details", on: a.customer_number_local_cif == e.account_no)
    |> join(:left, [a, b, c, d, e], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
    |> join(:left, [a, b, c, d, e, f], g in "tbl_relief_list", on: a.account_number == g.account_no and g.date == ^date)
    |> where([a, b, c, d, e, f, g], a.month_period == ^month and a.year_period == ^year)
    |> where([a, b, c, d, e, f, g],
      a.off_balance_sheet_flag == "On"
      and a.product_code_source in ^loan_scheme_codes()
      and a.business_unit in ^business_units()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0
      and a.source_system == "Finacle"
      and f.month == ^date
      and f.status == "A"
      and b.status == "A"
      # and c.status == "A"
      # and e.status == "A"
      # and d.status != "A"

      )
    |> group_by([a, b, c, d, e, f, g], [
      # a.business_unit,
      # e.ccr_sector,
      # b.facility_category,
      # c.facility_type2,
      # a.sector_code_and_description,
      # e.prudential_sector,
      # e.prudential_sub_sector,
      # a.account_number,
      # f.currency_code,
      # d.product_type,
      # a.currency_code,
      e.economic_sector,
      b.facility_category,
      e.economic_sub_sector,
      c.facility_type2,
      a.currency_code,
      b.business_unit,
      fragment("CASE WHEN ? is null THEN 'Normal Deposits' ELSE 'Dedicated Lines Of Credit' END", g.account_no)
    ])
    # |> order_by([a, b, c, d, e, f], desc: b.facility_category)
    |> select([a, b, c, d, e, f, g], %{
      "A" => fragment("CASE WHEN ? = 'Large Corporate' THEN 'Corporate Loan'  ELSE ? END", b.facility_category, b.facility_category),
      "B" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", e.economic_sector, e.economic_sector) ,
      "C" => fragment("CASE WHEN ? is null THEN 'Other Sectors'  ELSE ? END", e.economic_sub_sector, e.economic_sub_sector),
      # "D" => fragment("CASE WHEN ? is null THEN 'Normal' ELSE 'Dedicated' END", g.account_no),#,
      "F" => fragment("CASE WHEN ? is null THEN 'Other' ELSE ? END", c.facility_type2, c.facility_type2),
      "G" => fragment("CASE WHEN ? is null THEN 'Normal Deposits' ELSE 'Dedicated Lines Of Credit' END", max(g.account_no)),
      "H" => fragment("
                        CASE
                        WHEN ? = 'ZMW'  THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ?  END AS relationship",
                        a.currency_code,
                        sum(a.actual_debit_balance),
                        a.currency_code,
                        sum(a.actual_debit_balance),
                        sum(a.actual_debit_balance)
                      ),
      "I" => fragment("CASE WHEN ? = 'ZMW' THEN 'K'  ELSE 'USD' END", a.currency_code),
      "J" => fragment("CASE WHEN ? = 'Financial and insurance activities' THEN 'Microfinance institutions' ELSE '' END", e.economic_sector),
      "K" => fragment("
                      CASE
                      WHEN ? = 'ZMW'  THEN ? / 1000
                      WHEN ? = 'USD' THEN ?
                      ELSE ? END AS relationship",
                      a.currency_code,
                      sum(a.actual_debit_balance),
                      a.currency_code,
                      sum(a.actual_debit_balance),
                      sum(a.actual_debit_balance)
                      ),
      "business_unit" => b.business_unit,
      "currency_code" => a.currency_code,
      "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ", max(a.account_number), ^date),
      # # "K" => fragment("CASE WHEN ? = 'ZMW' THEN (? / 1000) ELSE (? * ?) / 1000 END", a.currency_code, sum(a.actual_debit_balance), sum(a.actual_debit_balance), ^exchange_rate),
      "account_no" => max(a.account_number)
    })
    |> Repo.all()

  end

  # def group_by_subquery(account_no, date) do
  #   fragment("CASE WHEN EXISTS (SELECT 1 FROM tbl_relief_list  WHERE account_no = ? AND date = ?) THEN 'Normal' ELSE 'Dedicated' END", account_no, date)
  # end

  def miscallaneous_data_18b(date) do

    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

 loanbook =   CustContribution
              |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
              |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
              |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product)
              |> join(:left, [a, b, c, d], e in "tbl_account_domicile_branch", on: a.branch_name == e.account_domicile_branch)
              |> where([a, b, c, d], a.month_period == ^month and a.year_period == ^year)
              |> where([a, b, c, d],
                a.off_balance_sheet_flag == "On"
                and a.product_code_source in ^loan_scheme_codes()
                and a.business_unit in ^business_units()
                and a.account_status in ["ACT", "DORMNT", "INACT"]
                and a.actual_debit_balance > 0
                and a.source_system == "Finacle"
                and b.status == "A"
                )
              |> order_by([a, b, c, d], desc: b.facility_category )
              |> select([a, b, c, d, e], %{
                "account_no" => a.account_number,
                "province" => e.province,
                "business_unit" => b.business_unit

              })
              |> Repo.all()


 overdraft =   CustContribution
                |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
                |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
                |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product)
                |> join(:left, [a, b, c, d], e in "tbl_account_domicile_branch", on: a.branch_name == e.account_domicile_branch)
                |> where([a, b, c, d], a.month_period == ^month and a.year_period == ^year)
                |> where([a, b, c, d],
                  a.off_balance_sheet_flag == "On"
                  and a.product_code_source in ^loan_scheme_codes()
                  and a.business_unit in ^business_units()
                  and a.account_status in ["ACT", "DORMNT", "INACT"]
                  and a.total_credit_limits > 0
                  and is_nil(a.actual_debit_balance)
                  and a.source_system == "Finacle"
                  and b.status == "A"
                  )
                |> order_by([a, b, c, d], desc: b.facility_category )
                |> select([a, b, c, d, e], %{
                  "account_no" => a.account_number,
                  "province" => e.province,
                  "business_unit" => b.business_unit
                })
                |> Repo.all()


    loanbook ++ overdraft
  end

  # def loan_returns(date) do
  #   LoanAdvance
  #   |> where(
  #     [a], a.date == ^date
  #   )
  #   |> group_by([a], [
  #     a.prudential_sector_new_temp,
  #     a.credit_type,
  #     a.facility_type,
  #     a.account_ccy_code,
  #     a.interconnectedness,
  #     a.sub_sector_desc,
  #     a.institutional_units,
  #     a.class_type
  #   ])
  #   |> order_by([a], asc: a.account_ccy_code)
  #   |> select([a], %{
  #     "A" => a.class_type,
  #     "B" => a.prudential_sector_new_temp,
  #     "C" => a.sub_sector_desc,
  #     "D" => a.institutional_units,
  #     "F" => a.facility_type,
  #     "G" => a.credit_type,
  #     "H" => sum(a.outstanding_amt),
  #     "I" => a.account_ccy_code,
  #     "J" => a.interconnectedness
  #   })
  #   |> Repo.all()
  # end


  def total_loans(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    CustContribution
    |> where([a],
      a.fmi_profit_center_name not in ["BB SAVINGS&INVESTMENTS", "MONEY MARKET FUNDING"]
      and a.business_unit in ^business_units()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0
      and a.source_system == "Finacle")
    |> where([a], a.month_period == ^month and a.year_period == ^year)
    |> select([a], sum(a.actual_debit_balance))
    |> Repo.all()
  end

  # def loan_returns(from, to) do
  #   LoanAdvance
  #   |> join(:left, [a], b in "tbl_loan_sectors", on: a.regulatory_sector == b.system_sector)
  #   |> join(:inner, [a, _b], c in "tbl_loan_business_units", on: a.bus_unit_short_desc == c.business_unit)
  #   |> join(:left, [a, _b, _c], d in "tbl_loan_scheme_codes", on: a.scheme_code == d.scheme_code)
  #   |> join(:right, [_a, _b, _c, d], e  in "tbl_loan_products", on: d.facility_type == e.product)
  #   |> where(
  #     [a],
  #     fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
  #     fragment("CAST(? AS DATE) <= ?", a.date, ^to)
  #   )
  #   |> group_by([a, b, _c, d, e], [
  #     e.product,
  #     b.prudential_sector,
  #     b.prudential_sub_sector,
  #     a.institutional_units,
  #     e.product_type,
  #     a.credit_type,
  #     a.account_ccy_code,
  #     a.interconnectedness
  #     # e.prudential_sub_sector,
  #     # a.institutional_units,
  #     # a.class_type
  #   ])
  #   |> order_by([a, b, c, d, e], asc: max(a.account_ccy_code))
  #   |> select([a, b, c, d, e], %{
  #     "A" => max(c.facility_category),
  #     "B" => max(b.prudential_sector),
  #     "C" => max(b.prudential_sub_sector),
  #     "D" => max(a.institutional_units),
  #     "F" => max(e.product_type),
  #     "G" => max(a.credit_type),
  #     "H" => fragment("CASE WHEN ? = 'K' THEN (? / 1000) ELSE ? END", a.account_ccy_code, sum(a.dr_book_bal), sum(a.dr_book_bal)),
  #     "I" => max(a.account_ccy_code),
  #     "J" => max(a.interconnectedness)
  #   })
  #   |> Repo.all()
  # end

  # def loan_returns(from, to) do
  #   LoanAdvance
  #   |> where(
  #     [a],
  #     fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
  #     fragment("CAST(? AS DATE) <= ?", a.date, ^to)
  #   )
  #   |> group_by([a], [
  #     a.facility_type,
  #     a.prudential_sector_new_temp,
  #     a.credit_type,
  #     a.account_ccy_code,
  #     a.interconnectedness,
  #     a.sub_sector_desc,
  #     a.institutional_units,
  #     a.class_type
  #   ])
  #   |> order_by([a], asc: a.account_ccy_code)
  #   |> select([a], %{
  #     "A" => a.class_type,
  #     "B" => a.prudential_sector_new_temp,
  #     "C" => a.sub_sector_desc,
  #     "D" => a.institutional_units,
  #     "F" => a.facility_type,
  #     "G" => a.credit_type,
  #     "H" => sum(a.outstanding_amt),
  #     "I" => a.account_ccy_code,
  #     "J" => a.interconnectedness
  #   })
  #   |> Repo.all()
  # end

  def loan_returns_segmentation(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate = usd_rate(date)

    CustContribution
    |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
    |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
    |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product)
    # |> join(:left, [a, b, c, d], e in "tbl_loan_sectors", on: a.sector_code_and_description == e.ccr_sector)
    |> join(:left, [a, b, c, d], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
    |> where([a, b, c, d, f], a.month_period == ^month and a.year_period == ^year )
    |> where([a, b, c, d, f],
      a.off_balance_sheet_flag == "On"
      and a.product_code_source in ^loan_scheme_codes()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0
      and a.source_system == "Finacle"
      and f.month == ^date
      and f.status == "A"
      and a.business_unit in ["Business & Commercial Banking", "Corporate & Investment Banking"]
      and a.sector_code_and_description == "FINANCIAL INTERMEDIATION, EXCEPT INS/PENS FUNDING"
    )
    |> select([a, b, c, d, f], %{
        "account_number" => a.account_number,
        "A" => a.customer_name,
        "currency" => a.currency_code,
        # "sector" => e.prudential_sector,
        "H" => fragment("
                        CASE
                        WHEN ? = 'ZMW'  THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ? END AS relationship",
                        a.currency_code,
                        a.actual_debit_balance,
                        a.currency_code,
                        a.actual_debit_balance,
                        a.actual_debit_balance
                      )
    })
    |> Repo.all()
  end

  def foreign_currency_denominated_loans(from, to) do
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> where([a], a.account_ccy_code != "K")
    |> group_by([a], [
      a.account_no,
      a.facility_type,
      a.account_ccy_code,
      a.prudential_sector_new_temp,
      a.sub_sector_desc,
      a.outstanding_amt
    ])
    |> order_by([a], asc: a.account_ccy_code)
    |> select([a], %{
       "A" => a.account_no,
       "B" => a.facility_type,
       "C" => a.account_ccy_code,
       "D" => a.prudential_sector_new_temp,
       "E" => a.sub_sector_desc,
       "F" => a.outstanding_amt,
       "L" => fragment("CASE WHEN ? > 1000 THEN 'Yes' ELSE 'No' END", a.outstanding_amt),
       "zmw" => fragment("(? * ?)/1000", a.outstanding_amt, 20.2),
      #  "loans_with_corresponding_fcy" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'")"",
      #  "loans_with_corresponding_lcy" => fragment()"",
      #  "loans_without_corresponding_fcy" => fragment()"",
      #  "loans_without_corresponding_lcy" => fragment()"",


    })
    |> Repo.all()
  end

  def exchange_rates do
    %{
      euros: Decimal.new("22.238"),
      jpy: Decimal.new("0.141"),
      zar: Decimal.new("1.116")
    }

  end

  def loan_returns_government_related_entities(from, to) do
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> where([a], a.institutional_units == "Central Government")
    |> group_by([a], [
      a.days_past_due,
      a.institutional_units,
      a.prudential_sector_new_temp,
      a.account_ccy_code,
    ])
    |> order_by([a], asc: a.account_ccy_code)
    |> select([a], %{
      "B" => a.institutional_units,
      "sector" => a.prudential_sector_new_temp,
      "D" => a.account_ccy_code,
      "E" => sum(a.outstanding_amt),
      "F" => sum(a.outstanding_amt),
      "G" => a.days_past_due
    })
    |> Repo.all()
  end

  def targeted_medium_term_efinancing_facility_2a(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    ReliefList
    |> join(:left, [a], b in "tbl_cust_contribution", on: a.account_no == b.account_number)
    |> join(:left, [a, b], c in "tbl_loan_business_units", on: b.business_unit == c.ccr_business_unit)
    |> join(:left, [a, b, c], d in "tbl_loan_scheme_codes", on: b.product_code_source == d.scheme_code)
    |> join(:left, [a, b, c, d], e in "tbl_exchange_rates", on: a.currency == e.currency_code)
    |> join(:left, [a, b, c, d, e], f in "tbl_customer_details", on: b.customer_number_local_cif == f.account_no)
    |> where([a, b, c, d, e, f], b.month_period == ^month and b.year_period == ^year and e.month == ^date and e.status == "A")
    |> where([a, b, c, d, e, f],
    a.date == ^date
    and b.off_balance_sheet_flag == "On"
    and b.product_code_source in ^loan_scheme_codes()
    and b.business_unit in ^business_units()
    and b.account_status in ["ACT", "DORMNT", "INACT"]
    and b.actual_debit_balance > 0
    and b.source_system == "Finacle")
    |> select([a, b, c, d, e, f, g], %{
      "A" => b.customer_name,
      "B" => "",
      "C" => "Zambian",
      "D" => c.facility_category,
      "E" => "",
      "F" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", f.economic_sector, f.economic_sector),
      "G" => "",
      "H" => a.currency,
      "I" => a.amount,
      "J" => ^Decimal.new("1.00"),
      "K" => "",
      "L" => a.amount_balance,
      "M" => ^Decimal.new("1.0000"),
      "N" => fragment("(? * ?) / 1000", a.amount_balance, ^Decimal.new("1")) ,
      "O" => b.effective_debit_rate,
      "P" => b.effective_debit_rate,
      "Q" => "",
      "R" => fragment(
              "CASE   WHEN ? = 'Credit Card Debt' THEN 'Unsecured'
                      WHEN ? = 'Credit Card' THEN 'Unsecured'
                      WHEN ? = 'Scheme Loan' THEN 'Unsecured'
                      WHEN ? = 'Staff Loan' THEN 'Unsecured'
                      ELSE 'Secured'
              END AS relationship",
             d.facility_type,
             d.facility_type,
             d.facility_type,
             d.facility_type
             ),
      "S" => fragment("select top(1) type_of_security from tbl_loan_products where schedule_type = '02G' and product_type = ?", d.facility_type),
      "T" => fragment("select top(1) security_value_ratio from tbl_loan_products where schedule_type = '02G' and product_type = ?", d.facility_type),
      "U" => "New Loan",
      "V" => d.facility_type,
      "days_past_due" => fragment("select top(1) past_due_days from tbl_loans_advances where account_no = ? and date = ?", a.account_no, ^date),
      "account_no" => a.account_no
    }
    )
    |> Repo.all()


  end

  def distribution_of_loans_and_advances_normal_deposits_2d(from, to) do
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> where([a], a.credit_type == "Normal Deposits")
    |> group_by([a], [
      a.prudential_sector_new_temp,
      a.sub_sector_desc,
      a.institutional_units,
      a.account_ccy_code
    ])
    |> order_by([a], asc: a.prudential_sector_new_temp)
    |> select([a], %{
      prudential_sector_new_temp: a.prudential_sector_new_temp,
      sub_sector_desc: a.sub_sector_desc,
      institutional_units: a.institutional_units,
      outstanding_amt: sum(a.outstanding_amt),
      account_ccy_code: a.account_ccy_code
    })
    |> Repo.all()

  end
  def balance_sheet_normal_deposits_loans(from, to) do
    exchange_rate = Application.get_env(:mis_reports, :exchange_rate) |> Decimal.new()
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> where([a], a.credit_type == "Normal Deposits")
    |> select([a], %{
      usd_amount: fragment("select sum((? * outstanding_amt )/ 1000) from tbl_loans_advances where credit_type = 'Normal Deposits' and account_ccy_code = 'USD'", ^exchange_rate),
      lcy_amount: fragment("select sum(outstanding_amt) from tbl_loans_advances where credit_type = 'Normal Deposits' and account_ccy_code = 'K'")}
    )
    |> limit(1)
    |> Repo.all()
    |> Enum.reduce(Decimal.new("0"), fn i, _a ->
      Decimal.add(i.usd_amount, i.lcy_amount)
    end)

  end

  def usd_rate(date)do
     ExchangeRates
     |> where([a], a.currency_code == "USD" and a.month == ^date)
     |> Repo.all()
     |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :exchange_rate_lcy, Decimal.new("0"))) end)
  end

  def funding_source(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate =  usd_rate(date)
        CustContribution
        |> join(:left, [a], b in "tbl_exchange_rates", on: a.currency_code == b.currency_code)
        |> where([a, b], a.month_period == ^month and a.year_period == ^year and b.month == ^date and b.status == "A")
        |> where([a],
        a.off_balance_sheet_flag == "On"
        and a.product_code_source in ^loan_scheme_codes()
        and a.business_unit in ^business_units()
        and a.account_status in ["ACT", "DORMNT", "INACT"]
        and a.actual_debit_balance > 0
        and a.source_system == "Finacle")
        |> select([a, b], %{
          account: a.account_number,
          amount: fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            a.currency_code,
                            a.actual_debit_balance,
                            a.currency_code,
                            a.actual_debit_balance,
                            a.actual_debit_balance
                            ),
          account_ccy_code: fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency_code),
          exchange_rate_lcy: b.exchange_rate_lcy
        }
        )
        |> Repo.all()


  end


  # def format_funding_source(date) do

  #   accounts =  List.flatten(Enum.map(relief_list(date), &[&1.account_no]))
  #   data = funding_source(date)
  #   credit_card_list = credit_card_list(date)
  #   # usd_rate = usd_rate(date)
  #   normal_deposits_zmw = Enum.map(data, fn i ->
  #                           %{
  #                             account: i.account,
  #                             amount: i.amount,
  #                             account_ccy_code: i.account_ccy_code,
  #                             exchange_rate_lcy: i.exchange_rate_lcy,
  #                             credit_type: if(i.account in accounts, do: "Dedicated Lines Of Credit", else: "Normal Deposits")
  #                           }
  #                         end)
  #                         |> Enum.filter(fn data -> data.credit_type == "Normal Deposits" and data.account_ccy_code == "K" end)
  #                         |> Enum.map(fn i ->
  #                               %{
  #                                 account: i.account,
  #                                 amount: i.amount,
  #                                 account_ccy_code: i.account_ccy_code,
  #                               }
  #                            end)
  #                         |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :amount, Decimal.new("0"))) end)

  #     normal_deposits_usd =   Enum.map(data, fn i ->
  #                               %{
  #                                 account: i.account,
  #                                 amount: i.amount,
  #                                 account_ccy_code: i.account_ccy_code,
  #                                 exchange_rate_lcy: i.exchange_rate_lcy,
  #                                 credit_type: if(i.account in accounts, do: "Dedicated Lines Of Credit", else: "Normal Deposits")
  #                               }
  #                             end)
  #                             |> Enum.filter(fn data -> data.credit_type == "Normal Deposits" and data.account_ccy_code == "USD" end)
  #                             |> Enum.map(fn i ->
  #                                   %{
  #                                     account: i.account,
  #                                     amount: i.amount,
  #                                     account_ccy_code: i.account_ccy_code,
  #                                   }
  #                             end)
  #                             |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :amount, Decimal.new("0"))) end)

  #   dedicated_lines_of_credit =  Enum.map(data, fn i ->
  #                                   %{
  #                                     account: i.account,
  #                                     amount: if(i.account_ccy_code == "K", do: i.amount, else:
  #                                      i.amount
  #                                     ),
  #                                     account_ccy_code: i.account_ccy_code,
  #                                     credit_type: if(i.account in accounts, do: "Dedicated Lines Of Credit", else: "Normal Deposits")
  #                                   }
  #                                 end)
  #                                 |> Enum.filter(fn data -> data.credit_type == "Dedicated Lines Of Credit" end)
  #                                 |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :amount, Decimal.new("0"))) end)


  #   normal_deposits = Decimal.add(normal_deposits_zmw, Decimal.div(normal_deposits_usd, Decimal.new("1000"))) |> Decimal.add(credit_card_list)
  #   gross = Decimal.add(normal_deposits, dedicated_lines_of_credit)

  #   %{normal_deposits: normal_deposits, dedicated_lines_of_credit: dedicated_lines_of_credit, gross: gross}
  # end

  def format_funding_source(date) do
      adjustments =  MisReports.Utilities.get_adjustments(date)

      MisReports.Workers.LoansAdvances.FundingSource.generate_display(date, adjustments)
  end



  def classification_of_loans_and_leases_provisions_4b(from, to) do
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    |> where([a], a.past_due_npl_return_class not in ["Special Mention", "Past due"])
    |> order_by([a], asc: a.past_due_npl_return_class)
    |> select([a], %{
      "B15" => ^Decimal.new("0"),
      "B17" => ^Decimal.new("0"),
      "B19" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Special Mention'"),
      "C19" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Special Mention'"),

      "B21" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      "C21" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),

      "B22" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 2'"),
      "C22" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 2'"),

      "B23" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1' or past_due_npl_return_class = 'Sub-standard 2'"),
      "C23" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1' or past_due_npl_return_class = 'Sub-standard 2'"),

      "B25" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1'"),
      "C25" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1'"),

      "B26" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 2'"),
      "C26" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 2'"),

      "B27" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1' or past_due_npl_return_class = 'Doubtful 2'"),
      "C27" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1' or past_due_npl_return_class = 'Doubtful 2'"),

      "B29" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Loss'"),
      "C29" => fragment("select sum(specific_provisions) from tbl_loans_advances where past_due_npl_return_class = 'Loss'"),

      "D15" => ^Decimal.new(0),
      "D17" => ^Decimal.new(0),
      "D19" => ^Decimal.new(0),
      "D21" => ^Decimal.new(0),
      "D22" => ^Decimal.new(0),
      "D25" => ^Decimal.new(0),
      "D26" => ^Decimal.new(0),
      "D29" => ^Decimal.new(0),

      # "E15" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E17" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E19" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E21" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E22" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E23" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E25" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E26" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E27" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "E29" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F15" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F17" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F19" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F21" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F22" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F23" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F25" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F26" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F27" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      # "F29" => fragment("select sum(outstanding_amt) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      "G15" => ^Decimal.new("0"),
      "G17" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Past due'"),
      "G19" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Special Mention'"),

      "G21" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1'"),
      "G22" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 2'"),

      "G23" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Sub-standard 1' or past_due_npl_return_class = 'Sub-standard 2'"),

      "G25" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1'"),
      "G26" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 2'"),

      "G27" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Doubtful 1' or past_due_npl_return_class = 'Doubtful 2' "),

      "G29" => fragment("select sum(susp_interest) from tbl_loans_advances where past_due_npl_return_class = 'Loss'")
    })
    |> limit(1)
    |> Repo.all()
  end

  # def past_due_and_non_performing_loans_5b(from, to) do
  #   exchange_rate = Application.get_env(:mis_reports, :exchange_rate) |> Decimal.new()
  #   LoanAdvance
  #   |> join(:left, [a], b in "tbl_loan_business_units", on: a.bus_unit_short_desc == b.business_unit)
  #   |> join(:left, [a], c in "tbl_loan_scheme_codes", on: a.scheme_code == c.scheme_code)
  #   |> join(:left, [c], d in "tbl_loan_products", on: c.facility_type == d.product)
  #   |> join(:left, [a], e  in "tbl_loan_sectors", on: a.regulatory_sector == e.system_sector)
  #   |> where(
  #     [a],
  #     fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
  #     fragment("CAST(? AS DATE) <= ?", a.date, ^to)
  #   )
  #   |> select([a, b, c, d, e], %{
  #      "A" => a.account_no,
  #      "B" => "",
  #      "C" => "Zambian",
  #      "D" => b.facility_category,
  #      "E" => "", #Gender
  #      "F" => b.facility_category,
  #      "G" => e.prudential_sector,
  #      "H" => e.prudential_sub_sector,
  #      "I" => c.facility_type,
  #      "J" => "",#Loan Classification
  #      "K" => "",#Days Past Due
  #      "L" => fragment("CASE WHEN ? = 'K' THEN (? / 1000) ELSE ? END", a.account_ccy_code, a.dr_book_bal, a.dr_book_bal),
  #      "M" => a.account_ccy_code,
  #      "N" => fragment("CASE WHEN ? = 'K' THEN (? / 1000) ELSE (? * ?) / 1000 END", a.account_ccy_code, a.dr_book_bal, a.dr_book_bal, ^exchange_rate),
  #      "O" => d.collateral_type,
  #      "P" => fragment("CASE WHEN ? = 'K' THEN (? / 1000) * ? ELSE ? * ? END", a.account_ccy_code, a.dr_book_bal, d.security_value_ratio, a.dr_book_bal, d.security_value_ratio),
  #      "Q" => "",
  #      "R" => "",
  #      "S" => "",
  #      "T" => "",
  #      "U" => "",
  #      "V" => "",
  #      "W" => "",
  #      "X" => "",
  #      "Y" => "",
  #      "Z" => "",#Suspense Interest,
  #      "AA" => "",
  #      "AB" => "",
  #      "AC" => "",
  #      "AD" => ""

  #   })
  #   |> Repo.all()
  # end
  def obddr_accounts(date) do
    Obddr
    |> where([a], a.month == ^date and a.debit_interest_zmw > 0)
    |> Repo.all()
  end

  def loan_scheme_codes() do
    LoanSchemeCodes
    |> where([a], a.status == "A")
    |> Repo.all()
    |> Enum.map(&[&1.scheme_code])
    |> List.flatten()
  end

  def credit_card_list_loan_classification(date) do
    CreditCards
    |> where([a], a.date == ^date and a.stage in ["stg1", "stg2", "stg3"] and a.utilization > 0)
    |> select([a],
              %{
                "account_no" => a.account_no,
                "days_past_due" => a.days_past_due,
                "A" => a.account_name,
                "B" => "",
                "C" => "",
                "D" => "Personal Loan",
                "E" => "",
                "F" => "Personal Loan",
                "G" => "Other (Sectors or Products)                     ",
                "H" => "Credit Cards",
                "I" => "Credit Card Debt",
                "J" => "",
                "K" => "",
                "L" => fragment("? / 1000",a.utilization),
                "M" => "K",
                "N" => fragment("? / 1000",a.utilization),
                "O" => "Non Held",
                "P" => "",
                "Q" => "",
                "R" => "",
                "S" => "",
                "T" => "",
                "U" => "",
                "V" => "",
                "W" => "",
                "X" => "",
                "Y" => fragment("select top(1) provisions_held_zmw from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_no, ^date),
                "Z" => fragment("select top(1) interest_in_suspense_zmw from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_no, ^date),
                "AA" => ""
              })
    |> Repo.all()
    |> Enum.map(fn i ->
      Map.merge(i,
        %{
           "L" => if(i["L"] == nil, do: Decimal.new("0"), else: i["L"]),
           "N" => if(i["N"] == nil, do: Decimal.new("0"), else: i["N"]),
           "P" =>   Decimal.new("0"),
           "Y" => if(i["Y"] == nil, do: Decimal.new("0"), else: i["Y"]),
           "Z" => if(i["Z"] == nil, do: Decimal.new("0"), else: i["Z"]),
           "days_past_due" => if(i["days_past_due"] == nil, do: 0, else: i["days_past_due"]),
           "security_value_ratio" => Decimal.new("0")
          }
      )
  end)


  end

  def credit_card_list_loan_classification_4b(date) do
    CreditCards
    |> where([a], a.date == ^date and a.stage in ["stg1", "stg2"] and a.utilization > 0)
    |> select([a],
              %{
                "account_no" => a.account_no,
                "days_past_due" => a.days_past_due,
                "A" => a.account_name,
                "B" => "",
                "C" => "",
                "D" => "Personal Loan",
                "E" => "",
                "F" => "Personal Loan",
                "G" => "Other (Sectors or Products)                     ",
                "H" => "Credit Cards",
                "I" => "Credit Card Debt",
                "J" => "",
                "K" => "",
                "L" => fragment("? / 1000",a.utilization),
                "M" => "K",
                "N" => fragment("? / 1000",a.utilization),
                "O" => "Non Held",
                "P" => "",
                "Q" => "",
                "R" => "",
                "S" => "",
                "T" => "",
                "U" => "",
                "V" => "",
                "W" => "",
                "X" => "",
                "Y" => fragment("select top(1) provisions_held_zmw from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_no, ^date),
                "Z" => fragment("select top(1) interest_in_suspense_zmw from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_no, ^date),
                "AA" => ""

              })
    |> Repo.all()

  end
  # def past_due_and_non_performing_loans_5b(date) do

  #   usd_rate = usd_rate(date)
  #   year = String.slice(date, 0..3)
  #   month = String.slice(date, 5..6)

    # CustContribution
    # |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
    # |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
    # |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product_type)
    # |> join(:left, [a, b, c, d], e in "tbl_loan_sectors", on: a.sector_code_and_description == e.ccr_sector)
    # |> join(:left, [a, b, c, d, e], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
    # |> join(:left, [a, b, c, d, e, f,], g in "tbl_obddr", on: a.account_number == g.finnacle_account_code)
    # |> where([a, b, c, d, e, f], a.month_period == ^month and a.year_period == ^year)
    # |> where([a, b, c, d, e, f],
    #   a.off_balance_sheet_flag == "On"
    #   and a.product_code_source in ["BFTL", "BRCL", "BUSIL", "BWCL", "CMPLN", "CRD", "FINJD", "FINLS", "FINSL",
    #                                 "HLA", "IMPLN", "INVD", "IPF", "MTL", "OD002", "OD003", "OD004", "OD005",
    #                                 "OD006", "OD007", "PERSL", "PFTL", "PRLC", "PRTL", "SESG", "SESN", "SHLA",
    #                                 "SPL", "SSL", "TRDRL", "CTRAD"]
    #   and a.business_unit in ^business_units()
    #   and a.account_status in ["ACT", "DORMNT", "INACT"]
    #   and a.actual_debit_balance > 0
    #   and a.source_system == "Finacle"
    #   and d.schedule_type == "05B"
    #   and f.month == ^date
    #   # and g.month == ^date
    #   and f.status == "A")
    # |> group_by([a, b, c, d, e, f], [
    #   a.account_number
    # ])
    # |> select([a, b, c, d, e, f], %{
    #    "account_number" => a.account_number,
    #    "A" => max(a.customer_name),
    #    "B" => "",
    #    "C" => "Zambian",
    #    "D" => max(b.facility_category),
    #    "E" => "", #Gender
    #    "F" => max(b.facility_category),
    #    "G" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", max(e.prudential_sector), max(e.prudential_sector)) ,
    #    "H" => fragment("CASE WHEN ? is null THEN 'Other Sectors'  ELSE ? END", max(e.prudential_sub_sector), max(e.prudential_sub_sector)),
    #    "I" => max(c.facility_type),
    #    "J" => "",#Loan Classification
    #    "K" => "",#Days Past Due
    #    "L" => fragment("
    #             CASE
    #             WHEN ? = 'ZMW'  THEN ? / 1000
    #             WHEN ? = 'USD' THEN ?
    #             ELSE (? * ?) / ? END AS relationship",
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             sum(a.actual_debit_balance),
    #             sum(f.exchange_rate_lcy),
    #             ^usd_rate
    #           ),
    #    "M" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", max(a.currency_code)),
    #    "N" => fragment("
    #             CASE
    #             WHEN ? = 'ZMW'  THEN ? / 1000
    #             WHEN ? = 'USD' THEN ?
    #             ELSE (? * ?) / ? END AS relationship",
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             sum(a.actual_debit_balance),
    #             sum(f.exchange_rate_lcy),
    #             ^usd_rate
    #           ),
    #    "O" => fragment("CASE WHEN ? is null  THEN 'None Held' ELSE ? END", max(c.security_type), max(c.security_type)),
    #    "P" =>
    #    fragment("
    #             CASE
    #             WHEN ? = 'ZMW'  THEN ? / 1000
    #             WHEN ? = 'USD' THEN ?
    #             ELSE (? * ?) / ? END AS relationship",
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             max(a.currency_code),
    #             sum(a.actual_debit_balance),
    #             sum(a.actual_debit_balance),
    #             sum(f.exchange_rate_lcy),
    #             ^usd_rate
    #           ),
    #    "Q" => "",
    #    "R" => "",
    #    "S" => "",
    #    "T" => "",
    #    "U" => "",
    #    "V" => "",
    #    "W" => "",
    #    "X" => "",
    #    "Y" => fragment("select top(1)sum(provisions_held_zmw / 1000) from tbl_obddr where finnacle_account_code = ? and month = ? ",a.account_number, ^date),
    #    "Z" => fragment("select top(1)sum(interest_in_suspense_zmw / 1000) from tbl_obddr where finnacle_account_code = ? and month = ? ",a.account_number, ^date),#fragment("? / 1000", sum(g.interest_in_suspense_zmw)),#Suspense Interest,
    #    "AA" => "",
    #    "AB" => "",
    #    "AC" => "",
    #    "AD" => "",
    #   "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ",a.account_number, ^date),
    #    "security_value_ratio" => sum(d.security_value_ratio),
    # })
    # |> Repo.all()
  #   # |> Enum.count()
  #   # |> Enum.filter(fn data -> data["account_number"] == nil  end)

  # end
  def past_due_and_non_performing_loans_4b(date) do

    # usd_rate = usd_rate(date)
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)


    obddr =   Obddr
              |> join(:left, [a], b in "tbl_loan_scheme_codes", on: a.scheme_code == b.scheme_code)
              |> where([a, b],
                a.month == ^date
              )
              |> select([a, b], %{
                "account_number" => a.finnacle_account_code,
                "A" => a.customer_name,
                "B" => "",
                "C" => "Zambian",
                "D" => a.business_segment2,
                "E" => "", #Gender
                "F" => a.business_segment2,
                "G" => a.industry_sector,
                "I" => fragment("CASE WHEN ? is null THEN 'Other'  ELSE ? END", b.facility_type, b.facility_type),
                "J" => "",#Loan Classification
                "K" => "",#Days Past Due
                "L" => fragment("? / 1000", a.debit_interest_zmw),
                "M" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency),
                "N" => fragment("? / 1000", a.debit_interest_zmw),
                "O" => fragment("select top(1) type_of_security from tbl_loan_products where schedule_type = '05B' and product_type = ?", b.facility_type),
                "P" => fragment("? / 1000", a.debit_interest_zmw),
                "Q" => "",
                "R" => "",
                "S" => "",
                "T" => "",
                "U" => "",
                "V" => "",
                "W" => "",
                "X" => "",
                "Y" => fragment("? / 1000", a.provisions_held_zmw),
                "Z" => fragment("? / 1000", a.interest_in_suspense_zmw),#fragment("? / 1000", sum(g.interest_in_suspense_zmw)),#Suspense Interest,
                "AA" => "",
                "AB" => "",
                "AC" => "",
                "AD" => "",
                "days_past_due" => fragment("
                                              SELECT
                                              CASE
                                                  WHEN ? = 'CRD' THEN (SELECT TOP(1) days_past_due FROM tbl_credit_cards WHERE account_no = ? AND date = ?)
                                                  ELSE (SELECT TOP(1) days_past_due FROM tbl_days_past_due WHERE account_no = ? AND date = ?)
                                              END
                                        ",
                                        a.scheme_code,
                                        a.finnacle_account_code,
                                        ^date,
                                        a.finnacle_account_code,
                                        ^date
                                        ),
                "scheme_code" => a.scheme_code,
                "security_value_ratio" => fragment("select top(1) security_value_ratio from tbl_loan_products where schedule_type = '05B' and product_type = ?", b.facility_type)
              })
              |> Repo.all()
              |> Enum.map(fn i ->
                    Map.merge(i,
                      %{
                         "D" => facility_category(i["D"]),
                         "F" => facility_category(i["F"]),
                         "G" => ObddrSectors.obddr_sectors_list(i["G"]).prudential_sector,
                         "H" => ObddrSectors.obddr_sectors_list(i["G"]).prudential_subsector,
                         "L" => if(i["L"] == nil, do: Decimal.new("0"), else: i["L"]),
                         "N" => if(i["N"] == nil, do: Decimal.new("0"), else: i["N"]),
                         "O" => i["O"],
                         "P" => if(i["P"] == nil, do: Decimal.new("0"), else: Decimal.mult(if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"]), i["P"])),
                         "Y" => if(i["Y"] == nil, do: Decimal.new("0"), else: i["Y"]),
                         "Z" => if(i["Z"] == nil, do: Decimal.new("0"), else: i["Z"]),
                        #  "days_past_due" => if(days_past_due_check(i["scheme_code"], i["account_number"], date) == nil, do: 0, else: days_past_due_check(i["scheme_code"], i["account_number"], date)),
                         "security_value_ratio" => if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"])
                        }
                    )
                end)


    ccr =      CustContribution
                |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
                |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
                |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product_type)
                |> join(:left, [a, b, c, d], e in "tbl_customer_details", on: a.customer_number_local_cif == e.account_no)
                |> join(:left, [a, b, c, d, e], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
                # |> join(:left, [a, b, c, d, e, f,], g in "tbl_days_past_due", on: a.account_number == g.account_no)
                |> where([a, b, c, d, e, f], a.month_period == ^month and a.year_period == ^year)
                |> where([a, b, c, d, e, f],
                          a.off_balance_sheet_flag == "On"
                          and a.product_code_source in ^loan_scheme_codes()
                  and a.business_unit in ^business_units()
                  and a.account_status in ["ACT", "DORMNT", "INACT"]
                  and a.actual_debit_balance > 0
                  and a.source_system == "Finacle"
                  and d.schedule_type == "05B"
                  and f.month == ^date
                  # and g.days_past_due >= 30
                  # and g.month == ^date
                  and f.status == "A")
                # |> group_by([a, b, c, d, e, f], [
                #   a.account_number
                # ])
                |> select([a, b, c, d, e, f], %{
                  "account_number" => a.account_number,
                  "A" => a.customer_name,
                  "B" => "",
                  "C" => "Zambian",
                  "D" => b.facility_category,
                  "E" => "", #Gender
                  "F" => b.facility_category,
                  "G" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", e.economic_sector, e.economic_sector) ,
                  "H" => fragment("CASE WHEN ? is null THEN 'Other Sectors'  ELSE ? END", e.economic_sub_sector, e.economic_sub_sector),
                  "I" => fragment("CASE WHEN ? is null THEN 'Other'  ELSE ? END", c.facility_type, c.facility_type),
                  "J" => "",#Loan Classification
                  "K" => "",#Days Past Due
                  "L" => fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            a.currency_code,
                            a.actual_debit_balance,
                            a.currency_code,
                            a.actual_debit_balance,
                            a.actual_debit_balance
                          ),
                  "M" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency_code),
                  "N" => fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            a.currency_code,
                            a.actual_debit_balance,
                            a.currency_code,
                            a.actual_debit_balance,
                            a.actual_debit_balance
                          ),
                  "O" =>  d.type_of_security,
                  "P" =>
                  fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            a.currency_code,
                            a.actual_debit_balance,
                            a.currency_code,
                            a.actual_debit_balance,
                            a.actual_debit_balance
                          ),
                  "Q" => "",
                  "R" => "",
                  "S" => "",
                  "T" => "",
                  "U" => "",
                  "V" => "",
                  "W" => "",
                  "X" => "",
                  "Y" => ^Decimal.new("0"),
                  "Z" => ^Decimal.new("0"),#fragment("? / 1000", sum(g.interest_in_suspense_zmw)),#Suspense Interest,
                  "AA" => "",
                  "AB" => "",
                  "AC" => "",
                  "AD" => "",
                  "currency_code" => a.currency_code,
                  "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ",a.account_number, ^date),
                  "security_value_ratio" => d.security_value_ratio,
                })
                |> Repo.all()
                |> Enum.map(fn i ->
                  Map.merge(i,
                    %{
                       "D" => facility_category(i["D"]),
                       "F" => facility_category(i["F"]),
                       "L" => if(i["L"] == nil, do: Decimal.new("0"), else: i["L"]),
                       "N" => if(i["N"] == nil, do: Decimal.new("0"), else: i["N"]),
                       "O" => if(i["O"] == nil or i["O"] == "None Held", do: "Non Held", else: i["O"]),
                       "P" =>  if(i["P"] == nil, do: Decimal.new("0"), else: Decimal.mult(if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"]), i["P"])),
                       "Y" => if(i["Y"] == nil, do: Decimal.new("0"), else: i["Y"]),
                       "Z" => if(i["Z"] == nil, do: Decimal.new("0"), else: i["Z"]),
                       "days_past_due" => if(i["days_past_due"] == nil, do: 0, else: i["days_past_due"]),
                       "security_value_ratio" => if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"])
                      }
                  )
              end)


    # ccr = []
    %{data_obddr: obddr, data_ccr: ccr}
  end










  def past_due_and_non_performing_loans_5b(date) do

    # usd_rate = usd_rate(date)
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)


    obddr =   Obddr
              |> join(:left, [a], b in "tbl_loan_scheme_codes", on: a.scheme_code == b.scheme_code)
              |> where([a, b],
                a.month == ^date
              )
              |> select([a, b], %{
                "account_number" => a.finnacle_account_code,
                "A" => a.customer_name,
                "B" => "",
                "C" => "Zambian",
                "D" => a.business_segment2,
                "E" => "", #Gender
                "F" => a.business_segment2,
                "G" => a.industry_sector,
                "I" => b.facility_type,
                "J" => "",#Loan Classification
                "K" => "",#Days Past Due
                "L" => fragment("? / 1000", a.debit_interest_zmw),
                "M" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency),
                "N" => fragment("? / 1000", a.debit_interest_zmw),
                "O" => fragment("select top(1) type_of_security from tbl_loan_products where schedule_type = '05B' and product_type = ?", b.facility_type),
                "P" => fragment("? / 1000", a.debit_interest_zmw),
                "Q" => "",
                "R" => "",
                "S" => "",
                "T" => "",
                "U" => "",
                "V" => "",
                "W" => "",
                "X" => "",
                "Y" => fragment("? / 1000", a.provisions_held_zmw),
                "Z" => fragment("? / 1000", a.interest_in_suspense_zmw),#fragment("? / 1000", sum(g.interest_in_suspense_zmw)),#Suspense Interest,
                "AA" => "",
                "AB" => "",
                "AC" => "",
                "AD" => "",
                "days_past_due" => fragment("
                                              SELECT
                                              CASE
                                                  WHEN ? = 'CRD' THEN (SELECT TOP(1) days_past_due FROM tbl_credit_cards WHERE account_no = ? AND date = ?)
                                                  ELSE (SELECT TOP(1) days_past_due FROM tbl_days_past_due WHERE account_no = ? AND date = ?)
                                              END
                                        ",
                                        a.scheme_code,
                                        a.finnacle_account_code,
                                        ^date,
                                        a.finnacle_account_code,
                                        ^date
                                        ),
                "scheme_code" => a.scheme_code,
                "security_value_ratio" => fragment("select top(1) security_value_ratio from tbl_loan_products where schedule_type = '05B' and product_type = ?", b.facility_type)
              })
              |> Repo.all()
              |> Enum.map(fn i ->
                    Map.merge(i,
                      %{
                         "D" => facility_category(i["D"]),
                         "F" => facility_category(i["F"]),
                         "G" => ObddrSectors.obddr_sectors_list(i["G"]).prudential_sector,
                         "H" => ObddrSectors.obddr_sectors_list(i["G"]).prudential_subsector,
                         "L" => if(i["L"] == nil, do: Decimal.new("0"), else: i["L"]),
                         "N" => if(i["N"] == nil, do: Decimal.new("0"), else: i["N"]),
                         "O" => i["O"],
                         "P" => if(i["P"] == nil, do: Decimal.new("0"), else: Decimal.mult(if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"]), i["P"])),
                         "Y" => if(i["Y"] == nil, do: Decimal.new("0"), else: i["Y"]),
                         "Z" => if(i["Z"] == nil, do: Decimal.new("0"), else: i["Z"]),
                        #  "days_past_due" => if(days_past_due_check(i["scheme_code"], i["account_number"], date) == nil, do: 0, else: days_past_due_check(i["scheme_code"], i["account_number"], date)),
                         "security_value_ratio" => if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"])
                        }
                    )
                end)


    ccr =      CustContribution
                |> join(:left, [a], b in "tbl_loan_business_units", on: a.business_unit == b.ccr_business_unit)
                |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
                |> join(:left, [a, b, c], d in "tbl_loan_products", on: c.facility_type == d.product_type)
                |> join(:left, [a, b, c, d], e in "tbl_customer_details", on: a.customer_number_local_cif == e.account_no)
                |> join(:left, [a, b, c, d, e], f in "tbl_exchange_rates", on: a.currency_code == f.currency_code)
                # |> join(:left, [a, b, c, d, e, f,], g in "tbl_days_past_due", on: a.account_number == g.account_no)
                |> where([a, b, c, d, e, f], a.month_period == ^month and a.year_period == ^year)
                |> where([a, b, c, d, e, f],
                          a.off_balance_sheet_flag == "On"
                          and a.product_code_source in ^loan_scheme_codes()
                  and a.business_unit in ^business_units()
                  and a.account_status in ["ACT", "DORMNT", "INACT"]
                  and a.actual_debit_balance > 0
                  and a.source_system == "Finacle"
                  and d.schedule_type == "05B"
                  and f.month == ^date
                  # and g.days_past_due >= 30
                  # and g.month == ^date
                  and f.status == "A")
                |> group_by([a, b, c, d, e, f], [
                  a.account_number
                ])
                |> select([a, b, c, d, e, f], %{
                  "account_number" => a.account_number,
                  "A" => max(a.customer_name),
                  "B" => "",
                  "C" => "Zambian",
                  "D" => max(b.facility_category),
                  "E" => "", #Gender
                  "F" => max(b.facility_category),
                  "G" => fragment("CASE WHEN ? is null THEN 'Other service activities'  ELSE ? END", max(e.economic_sector), max(e.economic_sector)) ,
                  "H" => fragment("CASE WHEN ? is null THEN 'Other Sectors'  ELSE ? END", max(e.economic_sub_sector), max(e.economic_sub_sector)),
                  "I" => max(c.facility_type),
                  "J" => "",#Loan Classification
                  "K" => "",#Days Past Due
                  "L" => fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            sum(a.actual_debit_balance)
                          ),
                  "M" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", max(a.currency_code)),
                  "N" => fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            sum(a.actual_debit_balance)
                          ),
                  "O" =>  max(d.type_of_security),
                  "P" =>
                  fragment("
                            CASE
                            WHEN ? = 'ZMW'  THEN ? / 1000
                            WHEN ? = 'USD' THEN ?
                            ELSE ? END AS relationship",
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            max(a.currency_code),
                            sum(a.actual_debit_balance),
                            sum(a.actual_debit_balance)
                          ),
                  "Q" => "",
                  "R" => "",
                  "S" => "",
                  "T" => "",
                  "U" => "",
                  "V" => "",
                  "W" => "",
                  "X" => "",
                  "Y" => ^Decimal.new("0"),
                  "Z" => ^Decimal.new("0"),#fragment("? / 1000", sum(g.interest_in_suspense_zmw)),#Suspense Interest,
                  "AA" => "",
                  "AB" => "",
                  "AC" => "",
                  "AD" => "",
                  "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ",a.account_number, ^date),
                  "security_value_ratio" => sum(d.security_value_ratio),
                })
                |> Repo.all()
                |> Enum.map(fn i ->
                  Map.merge(i,
                    %{
                       "D" => facility_category(i["D"]),
                       "F" => facility_category(i["F"]),
                       "L" => if(i["L"] == nil, do: Decimal.new("0"), else: i["L"]),
                       "N" => if(i["N"] == nil, do: Decimal.new("0"), else: i["N"]),
                       "O" => if(i["O"] == nil or i["O"] == "None Held", do: "Non Held", else: i["O"]),
                       "P" =>  if(i["P"] == nil, do: Decimal.new("0"), else: Decimal.mult(if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"]), i["P"])),
                       "Y" => if(i["Y"] == nil, do: Decimal.new("0"), else: i["Y"]),
                       "Z" => if(i["Z"] == nil, do: Decimal.new("0"), else: i["Z"]),
                       "days_past_due" => if(i["days_past_due"] == nil, do: 0, else: i["days_past_due"]),
                       "security_value_ratio" => if(i["security_value_ratio"] == nil, do: Decimal.new("0"), else: i["security_value_ratio"])
                      }
                  )
              end)
    # ccr = []

    %{data_obddr: obddr, data_ccr: ccr}
  end


  def days_past_due_check(scheme_code, account_no, date) do

    case scheme_code do
      "CRD" ->
        CreditCards
        |> where([t], t.account_no == ^account_no and t.date == ^date )
        |> select([t], t.days_past_due)
        |> Repo.one()
        _->
          DaysPastDue
          |> where([t], t.account_no == ^account_no and t.date == ^date )
          |> select([t], t.days_past_due)
          |> Repo.one()

    end
  end



  def facility_category(business_unit) do
    case business_unit do
      "PB" -> "Personal Loan"
      "BB" -> "SME"
      "CIB" -> "Large Corporate"
      "PVT" -> "Personal Loan"
      _-> "Personal Loan"
    end
  end

  def insider_lending(date) do
    LoanInsiderLending
    |> where([a], a.month == ^date)
    |> Repo.all()
  end

  # def large_loans_exposures_6a(date) do

  #   year = String.slice(date, 0..3)
  #   month = String.slice(date, 5..6)
  #   # usd_rate = usd_rate(date)

  #   CustContribution
  #   |> join(:left, [a], b in "tbl_large_loans", on: a.customer_number_local_cif == b.cif)
  #   |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
  #   |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: a.currency_code == d.currency_code)
  #   |> where([a, b, c, d],
  #     a.month_period == ^month
  #     and a.year_period == ^year
  #     and a.product_code_source in ^loan_scheme_codes()
  #     and a.business_unit in ^business_units()
  #     and a.account_status in ["ACT", "DORMNT", "INACT"]
  #     # and b.month == ^date
  #     # and fragment("? > 0 AND ? > 0", a.actual_debit_balance, a.total_credit_limits)
  #     and not ((a.actual_debit_balance == 0 and a.total_credit_limits == 0) or
  #                (a.actual_debit_balance < 0 and a.total_credit_limits < 0))
  #     and a.off_balance_sheet_flag in ["On", "Off"]
  #     and a.source_system == "Finacle"
  #     and d.month == ^date
  #     and d.status == "A")
  #   |> order_by([a, b, c, d], asc: b.group_no)
  #   |> select([a, b, c, d], %{
  #       "account_number" => a.account_number,
  #       "group_no" => b.group_no,
  #       "scheme_type" => c.scheme_type,
  #       "A" => a.customer_name,
  #       "B" => c.facility_type,
  #       "C" => fragment("
  #                       CASE
  #                       WHEN ? = 'ZMW'  THEN ? / 1000
  #                       WHEN ? = 'USD' THEN ?
  #                       ELSE ?  END AS relationship",
  #                       a.currency_code,
  #                       a.total_credit_limits,
  #                       a.currency_code,
  #                       a.total_credit_limits,
  #                       a.total_credit_limits
  #                     ),
  #       "E" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency_code),
  #       "F" => fragment("
  #                   CASE
  #                   WHEN ? = 'ZMW'  THEN ? / 1000
  #                   WHEN ? = 'USD' THEN ?
  #                   ELSE ?  END AS relationship",
  #                   a.currency_code,
  #                   a.actual_debit_balance,
  #                   a.currency_code,
  #                   a.actual_debit_balance,
  #                   a.actual_debit_balance
  #                 ),
  #       "I" => a.effective_debit_rate,
  #       "J" => "",
  #       "K" => a.account_open_date,
  #       "L" => a.account_maturity_date,
  #       "M" => fragment("select top(1)type_of_security from tbl_loan_products where product_type = ? and schedule_type = '05B' ", c.facility_type),
  #       "security_value_ratio" => fragment("select top(1)security_value_ratio from tbl_loan_products where product_type = ? and schedule_type = '05B'", c.facility_type),
  #       "O" => "",
  #       "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ", a.account_number, ^date),
  #   })
  #   |> Repo.all()
  #   # |> Enum.reject(fn record ->
  #   #   not (Decimal.eq?(record["C"], Decimal.new("0")) and Decimal.eq?(record["F"], Decimal.new("0"))) and
  #   #   not (Decimal.lt?(record["C"], Decimal.new("0")) and Decimal.lt?(record["F"], Decimal.new("0")))
  #   # end)
  #   |> Enum.map(fn i ->
  #         Map.merge(i,
  #             %{
  #               "C" => if(i["scheme_type"] == "LAA", do: i["F"], else: i["C"]),
  #             }
  #           )
  #     end)
  #     # |> IO.inspect(limit: :infinity)

  # end

  def large_loans_exposures_6a(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    # Preload commonly used values
    loan_schemes = loan_scheme_codes()
    bus_units = business_units()

    # Create subqueries for better performance
    loan_products_subquery = from(lp in "tbl_loan_products",
      where: lp.schedule_type == "05B",
      select: %{
        product_type: lp.product_type,
        type_of_security: lp.type_of_security,
        security_value_ratio: lp.security_value_ratio
      }
    )

    dpd_subquery = from(dpd in "tbl_days_past_due",
      where: dpd.date == ^date,
      select: %{
        account_no: dpd.account_no,
        days_past_due: dpd.days_past_due
      }
    )

    # Main query with optimizations
    base_query =
      CustContribution
      |> join(:left, [a], b in "tbl_large_loans", on: a.customer_number_local_cif == b.cif)
      |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
      |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: a.currency_code == d.currency_code)
      |> join(:left, [a, b, c, d], lp in subquery(loan_products_subquery),
             on: c.facility_type == lp.product_type)
      |> join(:left, [a, b, c, d, lp], dpd in subquery(dpd_subquery),
             on: a.account_number == dpd.account_no)
      |> where([a, b, c, d],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ^bus_units and
        a.account_status in ["ACT", "DORMNT", "INACT"]
        and
        not (a.actual_debit_balance <= 0 and a.total_credit_limits <= 0)
        and
        a.off_balance_sheet_flag in ["On", "Off"] and
        a.source_system == "Finacle" and
        d.month == ^date and
        d.status == "A"
      )
      |> order_by([a, b], asc: b.group_no)
      |> select([a, b, c, d, lp, dpd], %{
        "account_number" => a.account_number,
        "group_no" => b.group_no,
        "scheme_type" => c.scheme_type,
        "A" => a.customer_name,
        "B" => c.facility_type,
        "C" => fragment("
                        CASE
                        WHEN ? = 'ZMW' THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ? END",
                        a.currency_code,
                        a.total_credit_limits,
                        a.currency_code,
                        a.total_credit_limits,
                        a.total_credit_limits
                      ),
        "D" => fragment("
                        CASE
                        WHEN ? = 'ZMW'  THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ?  END AS relationship",
                        a.currency_code,
                        a.total_credit_limits,
                        a.currency_code,
                        a.total_credit_limits,
                        a.total_credit_limits
                      ),
        "E" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE 'USD' END", a.currency_code),
        "F" => fragment("
                        CASE
                        WHEN ? = 'ZMW' THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ? END",
                        a.currency_code,
                        a.actual_debit_balance,
                        a.currency_code,
                        a.actual_debit_balance,
                        a.actual_debit_balance
                      ),
        "G" => fragment("
                        CASE
                        WHEN ? = 'ZMW'  THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ? END AS relationship",
                        a.currency_code,
                        a.actual_debit_balance,
                        a.currency_code,
                        a.actual_debit_balance,
                        a.actual_debit_balance
                      ),
        "H" => fragment("
                        CASE
                        WHEN ? = 'ZMW'  THEN ? / 1000
                        WHEN ? = 'USD' THEN ?
                        ELSE ?  END AS relationship",
                        a.currency_code,
                        a.actual_debit_balance,
                        a.currency_code,
                        a.actual_debit_balance,
                        a.actual_debit_balance
                      ),
        "I" => a.effective_debit_rate,
        "J" => "",
        "K" => a.account_open_date,
        "L" => a.account_maturity_date,
        "M" => lp.type_of_security,
        "security_value_ratio" => lp.security_value_ratio,
        "O" => "",
        "days_past_due" => dpd.days_past_due
      })

    # Execute query and post-process results
    base_query
    |> Repo.all()
    |> Enum.reject(fn record ->
        Decimal.eq?(record["C"], Decimal.new("0")) and Decimal.eq?(record["F"], Decimal.new("0"))
    end)
    |> Enum.map(fn i ->
      Map.merge(i, %{
        "C" => if(i["scheme_type"] == "LAA", do: i["F"], else: i["C"])
      })
    end)

  end


def filter_check do

      data =
      [
        %{
          "account_number" => "123456",
          "customer_name" => "John Doe",
          "C" => Decimal.new("150.75"),
          "F" => Decimal.new("200.50")
        },
        %{
          "account_number" => "789012",
          "customer_name" => "Jane Smith",
          "C" => Decimal.new("-50.00"),
          "F" => Decimal.new("100.00")
        },
        %{
          "account_number" => "345678",
          "customer_name" => "Alice Brown",
          "C" => Decimal.new("0.00"),
          "F" => Decimal.new("75.25")
        },
        %{
          "account_number" => "901234",
          "customer_name" => "Bob White",
          "C" => Decimal.new("300.00"),
          "F" => Decimal.new("400.00")
        },
        %{
          "account_number" => "567890",
          "customer_name" => "Charlie Green",
          "C" => Decimal.new("0.00"),
          "F" => Decimal.new("0.00")
        }
      ]


    filtered_data = Enum.reject(data, fn record ->
      (Decimal.eq?(record["C"], 0) and Decimal.eq?(record["F"], 0))  |> IO.inspect(label: "ONE")
    end)
    |> Enum.reject(fn record ->
      (Decimal.lt?(record["C"], 0) and Decimal.lt?(record["F"], 0)) |> IO.inspect(label: "TWO")
    end)
    filtered_data
end




  def large_loans_exposures_6b(date) do
    regulatory_capital = regulatory_capital(date)
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate = usd_rate(date)
    LargeLoans
    |> join(:left, [a], b in "tbl_cust_contribution", on: a.cif == b.customer_number_local_cif)
    |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: b.product_code_source == c.scheme_code)
    |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: b.currency_code == d.currency_code)
    |> where([a, b, c, d], b.month_period == ^month and b.year_period == ^year)
    |> where([a, b, c, d],
      b.product_code_source in ^loan_scheme_codes()
      and b.business_unit in ^business_units()
      and b.account_status in ["ACT", "DORMNT", "INACT"]
      and b.actual_debit_balance > 0
      and b.source_system == "Finacle"
      and d.month == ^date
      and d.status == "A"
      and b.total_credit_limits >= ^Decimal.mult(regulatory_capital, Decimal.new("0.1")))
    |> select([a, b, c, d], %{
       "group_no" => max(a.group_no),
       "currency_code" => fragment("CASE WHEN ? = 'ZMW' THEN 'K'  ELSE 'USD' END", max(b.currency_code)),
       "A" => max(a.group_no),
       "B" => fragment("
                    CASE
                    WHEN ? = 'ZMW'  THEN ? / 1000
                    WHEN ? = 'USD' THEN ?
                    ELSE ? END AS relationship",
                    max(b.currency_code),
                    sum(b.total_credit_limits),
                    max(b.currency_code),
                    sum(b.total_credit_limits),
                    sum(b.total_credit_limits)
                  ),
       "C" => fragment("
                CASE
                WHEN ? = 'ZMW'  THEN ? / 1000
                WHEN ? = 'USD' THEN ?
                ELSE ? END AS relationship",
                max(b.currency_code),
                sum(b.total_credit_limits),
                max(b.currency_code),
                sum(b.total_credit_limits),
                sum(b.total_credit_limits)
              ),
       "D" => fragment("
                  CASE
                  WHEN ? = 'ZMW'  THEN ? / 1000
                  WHEN ? = 'USD' THEN ?
                  ELSE ? END AS relationship",
                  max(b.currency_code),
                  sum(b.actual_debit_balance),
                  max(b.currency_code),
                  sum(b.actual_debit_balance),
                  sum(b.actual_debit_balance)
                ),
       "E" => fragment("
                CASE
                WHEN ? = 'ZMW'  THEN ? / 1000
                WHEN ? = 'USD' THEN ?
                ELSE ? END AS relationship",
                max(b.currency_code),
                sum(b.actual_debit_balance),
                max(b.currency_code),
                sum(b.actual_debit_balance),
                sum(b.actual_debit_balance)
              )
    })
    |> Repo.all()
  end

  def insider_lending_exposures_7a(date) do

    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)
    # usd_rate = usd_rate(date)
    insider_lending  =    LoanInsiderLending
                          |> join(:left, [a], b in "tbl_cust_contribution", on: a.cif == b.customer_number_local_cif )
                          |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: b.product_code_source == c.scheme_code)
                          |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: b.currency_code == d.currency_code)
                          |> where([a, b, c, d],
                            b.year_period == ^year
                            and b.month_period == ^month
                            and a.month == ^date
                            and b.off_balance_sheet_flag in ["On"]
                            and b.product_code_source in ^loan_scheme_codes()
                            and b.business_unit in ^business_units()
                            and b.account_status in ["ACT", "DORMNT", "INACT"]
                            and b.actual_debit_balance > 0
                            and b.source_system == "Finacle"
                            and d.month == ^date
                          and d.status == "A")
                          |> order_by([a, b, c, d], asc: b.customer_name)
                          |> select([a, b, c, d], %{
                            "currency" => fragment("CASE WHEN ? = 'ZMW' THEN 'K'  ELSE 'USD' END", b.currency_code),
                            "cif_no" => b.customer_number_local_cif,
                            "A" => b.customer_name,
                            "B" => a.relationship_to_institution,
                            "C" => c.facility_type,
                            "D" => fragment("
                                          CASE
                                          WHEN ? = 'ZMW'  THEN ? / 1000
                                          WHEN ? = 'USD' THEN ?
                                          ELSE ?  END AS relationship",
                                          b.currency_code,
                                          b.total_credit_limits,
                                          b.currency_code,
                                          b.total_credit_limits,
                                          b.total_credit_limits
                                        ),
                            "E" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ? / 1000
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              b.currency_code,
                                              b.total_credit_limits,
                                              b.currency_code,
                                              b.total_credit_limits,
                                              b.total_credit_limits
                                            ),
                            "F" => fragment("
                                            CASE
                                            WHEN ? = 'ZMW'  THEN ? / 1000
                                            WHEN ? = 'USD' THEN ?
                                            ELSE ? END AS relationship",
                                            b.currency_code,
                                            b.actual_debit_balance,
                                            b.currency_code,
                                            b.actual_debit_balance,
                                            b.actual_debit_balance
                                          ),
                            "G" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ? / 1000
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              b.currency_code,
                                              b.actual_debit_balance,
                                              b.currency_code,
                                              b.actual_debit_balance,
                                              b.actual_debit_balance
                                            ),
                            "H" => b.effective_debit_rate,
                            "I" => "",
                            "J" => b.account_open_date,
                            "K" => b.account_maturity_date,
                            "L" => fragment("select top(1)type_of_security from tbl_loan_products where product_type = ? and schedule_type = '07A' ",c.facility_type),
                            "N" => "",
                            "security_value_ratio" => fragment("select top(1)security_value_ratio from tbl_loan_products where product_type = ? and schedule_type = '07A' ",c.facility_type),
                            "days_past_due" => fragment("select top(1)days_past_due from tbl_days_past_due where account_no = ? and date = ? ",b.account_number, ^date),
                          })
                          |> Repo.all()
                          |> Enum.map(fn i ->
                              Map.merge(i,
                                  %{
                                    "D" => if(i["D"] == nil, do: Decimal.new("0"), else: i["D"]),
                                    "E" => if(i["E"] == nil, do: Decimal.new("0"), else: i["E"]),
                                    "H" => if(i["H"] == nil, do: Decimal.new("0"), else: i["H"]),
                                    "J" => if(i["J"] == nil || i["J"] == "", do: "", else: Timex.format!(i["J"], "%m-%b-%Y", :strftime)),
                                    "K" => if(i["J"] == nil || i["K"] == "", do: "", else: Timex.format!(i["K"], "%m-%b-%Y", :strftime)),
                                  }
                                )
                          end)


    credit_cards =  LoanInsiderLending
                    |> join(:left, [a], b in "tbl_credit_cards", on: a.cif == b.cif)
                    |> where([a, b], a.month == ^date and b.date == ^date and b.stage in ["stg1", "stg2", "stg3"] and b.utilization > 0)
                    |> select([a, b],
                        %{
                          "A" => b.account_name,
                          "B" => a.relationship_to_institution,
                          "C" => "Credit Card Debt",
                          "D" => fragment("? / 1000",b.limit),
                          "E" => fragment("? / 1000",b.limit),
                          "F" => fragment("? / 1000",b.utilization),
                          "G" => fragment("? / 1000",b.utilization),
                          "H" => ^Decimal.new("0"),
                          "J" => b.account_open_date,
                          "K" => "",
                          "L" => "Unsecured",
                          "M" => "",
                          "N" => "Pass",
                          "currency" => "K",
                          "cif" => b.cif,
                          "security_value_ratio" => fragment("select top(1)security_value_ratio from tbl_loan_products where product_type = 'Credit Card Debt' and schedule_type = '07A'")
                        }
                    )
                    |> Repo.all()
                    |> Enum.map(fn i ->
                        Map.merge(i,
                          %{
                            "D" => if(i["D"] == nil, do: Decimal.new("0"), else: i["D"]),
                            "E" => if(i["E"] == nil, do: Decimal.new("0"), else: i["E"]),
                            "J" => format_and_add_years(i["J"])[:account_open_date],
                            "K" => format_and_add_years(i["J"])[:account_closed_date]
                          }
                        )
                    end)
      credit_cards ++ insider_lending

  end
  def format_and_add_years(date_string) do
    if date_string == nil do
      %{account_open_date: "", account_closed_date: "", actual_date: ""}
    else
      case Date.from_iso8601(String.slice(date_string, 0..3) <> "-" <> String.slice(date_string, 4..5) <> "-" <> String.slice(date_string, 6..7)) do
        {:ok, date} ->
          formatted_date = Timex.format!(date, "%d-%b-%Y", :strftime)
          future_date = Timex.shift(date, years: 5)
          formatted_future_date = Timex.format!(future_date, "%d-%b-%Y", :strftime)
          {:ok, {formatted_date, formatted_future_date}}
            %{account_open_date: formatted_date, account_closed_date: formatted_future_date, actual_date: date}
        {:error, _reason} ->
          ""
      end
    end

  end

  def regulatory_capital(date) do
    RegulatoryCapital
    |> where([a], a.date == ^date)
    |> Repo.all()
    |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, :regulatory_capital, Decimal.new("0"))) end)
  end

  def loans_restored_to_accrual_status_8a(date) do
    year = String.slice(date, 0..3)
    month = String.slice(date, 5..6)

    from = Date.from_iso8601!(date) |>  Timex.shift(days: -180) |> to_string()
    to = date
    Obddr
    |> join(:left, [a], b in "tbl_cust_contribution", on: a.finnacle_account_code == b.account_number)
    |> join(:left, [a, b], c in "tbl_loan_sectors", on: b.sector_code_and_description == c.ccr_sector)
    |> where([a, b, c, d],
      b.month_period == ^month
      and b.year_period == ^year
      and a.month == ^date
      # and fragment("CAST(? AS DATE) >= ?", d.date, ^from) and fragment("CAST(? AS DATE) <= ?", d.date, ^to)

    )
    |> select([a, b, c, d], %{
       "A" => a.customer_name,
       "B" => fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)'  ELSE ? END ", c.prudential_sector, c.prudential_sector),
       "C" => fragment("? / 1000", a.debit_interest_zmw),
       "D" => fragment("CASE WHEN ? = 'ZMW' THEN 'K' ELSE ? END ", a.currency, a.currency),
       "E" => fragment("? / 1000", a.debit_interest_zmw),
       "F" => "",
       "G" => fragment("? / 1000", a.debit_interest_zmw),
       "H" => "",
       "account_no" => a.finnacle_account_code,
       "days_past_due" => fragment("select count(*) from tbl_days_past_due where date BETWEEN ? AND ? and account_no = ? and days_past_due > 0", ^from, ^to, a.finnacle_account_code)

    })
    |> Repo.all()
    |> Enum.map(fn i ->
        Map.merge(i,
              %{
                "C" => if(i["C"] == nil, do: Decimal.new("0"), else: i["C"]),
                "E" => if(i["E"] == nil, do: Decimal.new("0"), else: i["E"]),
                "G" => if(i["G"] == nil, do: Decimal.new("0"), else: i["G"])
                }
        )
    end)
  end



    def largest_borrowers_summary_detailed_list_22a(date) do

      year = String.slice(date, 0..3)
      month = String.slice(date, 5..6)
      # usd_rate = usd_rate(date)
      CustContribution
      |> join(:left, [a], b in "tbl_large_loans", on: a.customer_number_local_cif == b.cif)
      |> join(:left, [a, b], c in "tbl_loan_scheme_codes", on: a.product_code_source == c.scheme_code)
      |> join(:left, [a, b, c], d in "tbl_exchange_rates", on: a.currency_code == d.currency_code)
      |> where([a, b, c, d], a.month_period == ^month and a.year_period == ^year)
      |> where([a, b, c, d],
        a.off_balance_sheet_flag == "On"
        and a.product_code_source in ^loan_scheme_codes()
        and a.business_unit in ^business_units()
        and a.account_status in ["ACT", "DORMNT", "INACT"]
        # and a.actual_debit_balance > 0
        and
        not (a.actual_debit_balance <= 0 and a.total_credit_limits <= 0)
        and a.source_system == "Finacle"
        and d.month == ^date
        and d.status == "A")
      |> order_by([a, b, c, d], asc: b.group_no)
      |> select([a, b, c, d], %{
        "group_no" => b.group_no,
        "account_number" => a.account_number,
        "scheme_type" => c.scheme_type,
        "B" => a.customer_name,
        "C" => c.facility_type,
        "D" => fragment("
                          CASE
                          WHEN ? = 'ZMW'  THEN ? / 1000
                          WHEN ? = 'USD' THEN ?
                          ELSE ? END AS relationship",
                          a.currency_code,
                          a.total_credit_limits,
                          a.currency_code,
                          a.total_credit_limits,
                          a.total_credit_limits
                        ),
        "E" => fragment("CASE WHEN ? = 'ZMW' THEN 'K'  ELSE 'USD' END", a.currency_code),
        "F" => a.effective_debit_rate,
        "G" => a.account_open_date,
        "H" => a.account_maturity_date,
        "I" => fragment("
                          CASE
                          WHEN ? = 'ZMW'  THEN ? / 1000
                          WHEN ? = 'USD' THEN ?
                          ELSE ? END AS relationship",
                          a.currency_code,
                          a.actual_debit_balance,
                          a.currency_code,
                          a.actual_debit_balance,
                          a.actual_debit_balance
                        ),
        "J" => fragment("
                          CASE
                          WHEN ? = 'ZMW'  THEN ? / 1000
                          WHEN ? = 'USD' THEN ?
                          ELSE ?  END AS relationship",
                          a.currency_code,
                          a.actual_debit_balance,
                          a.currency_code,
                          a.actual_debit_balance,
                          a.actual_debit_balance
                        ),
        "K" => "",
        "L" => fragment("select top(1)type_of_security from tbl_loan_products where product_type = ? and schedule_type = '05B' ",c.facility_type),
        "M" => fragment("select top(1)security_value_ratio from tbl_loan_products where product_type = ? and schedule_type = '05B' ",c.facility_type),
        "N" => "",
        "O" => ""
      })
      |> Repo.all()
      |> Enum.reject(fn record ->
        Decimal.eq?(record["D"], Decimal.new("0")) and Decimal.eq?(record["I"], Decimal.new("0"))
      end)
      |> Enum.map(fn i ->
          Map.merge(i,
              %{
                "D" => if(i["scheme_type"] == "LAA", do: i["I"], else: i["D"]),
              }
            )
      end)


    end

    def calculation_of_risk_weighted_assets_14(date) do

      year = String.slice(date, 0..3)
      month = String.slice(date, 5..6)

      CustContribution
      |> where([a],
        a.business_unit in ^business_units()
        and a.product_code_source in ["ODGLN", "ODLC", "ODBDG"]
        and a.account_status in ["ACT", "DORMNT", "INACT"]
        and a.source_system == "Finacle"
        and a.actual_debit_balance > 0
        and a.month_period == ^month
        and a.year_period == ^year
        and a.off_balance_sheet_flag == "Off"
        )
      |> select([a], %{
          "business_unit" => a.business_unit,
          "scheme_code" => a.product_code_source,
          "currency_code" => a.currency_code,
          "actual_debit_balance" => fragment("
                      CASE
                      WHEN ? = 'ZMW'  THEN ? / 1000
                      WHEN ? = 'USD' THEN ? / 1000
                      ELSE ? / 1000  END AS relationship",
                      a.currency_code,
                      a.actual_debit_balance,
                      a.currency_code,
                      a.actual_debit_balance,
                      a.actual_debit_balance
                    )
      })
      |> Repo.all()

    end


  def largest_borrowers_summary_22b(from, to) do
    total_loans = total_loans()
    LoanAdvance
    |> where(
      [a],
      fragment("CAST(? AS DATE) >= ?", a.date, ^from) and
      fragment("CAST(? AS DATE) <= ?", a.date, ^to)
    )
    # |> group_by([a], [
    #   a.cust_no,
    # ])
    |> order_by([a], desc: sum(a.outstanding_amt))
    |> select([a], %{
       "B" => fragment("CAST(? AS varchar)", a.cust_no),
       "C" => sum(a.limit_amount),
       "D" => sum(a.outstanding_amt),
       "E" => fragment(" (? / ?) * 100", sum(a.outstanding_amt), ^total_loans)
    })
    |> limit(20)
    |> Repo.all()
  end

  def get_quarter do
    quarter = Timex.now() |> Timex.quarter()
    case quarter do
      1 -> "Q1"
      2 -> "Q2"
      3 -> "Q3"
      4 -> "Q4"
    end
  end


def get_quarterly do
  MisReports.Prudentials.cmmp_agriculture_large("2024-10-01", "2024-12-31")
end

  #==========================================================CMMP================================================================
  #==========================================================AGRICULTURE LARGE=====================================================
  def cmmp_quartely(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    bus_units = business_units()

    ccr =   CustContribution
                |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
                |> join(:left, [a, b], c in "tbl_days_past_due", on: a.account_number == c.account_no)
                |> join(:left, [a, b, c], d in "tbl_cmmp_branches", on: a.branch_name == d.acc_dom_branch_name)
                |> join(:left, [a, b, c, d], e in "tbl_cmmp_product", on: a.product_description_source == e.prod_desc_src)
                |> where([a, b, c, d, e],
                    a.month_period == ^month and
                    a.year_period == ^year and
                    a.product_code_source in ^loan_schemes and
                    a.business_unit in ^bus_units and
                    a.account_status in ["ACT", "DORMNT", "INACT"] and
                    a.actual_debit_balance > 0 and
                    a.off_balance_sheet_flag in ["On"] and
                    a.source_system == "Finacle" and
                    c.date == ^end_date
                  )
                  |> select([a, b, c, d, e],
                    %{
                      "business_unit" => a.business_unit,
                      "account_open_date" => a.account_open_date,
                      "account_maturity_date" => a.account_maturity_date,
                      "product_description_source" => a.product_description_source,
                      "debtors_book_analysis" => e.debtors_book_analysis,
                      "account_number" => a.account_number,
                      "account_name" => a.customer_name,
                      "branch_name" => a.branch_name,
                      "actual_debit_balance" => fragment("
                                                          CASE
                                                          WHEN ? = 'ZMW'  THEN ?
                                                          WHEN ? = 'USD' THEN ?
                                                          ELSE ? END AS relationship",
                                                          a.currency_code,
                                                          a.actual_debit_balance,
                                                          a.currency_code,
                                                          a.actual_debit_balance,
                                                          a.actual_debit_balance
                                                        ),
                      "geo_dist" => d.geo_dist,
                      "economic_sector" => b.economic_sector,
                      "district" => d.district,
                      "province" => d.province,
                      "type_2" => e.type_2,
                      "type_1" => e.type_1,
                      "days_past_due" => c.days_past_due,
                      "subsegment_descr_source" => a.subsegment_descr_source,
                      "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_number, ^end_date)
                    }
                  )
                  |> Repo.all()


   credit_cards =  CreditCards
                        |> where([a], a.date == ^end_date and a.stage in ["stg1", "stg2", "stg3"] and a.utilization > 0)
                        |> select([a],
                                  %{
                                      "business_unit" => "Personal & Private Banking",
                                      "account_open_date" => a.account_open_date,
                                      "debtors_book_analysis" => "Revolving credit",
                                      "account_number" => a.account_no,
                                      "account_name" => a.account_name,
                                      "branch_name" => "HEAD OFFICE",
                                      "actual_debit_balance" => a.utilization,
                                      "geo_dist" => "Urban",
                                      "economic_sector" => "Other (Sectors or Products)",
                                      "district" => "Lusaka - District",
                                      "province" => "Lusaka",
                                      "type_2" => "Credit Cards and overdrafts",
                                      "type_1" => "Credit Cards and overdrafts",
                                      "days_past_due" => a.days_past_due,
                                      "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", a.account_no, ^end_date)}
                                      )
                                      |> Repo.all()
                                      |> Enum.map(fn i ->
                                          Map.merge(i,
                                            %{
                                              "account_maturity_date" => format_and_add_years(i["account_open_date"])[:account_closed_date],
                                              "account_open_date" => format_and_add_years(i["account_open_date"])[:actual_date],
                                            }
                                          )
                                      end)

      ccr ++ credit_cards
  end
  #==========================================================BUSINESS SMALL=====================================================
  def cmmp_business_small(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    # bus_units = business_units()

    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> join(:left, [a, b], c in "tbl_obddr", on: a.account_number == c.finnacle_account_code)
    |> join(:left, [a, b, c], d in "tbl_days_past_due", on: a.account_number == d.account_no)
    |> join(:left, [a, b, c, d], e in "tbl_cmmp_branches", on: a.branch_name == e.acc_dom_branch_name)
    |> join(:left, [a, b, c, d, e], f in "tbl_cmmp_product", on: a.product_description_source == f.prod_desc_src)
    |> where([a, b, c, d, e, f],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ["Business & Commercial Banking"] and
        a.account_status in ["ACT", "DORMNT", "INACT"] and
        a.actual_debit_balance > 0 and
        a.off_balance_sheet_flag in ["On"] and
        a.subsegment_descr_source != "PUBLIC SECTOR AND GOVERNMENT" and
        b.economic_sector != "Agriculture, forestry and fishing" and
        a.source_system == "Finacle" and
        d.date == ^end_date
        #and
        # c.month == ^end_date and
        # fragment("CAST(? AS DATE) >= ?", a.account_open_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.account_open_date, ^to)
      )
      |> select([a, b, c, d, e, f],
        %{
          "product_description_source" => a.product_description_source,
          "debtors_book_analysis" => f.debtors_book_analysis,
          "account_number" => a.account_number,
          "account_name" => a.customer_name,
          "branch_name" => a.branch_name,
          "actual_debit_balance" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ?
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.actual_debit_balance
                                            ),
          "geo_dist" => e.geo_dist,
          "district" => e.district,
          "province" => e.province,
          "type_2" => f.type_2,
          "type_1" => f.type_1,
          "days_past_due" => d.days_past_due,
          "account_open_date" => a.account_open_date,
          "account_maturity_date" => a.account_maturity_date,
          "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", c.finnacle_account_code, ^end_date)
        }
      )
      |> Repo.all()
      # |> Enum.count()

  end

  #==========================================================BUSINESS LARGE=====================================================
  def cmmp_business_large(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    # bus_units = business_units()

    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> join(:left, [a, b], c in "tbl_obddr", on: a.account_number == c.finnacle_account_code)
    |> join(:left, [a, b, c], d in "tbl_days_past_due", on: a.account_number == d.account_no)
    |> join(:left, [a, b, c, d], e in "tbl_cmmp_branches", on: a.branch_name == e.acc_dom_branch_name)
    |> join(:left, [a, b, c, d, e], f in "tbl_cmmp_product", on: a.product_description_source == f.prod_desc_src)
    |> where([a, b, c, d, e, f],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ["Corporate & Investment Banking"] and
        a.account_status in ["ACT", "DORMNT", "INACT"] and
        a.actual_debit_balance > 0 and
        a.off_balance_sheet_flag in ["On"] and
        a.subsegment_descr_source != "PUBLIC SECTOR AND GOVERNMENT" and
        b.economic_sector != "Agriculture, forestry and fishing" and
        a.source_system == "Finacle" and
        d.date == ^end_date #and
        # c.month == ^end_date and
        #fragment("CAST(? AS DATE) >= ?", a.account_open_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.account_open_date, ^to)
      )
      |> select([a, b, c, d, e, f],
        %{
          "product_description_source" => a.product_description_source,
          "debtors_book_analysis" => f.debtors_book_analysis,
          "account_number" => a.account_number,
          "account_name" => a.customer_name,
          "branch_name" => a.branch_name,
          "actual_debit_balance" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ?
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.actual_debit_balance
                                            ),
          "geo_dist" => e.geo_dist,
          "district" => e.district,
          "province" => e.province,
          "type_2" => f.type_2,
          "type_1" => f.type_1,
          "days_past_due" => d.days_past_due,
          "account_open_date" => a.account_open_date,
          "account_maturity_date" => a.account_maturity_date,
          "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", c.finnacle_account_code, ^end_date)
        }
      )
      |> Repo.all()
      # |> Enum.count()

  end

  #==========================================================HOUSE HOLD=====================================================
  def cmmp_household_individuals(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    # bus_units = business_units()

    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> join(:left, [a, b], c in "tbl_obddr", on: a.account_number == c.finnacle_account_code)
    |> join(:left, [a, b, c], d in "tbl_days_past_due", on: a.account_number == d.account_no)
    |> join(:left, [a, b, c, d], e in "tbl_cmmp_branches", on: a.branch_name == e.acc_dom_branch_name)
    |> join(:left, [a, b, c, d, e], f in "tbl_cmmp_product", on: a.product_description_source == f.prod_desc_src)
    |> where([a, b, c, d, e, f],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ["Personal & Private Banking"] and
        a.account_status in ["ACT", "DORMNT", "INACT"] and
        a.actual_debit_balance > 0 and
        a.off_balance_sheet_flag in ["On"] and
        a.subsegment_descr_source != "PUBLIC SECTOR AND GOVERNMENT" and
        b.economic_sector != "Agriculture, forestry and fishing" and
        a.source_system == "Finacle" and
        d.date == ^end_date #and
        # c.month == ^end_date and
        #fragment("CAST(? AS DATE) >= ?", a.account_open_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.account_open_date, ^to)
      )
      |> select([a, b, c, d, e, f],
        %{
          "product_description_source" => a.product_description_source,
          "debtors_book_analysis" => f.debtors_book_analysis,
          "account_number" => a.account_number,
          "account_name" => a.customer_name,
          "branch_name" => a.branch_name,
          "actual_debit_balance" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ?
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.actual_debit_balance
                                            ),
          "geo_dist" => e.geo_dist,
          "district" => e.district,
          "province" => e.province,
          "type_2" => f.type_2,
          "type_1" => f.type_1,
          "days_past_due" => d.days_past_due,
          "account_open_date" => a.account_open_date,
          "account_maturity_date" => a.account_maturity_date,
          "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", c.finnacle_account_code, ^end_date)
        }
      )
      |> Repo.all()


  end

  #==========================================================GOVERNMENT=====================================================
  def cmmp_government(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    bus_units = business_units()

    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> join(:left, [a, b], c in "tbl_obddr", on: a.account_number == c.finnacle_account_code)
    |> join(:left, [a, b, c], d in "tbl_days_past_due", on: a.account_number == d.account_no)
    |> join(:left, [a, b, c, d], e in "tbl_cmmp_branches", on: a.branch_name == e.acc_dom_branch_name)
    |> join(:left, [a, b, c, d, e], f in "tbl_cmmp_product", on: a.product_description_source == f.prod_desc_src)
    |> where([a, b, c, d, e, f],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ^bus_units and
        a.account_status in ["ACT", "DORMNT", "INACT"] and
        a.actual_debit_balance > 0 and
        a.off_balance_sheet_flag in ["On"] and
        a.subsegment_descr_source == "PUBLIC SECTOR AND GOVERNMENT" and
        # b.economic_sector != "Agriculture, forestry and fishing" and
        a.source_system == "Finacle" and
        d.date == ^end_date #and
        # c.month == ^end_date and
        # fragment("CAST(? AS DATE) >= ?", a.account_open_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.account_open_date, ^to)
      )
      |> select([a, b, c, d, e, f],
        %{

          "product_description_source" => a.product_description_source,
          "subsegment_descr_source" => a.subsegment_descr_source,
          "debtors_book_analysis" => f.debtors_book_analysis,
          "account_number" => a.account_number,
          "account_name" => a.customer_name,
          "branch_name" => a.branch_name,
          "actual_debit_balance" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ?
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.actual_debit_balance
                                            ),
          "geo_dist" => e.geo_dist,
          "district" => e.district,
          "province" => e.province,
          "type_2" => f.type_2,
          "type_1" => f.type_1,
          "account_open_date" => a.account_open_date,
          "account_maturity_date" => a.account_maturity_date,
          "days_past_due" => d.days_past_due,
          "business_unit" => a.business_unit,
          "economic_sector" => b.economic_sector,
          "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", c.finnacle_account_code, ^end_date)
        }
      )
      |> Repo.all()
      # |> Enum.count()

  end


  def cmmp_geographical(_start_date, end_date) do

    year = String.slice(end_date, 0..3)
    month = String.slice(end_date, 5..6)

    # from = start_date
    # to = end_date

    loan_schemes = loan_scheme_codes()
    bus_units = business_units()

    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> join(:left, [a, b], c in "tbl_obddr", on: a.account_number == c.finnacle_account_code)
    |> join(:left, [a, b, c], d in "tbl_days_past_due", on: a.account_number == d.account_no)
    |> join(:left, [a, b, c, d], e in "tbl_cmmp_branches", on: a.branch_name == e.acc_dom_branch_name)
    |> join(:left, [a, b, c, d, e], f in "tbl_cmmp_product", on: a.product_description_source == f.prod_desc_src)
    |> where([a, b, c, d, e, f],
        a.month_period == ^month and
        a.year_period == ^year and
        a.product_code_source in ^loan_schemes and
        a.business_unit in ^bus_units and
        a.account_status in ["ACT", "DORMNT", "INACT"] and
        a.actual_debit_balance > 0 and
        a.off_balance_sheet_flag in ["On"] and
        # a.subsegment_code_source == "PUBLIC SECTOR AND GOVERNMENT" and
        # b.economic_sector != "Agriculture, forestry and fishing" and
        a.source_system == "Finacle" and
        d.date == ^end_date #and
        # c.month == ^end_date and
      # fragment("CAST(? AS DATE) >= ?", a.account_open_date, ^from) and fragment("CAST(? AS DATE) <= ?", a.account_open_date, ^to)
      )
      |> select([a, b, c, d, e, f],
        %{
          "business_unit" => a.business_unit,
          "account_open_date" => a.account_open_date,
          "account_maturity_date" => a.account_maturity_date,
          "economic_sector" => b.economic_sector,
          "product_description_source" => a.product_description_source,
          "debtors_book_analysis" => f.debtors_book_analysis,
          "subsegment_code_source" => a.subsegment_code_source,
          "account_number" => a.account_number,
          "account_name" => a.customer_name,
          "branch_name" => a.branch_name,
          "actual_debit_balance" => fragment("
                                              CASE
                                              WHEN ? = 'ZMW'  THEN ?
                                              WHEN ? = 'USD' THEN ?
                                              ELSE ? END AS relationship",
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.currency_code,
                                              a.actual_debit_balance,
                                              a.actual_debit_balance
                                            ),
          "geo_dist" => e.geo_dist,
          "district" => e.district,
          "province" => e.province,
          "type_2" => f.type_2,
          "type_1" => f.type_1,
          "days_past_due" => d.days_past_due,
          "obddr" => fragment("select finnacle_account_code from tbl_obddr where finnacle_account_code = ? and month = ?", c.finnacle_account_code, ^end_date)
        }
      )
      |> Repo.all()
      # |> Enum.count()

  end


  #==========================================================CMMP================================================================
  def total_loans do
    Decimal.new("1000")
  end

  defp compose_loans_select(query) do
    select(query, [a], map(a, ^LoanAdvance.fields()))
  end

  def get_loan_business_unit!(id), do: Repo.get!(BusinessUnits, id)

  def get_loan_business_unit_by_reference!(reference), do: Repo.get_by!(BusinessUnits, reference: reference)

  def list_loan_business_units(params) do
    BusinessUnits
    |> preload([:checker, :maker])
    |> isearch_filter_loan_business_units(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_loan_business_units(query, nil), do: query

  defp isearch_filter_loan_business_units(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.business_unit, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.facility_category, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.ccr_business_unit, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def change_loan_product(%LoanProducts{} = loan_product, attrs \\ %{}) do
    LoanProducts.changeset(loan_product, attrs)
  end

  def get_loan_product!(id), do: Repo.get!(LoanProducts, id)

  def get_loan_product_by_reference!(reference), do: Repo.get_by!(LoanProducts, reference: reference)


  def update_loan_product(%LoanProducts{} = loan_product, attrs) do
    loan_product
    |> LoanProducts.changeset(attrs)
    |> Repo.update()
  end

  def list_loan_products(params) do
    LoanProducts
    |> preload([:checker, :maker])
    |> isearch_filter_loan_products(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_loan_products(query, nil), do: query

  defp isearch_filter_loan_products(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.product_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.type_of_security, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.security_value_ratio, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.schedule_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def change_loan_sector(%LoanSectors{} = loan_sector, attrs \\ %{}) do
    LoanSectors.changeset(loan_sector, attrs)
  end

  def get_loan_sector!(id), do: Repo.get!(LoanSectors, id)

  def list_loan_sectors(params) do
    LoanSectors
    |> isearch_filter_loan_sectors(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_loan_sectors(query, nil), do: query

  defp isearch_filter_loan_sectors(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.system_sector, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.prudential_sector, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.prudential_sub_sector, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.ccr_sector, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end
  def get_loan_scheme_code!(id), do: Repo.get!(LoanSchemeCodes, id)

  def get_loan_scheme_code_by_reference!(reference), do: Repo.get_by!(LoanSchemeCodes, reference: reference)

  def update_loan_scheme_code(%LoanSchemeCodes{} = loan_scheme_code, attrs) do
    loan_scheme_code
    |> LoanSchemeCodes.changeset(attrs)
    |> Repo.update()
  end

  def list_loan_scheme_codes(params) do
    LoanSchemeCodes
    |> preload([:checker, :maker])
    |> isearch_filter_loan_scheme_codes(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_loan_scheme_codes(query, nil), do: query

  defp isearch_filter_loan_scheme_codes(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.scheme_code, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.facility_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.scheme_type, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.scheme_type_description, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.scheme_code_description, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.facility_type2, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def change_loan_scheme_code(%LoanSchemeCodes{} = loan_scheme_code, attrs \\ %{}) do
    LoanSchemeCodes.changeset(loan_scheme_code, attrs)
  end

  def change_business_unit(%BusinessUnits{} = business_unit, attrs \\ %{}) do
    BusinessUnits.changeset(business_unit, attrs)
  end

  def update_business_unit(%BusinessUnits{} = business_unit, attrs) do
    business_unit
    |> BusinessUnits.changeset(attrs)
    |> Repo.update()
  end

  #----------------------------------REGULATORY CAPITAL
  def get_regulatory_capital!(id), do: Repo.get!(RegulatoryCapital, id)
  def get_regulatory_capital_reference!(reference), do: Repo.get_by!(RegulatoryCapital, reference: reference)

  def list_regulatory_capitals(params) do
    RegulatoryCapital
    |> preload([:checker, :maker])
    |> isearch_filter_regulatory_capital(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp isearch_filter_regulatory_capital(query, nil), do: query

  defp isearch_filter_regulatory_capital(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.regulatory_capital, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def change_regulatory_capital(%RegulatoryCapital{} = regulatory_capital, attrs \\ %{}) do
    RegulatoryCapital.changeset(regulatory_capital, attrs)
  end

  def update_regulatory_capital(%RegulatoryCapital{} = regulatoryCapital, attrs) do
    regulatoryCapital
    |> RegulatoryCapital.changeset(attrs)
    |> Repo.update()
  end

  def get_loan_classification!(id), do: Repo.get!(LoanClassifications, id)

  def get_loan_classification_by_reference!(reference) do
    Repo.get_by!(LoanClassifications, reference: reference)
  end

  def update_loan_classification(%LoanClassifications{} = loan_classification, attrs) do
    loan_classification
    |> LoanClassifications.changeset(attrs)
    |> Repo.update()
  end

  def list_loan_classifications(params) do
    LoanClassifications
    |> preload([:checker, :maker])
    |> isearch_filter_loan_classification(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end



  defp isearch_filter_loan_classification(query, nil), do: query

  defp isearch_filter_loan_classification(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.loan_classification, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.days_past_due, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.past_due_npl_classification, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"

  defp handle_filter(query, params) do
    Enum.reduce(params, query, fn
      {"isearch", value}, query when byte_size(value) > 0 ->
        isearch_filter(query, sanitize_term(value))

      {"account_no", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.account_no, ^sanitize_term(value))
        )

      {"outstanding_amt", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("CAST(? AS varchar) LIKE ?", a.outstanding_amt, ^"%#{value}%")
        )

      {"cust_no", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.cust_no, ^sanitize_term(value))
        )

      {"class_type", value}, query when byte_size(value) > 0 ->
        where(
          query,
          [a],
          fragment("lower(?) LIKE lower(?)", a.class_type, ^sanitize_term(value))
        )

      {"from", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b, _c, _d, _e, _f], fragment("CAST(? AS DATE) >= ?", a.txn_dt, ^value))

      {"to", value}, query when byte_size(value) > 0 ->
        where(query, [a, _b, _c, _d, _e, _f], fragment("CAST(? AS DATE) <= ?", a.txn_dt, ^value))

      {_, _}, query ->
        # Not a where parameter
        query
    end)
  end

  defp isearch_filter(query, search_term) do
    where(
      query,
      [a],
      fragment("lower(?) LIKE lower(?)", a.account_no, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.cust_no, ^search_term) or
        fragment("lower(?) LIKE lower(?)", a.class_type, ^search_term) or
        fragment("CAST(? AS varchar) LIKE ?", a.outstanding_amt, ^search_term)
    )
  end

  def change_loan_classification(%LoanClassifications{} = loan_classification, attrs \\ %{}) do
    LoanClassifications.changeset(loan_classification, attrs)
  end

  def empty_scrivener do
    %Scrivener.Page{
      entries: [],
      page_number: 1,
      page_size: 10,
      total_entries: 0,
      total_pages: 1
    }
  end

  alias MisReports.Prudentials.PrudReport

  @doc """
  Returns the list of tbl_prud_report.

  ## Examples

      iex> list_tbl_prud_report()
      [%PrudReport{}, ...]

  """
  def prudential_reports do
    Repo.all(PrudReport)
  end

  @doc """
  Gets the PrudReport entries between two date ranges.

  ## Examples

      iex> get_prud_reports_between_dates(~D[2023-01-01], ~D[2023-12-31], ~D[2024-01-01], ~D[2024-12-31])
      %{
        period_one: [%PrudReport{}, ...],
        period_two: [%PrudReport{}, ...]
      }

  """
  def get_prud_reports_between_dates(start_date, end_date, start_dt, end_dt) do
    period_one =
      PrudReport
      |> where([p], p.start_date >= ^start_date and p.end_date <= ^end_date)
      |> select([p], %{
        month: p.month,
        year: p.year,
        start_date: p.start_date,
        end_date: p.end_date,
        income_stmt: p.income_stmt
      })
      |> Repo.all()
      |> Enum.map(&decode_income_stmt/1)

    period_two =
      PrudReport
      |> where([p], p.start_date >= ^start_dt and p.end_date <= ^end_dt)
      |> select([p], %{
        month: p.month,
        year: p.year,
        start_date: p.start_date,
        end_date: p.end_date,
        income_stmt: p.income_stmt
      })
      |> Repo.all()
      |> Enum.map(&decode_income_stmt/1)

    %{
      period_one: period_one,
      period_two: period_two
    }
  end

  defp decode_income_stmt(entry) do
    case Poison.decode(entry.income_stmt) do
      {:ok, decoded} -> Map.put(entry, :income_stmt, decoded)
      {:error, _} -> entry
    end
  end

  def get_income_stmt_and_balance_sheet do
    PrudReport
    |> select([p], %{
      income_stmt: p.income_stmt,
      balance_sheet: p.bal_sheet,
      end_date: p.end_date
    })
    |> Repo.all()
    |> Enum.map(&decode_fields/1)
    |> Enum.group_by(&format_end_date/1)
    |> Enum.into(%{}, fn {key, value} -> {key, Enum.map(value, &Map.drop(&1, [:end_date]))} end)
  end

  defp decode_fields(entry) do
    income_stmt = decode_json(entry.income_stmt)
    balance_sheet = decode_json(entry.balance_sheet)

    Map.put(entry, :income_stmt, income_stmt)
    |> Map.put(:balance_sheet, balance_sheet)
  end

  defp decode_json(json_string) do
    case Poison.decode(json_string) do
      {:ok, decoded} -> decoded
      {:error, _} -> json_string
    end
  end

  defp format_end_date(entry) do
    Timex.format!(entry.end_date, "%Y%m", :strftime)
  end

  def test do
    key = "202410"
        case Map.get(get_income_stmt_and_balance_sheet(), key) do
          nil -> []
          entries -> Enum.map(entries, & &1.balance_sheet)
      end
  end

  @doc """
  Gets a single prud_report.

  Raises `Ecto.NoResultsError` if the Prud report does not exist.

  ## Examples

      iex> get_prud_report!(123)
      %PrudReport{}

      iex> get_prud_report!(456)
      ** (Ecto.NoResultsError)

  """
  def get_prud_report!(id), do: Repo.get!(PrudReport, id)

  @doc """
  Creates a prud_report.

  ## Examples

      iex> create_prud_report(%{field: value})
      {:ok, %PrudReport{}}

      iex> create_prud_report(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_prud_report(attrs \\ %{}) do
    %PrudReport{}
    |> PrudReport.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a prud_report.

  ## Examples

      iex> update_prud_report(prud_report, %{field: new_value})
      {:ok, %PrudReport{}}

      iex> update_prud_report(prud_report, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_prud_report(%PrudReport{} = prud_report, attrs) do
    prud_report
    |> PrudReport.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a prud_report.

  ## Examples

      iex> delete_prud_report(prud_report)
      {:ok, %PrudReport{}}

      iex> delete_prud_report(prud_report)
      {:error, %Ecto.Changeset{}}

  """
  def delete_prud_report(%PrudReport{} = prud_report) do
    Repo.delete(prud_report)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking prud_report changes.

  ## Examples

      iex> change_prud_report(prud_report)
      %Ecto.Changeset{data: %PrudReport{}}

  """
  def change_prud_report(%PrudReport{} = prud_report, attrs \\ %{}) do
    PrudReport.changeset(prud_report, attrs)
  end


  def get_prudential_report(month, type, year) do
    PrudReport
    |> where([a], a.month == ^month and a.type == ^type and a.year ==^year)
    |> Repo.one()
  end

  def get_prudential_report_schedules(ref) do
    PrudReport
    |> where([a], a.ref == ^ref)
    |> Repo.one()
  end

  def get_prudential_report_status(status) do
    PrudReport
    |> where([a], a.status == ^status)
    |> Repo.all()
  end

  def prudential_report_list(params) do
    PrudReport
    |> preload([:checker, :maker])
    |> prudential_report_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp prudential_report_isearch_filter(query, nil), do: query

  defp prudential_report_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.month, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.year, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def pending_export() do
    PrudReport
    |> where([a], is_nil(a.filename))
    |> Repo.all()
  end

  def get_income_stmt_for_trend(start_date, end_date) do
    year = String.replace(start_date, "-", "") |> String.slice(0..3)

    # start_month = String.replace(start_date, "-", "") |> String.slice(4..5)
    end_month = String.replace(end_date, "-", "") |> String.slice(4..5)

    PrudReport
    |> where([a],
      a.year == ^year and
      # a.month >= ^start_month and
      a.month <= ^end_month
    )
    |> select([a], %{
      year: a.year,
      month: a.month,
      income_stmt: a.income_stmt
    })
    |> Repo.all()
    |> Enum.group_by(fn record ->
      cond do
        record.month in ["01", "02", "03"] -> :quarter_1
        record.month in ["04", "05", "06"] -> :quarter_2
        record.month in ["07", "08", "09"] -> :quarter_3
        record.month in ["10", "11", "12"] -> :quarter_4
      end
    end)
    |> Enum.map(fn {quarter, records} ->
      %{quarter => records}
    end)
    |> Enum.reduce(%{}, fn map, acc -> Map.merge(acc, map) end)
  end


  alias MisReports.Prudentials.BsaMaster

  @doc """
  Returns the list of tbl_bsa_master.

  ## Examples

      iex> list_tbl_bsa_master()
      [%BsaMaster{}, ...]

  """
  def list_tbl_bsa_master do
    Repo.all(BsaMaster)
  end

  @doc """
  Gets a single bsa_master.

  Raises `Ecto.NoResultsError` if the Bsa master does not exist.

  ## Examples

      iex> get_bsa_master!(123)
      %BsaMaster{}

      iex> get_bsa_master!(456)
      ** (Ecto.NoResultsError)

  """
  def get_bsa_master!(id), do: Repo.get!(BsaMaster, id)

  @doc """
  Creates a bsa_master.

  ## Examples

      iex> create_bsa_master(%{field: value})
      {:ok, %BsaMaster{}}

      iex> create_bsa_master(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def create_bsa_master(attrs \\ %{}) do
    %BsaMaster{}
    |> BsaMaster.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a bsa_master.

  ## Examples

      iex> update_bsa_master(bsa_master, %{field: new_value})
      {:ok, %BsaMaster{}}

      iex> update_bsa_master(bsa_master, %{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def update_bsa_master(%BsaMaster{} = bsa_master, attrs) do
    bsa_master
    |> BsaMaster.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a bsa_master.

  ## Examples

      iex> delete_bsa_master(bsa_master)
      {:ok, %BsaMaster{}}

      iex> delete_bsa_master(bsa_master)
      {:error, %Ecto.Changeset{}}

  """
  def delete_bsa_master(%BsaMaster{} = bsa_master) do
    Repo.delete(bsa_master)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking bsa_master changes.

  ## Examples

      iex> change_bsa_master(bsa_master)
      %Ecto.Changeset{data: %BsaMaster{}}

  """
  def change_bsa_master(%BsaMaster{} = bsa_master, attrs \\ %{}) do
    BsaMaster.changeset(bsa_master, attrs)
  end

  def list_bsa_reports(params, _status) do
    BsaMaster
    # |> preload([:checker, :maker])
    |> bsa_isearch_filter(params.isearch)
    # |> where([a], a.status in ^status)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp bsa_isearch_filter(query, nil), do: query

  defp bsa_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> or_where([a], fragment("lower(?) like lower(?)", a.start_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.end_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.schedule, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  def get_prud_report_select_by_ref(id) do
    PrudReport
    |> where([p], p.ref == ^id)
    |> select([p],
      %{
      id: p.id,
      ref: p.ref,
      type: p.type,
      month: p.month,
      year: p.year,
      start_date: p.start_date,
      end_date: p.end_date,
      status: p.status,
      submission_status: p.submission_status,
      maker_id: p.maker_id,
      checker_id: p.checker_id,
      bal_sheet: p.bal_sheet,
      income_stmt: p.income_stmt,
      schedule_01c: p.schedule_01c,
      schedule_01d: p.schedule_01d,
      schedule_01e: p.schedule_01e,
      schedule_01f: p.schedule_01f,
      schedule_02a1: p.schedule_02a1,
      schedule_02c: p.schedule_02c,
      schedule_02d: p.schedule_02d,
      schedule_02e: p.schedule_02e,
      schedule_02f: p.schedule_02f,
      schedule_02g: p.schedule_02g,
      schedule_02h: p.schedule_02h,
      schedule_02j: p.schedule_02j,
      schedule_03a: p.schedule_03a,
      schedule_03b: p.schedule_03b,
      schedule_04b: p.schedule_04b,
      schedule_04c: p.schedule_04c,
      schedule_04d: p.schedule_04d,
      schedule_05b: p.schedule_05b,
      schedule_05c: p.schedule_05c,
      schedule_05d: p.schedule_05d,
      schedule_06a: p.schedule_06a,
      schedule_06b: p.schedule_06b,
      schedule_07a: p.schedule_07a,
      schedule_08a: p.schedule_08a,
      schedule_08b: p.schedule_08b,
      schedule_09a: p.schedule_09a,
      schedule_10c: p.schedule_10c,
      schedule_11d: p.schedule_11d,
      schedule_11e: p.schedule_11e,
      schedule_11f: p.schedule_11f,
      schedule_11g: p.schedule_11g,
      schedule_11h: p.schedule_11h,
      schedule_11j: p.schedule_11j,
      schedule_11k: p.schedule_11k,
      schedule_12: p.schedule_12,
      schedule_13: p.schedule_13,
      schedule_14: p.schedule_14,
      schedule_15: p.schedule_15,
      schedule_17a: p.schedule_17a,
      schedule_17b: p.schedule_17b,
      schedule_17c: p.schedule_17c,
      schedule_17e: p.schedule_17e,
      schedule_18a: p.schedule_18a,
      schedule_18b: p.schedule_18b,
      schedule_18c: p.schedule_18c,
      schedule_18d: p.schedule_18d,
      schedule_19: p.schedule_19,
      schedule_20a: p.schedule_20a,
      schedule_21b: p.schedule_21b,
      schedule_21c: p.schedule_21c,
      schedule_22a: p.schedule_22a,
      schedule_22b: p.schedule_22b,
      schedule_23a: p.schedule_23a,
      schedule_23b: p.schedule_23b,
      schedule_24: p.schedule_24,
      schedule_25: p.schedule_25,
      schedule_26: p.schedule_26,
      schedule_27: p.schedule_27,
      schedule_27a: p.schedule_27a,
      schedule_28a: p.schedule_28a,
      schedule_28b: p.schedule_28b,
      schedule_29a: p.schedule_29a,
      schedule_30b: p.schedule_30b,
      schedule_30b1: p.schedule_30b1,
      schedule_30c: p.schedule_30c,
      schedule_30c1: p.schedule_30c1,
      schedule_30d: p.schedule_30d,
      schedule_31c: p.schedule_31c,
      schedule_31d: p.schedule_31d,
      schedule_31e: p.schedule_31e,
      schedule_31f: p.schedule_31f,
      schedule_32a: p.schedule_32a

      })
    |> Repo.one()
  end
  def get_current_and_latest_date() do
    PrudReport
    |> where([p], p.status == "EXPORT_COMPLETE")
    |> select([p], p.end_date)
    |> Repo.all()
    |> Enum.max(Date)
    |> case do
      nil -> Timex.now()
      date -> date
    end

  end
  #========================================================CREDIT MIS REPORTS
  def get_prud_report_misresport(start_date, end_date) do

    current_month =   PrudReport
                      |> where([p], p.end_date == ^start_date and p.status == "EXPORT_COMPLETE")
                      |> select([p],
                        %{
                          schedule_02c: p.schedule_02c,
                          schedule_04b: p.schedule_04b,
                          schedule_04d: p.schedule_04d,
                          schedule_05b: p.schedule_05b,
                        })
                        |> Repo.one()

      previous_month =  PrudReport
                        |> where([p], p.end_date == ^end_date and p.status == "EXPORT_COMPLETE")
                        |> select([p],
                          %{
                            schedule_02c: p.schedule_02c,
                            schedule_04b: p.schedule_04b,
                            schedule_04d: p.schedule_04d,
                            schedule_05b: p.schedule_05b,
                          })
                          |> Repo.one()
    %{
      current_month: current_month,
      previous_month: previous_month
    }
  end

  def sector_analysis(start_date, end_date) do

    current_date = end_date |> Date.from_iso8601!()
    previous_date = start_date |> Date.from_iso8601!()

    current_usd_rate = usd_rate(current_date)
    previous_usd_rate = usd_rate(previous_date)

    current_month_year = String.slice(current_date |> to_string(), 0..3)
    current_month = String.slice(current_date |> to_string(), 5..6)

    previous_month_year = String.slice(previous_date |> to_string(), 0..3)
    previous_month = String.slice(previous_date |> to_string(), 5..6)

      current_month =  CustContribution
                      |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
                      |> group_by([a, b],
                        [b.economic_sector,
                        # c.facility_category,
                        a.currency_code,
                        fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector)
                        ]
                        )
                      |> where([a, b],
                        a.month_period == ^current_month
                        and a.year_period == ^current_month_year
                        and a.off_balance_sheet_flag == "On"
                        and a.product_code_source in ^loan_scheme_codes()
                        and a.business_unit in ^business_units()
                        and a.account_status in ["ACT", "DORMNT", "INACT"]
                        and a.actual_debit_balance > 0
                        and a.source_system == "Finacle")
                      |> select([a, b], %{
                        "economic_sector" => fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector),
                        "currency_code" => a.currency_code,
                        "outstanding_amount_lcy" => sum(a.actual_debit_balance),
                        "outstanding_amount_fcy" => fragment("? / ?", sum(a.actual_debit_balance), ^current_usd_rate)
                      })
                      |> Repo.all()
                      |> Enum.map(fn data ->
                       %{
                         "economic_sector" => data["economic_sector"],
                         "currency_code" => data["currency_code"],
                         "outstanding_amount_lcy" => if(data["outstanding_amount_lcy"] == nil, do: Decimal.new(0), else: data["outstanding_amount_lcy"] |> Decimal.div(Decimal.new("1000"))),
                         "outstanding_amount_fcy" => if(data["outstanding_amount_fcy"] == nil, do: Decimal.new(0), else: data["outstanding_amount_fcy"] |> Decimal.div(Decimal.new("1000")))
                       }
                      end)

      previous_month =  CustContribution
                      |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
                      |> group_by([a, b],
                        [b.economic_sector,
                        # c.facility_category,
                        a.currency_code,
                        fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector)
                        ]
                        )
                      |> where([a, b],
                        a.month_period == ^previous_month and a.year_period == ^previous_month_year
                        and a.off_balance_sheet_flag == "On"
                        and a.product_code_source in ^loan_scheme_codes()
                        and a.business_unit in ^business_units()
                        and a.account_status in ["ACT", "DORMNT", "INACT"]
                        and a.actual_debit_balance > 0
                        and a.source_system == "Finacle")
                      |> select([a, b], %{
                        "economic_sector" => fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector),
                        "currency_code" => a.currency_code,
                        "outstanding_amount_lcy" => sum(a.actual_debit_balance),
                        "outstanding_amount_fcy" => fragment("? / ?", sum(a.actual_debit_balance), ^previous_usd_rate)
                      })
                      |> Repo.all()
                      |> case do
                        [] ->
                          [%{
                            "economic_sector" => "Other (Sectors or Products)",
                            "currency_code" => "ZMW",
                            "outstanding_amount_lcy" => Decimal.new(0),
                            "outstanding_amount_fcy" => Decimal.new(0)
                          }]
                        data -> data
                      end
                      |> Enum.map(fn data ->
                        %{
                          "economic_sector" => data["economic_sector"],
                          "currency_code" => data["currency_code"],
                          "outstanding_amount_lcy" => if(data["outstanding_amount_lcy"] == nil, do: Decimal.new(0), else: data["outstanding_amount_lcy"] |> Decimal.div(Decimal.new("1000"))),
                          "outstanding_amount_fcy" => if(data["outstanding_amount_fcy"] == nil, do: Decimal.new(0), else: data["outstanding_amount_fcy"] |> Decimal.div(Decimal.new("1000")))
                        }
                       end)
                       |> case do
                        [] ->
                          [%{
                            "economic_sector" => "Other (Sectors or Products)",
                            "currency_code" => "ZMW",
                            "outstanding_amount_lcy" => Decimal.new(0),
                            "outstanding_amount_fcy" => Decimal.new(0)
                          }]
                        data -> data

                      end
      %{
        current_month: current_month,
        previous_month: previous_month
      }
  end


  def individual_sector_analysis(end_date) do

    # previous_date = Timex.shift(current_date, months: -1) |> Timex.end_of_month()

    current_usd_rate = usd_rate(end_date)

    current_month_year = String.slice(end_date |> to_string(), 0..3)
    current_month = String.slice(end_date |> to_string(), 5..6)


    CustContribution
    |> join(:left, [a], b in "tbl_customer_details", on: a.customer_number_local_cif == b.account_no)
    |> group_by([a, b],
      [b.economic_sector,
      a.account_number,
      a.currency_code,
      fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector)
      ]
      )
    |> where([a, b],
      a.month_period == ^current_month
      and a.year_period == ^current_month_year
      and a.off_balance_sheet_flag == "On"
      and a.product_code_source in ^loan_scheme_codes()
      and a.business_unit in ^business_units()
      and a.account_status in ["ACT", "DORMNT", "INACT"]
      and a.actual_debit_balance > 0
      and a.source_system == "Finacle")
    |> select([a, b], %{
      "customer_name" => max(a.customer_name),
      "economic_sector" => fragment("CASE WHEN ? is null THEN 'Other (Sectors or Products)' ELSE ? END", b.economic_sector, b.economic_sector),
      "currency_code" => a.currency_code,
      "outstanding_amount_lcy" => sum(a.actual_debit_balance),
      "outstanding_amount_fcy" => fragment("? / ?", sum(a.actual_debit_balance), ^current_usd_rate)
    })
    |> Repo.all()



  end


  def get_cmmp_report_status(status) do
    CmmpFileExport
    |> where([a], a.status == ^status)
    |> Repo.all()
  end

  def get_cmmp_report_schedules(ref) do
    CmmpReport
    |> where([a], a.ref == ^ref)
    |> Repo.one()
  end

  def update_cmmp_report(%CmmpReport{} = cmmp_report, attrs) do
    cmmp_report
    |> CmmpReport.changeset(attrs)
    |> Repo.update()
  end


  def cmmp_report_list(params) do
    CmmpFileExport
    |> preload([:checker, :maker])
    |> cmmp_report_isearch_filter(params.isearch)
    |> order_by(^[params.sort_by])
    |> Repo.paginate(page: params.page, page_size: params.page_size)
  end

  defp cmmp_report_isearch_filter(query, nil), do: query

  defp cmmp_report_isearch_filter(query, isearch) do
    search_term = sanitize_term(isearch)

    query
    |> where([a], fragment("lower(?) like lower(?)", a.status, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.report_date, ^search_term))
    |> or_where([a], fragment("lower(?) like lower(?)", a.inserted_at, ^search_term))
  end

  # def sanitize_term(term), do: "%#{String.replace(term, "%", "\\%")}%"
end
