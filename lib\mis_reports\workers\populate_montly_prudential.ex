defmodule MisReports.Workers.PopulateMonthlyPrudential do
  alias <PERSON>sReports.Workers.Utils
  alias <PERSON>sReports.Utilities
  alias MisReports.Workers.Sh20a

  def perform(data) do
    IO.inspect("------------------------POPULATE EXPORTING-----------------------------")
    rate =
      case Utilities.get_exchange_rate_by_date_and_code("#{data.end_date}", "USD") do
        nil -> 1.0
        rate -> Decimal.to_float(rate[:exchange_rate_lcy])
      end

    file = gen_file(data)

    case Excelizer.open(file, &populate(&1, data, rate)) do
      :ok -> {:ok,  file}
      error -> error
    end
  end

  def gen_file(item) do
    dir = MisReports.Utilities.get_directory_params()
    datetime = Timex.local() |> Timex.format!("%Y%m%d%H%M", :strftime)
    name = "Prudential_Report_#{datetime}.xlsx"
    MisReports.Prudentials.update_prud_report(item, %{filename: name})
    dest_file = "#{dir.complete}/#{name}"
    template_file = "#{dir.templates}/Prudential_template.xlsx"
    File.copy!(template_file, dest_file)
    dest_file
  end

  def populate(file_id, data, rate) do
    gen_info(file_id, data, rate)
    income_stmt(file_id, data)
    bal_sheet(file_id, data)
    schedule_01c(file_id, data)
    schedule_01e(file_id, data)
    schedule_01f(file_id, data)
    schedule_02a1(file_id, data)
    schedule_02c(file_id, data)
    schedule_02g(file_id, data)
    schedule_02h(file_id, data)
    schedule_3a(file_id, data)
    schedule_04b(file_id, data)
    schedule_4d(file_id, data)
    schedule_05b(file_id, data)
    schedule_6a(file_id, data)
    schedule_07a(file_id, data)
    schedule_08a(file_id, data)
    schedule_09a(file_id, data)
    schedule_11d(file_id, data)
    schedule_11e(file_id, data)
    schedule_12(file_id, data)
    schedule_13(file_id, data)
    schedule_14(file_id, data)
    schedule_15(file_id, data)
    schedule_17b(file_id, data)
    schedule_18b(file_id, data)
    schedule_18c(file_id, data)
    schedule_18d(file_id, data)
    schedule_19(file_id, data)
    schedule_20a(file_id, data)
    #schedule_21b(file_id, data)
    schedule_22a(file_id, data)
    schedule_23a(file_id, data)
    schedule_24(file_id, data)
    schedule_25(file_id, data)
    schedule_26(file_id, data)
    schedule_27(file_id, data)
    schedule_27a(file_id, data)
    schedule_28a(file_id, data)
    schedule_28b(file_id, data)
    schedule_29a(file_id, data)
    schedule_30b(file_id, data)
    schedule_30c(file_id, data)
    schedule_30d(file_id, data)
    schedule_31c(file_id, data)
    schedule_31d(file_id, data)
    schedule_31f(file_id, data)
    schedule_32a(file_id, data)
  end

  def gen_info(file_id, data, rate) do
    date = data.end_date
    %{"G10" => date.year, "G11" => date.month, "G12" => rate}
    |> Enum.each(fn {index, value} when is_binary(value)->
        Excelizer.Cell.set_cell_value(file_id,
          "General Information",
            index,
            "string",
            value
        )
    {"G12", value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "General Information",
            "G12",
          "float",
          value
        )
    {index, value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "General Information",
            index,
          "int",
          value
        )
    end)
  end

  def income_stmt(file_id, data) do
    data.income_stmt
    |> Poison.decode!()
    # |> Enum.filter(fn {index, _value} ->
    #    index in ["B13", "B14", "B15", "B17", "B18", "B19", "B20", "B21", "B53", "B54", "B87", "B88", "B89"]
    # end)
    |> Enum.map(fn {index, value} ->
        {_column, row} = String.split_at(index, 1)
        {"B#{row}", value}
    end)
    |> Stream.each(fn

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Income Statement", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Income Statement", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Income Statement", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def bal_sheet(file_id, data) do
    data.bal_sheet
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
       index in [
        "B13", "B14", "B15", "B17", "B18", "B19", "B20", "B21", "B47",
        "B53", "B54", "B58", "B69","B71", "B79", "B87", "B88", "B89",
        "B109", "B110", "B111", "B112", "B114", "B115", "B116", "B117", "B118",
      ]
    end)
    |> Stream.each(fn

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Balance Sheet", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Balance Sheet", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Balance Sheet", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_01c(file_id, data) do
    data.schedule_01c
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
       index in ["B15","B16", "B17", "B18", "B27", "B28", "B29", "C15","C16", "C17", "C18", "C27", "C28", "C29","D15","D16", "D17", "D18", "D27", "D28", "D29", "E15", "E16", "F15", "F16"]
    end)
    |> Stream.each(fn {index, value} ->
      Excelizer.Cell.set_cell_value(file_id,
        "Schedule 01C",
         index,
        "float",
        value |> string_to_decimal() |> Decimal.to_float()
      )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_01e(file_id, data) do
    data.schedule_01e
    |> Poison.decode!()
    # |> Enum.filter(fn {index, _value} ->
    #    index not in filter_locked_cel_sch29a()
    # end)
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0", "0.00"]->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 01E",
          index,
          "string",
          ""
      )
      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 01E",
           index,
          "int",
          Utils.string_to_integer(value)
        )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_01f(file_id, data) do
    data.schedule_01f
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      new_map = %{
        "A#{index}" => item["security_type"],
        "B#{index}" => item["counter_name"],
        "C#{index}" => item["purpose_pledge"],
        "D#{index}" => item["pledge_date"],
        "E#{index}" => item["expiry_date"],
        "F#{index}" => item["currency"],
        "G#{index}" => item["cost_security"],
        "H#{index}" => item["security_pledge"],
        "I#{index}" => item["total_secured"],
      }
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 01F", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 01F", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 01F", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 01F", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_02a1(file_id, data) do
    data.schedule_02a1
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(14)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02A1", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02A1", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02A1", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def map_sch02a1_indices(item,index) do
    Enum.reduce(item, %{}, fn  {key, value}, acc ->
      value =
        cond do
          is_map(value) ->  Decimal.new(value["sign"], value["coef"], value["exp"])

          value == 0 -> 0

          Regex.match?(~r/^\d+$/, value || "") and is_binary(value) ->
            if Regex.match?(~r/\.\d+$/, value), do: Utils.string_to_float(value), else: Utils.string_to_integer(value)

          true -> value
        end

      Map.merge(acc, %{"#{key}#{index}" => value})
    end)
  end

  def schedule_02c(file_id, data) do
    data.schedule_02c
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(16)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02C", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02C", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02C", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_02g(file_id, data) do
    data.schedule_02g
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(20)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02G", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02G", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02G", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_02h(file_id, data) do
    data.schedule_02h
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(15)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02H", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02H", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 02H", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_3a(file_id, data) do
    data.schedule_03a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["E28", "C28", "D20", "A27", "B28", "D27", "B26", "E26", "C20", "B21", "A28",
       "E27", "B18", "C17", "B20", "C14", "D18", "D17", "D19", "D16", "D28", "C19",
       "D14", "B17", "B15", "C21", "C26", "B27", "D24", "A26", "D26", "C27", "D15",
       "D21", "B14", "B19", "B16", "C18"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 03A", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 03A", index, "int", 0)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 03A", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 03A", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_04b(file_id, data) do
    data.schedule_04b
    |> case do
      nil -> []
      data ->
        data
        |> Poison.decode!()
        |> Map.get("list")
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["B15", "B17", "D15", "D17", "D19"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 04B", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 04B", index, "int", 0)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 04B", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 04B", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_4d(file_id, data) do
    data.schedule_04d
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["C28", "I32", "C22", "G28", "H29", "K24", "J24", "J28", "L25", "H16", "C20",
       "G32", "H25", "H24", "C32", "C17", "L28", "C31", "L29", "I16", "C19", "C25",
       "L16", "K32", "G24", "J32", "C30", "K28", "L24", "C21", "H28", "C26", "C16",
       "H32", "K16", "J29", "C29", "G29", "C23", "G25", "K25", "C24", "J25", "G16",
       "K29", "C18"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 04D", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 04D", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 04D", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 04D", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_05b(file_id, data) do
    data.schedule_05b
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(15)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 05B", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 05B", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 05B", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_6a(file_id, data) do
    data.schedule_06a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.zip_reduce(schedule6a_rows_index(), %{}, fn entries, index, acc ->
       cust_items =
        Enum.with_index(entries, (index + 1))
        |> Enum.reduce(%{}, fn {item, row}, acc ->
          new_map = prepare_params_6a(item, row)
          Map.merge(acc, new_map)
        end)

      cust_items_with_header =
        Map.merge(cust_items, prepare_header_6a(index, entries))

        Map.merge(acc, cust_items_with_header)
    end)
    |> Stream.each(fn
      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 06A", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 06A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 06A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 06A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def prepare_params_6a(entry, index) do
    %{
      "A#{index}" => entry["A"],
      "B#{index}" => entry["B"],
      "C#{index}" => entry["C"],
      "E#{index}" => entry["E"],
      "F#{index}" => entry["F"],
      "I#{index}" => entry["I"],
      "K#{index}" => entry["K"],
      "L#{index}" => entry["L"],
      "M#{index}" => entry["M"],
      "N#{index}" => entry["N"],
      "O#{index}" => entry["O"]
    }
  end

  def prepare_header_6a(index, entries) do
    [entry | _] = entries

    %{
      "A#{index}" => entry["group_no"],
      "B#{index}" => "",
      "C#{index}" => "",
      "E#{index}" => "",
      "F#{index}" => "",
      "I#{index}" => "",
      "K#{index}" => "",
      "L#{index}" => "",
      "M#{index}" => "",
      "N#{index}" => "",
      "O#{index}" => ""
    }
  end

  def schedule6a_rows_index() do
    [15, 77, 139, 201, 263, 325, 387, 449, 511, 573, 635, 697, 759, 821, 883, 945, 1007, 1069, 1131, 1193, 1255, 1317, 1379, 1441, 1503, 1565, 1627, 1689, 1751, 1813, 1875, 1937, 1999, 2061, 2123, 2185, 2247, 2309, 2371, 2433, 2495, 2557, 2619, 2681, 2743, 2805, 2867, 2929, 2991, 3053, 3115, 3177, 3239, 3301, 3363, 3425, 3487, 3549, 3611, 3673]
  end

  def schedule_07a(file_id, data) do
    data.schedule_07a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(16)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 07A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 07A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 07A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_08a(file_id, data) do
    data.schedule_08a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(16)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 08A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 08A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 08A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_09a(file_id, data) do
    data.schedule_09a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      new_map = %{
        "A#{index}" => item["name_of_borrower"],
        "B#{index}" => item["nature_of_property_rep"],
        "C#{index}" => item["location_of_the_property"],
        "D#{index}" => item["date_rep"],
        "E#{index}" => item["type_of_security_held"],
        "F#{index}" => item["owner_of_the_property"],
        "G#{index}" => item["type_of_facility"],
        "H#{index}" => item["amount_outstanding"],
        "I#{index}" => item["provisions"],
        "K#{index}" => item["estimated_market_value"],
        "L#{index}" => item["date_of_last_valuation"]
      }
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 09A", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 09A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 09A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 09A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end


  def schedule_11d(file_id, data) do
    data.schedule_11d
    |> Poison.decode!()
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
      new_map = new_deposit(value, index)
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 11D", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 11D", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 11D", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def new_deposit(entry, index) do
    %{
       "A#{index}" => entry["cust_type"],
       "B#{index}" => entry["economic_sector"],
       "C#{index}" => entry["economic_sub_sector"],
       "D#{index}" => entry["institutional_units_and_sectors"],
       "F#{index}" => entry["deposit_type"],
       "G#{index}" => entry["type_of_time_deposit"],
       "H#{index}" => (if entry["amount"] != nil, do: Decimal.new(entry["amount"]["sign"], entry["amount"]["coef"], entry["amount"]["exp"]), else: 0),
       "I#{index}" => entry["currency"]
     }
  end

  def schedule_11e(file_id, data) do
    data.schedule_11e
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
        columns = ~w(E F G H)
        rows = 16..26 |> Enum.to_list()
        index in  Sh20a.get_schedule_params(columns, rows)
    end)
    |> Enum.map(fn {index, value} ->
      update_11e_cells(index, value)
    end)
    |> Stream.each(fn

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 11E", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 11E", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 11E", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def update_11e_cells(index, value) do
    {column, row} = String.split_at(index, 1)

    new_index =
      case column do
        "E" -> "D#{row}"
        "F" -> "E#{row}"
        "G" -> "F#{row}"
        "H" -> "G#{row}"
        _ -> index
      end

    {new_index, value}
  end

  def schedule_12(file_id, data) do
    data.schedule_12
    |> Poison.decode!()
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 12", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 12", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 12", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_13(file_id, data) do
    data.schedule_13
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
      index not in [ "G39", "H39"]
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 13", index, "string", nil)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 13", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 13", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_14(file_id, data) do
    data.schedule_14
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
      index in [ "E31", "E32", "E34", "E35","E37", "E38", "E42", "E43", "E45", "E46", "E48","E49", "E50", "E51", "E52", "E53","E57", "E59"]
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 14", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 14", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 14", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_15(file_id, data) do
    data.schedule_15
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} ->
        index in
        ["C22","C23","C27","C30","C31","C32","C33","C34","C35","C36","C37",
          "C38","C39","C44","C45","C49","C50","C51","C52","C55","C62"
        ]
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 15", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 15", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 15", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_17b(file_id, data) do
    data.schedule_17b
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.with_index(16)
    |> Enum.reduce(%{}, fn  {value, index}, acc ->
        new_map =  map_sch02a1_indices(value, index)
        Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 17B", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 17B", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 17B", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end


  def schedule_18b(file_id, data) do
    data.schedule_18b
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["D79","E29", "E28", "G20", "G79", "E30", "C28", "H63",
       "H59", "H26", "F38", "E24", "K68", "K59", "D20", "C68", "F25", "H38",
       "D67", "C59", "F32", "K60", "G28", "H29", "L61", "H69", "F79", "G67", "E69",
       "E26", "L79", "G60", "B59", "I60", "C20", "D60", "E60", "E68", "J69",
       "F33", "F67", "J68", "B61", "K79", "B63", "C60", "G32", "J79", "H25", "H24",
       "L38", "K63", "H30", "E67", "C32", "D38", "C17", "K61", "F30", "G59",
       "I63", "D63", "F63", "J63", "F60", "E25", "I61", "H33", "G69", "G30",
       "K69", "F68", "B69", "I68", "C79", "D68", "G33", "L60",
       "F29", "J60", "H60", "B38", "B68", "I79", "D33", "H61", "F28", "H79",
       "D28", "H67", "E61", "C25", "G63", "C69", "J59", "C61", "C67", "I69",
       "B67", "D32", "G24", "F61", "F20", "F26", "K38", "D25", "C30",
       "F69", "L69", "E19", "H28", "C26", "H20", "C16", "D24", "H32", "D26", "E32",
       "F24", "D59", "E63", "C38", "C63", "D29", "E38", "B60", "E33", "L63", "G38",
       "K67", "C29", "J61", "G29", "J38", "E20", "D61", "G25", "E79", "F59",
       "D69", "L68", "G61", "L67", "G26", "D30", "C24", "I38",
       "H68", "E59", "I67", "G68", "L59", "C33", "B79", "J67", "I59"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18B", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18B", index, "int", 0)

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18B", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 18B", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 18B", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_18c(file_id, data) do
    data.schedule_18c
    |> Poison.decode!()

    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in ["B51", "D79", "D170", "B103", "D102", "C55", "B197", "D207", "B128", "C180",
       "B244", "B170", "B204", "E133", "C256", "E219", "B55", "B172", "C222", "D158",
       "B281", "B96", "D229", "B254", "E182", "D80", "E252", "D94", "C82", "B132",
       "C95", "B219", "E229", "E208", "B70", "D206", "C229", "D252", "D251", "C70",
       "C52", "D133", "E78", "C281", "E121", "C106", "C126", "D269", "C280", "D57",
       "C144", "B153", "E96", "B133", "E131", "B183", "B78", "C118", "E144", "E56",
       "B56", "B169", "C127", "B179", "D169", "E227", "E44", "C101", "D56", "B47",
       "E77", "C182", "E51", "C179", "D53", "D82", "B58", "D101", "D43", "C283",
       "C105", "C156", "D172", "D202", "C78", "C68", "D71", "D54", "E101", "C271",
       "C80", "B243", "B203", "B118", "D130", "C208", "E271", "B268", "D105", "E177",
       "B258", "B253", "B71", "B57", "C57", "C205", "D220", "E220", "C128", "C227",
       "D132", "B106", "D257", "C204", "D271", "C157", "E196", "B93", "B81", "E132",
       "E221", "D201", "C177", "C251", "D96", "D46", "C226", "E256", "E103", "C152",
       "C132", "D129", "E254", "E170", "B283", "E69", "E107", "E145", "B231", "C54",
       "E155", "D55", "B178", "D128", "C122", "B252", "C43", "E232", "C201", "E276",
       "E195", "D156", "C228", "C169", "E68", "E280", "E156", "D144", "B270", "B220",
       "C194", "B232", "B246", "D255", "C247", "B278", "C103", "E152", "B101", "D219",
       "B168", "D218", "D254", "E169", "E93", "D118", "C244", "D168", "B129", "D268",
       "E47", "D151", "D233", "B280", "C56", "C207", "C155", "E154", "E179", "C206",
       "E243", "E202", "D58", "C202", "E228", "D155", "B54", "C221", "B80", "E244",
       "E282", "B44", "C143", "B46", "E180", "E269", "D279", "C181", "D77", "D103",
       "E126", "C94", "B152", "C233", "C269", "E226", "B230", "D152", "C153", "E119",
       "D208", "E45", "C96", "E151", "C107", "C276", "D278", "E143", "B145", "B76",
       "E57", "C246", "B271", "E106", "C145", "E207", "D177", "D119", "D282", "D176",
       "C272", "E245", "B45", "E201", "E247", "B69", "B181", "D204", "E53", "B227",
       "D145", "D195", "E82", "D205", "C104", "C45", "D93", "D270", "C79", "D68",
       "E102", "D83", "C279", "B122", "B144", "D146", "E268", "C93", "C243", "D153",
       "C102", "D183", "B82", "B97", "B156", "E206", "C47", "D245", "E122", "D230",
       "C183", "D78", "C121", "E118", "C219", "D120", "B68", "D72", "D52", "B157",
       "D243", "D246", "B146", "E120", "B206", "E255", "B77", "C108", "C170", "D272",
       "C252", "C230", "B272", "B177", "C203", "D45", "D181", "D108", "E246", "C133",
       "C154", "E168", "E70", "B221", "D122", "B120", "C69", "B151", "B104", "D51",
       "C196", "D231", "E194", "E83", "D95", "D171", "B95", "B218", "D154", "B154",
       "B130", "E158", "D44", "B121", "B282", "D47", "E72", "B193", "C195", "E147",
       "D222", "D194", "D226", "B208", "D258", "C58", "C44", "E222", "B171", "C147",
       "E230", "B72", "D121", "E58", "E178", "C119", "D227", "D232", "B229", "B245",
       "B126", "C158", "E253", "C255", "B131", "C76", "D179", "B107", "B269", "E218",
       "B155", "C81", "E80", "D126", "E130", "C254", "C129", "C131", "E251", "B53",
       "E258", "C268", "C171", "D193", "D104", "D157", "C51", "D196", "E81", "E157",
       "C253", "D276", "C231", "E197", "D147", "C197", "B257", "E43", "E153", "D228",
       "E52", "D127", "B207", "C176", "B279", "B43", "C218", "E283", "C257", "C232",
       "E176", "B195", "C146", "B127", "B83", "E97", "B256", "E172", "B119", "E257",
       "C193", "E204", "B233", "E278", "C282", "B147", "D244", "E129", "B180", "C172",
       "C258", "E105", "C278", "E71", "C178", "B276", "C120", "E108", "C277", "E127",
       "D283", "E55", "D97", "C245", "D281", "B194", "B108", "E270", "D197", "B228",
       "C46", "E95", "B201", "D106", "C130", "B247", "E79", "E76", "D81", "C168",
       "D69", "B143", "B94", "E233", "D131", "B158", "C77", "E94", "E146", "E183",
       "B251", "B277", "E104", "B52", "D203", "B196", "D280", "B176", "E231", "E279",
       "D247", "E181", "E281", "B222", "D182", "C53", "B205", "D107", "B182", "B102",
       "D178", "E277", "E46", "B255", "D70", "B202", "E193", "D143", "B79", "D221",
       "C83", "E205", "E272", "E54", "C270", "C72", "E171", "C220", "C151", "D76",
       "D277", "D180", "B226", "D253", "E128", "B105", "C71", "C97", "D256", "E203"]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18C", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18C", index, "int", 0)

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18C", index, "int", value)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 18C", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 18C", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end



  def schedule_18d(file_id, data) do
    data.schedule_18d
    |> Poison.decode!()
    |> Enum.with_index(15)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      new_map = %{
        "A#{index}" => item["name"],
        "B#{index}" => item["phy_addr"],
        "C#{index}" => item["post_addr"],
        "D#{index}" => item["town"],
        "E#{index}" => item["province"],
        "F#{index}" => item["manager_name"],
        "G#{index}" => item["phone"],
        "I#{index}" => item["email"]
      }
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18D", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18D", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 18D", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end


  def schedule_19(file_id, data) do
    data.schedule_19
    |> Poison.decode!()
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      limit_value = cond do
        is_nil(item["limit"]) -> 0
        is_map(item["limit"]) ->
          Decimal.new(item["limit"]["sign"], item["limit"]["coef"], item["limit"]["exp"])
        is_binary(item["limit"]) ->
          String.replace(item["limit"], ",", "") |> Decimal.new()
        true -> 0
      end
      new_map = %{
        "A#{index}" => item["counter_party"],
        "B#{index}" => limit_value,
        "C#{index}" => item["currency"],
        "D#{index}" => item["clean_secured"],
        "E#{index}" => item["securiy_type"],
      }
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 19", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 19", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 19", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_20a(file_id, data) do
    data.schedule_20a
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
      {_column, row} = String.split_at(index, 1)
        row in ["6","7","8","12","13","15","16","17","19","28","31","53"]
    end)
    |> Enum.map(fn {index, value} ->
       update_20a_cells(index, value)
    end)
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0", "0.00"]->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 20A",
          index,
          "float",
           0.0
      )

      {index, value}  ->
        case Regex.match?(~r/^\d+\.\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 20A", index, "float", Utils.string_to_float(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 20A", index, "int", Utils.string_to_integer(value))
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def update_20a_cells(index, value) do
    case String.split_at(index, 1) do
      {column, "6"} ->
        {update_20a_column(column,"78"), value}

      {column, "7"} ->
        {update_20a_column(column,"98"), value}

      {column, "8"} ->
        {update_20a_column(column,"17"), value}

      {column, "12"} ->
        {update_20a_column(column,"22"), value}

      {column, "13"} ->
        {update_20a_column(column,"24"), value}

      {column, "15"} ->
        {update_20a_column(column,"32"), value}

      {column, "16"} ->
        {update_20a_column(column,"41"), value}

      {column, "17"} ->
        {update_20a_column(column,"34"), value}

      {column, "19"} ->
        {update_20a_column(column,"47"), value}

      {column, "28"} ->
        {update_20a_column(column,"66"), value}

      {column, "31"} ->
        {update_20a_column(column,"77"), value}

      {column, "53"} ->
        {update_20a_column(column,"53"), value}

      _ -> {index, value}

    end
  end

  def update_20a_column(column, row) do
    case column do
      "D" -> "C#{row}"
      "E" -> "D#{row}"
      "F" -> "E#{row}"
      "G" -> "F#{row}"
      "H" -> "G#{row}"
      "I" -> "H#{row}"
      "J" -> "I#{row}"
      "K" -> "J#{row}"
      "L" -> "K#{row}"
      "M" -> "L#{row}"
      _ ->  "N105"
    end
  end

  def schedule_21b(file_id, data) do
    data.schedule_21b
    |> Poison.decode!()
    |> Enum.filter(fn {index, _value} -> index not in ["B21", "B22", "B27"]
    end)
    |> Stream.each(fn
      {"B28", value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 21B",
          "A28",
          "string",
          value
        )

      {"C28", value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 21B",
          "B28",
          "int",
          (if value in ["0", "0.0"], do: 0, else: Utils.string_to_integer(value))
        )

      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 21B",
          index,
          "int",
          (if value in ["0", "0.0"], do: 0, else: Utils.string_to_integer(value))
        )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_22a(file_id, data) do
    data.schedule_22a
    |> Poison.decode!()
    |> Map.get("list")
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.zip_reduce(schedule22a_rows_index(), %{}, fn entries, index, acc ->
       cust_items =
        Enum.with_index(entries, (index + 1))
        |> Enum.reduce(%{}, fn {item, row}, acc ->
          new_map = prepare_params_22a(item, row)
          Map.merge(acc, new_map)
        end)

      cust_items_with_header =
        Map.merge(cust_items, prepare_header_22a(index, entries))

        Map.merge(acc, cust_items_with_header)
    end)
    |> Stream.each(fn
      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 22A", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 22A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 22A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 22A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def prepare_params_22a(entry, index) do
    %{
      "B#{index}" => entry["B"],
      "C#{index}" => entry["C"],
      "D#{index}" => entry["D"],
      "E#{index}" => entry["E"],
      "F#{index}" => entry["F"],
      "G#{index}" => entry["G"],
      "H#{index}" => entry["H"],
      "I#{index}" => entry["I"],
      "K#{index}" => entry["K"],
      "L#{index}" => entry["L"],
      "M#{index}" => entry["M"],
      "N#{index}" => entry["N"]
    }
  end

  def prepare_header_22a(index, entries) do
    [entry | _] = entries

    %{
      "B#{index}" => entry["group_no"],
      "C#{index}" => "",
      "D#{index}" => "",
      "E#{index}" => "",
      "F#{index}" => "",
      "G#{index}" => "",
      "H#{index}" => "",
      "I#{index}" => "",
      "K#{index}" => "",
      "L#{index}" => "",
      "M#{index}" => "",
      "N#{index}" => ""
    }
  end

  def schedule22a_rows_index() do
    [16, 99, 182, 265, 348, 431, 514, 597, 680, 763, 846, 929, 1012, 1095, 1178, 1261, 1344, 1427, 1510, 1593, 1676, 1759, 1842, 1925, 2008, 2091, 2174, 2257, 2340, 2423, 2506, 2589, 2672, 2755, 2838, 2921, 3004, 3087, 3170, 3253, 3336, 3419, 3502, 3585, 3668, 3751, 3834, 3917, 4000, 4083, 4166, 4249, 4332, 4415, 4498, 4581, 4664, 4747, 4830, 4913, 4996]
  end

  def schedule_23a(file_id, data) do
    data.schedule_23a
    |> Poison.decode!()
    |> Enum.zip_reduce(schedule23a_rows_index(), %{}, fn entries, index, acc ->
       cust_items =
        Enum.with_index(entries, (index + 1))
        |> Enum.reduce(%{}, fn {item, row}, acc ->
          new_map = new_cust_deposit(item, row)
          Map.merge(acc, new_map)
        end)

      cust_items_with_header =
        Map.merge(cust_items, cust_deposit_header(index, entries))

        Map.merge(acc, cust_items_with_header)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 23A", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 23A", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 23A", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_24(file_id, data) do
    data.schedule_24
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index in [
                  "F44", "C34", "B41", "F32", "D34", "C20",
                  "C43", "F33", "E34", "I34", "G32", "B44", "C32", "H42", "C17", "B45",
                  "C45", "G33", "C15", "F34", "H41", "D33", "F43", "C25", "G34",
                  "H43","D32", "C44", "C42", "F41", "C41", "B42", "H32", "H44", "E32",
                  "B43", "F42", "E33", "H34", "C33"
                ]
    end)
    |> Stream.each(fn

      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 24", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 24", index, "int", 0)

      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 24", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 24", index, "string", value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def new_cust_deposit(entry, index) do
    %{
      "B#{index}" => entry["cust_name"],
      "C#{index}" => entry["ccy"],
      "D#{index}" => (if entry["demand_amt"] != nil, do: Decimal.new(entry["demand_amt"]["sign"], entry["demand_amt"]["coef"], entry["demand_amt"]["exp"]), else: 0),
      "F#{index}" => (if entry["savings_amt"] != nil, do: Decimal.new(entry["savings_amt"]["sign"], entry["savings_amt"]["coef"], entry["savings_amt"]["exp"]), else: 0),
      "H#{index}" => (if entry["time_amt"] != nil, do: Decimal.new(entry["time_amt"]["sign"], entry["time_amt"]["coef"], entry["time_amt"]["exp"]), else: 0),
      "I#{index}" => (if entry["rate"] not in [nil, ""], do: Decimal.new(entry["rate"]["sign"], entry["rate"]["coef"], entry["rate"]["exp"]), else: 0),
      "J#{index}" => entry["maturity_date"]
    }
  end

  def cust_deposit_header(index, entries) do
    [entry | _] = entries

    %{
      "B#{index}" => entry["cust_name"],
      "C#{index}" => "",
      "D#{index}" => "",
      "F#{index}" => "",
      "H#{index}" => "",
      "I#{index}" => "",
      "J#{index}" => ""
    }
  end

  def schedule23a_rows_index() do
    [16, 97, 178, 259, 340, 421, 502, 583, 664, 745, 826, 907, 988, 1069, 1150, 1231, 1312, 1393, 1474, 1555]
  end

  def schedule_25(file_id, data) do
    data.schedule_25
    |> Poison.decode!()
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 25", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 25", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 25", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_26(file_id, data) do
    data.schedule_26
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(32)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 26", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 26", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 26", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_27(file_id, data) do
    data.schedule_27
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(15)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 27", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 27", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 27", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_27a(file_id, data) do
    data.schedule_27a
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(14)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 27A", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 27A", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 27A", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_28a(file_id, data) do
    data.schedule_28a
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(14)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 28A", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 28A", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 28A", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_28b(file_id, data) do
    data.schedule_28b
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(15)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 28B", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 28B", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 28B", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_29a(file_id, data) do
     data.schedule_29a
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index not in filter_locked_cel_sch29a()
    end)
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0", "0.00"]->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 29A",
          index,
          "string",
          ""
      )
      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 29A",
           index,
          "int",
          Utils.string_to_integer(value)
        )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_30b(file_id, data) do
    data.schedule_30b
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0", "0.00"]->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 30B",
          index,
          "float",
          0.0
      )
      {index, value} ->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 30B",
           index,
          "float",
          Utils.string_to_float(value)
        )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def filter_locked_cel_sch29a() do
    ["C44","D44", "E44", "F44", "G44", "H44", "I44",
    "C45","D45", "E45", "F45", "G45", "H45", "I45",
    "C46","D46", "E46", "F46", "G46", "H46", "I46",
    "C47","D47", "E47", "F47", "G47", "H47", "I47"
    ]
  end

  def schedule_30c(file_id, data) do
    data.schedule_30c
    |> Poison.decode!()
    |> case do
      nil -> []
      data -> data
    end
    |> Enum.filter(fn {index, _value} ->
       index not in filter_30c_dollar_amt()
    end)
    |> Stream.each(fn
      {index, value} when value in ["0", "0.0", "0.00"]->
        Excelizer.Cell.set_cell_value(file_id,
          "Schedule 30C",
          index,
          "float",
          0.0
        )
        {index, value} ->
          Excelizer.Cell.set_cell_value(file_id,
            "Schedule 30C",
             index,
            "float",
            Utils.string_to_float(value)
          )
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def filter_30c_dollar_amt() do
    [
      "B62", "C62", "D62", "E62", "F62", "G62", "H62", "I62", "J62", "K62",
      "B64", "C64", "D64", "E64", "F64", "G64", "H64", "I64", "J64", "K64",
      "B66", "C66", "D66", "E66", "F66", "G66", "H66", "I66", "J66", "K66",
      "B68", "C68", "D68", "E68", "F68", "G68", "H68", "I68", "J68", "K68",
      "B70", "C70", "D70", "E70", "F70", "G70", "H70", "I70", "J70", "K70",
      "B72", "C72", "D72", "E72", "F72", "G72", "H72", "I72", "J72", "K72",
      "B74", "C74", "D74", "E74", "F74", "G74", "H74", "I74", "J74", "K74",
      "B76", "C76", "D76", "E76", "F76", "G76", "H76", "I76", "J76", "K76",
      "B78", "C78", "D78", "E78", "F78", "G78", "H78", "I78", "J78", "K78",
      "B80", "C80", "D80", "E80", "F80", "G80", "H80", "I80", "J80", "K80",
      "B82", "C82", "D82", "E82", "F82", "G82", "H82", "I82", "J82", "K82",
      "B84", "C84", "D84", "E84", "F84", "G84", "H84", "I84", "J84", "K84"
      ]
  end

  def schedule_30d(file_id, data) do
    data.schedule_30d
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      new_map = %{
        "A#{index}" => item["product_type"] || "N/A",
        "B#{index}" => item["currency"],
        "C#{index}" => (if item["receive_amount"] != nil, do: item["receive_amount"] |> string_to_decimal(), else: Decimal.new("0")),
        "D#{index}" => (if item["mtm_base"] != nil, do: item["mtm_base"] |> string_to_decimal(), else: Decimal.new("0")),
        "E#{index}" => item["trade_date"] || "N/A",
        "F#{index}" => item["cash_flow_date"] || "N/A",
        "G#{index}" => item["counter_party"] || "N/A",
        "H#{index}" => item["counter_party_type"] || "N/A",
      }
      totals = data.schedule_30d
                |> Poison.decode!()
                |> Map.get("total")

      extra_map = %{"G3" => totals["forward_total"] |> string_to_decimal(), "G8" => totals["swap_total"] |> string_to_decimal()}
      new_map = Map.merge(new_map, extra_map)

      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 30D", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 30D", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 30D", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_31c(file_id, data) do
    data.schedule_31c
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(15)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["institution_type"],
          "B#{index}" =>  item["relationship"],
          "C#{index}" =>  "K",
          "D#{index}" =>  item["counter_party"],
          "E#{index}" =>  item["cur_classification"],
          "F#{index}" =>  item["new_trade_cur"],
          "G#{index}" =>  item["new_clean_settlement"] |> string_to_decimal(),
          "I#{index}" =>  item["balance_type"],
          "J#{index}" =>  item["new_interest"],
          "K#{index}" =>  item["deal_date"],
          "L#{index}" =>  item["maturity_date"],
          "M#{index}" =>  item["further_details"],
        })
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31C", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31C", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31C", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_31d(file_id, data) do
    data.schedule_31d
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["bank_classification"],
          "B#{index}" =>  item["relationship"],
          "C#{index}" =>  item["institution_type"],
          "D#{index}" =>  item["account_name"],
          "E#{index}" =>  item["cur_classification"],
          "F#{index}" =>  "K",
          "G#{index}" =>  Decimal.new(item["actual_credit"]["sign"], item["actual_credit"]["coef"], item["actual_credit"]["exp"]) |> Decimal.div(Decimal.new("1000")),
          "I#{index}" =>  item["nature_of_amt_owed"],
          "J#{index}" =>  Decimal.new(item["interest_rate"]["sign"], item["interest_rate"]["coef"], item["interest_rate"]["exp"]),
          "K#{index}" =>  item["deal_date"],
          "L#{index}" =>  item["maturity_date"],
          "M#{index}" =>  item["further_details"],
        })
    end)
    |> Stream.each(fn
      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31D", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31D", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31D", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def schedule_31f(file_id, data) do
    data.schedule_31f
    |> Poison.decode!()
    |> Enum.with_index(16)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
      new_map = %{
        "A#{index}" => item["actual_credit_balance_unformatted"],
        "B#{index}" => if(item["currency_code"] == "ZMW", do: "K", else: item["currency_code"]),
        "D#{index}" => item["effective_credit_rate_unformatted"],
        "E#{index}" => item["account_open_date"],
        "F#{index}" => item["account_maturity_date"],
        "G#{index}" => item["actual_credit_balance_unformatted"],
        "H#{index}" => "Performing assets"
      }
      Map.merge(acc, new_map)
    end)
    |> Stream.each(fn
      {index, value} when is_map(value)  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31F", index, "float", Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())

      {index,  %Decimal{} = value} ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31F", index, "float", Decimal.to_float(value))

      {index, value} when is_integer(value) ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31F", index, "int", value)

      {index, value}  ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 31F", index, "string", value)
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  def format_actual_cr_bal(amt) do
    Decimal.abs(amt)
    |> Decimal.div(Decimal.new("1000"))
    |> Decimal.round(0)

  end

  def schedule_32a(file_id, data) do
    data.schedule_32a
    |> Poison.decode!()
    |> Map.get("list")
    |> Enum.with_index(15)
    |> Enum.reduce(%{}, fn  {item, index}, acc ->
        Map.merge(acc, %{
          "A#{index}" => item["name"],
          "B#{index}" =>  item["value"]
        })
    end)
    |> Stream.each(fn
      {index, value} when value in  ["0", "0.0"] ->
        Excelizer.Cell.set_cell_value(file_id, "Schedule 32A", index, "int", 0)
      {index, value}  ->
        case Regex.match?(~r/^\d+$/, String.replace(value, ",", "")) do
          true ->
            Excelizer.Cell.set_cell_value(file_id, "Schedule 32A", index, "int", Utils.string_to_integer(value))
          false ->
            Excelizer.Cell.set_cell_value(file_id,"Schedule 32A", index, "string",value)
        end
    end)
    |> Stream.run()

    Excelizer.Workbook.save(file_id)
  end

  defp string_to_decimal(string) do
    String.replace(string || "0", ",", "") |> Decimal.new()
  end

end
