defmodule MisReports.Workers.BozReq.Schedule04d do

  def perform(item) do

    decoded_item =
      case item.schedule_04d do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    decoded_item = format_scientific_values(decoded_item)
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    # %{
    #   "ReturnKey" => "DEMOBSO1001",
    #   "InstCode" => "#{settings.institution_code}",
    #   "FinYear" => 2024,
    #   "StartDate" => "2024-01-01",
    #   "EndDate" => "2024-01-31",
    #   "ReturnItemsList" => [
    #     %{
    #       "Code" => "BSO1001_00001",
    #       "Value" => "6",
    #       "_dataType" => "NUMERIC"
    #     },
    #     %{
    #       "Code" => "BSO1001_00002",
    #       "Value" => "#{decoded_item["B15"] || "0"}",
    #       "_dataType" => "NUMERIC"
    #     }
    #     # ... (rest of the return items would follow the same pattern)
    #   ]
    # }

    %{
      "returnKey" => "ZM-9XSCH2H9X002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1145_00001",
          "Value" => "#{decoded_item["C16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00002",
          "Value" => "#{decoded_item["D16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00003",
          "Value" => "#{decoded_item["F16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00004",
          "Value" => "#{decoded_item["G16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00005",
          "Value" => "#{decoded_item["H16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00006",
          "Value" => "#{decoded_item["I16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00007",
          "Value" => "#{decoded_item["J16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00008",
          "Value" => "#{decoded_item["K16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00009",
          "Value" => "#{decoded_item["L16"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00010",
          "Value" => "#{decoded_item["C17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00011",
          "Value" => "#{decoded_item["D17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00012",
          "Value" => "#{decoded_item["F17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00013",
          "Value" => "#{decoded_item["G17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00014",
          "Value" => "#{decoded_item["H17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00015",
          "Value" => "#{decoded_item["I17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00016",
          "Value" => "#{decoded_item["J17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00017",
          "Value" => "#{decoded_item["K17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00018",
          "Value" => "#{decoded_item["L17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00019",
          "Value" => "#{decoded_item["C18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00020",
          "Value" => "#{decoded_item["D18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00021",
          "Value" => "#{decoded_item["F18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00022",
          "Value" => "#{decoded_item["G18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00023",
          "Value" => "#{decoded_item["H18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00024",
          "Value" => "#{decoded_item["I18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00025",
          "Value" => "#{decoded_item["J18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00026",
          "Value" => "#{decoded_item["K18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00027",
          "Value" => "#{decoded_item["L18"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00028",
          "Value" => "#{decoded_item["C19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00029",
          "Value" => "#{decoded_item["D19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00030",
          "Value" => "#{decoded_item["F19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00031",
          "Value" => "#{decoded_item["G19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00032",
          "Value" => "#{decoded_item["H19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00033",
          "Value" => "#{decoded_item["I19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00034",
          "Value" => "#{decoded_item["J19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00035",
          "Value" => "#{decoded_item["K19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00036",
          "Value" => "#{decoded_item["L19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00037",
          "Value" => "#{decoded_item["C20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00038",
          "Value" => "#{decoded_item["D20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00039",
          "Value" => "#{decoded_item["F20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00040",
          "Value" => "#{decoded_item["G20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00041",
          "Value" => "#{decoded_item["H20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00042",
          "Value" => "#{decoded_item["I20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00043",
          "Value" => "#{decoded_item["J20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00044",
          "Value" => "#{decoded_item["K20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00045",
          "Value" => "#{decoded_item["L20"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00046",
          "Value" => "#{decoded_item["C21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00047",
          "Value" => "#{decoded_item["D21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00048",
          "Value" => "#{decoded_item["F21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00049",
          "Value" => "#{decoded_item["G21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00050",
          "Value" => "#{decoded_item["H21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00051",
          "Value" => "#{decoded_item["I21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00052",
          "Value" => "#{decoded_item["J21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00053",
          "Value" => "#{decoded_item["K21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00054",
          "Value" => "#{decoded_item["L21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00055",
          "Value" => "#{decoded_item["C22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00056",
          "Value" => "#{decoded_item["D22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00057",
          "Value" => "#{decoded_item["F22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00058",
          "Value" => "#{decoded_item["G22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00059",
          "Value" => "#{decoded_item["H22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00060",
          "Value" => "#{decoded_item["I22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00061",
          "Value" => "#{decoded_item["J22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00062",
          "Value" => "#{decoded_item["K22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00063",
          "Value" => "#{decoded_item["L22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00064",
          "Value" => "#{decoded_item["C23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00065",
          "Value" => "#{decoded_item["D23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00066",
          "Value" => "#{decoded_item["F23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00067",
          "Value" => "#{decoded_item["G23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00068",
          "Value" => "#{decoded_item["H23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00069",
          "Value" => "#{decoded_item["I23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
    %{
          "Code" => "1145_00070",
          "Value" => "#{decoded_item["J23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00071",
          "Value" => "#{decoded_item["K23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00072",
          "Value" => "#{decoded_item["L23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00073",
          "Value" => "#{decoded_item["C24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00074",
          "Value" => "#{decoded_item["D24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00075",
          "Value" => "#{decoded_item["F24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00076",
          "Value" => "#{decoded_item["G24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00077",
          "Value" => "#{decoded_item["H24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00078",
          "Value" => "#{decoded_item["I24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00079",
          "Value" => "#{decoded_item["J24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00080",
          "Value" => "#{decoded_item["K24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00081",
          "Value" => "#{decoded_item["L24"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00082",
          "Value" => "#{decoded_item["C25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00083",
          "Value" => "#{decoded_item["D25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00084",
          "Value" => "#{decoded_item["F25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00085",
          "Value" => "#{decoded_item["G25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00086",
          "Value" => "#{decoded_item["H25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00087",
          "Value" => "#{decoded_item["I25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00088",
          "Value" => "#{decoded_item["J25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00089",
          "Value" => "#{decoded_item["K25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00090",
          "Value" => "#{decoded_item["L25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00091",
          "Value" => "#{decoded_item["C26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00092",
          "Value" => "#{decoded_item["D26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00093",
          "Value" => "#{decoded_item["F26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00094",
          "Value" => "#{decoded_item["G26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00095",
          "Value" => "#{decoded_item["H26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00096",
          "Value" => "#{decoded_item["I26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00097",
          "Value" => "#{decoded_item["J26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00098",
          "Value" => "#{decoded_item["K26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00099",
          "Value" => "#{decoded_item["L26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00100",
          "Value" => "#{decoded_item["C27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00101",
          "Value" => "#{decoded_item["D27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00102",
          "Value" => "#{decoded_item["F27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00103",
          "Value" => "#{decoded_item["G27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00104",
          "Value" => "#{decoded_item["H27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00105",
          "Value" => "#{decoded_item["I27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00106",
          "Value" => "#{decoded_item["J27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00107",
          "Value" => "#{decoded_item["K27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00108",
          "Value" => "#{decoded_item["L27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00109",
          "Value" => "#{decoded_item["C28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00110",
          "Value" => "#{decoded_item["D28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00111",
          "Value" => "#{decoded_item["F28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00112",
          "Value" => "#{decoded_item["G28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00113",
          "Value" => "#{decoded_item["H28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00114",
          "Value" => "#{decoded_item["I28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00115",
          "Value" => "#{decoded_item["J28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00116",
          "Value" => "#{decoded_item["K28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00117",
          "Value" => "#{decoded_item["L28"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00118",
          "Value" => "#{decoded_item["C29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00119",
          "Value" => "#{decoded_item["D29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00120",
          "Value" => "#{decoded_item["F29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00121",
          "Value" => "#{decoded_item["G29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00122",
          "Value" => "#{decoded_item["H29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00123",
          "Value" => "#{decoded_item["I29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00124",
          "Value" => "#{decoded_item["J29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00125",
          "Value" => "#{decoded_item["K29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00126",
          "Value" => "#{decoded_item["L29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00127",
          "Value" => "#{decoded_item["C30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00128",
          "Value" => "#{decoded_item["D30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00129",
          "Value" => "#{decoded_item["F30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00130",
          "Value" => "#{decoded_item["G30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00131",
          "Value" => "#{decoded_item["H30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00132",
          "Value" => "#{decoded_item["I30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00133",
          "Value" => "#{decoded_item["J30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00134",
          "Value" => "#{decoded_item["K30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00135",
          "Value" => "#{decoded_item["L30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00136",
          "Value" => "#{decoded_item["C31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00137",
          "Value" => "#{decoded_item["D31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00138",
          "Value" => "#{decoded_item["F31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00139",
          "Value" => "#{decoded_item["G31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00140",
          "Value" => "#{decoded_item["H31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00141",
          "Value" => "#{decoded_item["I31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00142",
          "Value" => "#{decoded_item["J31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00143",
          "Value" => "#{decoded_item["K31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00144",
          "Value" => "#{decoded_item["L31"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00145",
          "Value" => "#{decoded_item["C32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00146",
          "Value" => "#{decoded_item["D32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00147",
          "Value" => "#{decoded_item["F32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00148",
          "Value" => "#{decoded_item["G32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00149",
          "Value" => "#{decoded_item["H32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00150",
          "Value" => "#{decoded_item["I32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00151",
          "Value" => "#{decoded_item["J32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00152",
          "Value" => "#{decoded_item["K32"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1145_00153",
          "Value" => "#{decoded_item["L32"] || "0"}",
          "_dataType" => "NUMERIC"
        }
      ]

    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> get_in(["list"])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values like "0"
  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
  # If String.to_float fails, try integer conversion
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

 defp convert_to_number(_), do: 0.0
end
