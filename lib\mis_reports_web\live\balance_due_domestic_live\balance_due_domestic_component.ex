defmodule MisReportsWeb.BalanceDueDomesticLive.BalanceDueDomesticComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{Utilities, Repo}
  alias MisReports.Utilities.BalanceDueDomestic
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= Phoenix.View.render(MisReportsWeb.BalanceDueDomesticView, "balance_due_domestic.html", assigns) %>
    </div>
    """
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.BalanceDueDomesticView, "balance_due_domestic.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.BalanceDueDomesticView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end
  @impl true
  def update(%{balance_due_domestic: balance_due_domestic} = assigns, socket) do
    changeset = Utilities.change_balance_due_domestic(balance_due_domestic)

    # Extract URL parameters with default values
    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"balance_due_domestic" => balance_due_domestic_params}, socket) do
    changeset =
      socket.assigns.balance_due_domestic
      |> BalanceDueDomestic.changeset(balance_due_domestic_params)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"balance_due_domestic" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      # Reject action
      "96" ->
        save_approval(socket, :reject, params)

      # Approve action
      "97" ->
        save_approval(socket, :approve, params)

      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg =  "Created new record with report date \"#{params["report_dt"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:balance_due_domestic, BalanceDueDomestic.changeset(%BalanceDueDomestic{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok,%{balance_due_domestic: balance_due_domestic}} ->
       case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of Balance Due Domestic Creation"
             ) do
          {:ok, reference_number} ->
            case Utilities.update_balance_due_domestic(balance_due_domestic, %{reference: reference_number}) do
              {:ok, updated_balance_due_domestic} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Balance due domestic created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update balance due domestic reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Balance due domestic created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end


        {:error, %Ecto.Changeset{} = changeset} ->
          {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_save(socket, :edit, params) do
    balance_due_domestic = socket.assigns.balance_due_domestic
    socket
    |> handle_update(params, balance_due_domestic)
    |> case do
      {:ok, balance_due_domestic} ->
        {:noreply,
          socket
          |> put_flash(:info, "Balance due domestic updated successfully")
          |> push_redirect(to: Routes.balance_due_domestic_index_path(socket, :edit, balance_due_domestic))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, balance_due_domestic) do
    audit_msg = "Updated "

    Ecto.Multi.new()
    |> Ecto.Multi.update(:balance_due_domestic, BalanceDueDomestic.changeset(balance_due_domestic, Map.merge(params, %{"status" => "D", "checker_id" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{balance_due_domestic: balance_due_domestic, audit_log: _user_log}} ->
        {:ok, balance_due_domestic}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp save_aprroval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Balance Due Domestic Rejected"

    # Add action_id from params or use default
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Balance due domestic rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject balance due domestic: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp save_approval(socket, :approve, params) do
    balance_due_domestic = socket.assigns.balance_due_domestic
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Balance Due Domestic Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      BalanceDueDomestic.changeset(balance_due_domestic, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_balance_due_domestic}} ->
        # Then call the workflow
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Balance due domestic approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Balance due domestic approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve balance due domestic")
         |> assign(:changeset, %{balance_due_domestic.changeset | errors: failed_value.errors})}
    end
  end

end
