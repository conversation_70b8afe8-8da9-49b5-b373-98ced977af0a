
defmodule MisReportsWeb.DebtorsBookAnalysisLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component
  import Ecto.Query, warn: false
  alias MisReports.Repo
  on_mount MisReportsWeb.UserLiveAuth
  alias MisReports.SourceData.DebtorsBookAnalysis
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes
  alias MisReportsWeb.LiveHelpers
  alias MisReports.SourceData
  alias MisReportsWeb.UserLiveAuth

  @impl true
  def mount(_params, _session, socket) do
    {:ok,
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000],
      action: nil
    )}
  end

  @impl true
  def handle_params(params, _url, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: socket.assigns.live_action]

    if UserLiveAuth.authorize?(socket, opts) do

    socket =
      socket
        |> assign(:process_id, params["process_id"])
        |> assign(:reference, params["reference"])
        |> assign(:step_id, params["step_id"])

      {:noreply,
        socket
        |> apply_action(socket.assigns.live_action, params)}
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    debtors_book_analysis = SourceData.get_debtors_book_analysis!(id)
    values = get_next_nil(debtors_book_analysis)

    socket
    |> assign(:debtors_book_analysis, debtors_book_analysis)
    |> assign(:values, values)
  end

  defp apply_action(socket, :new, _params) do

    socket
    |> assign(:debtors_book_analysis, %DebtorsBookAnalysis{})
    |> assign(:values, nil)
  end

  defp apply_action(socket, :index, _params), do: list_debtors_book_analysiss(socket)

  defp apply_action(socket, :cmmp_report_listing, _params), do: cmmp_report_list(socket)

  defp apply_action(socket, :update_status, params) do
    reference = socket.assigns.reference || params["reference"]

    case reference do
      nil ->
        socket
        # |> put_flash(:error, "No reference number provided")
        |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))

      reference ->
        debtors_book_analysis = SourceData.get_debtors_book_analysis_by_reference!(reference)
        values = get_next_nil(debtors_book_analysis)

        socket
        |> assign(:debtors_book_analysis, debtors_book_analysis)
        |> assign(:changeset, SourceData.change_debtors_book_analysis(debtors_book_analysis))
        # Explicitly reassign reference
        |> assign(:reference, reference)
        |> assign(:values, values)
    end
  end

  def handle_event("update_status", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_update_status(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end

  @impl true
  def handle_event("delete", params, socket) do
    opts = [module_callback: &__MODULE__.authorize/1, action_name: :update_status]

    if UserLiveAuth.authorize?(socket, opts) do
      handle_delete(params, socket)
    else
      UserLiveAuth.unauthorized(socket)
    end
  end


  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    {:noreply, assign(socket, page_size: page_size) |> list_debtors_book_analysiss()}
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    {:noreply, assign(socket, isearch: isearch) |> list_debtors_book_analysiss()}
  end

  def handle_event("paginate", %{"page" => page}, socket) do
    {:noreply, assign(socket, page: page) |> list_debtors_book_analysiss()}
  end

  def audit_log(multi, user_id, audit_msg) do
    Ecto.Multi.run(multi, :audit_log, fn repo, _changes_so_far ->
      MisReports.Audit.UserLog.changeset(%MisReports.Audit.UserLog{}, %{user_id: user_id, activity: audit_msg})
      |> repo.insert()
    end)
  end

  def traverse_errors(errors), do: for({key, {msg, _opts}} <- errors, do: "#{key} #{msg}")


  def handle_update_status(params, socket) do
    id = params["id"]
    status = params["status"]
    debtors_book_analysis = SourceData.get_debtors_book_analysis!(id)
    audit_msg = "changed status for debtor book analysis: \"#{debtors_book_analysis.type}\" with report date \"#{debtors_book_analysis.report_date}\", to: #{status}"
    current_user = socket.assigns.current_user

    if current_user.id == debtors_book_analysis.maker_id do
      {:noreply,
       socket
       |> put_flash(:error, "You cannot approve your own submission. Please have another user review and activate it.")
       |> push_redirect(to: Routes.debtors_book_analysis_index_path(socket, :index))}
    else
      audit_msg = "changed status for debtor book analysis: \"#{debtors_book_analysis.type}\" with report date \"#{debtors_book_analysis.report_date}\", to: #{status}"


    Ecto.Multi.new()
    |> Ecto.Multi.update(
    :update,
    DebtorsBookAnalysis.changeset(debtors_book_analysis, %{status: status, checker_id: current_user.id})
    )
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{update: _debtors_book_analysis, audit_log: _audit_log}} ->
        {:noreply,
        socket
        |> put_flash(:info, "Operation Succesfull!")
        |> push_redirect(
          to: Routes.debtors_book_analysis_index_path(socket, :index)
        )}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
      end
    end
  end

  def handle_delete(params, socket) do
    id = params["id"]
    debtors_book_analysis = SourceData.get_debtors_book_analysis!(id)
    audit_msg = "Deleted debtor book analysis: \"#{debtors_book_analysis.type}\" with report date \"#{debtors_book_analysis.report_date}\"  "
    current_user = socket.assigns.current_user

    Ecto.Multi.new()
    |> Ecto.Multi.delete(:del_debtors_book_analysis, debtors_book_analysis)
    |> UserController.audit_log(current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
    {:ok, %{del_debtors_book_analysis: _debtors_book_analysis, audit_log: _audit_log}} ->
      {:noreply,
      socket
      |> put_flash(:info, "debtor book analysi deleted successfully!")
      |> push_redirect(to: Routes.debtors_book_analysis_index_path(socket, :index))}

    {:error, failed_value} ->
      {:error, failed_value}
    end
  end

  def get_next_nil(debtors_book_analysis) do
    Map.from_struct(debtors_book_analysis)
    |> Map.take([:allowances_for_losses, :num_of_acc_with_allowances])
    |> MisReports.Workers.Utils.to_atomic_map()
  end


  defp list_debtors_book_analysiss(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          SourceData.list_tbl_debtors_book_analysis(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, stats: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  defp cmmp_report_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          MisReports.Prudentials.cmmp_report_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, report_items: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(report_type: "cmmp_report_list")
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end


  def put_conn_user(socket) do
    Map.update(socket, :assigns, %{}, fn assigns ->
      Map.put(assigns, :user, assigns.current_user)
    end)
  end

  def authorize(socket) do
    case socket.assigns.action_name do
      act when act in ~w(new)a ->
        {"debtors_book_analysis", "new"}

      act when act in ~w(edit)a ->
        {"debtors_book_analysis", "edit"}

      act when act in ~w(update_status)a ->
        {"debtors_book_analysis", "update_status"}

      act when act in ~w(delete)a ->
        {"debtors_book_analysis", "delete"}

      act when act in ~w(index)a ->
        {"debtors_book_analysis", "index"}

      _ ->
        {"debtors_book_analysis", "unknown"}
    end
  end
end
