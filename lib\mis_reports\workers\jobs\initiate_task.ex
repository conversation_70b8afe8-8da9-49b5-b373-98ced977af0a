defmodule MisReports.Workers.Jobs.InitiateTask do
  require <PERSON><PERSON>
  alias MisReport<PERSON>.Workflow

  # MisReports.Workers.Jobs.InitiateTask.perform(1000)
  def perform(process_id) do
    {:ok, reference_number} = Workflow.ensure_unique_reference()

    {:ok, reference} =
      MisReports.Workflow.call_workflow(
        reference_number,
        process_id,
        "2",
        81,
        "",
        "Creation of the Main Task "
      )

    # Initiate the sub tasks based on process_id
    initiate_sub_tasks(process_id, reference)

    # Create the weekly export record
    # MisReports.Services.WeeklyExportService.create_export_record(process_id, reference)

    # Run the SaveWeeklyReport job to generate the report data
    # MisReports.Workers.Jobs.SaveWeeklyReport.perform()

    # Return the reference for further use
    {:ok, reference}
  end

  defp initiate_sub_tasks(process_id, prev_reference) do
    subtasks = case process_id do
      1000 -> get_prudential_SubTasks()  # Prudential
      2000 -> get_weekly_SubTasks()  # Core Liquid Assets
      3000 -> get_forex_SubTasks()   # Weekly Forex
      _ -> []
    end

    Enum.each(subtasks, fn sub_task_id ->
      {:ok, sub_reference} = Workflow.ensure_unique_reference()

      MisReports.Workflow.call_workflow(
        sub_reference,
        sub_task_id,
        "2",
        81,
        "",
        "",
        "Creation of required Tasks",
        prev_reference
      )
    end)
  end

  defp get_prudential_SubTasks() do
    # get the sub tasks
    [
      # 1003,
      # 1004,
      # 1005,
      # 1006,
      # 1011,
      # 1012,
      # 1013,
      # 1014,
      # 1015,
      #1016,
      # 1017,
      # 1018,
      # 1019,
      # 1026,
      # 1027,
      # 1029,
      # 1030,
      # 1032,
      # 1033,
      # 1034,
      # 1036,
      # 1037,
      # 1041,
      # 1040,
      # 1039,
      # 1056,
      # 1109,
      # 1110,
      # 1111,
      # 1112,
      # 1113,
      # 1114,
      # 1115,
      # 1116,
      1118
    ]
  end

  defp get_weekly_SubTasks() do
    # Core Liquid Assets subtasks
    [



      # 1108,
      # 1023,
      # 1024,
      # 1010,
      # 1106,
        1020
    ]
  end

  # Add new function for forex subtasks
  defp get_forex_SubTasks() do
    [
      # 1022,
       #1025,
       #1107
      # Add other forex-specific subtasks
    ]
  end


end
