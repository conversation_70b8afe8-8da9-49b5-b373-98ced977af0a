defmodule MisReports.Workers.BozReq.Schedule18c do
  def perform(item) do
    decoded_item =
      case item.schedule_18c do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    decoded_item = format_scientific_values(decoded_item)
    settings = MisReports.Utilities.get_comapany_settings_params()

    # Build the full request payload
    %{
      "ReturnKey" => "ZM-2KSCH18C2K003",
      "InstCode" => settings.institution_code,
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "EndDate" => item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "ReturnItemsList" => return_items_list(decoded_item),
      "DynamicItemsList" => []
    }
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, _} -> Decimal.from_float(num)
      :error -> Decimal.new(0)
    end
  rescue
    _ -> Decimal.new(0)
  end

  defp convert_to_number(_), do: Decimal.new(0)

  defp get_value(item, key) do
    value = Map.get(item, key, "0")
    case value do
      %{"sign" => _, "exp" => _, "coef" => _} = sci -> convert_to_number(sci)
      v when is_binary(v) -> convert_to_number(v)
      _ -> Decimal.new(0)
    end
  end

  defp return_items_list(decoded_item) do
    [
      %{"Code" => "1205_00001", "Value" => "#{get_value(decoded_item, "B43")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00002", "Value" => "#{get_value(decoded_item, "C43")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00003", "Value" => "#{get_value(decoded_item, "D43")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00004", "Value" => "#{get_value(decoded_item, "E43")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00005", "Value" => "#{get_value(decoded_item, "B19")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00006", "Value" => "#{get_value(decoded_item, "B44")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00007", "Value" => "#{get_value(decoded_item, "C44")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00008", "Value" => "#{get_value(decoded_item, "D44")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00009", "Value" => "#{get_value(decoded_item, "E44")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00010", "Value" => "#{get_value(decoded_item, "F44")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00011", "Value" => "#{get_value(decoded_item, "B45")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00012", "Value" => "#{get_value(decoded_item, "C45")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00013", "Value" => "#{get_value(decoded_item, "D45")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00014", "Value" => "#{get_value(decoded_item, "E45")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00015", "Value" => "#{get_value(decoded_item, "F45")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00016", "Value" => "#{get_value(decoded_item, "B46")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00017", "Value" => "#{get_value(decoded_item, "C46")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00018", "Value" => "#{get_value(decoded_item, "D46")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00019", "Value" => "#{get_value(decoded_item, "E46")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00020", "Value" => "#{get_value(decoded_item, "F46")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00021", "Value" => "#{get_value(decoded_item, "B47")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00022", "Value" => "#{get_value(decoded_item, "C47")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00023", "Value" => "#{get_value(decoded_item, "D47")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00024", "Value" => "#{get_value(decoded_item, "E47")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00025", "Value" => "#{get_value(decoded_item, "F47")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00026", "Value" => "#{get_value(decoded_item, "B49")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00027", "Value" => "#{get_value(decoded_item, "C49")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00028", "Value" => "#{get_value(decoded_item, "D49")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00029", "Value" => "#{get_value(decoded_item, "E49")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00030", "Value" => "#{get_value(decoded_item, "F49")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00031", "Value" => "#{get_value(decoded_item, "B51")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00032", "Value" => "#{get_value(decoded_item, "C51")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00033", "Value" => "#{get_value(decoded_item, "D51")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00034", "Value" => "#{get_value(decoded_item, "E51")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00035", "Value" => "#{get_value(decoded_item, "F51")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00036", "Value" => "#{get_value(decoded_item, "B52")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00037", "Value" => "#{get_value(decoded_item, "C52")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00038", "Value" => "#{get_value(decoded_item, "D52")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00039", "Value" => "#{get_value(decoded_item, "E52")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00040", "Value" => "#{get_value(decoded_item, "F52")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00041", "Value" => "#{get_value(decoded_item, "B53")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00042", "Value" => "#{get_value(decoded_item, "C53")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00043", "Value" => "#{get_value(decoded_item, "D53")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00044", "Value" => "#{get_value(decoded_item, "E53")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00045", "Value" => "#{get_value(decoded_item, "F53")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00046", "Value" => "#{get_value(decoded_item, "B54")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00047", "Value" => "#{get_value(decoded_item, "C54")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00048", "Value" => "#{get_value(decoded_item, "D54")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00049", "Value" => "#{get_value(decoded_item, "E54")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00050", "Value" => "#{get_value(decoded_item, "F54")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00051", "Value" => "#{get_value(decoded_item, "B55")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00052", "Value" => "#{get_value(decoded_item, "C55")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00053", "Value" => "#{get_value(decoded_item, "D55")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00054", "Value" => "#{get_value(decoded_item, "E55")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00055", "Value" => "#{get_value(decoded_item, "F55")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00056", "Value" => "#{get_value(decoded_item, "B56")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00057", "Value" => "#{get_value(decoded_item, "C56")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00058", "Value" => "#{get_value(decoded_item, "D56")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00059", "Value" => "#{get_value(decoded_item, "E56")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00060", "Value" => "#{get_value(decoded_item, "F56")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00061", "Value" => "#{get_value(decoded_item, "B57")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00062", "Value" => "#{get_value(decoded_item, "C57")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00063", "Value" => "#{get_value(decoded_item, "D57")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00064", "Value" => "#{get_value(decoded_item, "E57")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00065", "Value" => "#{get_value(decoded_item, "F57")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00066", "Value" => "#{get_value(decoded_item, "B58")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00067", "Value" => "#{get_value(decoded_item, "C58")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00068", "Value" => "#{get_value(decoded_item, "D58")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00069", "Value" => "#{get_value(decoded_item, "E58")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00070", "Value" => "#{get_value(decoded_item, "F58")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00071", "Value" => "#{get_value(decoded_item, "B60")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00072", "Value" => "#{get_value(decoded_item, "C60")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00073", "Value" => "#{get_value(decoded_item, "D60")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00074", "Value" => "#{get_value(decoded_item, "E60")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00075", "Value" => "#{get_value(decoded_item, "F60")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00076", "Value" => "#{get_value(decoded_item, "B68")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00077", "Value" => "#{get_value(decoded_item, "C68")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00078", "Value" => "#{get_value(decoded_item, "D68")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00079", "Value" => "#{get_value(decoded_item, "E68")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00080", "Value" => "#{get_value(decoded_item, "F68")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00081", "Value" => "#{get_value(decoded_item, "B69")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00082", "Value" => "#{get_value(decoded_item, "C69")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00083", "Value" => "#{get_value(decoded_item, "D69")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00084", "Value" => "#{get_value(decoded_item, "E69")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00085", "Value" => "#{get_value(decoded_item, "F69")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00086", "Value" => "#{get_value(decoded_item, "B71")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00087", "Value" => "#{get_value(decoded_item, "C71")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00088", "Value" => "#{get_value(decoded_item, "D71")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00089", "Value" => "#{get_value(decoded_item, "E71")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00090", "Value" => "#{get_value(decoded_item, "F71")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00091", "Value" => "#{get_value(decoded_item, "B72")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00092", "Value" => "#{get_value(decoded_item, "C72")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00093", "Value" => "#{get_value(decoded_item, "D72")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00094", "Value" => "#{get_value(decoded_item, "E72")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00095", "Value" => "#{get_value(decoded_item, "F72")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00096", "Value" => "#{get_value(decoded_item, "B74")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00097", "Value" => "#{get_value(decoded_item, "C74")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00098", "Value" => "#{get_value(decoded_item, "D74")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00099", "Value" => "#{get_value(decoded_item, "E74")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00100", "Value" => "#{get_value(decoded_item, "F74")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00101", "Value" => "#{get_value(decoded_item, "B76")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00102", "Value" => "#{get_value(decoded_item, "C76")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00103", "Value" => "#{get_value(decoded_item, "D76")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00104", "Value" => "#{get_value(decoded_item, "E76")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00105", "Value" => "#{get_value(decoded_item, "F76")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00106", "Value" => "#{get_value(decoded_item, "B77")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00107", "Value" => "#{get_value(decoded_item, "C77")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00108", "Value" => "#{get_value(decoded_item, "D77")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00109", "Value" => "#{get_value(decoded_item, "E77")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00110", "Value" => "#{get_value(decoded_item, "F77")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00111", "Value" => "#{get_value(decoded_item, "B78")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00112", "Value" => "#{get_value(decoded_item, "C78")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00113", "Value" => "#{get_value(decoded_item, "D78")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00114", "Value" => "#{get_value(decoded_item, "E78")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00115", "Value" => "#{get_value(decoded_item, "F78")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00116", "Value" => "#{get_value(decoded_item, "B79")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00117", "Value" => "#{get_value(decoded_item, "C79")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00118", "Value" => "#{get_value(decoded_item, "D79")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00119", "Value" => "#{get_value(decoded_item, "E79")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00120", "Value" => "#{get_value(decoded_item, "F79")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00121", "Value" => "#{get_value(decoded_item, "B80")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00122", "Value" => "#{get_value(decoded_item, "C80")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00123", "Value" => "#{get_value(decoded_item, "D80")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00124", "Value" => "#{get_value(decoded_item, "E80")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00125", "Value" => "#{get_value(decoded_item, "F80")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00126", "Value" => "#{get_value(decoded_item, "B81")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00127", "Value" => "#{get_value(decoded_item, "C81")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00128", "Value" => "#{get_value(decoded_item, "D81")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00129", "Value" => "#{get_value(decoded_item, "E81")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00130", "Value" => "#{get_value(decoded_item, "F81")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00131", "Value" => "#{get_value(decoded_item, "B82")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00132", "Value" => "#{get_value(decoded_item, "C82")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00133", "Value" => "#{get_value(decoded_item, "D82")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00134", "Value" => "#{get_value(decoded_item, "E82")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00135", "Value" => "#{get_value(decoded_item, "F82")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00136", "Value" => "#{get_value(decoded_item, "B83")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00137", "Value" => "#{get_value(decoded_item, "C83")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00138", "Value" => "#{get_value(decoded_item, "D83")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00139", "Value" => "#{get_value(decoded_item, "E83")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00140", "Value" => "#{get_value(decoded_item, "F83")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00141", "Value" => "#{get_value(decoded_item, "B85")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00142", "Value" => "#{get_value(decoded_item, "C85")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00143", "Value" => "#{get_value(decoded_item, "D85")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00144", "Value" => "#{get_value(decoded_item, "E85")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00145", "Value" => "#{get_value(decoded_item, "F85")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00146", "Value" => "#{get_value(decoded_item, "B93")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00147", "Value" => "#{get_value(decoded_item, "C93")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00148", "Value" => "#{get_value(decoded_item, "D93")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00149", "Value" => "#{get_value(decoded_item, "E93")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00150", "Value" => "#{get_value(decoded_item, "F93")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00151", "Value" => "#{get_value(decoded_item, "B94")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00152", "Value" => "#{get_value(decoded_item, "C94")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00153", "Value" => "#{get_value(decoded_item, "D94")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00154", "Value" => "#{get_value(decoded_item, "E94")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00155", "Value" => "#{get_value(decoded_item, "F94")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00156", "Value" => "#{get_value(decoded_item, "B95")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00157", "Value" => "#{get_value(decoded_item, "C95")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00158", "Value" => "#{get_value(decoded_item, "D95")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00159", "Value" => "#{get_value(decoded_item, "E95")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00160", "Value" => "#{get_value(decoded_item, "F95")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00161", "Value" => "#{get_value(decoded_item, "B96")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00162", "Value" => "#{get_value(decoded_item, "C96")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00163", "Value" => "#{get_value(decoded_item, "D96")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00164", "Value" => "#{get_value(decoded_item, "E96")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00165", "Value" => "#{get_value(decoded_item, "F96")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00166", "Value" => "#{get_value(decoded_item, "B97")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00167", "Value" => "#{get_value(decoded_item, "C97")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00168", "Value" => "#{get_value(decoded_item, "D97")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00169", "Value" => "#{get_value(decoded_item, "E97")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00170", "Value" => "#{get_value(decoded_item, "F97")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00171", "Value" => "#{get_value(decoded_item, "B99")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00172", "Value" => "#{get_value(decoded_item, "C99")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00173", "Value" => "#{get_value(decoded_item, "D99")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00174", "Value" => "#{get_value(decoded_item, "E99")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00175", "Value" => "#{get_value(decoded_item, "F99")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00176", "Value" => "#{get_value(decoded_item, "B101")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00177", "Value" => "#{get_value(decoded_item, "C101")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00178", "Value" => "#{get_value(decoded_item, "D101")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00179", "Value" => "#{get_value(decoded_item, "E101")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00180", "Value" => "#{get_value(decoded_item, "F101")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00181", "Value" => "#{get_value(decoded_item, "B102")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00182", "Value" => "#{get_value(decoded_item, "C102")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00183", "Value" => "#{get_value(decoded_item, "D102")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00184", "Value" => "#{get_value(decoded_item, "E102")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00185", "Value" => "#{get_value(decoded_item, "F102")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00186", "Value" => "#{get_value(decoded_item, "B103")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00187", "Value" => "#{get_value(decoded_item, "C103")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00188", "Value" => "#{get_value(decoded_item, "D103")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00189", "Value" => "#{get_value(decoded_item, "E103")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00190", "Value" => "#{get_value(decoded_item, "F103")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00191", "Value" => "#{get_value(decoded_item, "B104")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00192", "Value" => "#{get_value(decoded_item, "C104")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00193", "Value" => "#{get_value(decoded_item, "D104")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00194", "Value" => "#{get_value(decoded_item, "E104")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00195", "Value" => "#{get_value(decoded_item, "F104")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00196", "Value" => "#{get_value(decoded_item, "B105")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00197", "Value" => "#{get_value(decoded_item, "C105")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00198", "Value" => "#{get_value(decoded_item, "D105")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00199", "Value" => "#{get_value(decoded_item, "E105")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00200", "Value" => "#{get_value(decoded_item, "F105")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00201", "Value" => "#{get_value(decoded_item, "B106")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00202", "Value" => "#{get_value(decoded_item, "C106")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00203", "Value" => "#{get_value(decoded_item, "D106")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00204", "Value" => "#{get_value(decoded_item, "E106")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00205", "Value" => "#{get_value(decoded_item, "F106")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00206", "Value" => "#{get_value(decoded_item, "B107")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00207", "Value" => "#{get_value(decoded_item, "C107")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00208", "Value" => "#{get_value(decoded_item, "D107")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00209", "Value" => "#{get_value(decoded_item, "E107")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00210", "Value" => "#{get_value(decoded_item, "F107")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00211", "Value" => "#{get_value(decoded_item, "B108")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00212", "Value" => "#{get_value(decoded_item, "C108")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00213", "Value" => "#{get_value(decoded_item, "D108")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00214", "Value" => "#{get_value(decoded_item, "E108")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00215", "Value" => "#{get_value(decoded_item, "F108")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00216", "Value" => "#{get_value(decoded_item, "B110")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00217", "Value" => "#{get_value(decoded_item, "C110")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00218", "Value" => "#{get_value(decoded_item, "D110")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00219", "Value" => "#{get_value(decoded_item, "E110")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00220", "Value" => "#{get_value(decoded_item, "F110")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00221", "Value" => "#{get_value(decoded_item, "B118")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00222", "Value" => "#{get_value(decoded_item, "C118")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00223", "Value" => "#{get_value(decoded_item, "D118")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00224", "Value" => "#{get_value(decoded_item, "E118")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00225", "Value" => "#{get_value(decoded_item, "F118")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00226", "Value" => "#{get_value(decoded_item, "B119")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00227", "Value" => "#{get_value(decoded_item, "C119")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00228", "Value" => "#{get_value(decoded_item, "D119")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00229", "Value" => "#{get_value(decoded_item, "E119")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00230", "Value" => "#{get_value(decoded_item, "F119")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00231", "Value" => "#{get_value(decoded_item, "B120")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00232", "Value" => "#{get_value(decoded_item, "C120")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00233", "Value" => "#{get_value(decoded_item, "D120")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00234", "Value" => "#{get_value(decoded_item, "E120")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00235", "Value" => "#{get_value(decoded_item, "F120")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00236", "Value" => "#{get_value(decoded_item, "B121")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00237", "Value" => "#{get_value(decoded_item, "C121")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00238", "Value" => "#{get_value(decoded_item, "D121")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00239", "Value" => "#{get_value(decoded_item, "E121")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00240", "Value" => "#{get_value(decoded_item, "F121")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00241", "Value" => "#{get_value(decoded_item, "B122")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00242", "Value" => "#{get_value(decoded_item, "C122")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00243", "Value" => "#{get_value(decoded_item, "D122")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00244", "Value" => "#{get_value(decoded_item, "E122")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00245", "Value" => "#{get_value(decoded_item, "F122")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00246", "Value" => "#{get_value(decoded_item, "B124")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00247", "Value" => "#{get_value(decoded_item, "C124")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00248", "Value" => "#{get_value(decoded_item, "D124")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00249", "Value" => "#{get_value(decoded_item, "E124")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00250", "Value" => "#{get_value(decoded_item, "F124")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00251", "Value" => "#{get_value(decoded_item, "B126")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00252", "Value" => "#{get_value(decoded_item, "C126")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00253", "Value" => "#{get_value(decoded_item, "D126")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00254", "Value" => "#{get_value(decoded_item, "E126")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00255", "Value" => "#{get_value(decoded_item, "F126")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00256", "Value" => "#{get_value(decoded_item, "B127")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00257", "Value" => "#{get_value(decoded_item, "B128")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00258", "Value" => "#{get_value(decoded_item, "C128")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00259", "Value" => "#{get_value(decoded_item, "D128")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00260", "Value" => "#{get_value(decoded_item, "E128")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00261", "Value" => "#{get_value(decoded_item, "F128")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00262", "Value" => "#{get_value(decoded_item, "B129")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00263", "Value" => "#{get_value(decoded_item, "C129")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00264", "Value" => "#{get_value(decoded_item, "D129")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00265", "Value" => "#{get_value(decoded_item, "E129")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00266", "Value" => "#{get_value(decoded_item, "F129")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00267", "Value" => "#{get_value(decoded_item, "B130")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00268", "Value" => "#{get_value(decoded_item, "C130")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00269", "Value" => "#{get_value(decoded_item, "D130")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00270", "Value" => "#{get_value(decoded_item, "E130")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00271", "Value" => "#{get_value(decoded_item, "F130")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00272", "Value" => "#{get_value(decoded_item, "B131")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00273", "Value" => "#{get_value(decoded_item, "C131")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00274", "Value" => "#{get_value(decoded_item, "D131")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00275", "Value" => "#{get_value(decoded_item, "E131")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00276", "Value" => "#{get_value(decoded_item, "F131")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00277", "Value" => "#{get_value(decoded_item, "B132")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00278", "Value" => "#{get_value(decoded_item, "C132")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00279", "Value" => "#{get_value(decoded_item, "D132")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00280", "Value" => "#{get_value(decoded_item, "E132")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00281", "Value" => "#{get_value(decoded_item, "F132")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00282", "Value" => "#{get_value(decoded_item, "B133")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00283", "Value" => "#{get_value(decoded_item, "C133")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00284", "Value" => "#{get_value(decoded_item, "D133")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00285", "Value" => "#{get_value(decoded_item, "E133")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00286", "Value" => "#{get_value(decoded_item, "F133")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00287", "Value" => "#{get_value(decoded_item, "B143")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00288", "Value" => "#{get_value(decoded_item, "C143")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00289", "Value" => "#{get_value(decoded_item, "D143")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00290", "Value" => "#{get_value(decoded_item, "E143")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00291", "Value" => "#{get_value(decoded_item, "F143")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00292", "Value" => "#{get_value(decoded_item, "B144")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00293", "Value" => "#{get_value(decoded_item, "C144")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00294", "Value" => "#{get_value(decoded_item, "D144")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00295", "Value" => "#{get_value(decoded_item, "E144")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00296", "Value" => "#{get_value(decoded_item, "F144")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00297", "Value" => "#{get_value(decoded_item, "B145")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00298", "Value" => "#{get_value(decoded_item, "B146")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00299", "Value" => "#{get_value(decoded_item, "C146")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00300", "Value" => "#{get_value(decoded_item, "D146")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00301", "Value" => "#{get_value(decoded_item, "E146")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00302", "Value" => "#{get_value(decoded_item, "F146")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00303", "Value" => "#{get_value(decoded_item, "B147")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00304", "Value" => "#{get_value(decoded_item, "C147")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00305", "Value" => "#{get_value(decoded_item, "D147")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00306", "Value" => "#{get_value(decoded_item, "E147")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00307", "Value" => "#{get_value(decoded_item, "F147")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00308", "Value" => "#{get_value(decoded_item, "B149")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00309", "Value" => "#{get_value(decoded_item, "C149")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00310", "Value" => "#{get_value(decoded_item, "D149")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00311", "Value" => "#{get_value(decoded_item, "E149")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00312", "Value" => "#{get_value(decoded_item, "F149")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00313", "Value" => "#{get_value(decoded_item, "B151")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00314", "Value" => "#{get_value(decoded_item, "C151")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00315", "Value" => "#{get_value(decoded_item, "D151")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00316", "Value" => "#{get_value(decoded_item, "E151")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00317", "Value" => "#{get_value(decoded_item, "F151")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00318", "Value" => "#{get_value(decoded_item, "B152")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00319", "Value" => "#{get_value(decoded_item, "C152")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00320", "Value" => "#{get_value(decoded_item, "D152")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00321", "Value" => "#{get_value(decoded_item, "E152")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00322", "Value" => "#{get_value(decoded_item, "F152")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00323", "Value" => "#{get_value(decoded_item, "B153")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00324", "Value" => "#{get_value(decoded_item, "C153")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00325", "Value" => "#{get_value(decoded_item, "D153")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00326", "Value" => "#{get_value(decoded_item, "E153")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00327", "Value" => "#{get_value(decoded_item, "F153")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00328", "Value" => "#{get_value(decoded_item, "B154")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00329", "Value" => "#{get_value(decoded_item, "C154")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00330", "Value" => "#{get_value(decoded_item, "D154")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00331", "Value" => "#{get_value(decoded_item, "E154")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00332", "Value" => "#{get_value(decoded_item, "F154")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00333", "Value" => "#{get_value(decoded_item, "B155")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00334", "Value" => "#{get_value(decoded_item, "C155")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00335", "Value" => "#{get_value(decoded_item, "D155")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00336", "Value" => "#{get_value(decoded_item, "E155")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00337", "Value" => "#{get_value(decoded_item, "F155")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00338", "Value" => "#{get_value(decoded_item, "B156")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00339", "Value" => "#{get_value(decoded_item, "C156")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00340", "Value" => "#{get_value(decoded_item, "D156")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00341", "Value" => "#{get_value(decoded_item, "E156")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00342", "Value" => "#{get_value(decoded_item, "F156")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00343", "Value" => "#{get_value(decoded_item, "B157")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00344", "Value" => "#{get_value(decoded_item, "C157")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00345", "Value" => "#{get_value(decoded_item, "D157")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00346", "Value" => "#{get_value(decoded_item, "E157")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00347", "Value" => "#{get_value(decoded_item, "F157")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00348", "Value" => "#{get_value(decoded_item, "B158")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00349", "Value" => "#{get_value(decoded_item, "C158")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00350", "Value" => "#{get_value(decoded_item, "D158")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00351", "Value" => "#{get_value(decoded_item, "E158")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00352", "Value" => "#{get_value(decoded_item, "F158")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00353", "Value" => "#{get_value(decoded_item, "B160")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00354", "Value" => "#{get_value(decoded_item, "C160")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00355", "Value" => "#{get_value(decoded_item, "D160")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00356", "Value" => "#{get_value(decoded_item, "E160")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00357", "Value" => "#{get_value(decoded_item, "F160")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00358", "Value" => "#{get_value(decoded_item, "B168")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00359", "Value" => "#{get_value(decoded_item, "C168")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00360", "Value" => "#{get_value(decoded_item, "D168")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00361", "Value" => "#{get_value(decoded_item, "E168")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00362", "Value" => "#{get_value(decoded_item, "F168")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00363", "Value" => "#{get_value(decoded_item, "B170")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00364", "Value" => "#{get_value(decoded_item, "C170")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00365", "Value" => "#{get_value(decoded_item, "D170")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00366", "Value" => "#{get_value(decoded_item, "E170")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00367", "Value" => "#{get_value(decoded_item, "F170")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00368", "Value" => "#{get_value(decoded_item, "B171")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00369", "Value" => "#{get_value(decoded_item, "C171")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00370", "Value" => "#{get_value(decoded_item, "D171")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00371", "Value" => "#{get_value(decoded_item, "E171")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00372", "Value" => "#{get_value(decoded_item, "F171")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00373", "Value" => "#{get_value(decoded_item, "B172")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00374", "Value" => "#{get_value(decoded_item, "C172")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00375", "Value" => "#{get_value(decoded_item, "D172")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00376", "Value" => "#{get_value(decoded_item, "E172")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00377", "Value" => "#{get_value(decoded_item, "F172")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00378", "Value" => "#{get_value(decoded_item, "B174")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00379", "Value" => "#{get_value(decoded_item, "C174")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00380", "Value" => "#{get_value(decoded_item, "D174")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00381", "Value" => "#{get_value(decoded_item, "E174")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00382", "Value" => "#{get_value(decoded_item, "F174")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00383", "Value" => "#{get_value(decoded_item, "B176")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00384", "Value" => "#{get_value(decoded_item, "C176")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00385", "Value" => "#{get_value(decoded_item, "D176")}", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00404", "Value" => "#{decoded_item["C180"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00405", "Value" => "#{decoded_item["D180"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00406", "Value" => "#{decoded_item["E180"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00407", "Value" => "#{decoded_item["F180"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00408", "Value" => "#{decoded_item["B181"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00409", "Value" => "#{decoded_item["C181"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00410", "Value" => "#{decoded_item["D181"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00411", "Value" => "#{decoded_item["E181"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00412", "Value" => "#{decoded_item["F181"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00413", "Value" => "#{decoded_item["B182"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00414", "Value" => "#{decoded_item["C182"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00415", "Value" => "#{decoded_item["D182"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00416", "Value" => "#{decoded_item["E182"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00417", "Value" => "#{decoded_item["F182"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00418", "Value" => "#{decoded_item["B183"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00419", "Value" => "#{decoded_item["C183"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00420", "Value" => "#{decoded_item["D183"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00421", "Value" => "#{decoded_item["E183"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00422", "Value" => "#{decoded_item["F183"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00423", "Value" => "#{decoded_item["B185"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00424", "Value" => "#{decoded_item["C185"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00425", "Value" => "#{decoded_item["D185"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00426", "Value" => "#{decoded_item["E185"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00427", "Value" => "#{decoded_item["F185"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00428", "Value" => "#{decoded_item["B193"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00429", "Value" => "#{decoded_item["C193"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00430", "Value" => "#{decoded_item["D193"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00431", "Value" => "#{decoded_item["E193"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00432", "Value" => "#{decoded_item["F193"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00433", "Value" => "#{decoded_item["B194"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00434", "Value" => "#{decoded_item["C194"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00435", "Value" => "#{decoded_item["D194"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00436", "Value" => "#{decoded_item["E194"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00437", "Value" => "#{decoded_item["F194"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00438", "Value" => "#{decoded_item["B195"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00439", "Value" => "#{decoded_item["C195"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00440", "Value" => "#{decoded_item["D195"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00441", "Value" => "#{decoded_item["E195"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00442", "Value" => "#{decoded_item["F195"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00443", "Value" => "#{decoded_item["B196"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00444", "Value" => "#{decoded_item["C196"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00445", "Value" => "#{decoded_item["D196"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00446", "Value" => "#{decoded_item["E196"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00447", "Value" => "#{decoded_item["F196"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00448", "Value" => "#{decoded_item["B197"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00449", "Value" => "#{decoded_item["C197"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00450", "Value" => "#{decoded_item["D197"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00451", "Value" => "#{decoded_item["E197"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00452", "Value" => "#{decoded_item["F197"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00453", "Value" => "#{decoded_item["B199"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00454", "Value" => "#{decoded_item["C199"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00455", "Value" => "#{decoded_item["D199"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00456", "Value" => "#{decoded_item["E199"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00457", "Value" => "#{decoded_item["F199"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00458", "Value" => "#{decoded_item["B201"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00459", "Value" => "#{decoded_item["C201"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00460", "Value" => "#{decoded_item["D201"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00461", "Value" => "#{decoded_item["E201"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00462", "Value" => "#{decoded_item["F201"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00463", "Value" => "#{decoded_item["B202"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00464", "Value" => "#{decoded_item["C202"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00465", "Value" => "#{decoded_item["D202"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00466", "Value" => "#{decoded_item["E202"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00467", "Value" => "#{decoded_item["F202"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00468", "Value" => "#{decoded_item["B203"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00469", "Value" => "#{decoded_item["C203"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00470", "Value" => "#{decoded_item["D203"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00471", "Value" => "#{decoded_item["E203"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00472", "Value" => "#{decoded_item["F203"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00473", "Value" => "#{decoded_item["B204"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00474", "Value" => "#{decoded_item["C204"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00475", "Value" => "#{decoded_item["D204"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00476", "Value" => "#{decoded_item["E204"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00477", "Value" => "#{decoded_item["F204"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00478", "Value" => "#{decoded_item["B205"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00479", "Value" => "#{decoded_item["C205"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00480", "Value" => "#{decoded_item["D205"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00481", "Value" => "#{decoded_item["E205"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00482", "Value" => "#{decoded_item["F205"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00483", "Value" => "#{decoded_item["B206"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00484", "Value" => "#{decoded_item["C206"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00485", "Value" => "#{decoded_item["D206"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00486", "Value" => "#{decoded_item["E206"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00487", "Value" => "#{decoded_item["F206"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00488", "Value" => "#{decoded_item["B207"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00489", "Value" => "#{decoded_item["C207"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00490", "Value" => "#{decoded_item["D207"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00491", "Value" => "#{decoded_item["E207"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00492", "Value" => "#{decoded_item["F207"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00493", "Value" => "#{decoded_item["B208"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00494", "Value" => "#{decoded_item["C208"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00495", "Value" => "#{decoded_item["D208"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00496", "Value" => "#{decoded_item["E208"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00497", "Value" => "#{decoded_item["F208"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00498", "Value" => "#{decoded_item["B210"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00499", "Value" => "#{decoded_item["C210"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00500", "Value" => "#{decoded_item["D210"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00501", "Value" => "#{decoded_item["E210"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00502", "Value" => "#{decoded_item["F210"] || "0" }", "_dataType" => "NUMERIC"},


      %{"Code" => "1205_00504", "Value" => "#{decoded_item["B218"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00505", "Value" => "#{decoded_item["C218"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00506", "Value" => "#{decoded_item["D218"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00507", "Value" => "#{decoded_item["E218"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00508", "Value" => "#{decoded_item["F218"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00509", "Value" => "#{decoded_item["B219"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00510", "Value" => "#{decoded_item["C219"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00511", "Value" => "#{decoded_item["D219"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00512", "Value" => "#{decoded_item["E219"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00513", "Value" => "#{decoded_item["F219"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00514", "Value" => "#{decoded_item["B220"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00515", "Value" => "#{decoded_item["C220"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00516", "Value" => "#{decoded_item["D220"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00517", "Value" => "#{decoded_item["E220"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00518", "Value" => "#{decoded_item["F220"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00519", "Value" => "#{decoded_item["B221"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00520", "Value" => "#{decoded_item["C221"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00521", "Value" => "#{decoded_item["D221"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00522", "Value" => "#{decoded_item["E221"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00523", "Value" => "#{decoded_item["F221"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00524", "Value" => "#{decoded_item["B222"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00525", "Value" => "#{decoded_item["C222"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00526", "Value" => "#{decoded_item["D222"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00527", "Value" => "#{decoded_item["E222"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00528", "Value" => "#{decoded_item["F222"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00529", "Value" => "#{decoded_item["B224"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00530", "Value" => "#{decoded_item["C224"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00531", "Value" => "#{decoded_item["D224"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00532", "Value" => "#{decoded_item["E224"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00533", "Value" => "#{decoded_item["F224"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00534", "Value" => "#{decoded_item["B226"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00535", "Value" => "#{decoded_item["C226"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00536", "Value" => "#{decoded_item["D226"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00537", "Value" => "#{decoded_item["E226"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00538", "Value" => "#{decoded_item["F226"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00539", "Value" => "#{decoded_item["B227"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00540", "Value" => "#{decoded_item["C227"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00541", "Value" => "#{decoded_item["D227"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00542", "Value" => "#{decoded_item["E227"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00543", "Value" => "#{decoded_item["F227"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00544", "Value" => "#{decoded_item["B228"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00545", "Value" => "#{decoded_item["C228"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00546", "Value" => "#{decoded_item["D228"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00547", "Value" => "#{decoded_item["E228"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00548", "Value" => "#{decoded_item["F228"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00549", "Value" => "#{decoded_item["B229"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00550", "Value" => "#{decoded_item["C229"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00551", "Value" => "#{decoded_item["D229"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00552", "Value" => "#{decoded_item["E229"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00553", "Value" => "#{decoded_item["F229"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00554", "Value" => "#{decoded_item["B230"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00555", "Value" => "#{decoded_item["C230"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00556", "Value" => "#{decoded_item["D230"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00557", "Value" => "#{decoded_item["E230"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00558", "Value" => "#{decoded_item["F230"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00559", "Value" => "#{decoded_item["B231"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00560", "Value" => "#{decoded_item["C231"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00561", "Value" => "#{decoded_item["D231"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00562", "Value" => "#{decoded_item["E231"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00563", "Value" => "#{decoded_item["F231"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00564", "Value" => "#{decoded_item["B232"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00565", "Value" => "#{decoded_item["C232"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00566", "Value" => "#{decoded_item["D232"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00567", "Value" => "#{decoded_item["E232"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00568", "Value" => "#{decoded_item["F232"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00569", "Value" => "#{decoded_item["B233"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00570", "Value" => "#{decoded_item["C233"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00571", "Value" => "#{decoded_item["D233"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00572", "Value" => "#{decoded_item["E233"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00573", "Value" => "#{decoded_item["F233"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00574", "Value" => "#{decoded_item["B235"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00575", "Value" => "#{decoded_item["C235"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00576", "Value" => "#{decoded_item["D235"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00577", "Value" => "#{decoded_item["E235"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00578", "Value" => "#{decoded_item["F235"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00579", "Value" => "#{decoded_item["B243"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00580", "Value" => "#{decoded_item["C243"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00581", "Value" => "#{decoded_item["D243"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00582", "Value" => "#{decoded_item["E243"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00583", "Value" => "#{decoded_item["F243"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00584", "Value" => "#{decoded_item["B244"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00585", "Value" => "#{decoded_item["C244"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00586", "Value" => "#{decoded_item["D244"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00587", "Value" => "#{decoded_item["E244"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00588", "Value" => "#{decoded_item["F244"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00589", "Value" => "#{decoded_item["B245"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00590", "Value" => "#{decoded_item["C245"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00591", "Value" => "#{decoded_item["D245"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00592", "Value" => "#{decoded_item["E245"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00593", "Value" => "#{decoded_item["F245"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00594", "Value" => "#{decoded_item["B246"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00595", "Value" => "#{decoded_item["C246"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00596", "Value" => "#{decoded_item["D246"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00597", "Value" => "#{decoded_item["E246"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00598", "Value" => "#{decoded_item["F246"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00599", "Value" => "#{decoded_item["B247"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00600", "Value" => "#{decoded_item["C247"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00601", "Value" => "#{decoded_item["D247"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00602", "Value" => "#{decoded_item["E247"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00603", "Value" => "#{decoded_item["F247"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00604", "Value" => "#{decoded_item["B249"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00605", "Value" => "#{decoded_item["C249"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00606", "Value" => "#{decoded_item["D249"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00607", "Value" => "#{decoded_item["E249"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00608", "Value" => "#{decoded_item["F249"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00609", "Value" => "#{decoded_item["B251"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00610", "Value" => "#{decoded_item["C251"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00611", "Value" => "#{decoded_item["D251"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00612", "Value" => "#{decoded_item["E251"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00613", "Value" => "#{decoded_item["F251"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00614", "Value" => "#{decoded_item["B252"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00615", "Value" => "#{decoded_item["C252"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00616", "Value" => "#{decoded_item["D252"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00617", "Value" => "#{decoded_item["E252"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00618", "Value" => "#{decoded_item["F252"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00619", "Value" => "#{decoded_item["B253"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00620", "Value" => "#{decoded_item["C253"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00621", "Value" => "#{decoded_item["D253"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00622", "Value" => "#{decoded_item["E253"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00623", "Value" => "#{decoded_item["F253"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00624", "Value" => "#{decoded_item["B254"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00625", "Value" => "#{decoded_item["C254"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00626", "Value" => "#{decoded_item["D254"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00627", "Value" => "#{decoded_item["E254"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00628", "Value" => "#{decoded_item["F254"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00629", "Value" => "#{decoded_item["B255"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00630", "Value" => "#{decoded_item["C255"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00631", "Value" => "#{decoded_item["D255"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00632", "Value" => "#{decoded_item["E255"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00633", "Value" => "#{decoded_item["F255"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00634", "Value" => "#{decoded_item["B256"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00635", "Value" => "#{decoded_item["C256"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00636", "Value" => "#{decoded_item["D256"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00637", "Value" => "#{decoded_item["E256"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00638", "Value" => "#{decoded_item["F256"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00639", "Value" => "#{decoded_item["B257"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00640", "Value" => "#{decoded_item["C257"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00641", "Value" => "#{decoded_item["D257"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00642", "Value" => "#{decoded_item["E257"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00643", "Value" => "#{decoded_item["F257"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00644", "Value" => "#{decoded_item["B258"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00645", "Value" => "#{decoded_item["C258"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00646", "Value" => "#{decoded_item["D258"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00647", "Value" => "#{decoded_item["E258"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00648", "Value" => "#{decoded_item["F258"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00649", "Value" => "#{decoded_item["B259"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00650", "Value" => "#{decoded_item["C259"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00651", "Value" => "#{decoded_item["D259"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00652", "Value" => "#{decoded_item["E259"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00653", "Value" => "#{decoded_item["F259"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00654", "Value" => "#{decoded_item["B260"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00655", "Value" => "#{decoded_item["C260"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00656", "Value" => "#{decoded_item["D260"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00657", "Value" => "#{decoded_item["E260"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00658", "Value" => "#{decoded_item["F260"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00659", "Value" => "#{decoded_item["B261"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00660", "Value" => "#{decoded_item["C261"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00661", "Value" => "#{decoded_item["D261"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00662", "Value" => "#{decoded_item["E261"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00663", "Value" => "#{decoded_item["F261"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00664", "Value" => "#{decoded_item["B262"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00665", "Value" => "#{decoded_item["C262"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00666", "Value" => "#{decoded_item["D262"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00667", "Value" => "#{decoded_item["E262"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00668", "Value" => "#{decoded_item["F262"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00669", "Value" => "#{decoded_item["B263"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00670", "Value" => "#{decoded_item["C263"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00671", "Value" => "#{decoded_item["D263"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00672", "Value" => "#{decoded_item["E263"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00673", "Value" => "#{decoded_item["F263"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00674", "Value" => "#{decoded_item["B264"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00675", "Value" => "#{decoded_item["C264"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00676", "Value" => "#{decoded_item["D264"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00677", "Value" => "#{decoded_item["E264"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00678", "Value" => "#{decoded_item["F264"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00679", "Value" => "#{decoded_item["B265"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00680", "Value" => "#{decoded_item["C265"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00681", "Value" => "#{decoded_item["D265"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00682", "Value" => "#{decoded_item["E265"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00683", "Value" => "#{decoded_item["F265"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00684", "Value" => "#{decoded_item["B266"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00685", "Value" => "#{decoded_item["C266"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00686", "Value" => "#{decoded_item["D266"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00687", "Value" => "#{decoded_item["E266"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00688", "Value" => "#{decoded_item["F266"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00689", "Value" => "#{decoded_item["B267"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00690", "Value" => "#{decoded_item["C267"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00691", "Value" => "#{decoded_item["D267"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00692", "Value" => "#{decoded_item["E267"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00693", "Value" => "#{decoded_item["F267"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00694", "Value" => "#{decoded_item["B268"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00695", "Value" => "#{decoded_item["C268"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00696", "Value" => "#{decoded_item["D268"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00697", "Value" => "#{decoded_item["E268"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00698", "Value" => "#{decoded_item["F268"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00699", "Value" => "#{decoded_item["B269"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00700", "Value" => "#{decoded_item["C269"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00701", "Value" => "#{decoded_item["D269"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00702", "Value" => "#{decoded_item["E269"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00703", "Value" => "#{decoded_item["F269"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00704", "Value" => "#{decoded_item["B270"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00705", "Value" => "#{decoded_item["C270"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00706", "Value" => "#{decoded_item["D270"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00707", "Value" => "#{decoded_item["E270"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00708", "Value" => "#{decoded_item["F270"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00709", "Value" => "#{decoded_item["B271"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00710", "Value" => "#{decoded_item["C271"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00711", "Value" => "#{decoded_item["D271"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00712", "Value" => "#{decoded_item["E271"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00713", "Value" => "#{decoded_item["F271"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00714", "Value" => "#{decoded_item["B272"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00715", "Value" => "#{decoded_item["C272"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00716", "Value" => "#{decoded_item["D272"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00717", "Value" => "#{decoded_item["E272"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00718", "Value" => "#{decoded_item["F272"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00719", "Value" => "#{decoded_item["B274"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00720", "Value" => "#{decoded_item["C274"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00721", "Value" => "#{decoded_item["D274"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00722", "Value" => "#{decoded_item["E274"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00723", "Value" => "#{decoded_item["F274"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00724", "Value" => "#{decoded_item["B276"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00725", "Value" => "#{decoded_item["C276"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00726", "Value" => "#{decoded_item["D276"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00727", "Value" => "#{decoded_item["E276"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00728", "Value" => "#{decoded_item["F276"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00729", "Value" => "#{decoded_item["B277"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00730", "Value" => "#{decoded_item["C277"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00731", "Value" => "#{decoded_item["D277"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00732", "Value" => "#{decoded_item["E277"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00733", "Value" => "#{decoded_item["F277"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00734", "Value" => "#{decoded_item["B278"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00735", "Value" => "#{decoded_item["C278"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00736", "Value" => "#{decoded_item["D278"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00737", "Value" => "#{decoded_item["E278"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00738", "Value" => "#{decoded_item["F278"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00739", "Value" => "#{decoded_item["B279"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00740", "Value" => "#{decoded_item["C279"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00741", "Value" => "#{decoded_item["D279"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00742", "Value" => "#{decoded_item["E279"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00743", "Value" => "#{decoded_item["F279"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00744", "Value" => "#{decoded_item["B280"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00745", "Value" => "#{decoded_item["C280"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00746", "Value" => "#{decoded_item["D280"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00747", "Value" => "#{decoded_item["E280"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00748", "Value" => "#{decoded_item["F280"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00749", "Value" => "#{decoded_item["B281"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00750", "Value" => "#{decoded_item["C281"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00751", "Value" => "#{decoded_item["D281"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00752", "Value" => "#{decoded_item["E281"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00753", "Value" => "#{decoded_item["F281"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00754", "Value" => "#{decoded_item["B282"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00755", "Value" => "#{decoded_item["C282"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00756", "Value" => "#{decoded_item["D282"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00757", "Value" => "#{decoded_item["E282"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00758", "Value" => "#{decoded_item["F282"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00759", "Value" => "#{decoded_item["B283"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00760", "Value" => "#{decoded_item["C283"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00761", "Value" => "#{decoded_item["D283"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00762", "Value" => "#{decoded_item["E283"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00763", "Value" => "#{decoded_item["F283"] || "0" }", "_dataType" => "NUMERIC"},

# tatals

      %{"Code" => "1205_00784", "Value" => "#{decoded_item["B19"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00785", "Value" => "#{decoded_item["C19"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00786", "Value" => "#{decoded_item["D19"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00787", "Value" => "#{decoded_item["E19"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00788", "Value" => "#{decoded_item["F19"] || "0" }", "_dataType" => "NUMERIC"},

      %{"Code" => "1205_00789", "Value" => "#{decoded_item["B20"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00790", "Value" => "#{decoded_item["C20"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00791", "Value" => "#{decoded_item["D20"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00792", "Value" => "#{decoded_item["E20"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00793", "Value" => "#{decoded_item["F20"] || "0" }", "_dataType" => "NUMERIC"},

      %{"Code" => "1205_00794", "Value" => "#{decoded_item["B24"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00795", "Value" => "#{decoded_item["C24"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00796", "Value" => "#{decoded_item["D24"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00797", "Value" => "#{decoded_item["E24"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00798", "Value" => "#{decoded_item["F24"] || "0" }", "_dataType" => "NUMERIC"},

      %{"Code" => "1205_00799", "Value" => "#{decoded_item["B26"] || "0" }", "_dataType" => "NUMERIC"},
      %{"Code" => "1205_00800", "Value" => "#{decoded_item["C26"] || "0" }", "_dataType" => "NUMERIC"}
    ]
    |> format_number()
  end





  def format_number(data) when is_list(data) do
    Enum.map(data, fn map ->
      value = Map.get(map, "Value", "0") |> String.replace(",", "")
      %{map | "Value" => value}  # Update only the "Value" field
    end)
  end



  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end



  def format_scientific_values(map) when is_map(map) do
    (get_in(map, ["list"]) || [])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  def format_scientific_values(_), do: %{}

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end







  def perform_sums(decoded_item) do
    decoded_item
# Target: B19
    |> add_sums(["B43","B68","B93","B118","B143","B168","B193","B218","B243","B268"], "B19")
    |> add_sums(["C43","C68","C93","C118","C143","C168","C193","C218","C243","C268"], "C19")
    |> add_sums(["D43","D68","D93","D118","D143","D168","D193","D218","D243","D268"], "D19")
    |> add_sums(["E43","E68","E93","E118","E143","E168","E193","E218","E243","E268"], "E19")
    |> add_sums(["F43","F68","F93","F118","F143","F168","F193","F218","F243","F268"], "F19")
   # Target: B20
  |> add_sums(["B44", "B69", "B94", "B119", "B144", "B169", "B194", "B219", "B244", "B269"], "B20")
  |> add_sums(["C44", "C69", "C94", "C119", "C144", "C169", "C194", "C219", "C244", "C269"], "C20")
  |> add_sums(["D44", "D69", "D94", "D119", "D144", "D169", "D194", "D219", "D244", "D269"], "D20")
  |> add_sums(["E44", "E69", "E94", "E119", "E144", "E169", "E194", "E219", "E244", "E269"], "E20")
  |> add_sums(["F44", "F69", "F94", "F119", "F144", "F169", "F194", "F219", "F244", "F269"], "F20")

  # Target: B21
  |> add_sums(["B45", "B70", "B95", "B120", "B145", "B170", "B195", "B220", "B245", "B270"], "B21")
  |> add_sums(["C45", "C70", "C95", "C120", "C145", "C170", "C195", "C220", "C245", "C270"], "C21")
  |> add_sums(["D45", "D70", "D95", "D120", "D145", "D170", "D195", "D220", "D245", "D270"], "D21")
  |> add_sums(["E45", "E70", "E95", "E120", "E145", "E170", "E195", "E220", "E245", "E270"], "E21")
  |> add_sums(["F45", "F70", "F95", "F120", "F145", "F170", "F195", "F220", "F245", "F270"], "F21")
  # Target: B22

  |> add_sums(["B46", "B71", "B96", "B121", "B146", "B171", "B196", "B221", "B246", "B271"], "B22")
  |> add_sums(["C46", "C71", "C96", "C121", "C146", "C171", "C196", "C221", "C246", "C271"], "C22")
  |> add_sums(["D46", "D71", "D96", "D121", "D146", "D171", "D196", "D221", "D246", "D271"], "D22")
  |> add_sums(["E46", "E71", "E96", "E121", "E146", "E171", "E196", "E221", "E246", "E271"], "E22")
  |> add_sums(["F46", "F71", "F96", "F121", "F146", "F171", "F196", "F221", "F246", "F271"], "F22")
  # Target: B23
  |> add_sums(["B47", "B72", "B97", "B122", "B147", "B172", "B197", "B222", "B247", "B272"], "B23")
  |> add_sums(["C47", "C72", "C97", "C122", "C147", "C172", "C197", "C222", "C247", "C272"], "C23")
  |> add_sums(["D47", "D72", "D97", "D122", "D147", "D172", "D197", "D222", "D247", "D272"], "D23")
  |> add_sums(["E47", "E72", "E97", "E122", "E147", "E172", "E197", "E222", "E247", "E272"], "E23")
  |> add_sums(["F47", "F72", "F97", "F122", "F147", "F172", "F197", "F222", "F247", "F272"], "F23")


  # Target24: B26
  |> add_sums(["B19","B23"], "B24")
  |>add_sums(["C19","C23"], "C24")
  |> add_sums(["D19","D23"], "D24")
  |> add_sums(["E19","E23"], "E24")
  |> add_sums(["F19","F23"], "F24")



  # Target: B26
  |> add_sums(["B51", "B76", "B101", "B126", "B151", "B176", "B201", "B226", "B251", "B276"], "B26")
  |> add_sums(["C51", "C76", "C101", "C126", "C151", "C176", "C201", "C226", "C251", "C276"], "C26")
  |> add_sums(["D51", "D76", "D101", "D126", "D151", "D176", "D201", "D226", "D251", "D276"], "D26")
  |> add_sums(["E51", "E76", "E101", "E126", "E151", "E176", "E201", "E226", "E251", "E276"], "E26")
  |> add_sums(["F51", "F76", "F101", "F126", "F151", "F176", "F201", "F226", "F251", "F276"], "F26")

  # Target: B27
  |> add_sums(["B52", "B77", "B102", "B127", "B152", "B177", "B202", "B227", "B252", "B277"], "B27")
  |> add_sums(["C52", "C77", "C102", "C127", "C152", "C177", "C202", "C227", "C252", "C277"], "C27")
  |> add_sums(["D52", "D77", "D102", "D127", "D152", "D177", "D202", "D227", "D252", "D277"], "D27")
  |> add_sums(["E52", "E77", "E102", "E127", "E152", "E177", "E202", "E227", "E252", "E277"], "E27")
  |> add_sums(["F52", "F77", "F102", "F127", "F152", "F177", "F202", "F227", "F252", "F277"], "F27")

  # Target: B28
  |> add_sums(["B53", "B78", "B103", "B128", "B153", "B178", "B203", "B228", "B253", "B278"], "B28")
  |> add_sums(["C53", "C78", "C103", "C128", "C153", "C178", "C203", "C228", "C253", "C278"], "C28")
  |> add_sums(["D53", "D78", "D103", "D128", "D153", "D178", "D203", "D228", "D253", "D278"], "D28")
  |> add_sums(["E53", "E78", "E103", "E128", "E153", "E178", "E203", "E228", "E253", "E278"], "E28")
  |> add_sums(["F53", "F78", "F103", "F128", "F153", "F178", "F203", "F228", "F253", "F278"], "F28")

  # Target: B29
  |> add_sums(["B54", "B79", "B104", "B129", "B154", "B179", "B204", "B229", "B254", "B279"], "B29")
  |> add_sums(["C54", "C79", "C104", "C129", "C154", "C179", "C204", "C229", "C254", "C279"], "C29")
  |> add_sums(["D54", "D79", "D104", "D129", "D154", "D179", "D204", "D229", "D254", "D279"], "D29")
  |> add_sums(["E54", "E79", "E104", "E129", "E154", "E179", "E204", "E229", "E254", "E279"], "E29")
  |> add_sums(["F54", "F79", "F104", "F129", "F154", "F179", "F204", "F229", "F254", "F279"], "F29")

  # Target: B30
  |> add_sums(["B55", "B80", "B105", "B130", "B155", "B180", "B205", "B230", "B255", "B280"], "B30")
  |> add_sums(["C55", "C80", "C105", "C130", "C155", "C180", "C205", "C230", "C255", "C280"], "C30")
  |> add_sums(["D55", "D80", "D105", "D130", "D155", "D180", "D205", "D230", "D255", "D280"], "D30")
  |> add_sums(["E55", "E80", "E105", "E130", "E155", "E180", "E205", "E230", "E255", "E280"], "E30")
  |> add_sums(["F55", "F80", "F105", "F130", "F155", "F180", "F205", "F230", "F255", "F280"], "F30")

  # Target: B31
  |> add_sums(["B56", "B81", "B106", "B131", "B156", "B181", "B206", "B231", "B256", "B281"], "B31")
  |> add_sums(["C56", "C81", "C106", "C131", "C156", "C181", "C206", "C231", "C256", "C281"], "C31")
  |> add_sums(["D56", "D81", "D106", "D131", "D156", "D181", "D206", "D231", "D256", "D281"], "D31")
  |> add_sums(["E56", "E81", "E106", "E131", "E156", "E181", "E206", "E231", "E256", "E281"], "E31")
  |> add_sums(["F56", "F81", "F106", "F131", "F156", "F181", "F206", "F231", "F256", "F281"], "F31")

  # Target: B32
  |> add_sums(["B57", "B82", "B107", "B132", "B157", "B182", "B207", "B232", "B257", "B282"], "B32")
  |> add_sums(["C57", "C82", "C107", "C132", "C157", "C182", "C207", "C232", "C257", "C282"], "C32")
  |> add_sums(["D57", "D82", "D107", "D132", "D157", "D182", "D207", "D232", "D257", "D282"], "D32")
  |> add_sums(["E57", "E82", "E107", "E132", "E157", "E182", "E207", "E232", "E257", "E282"], "E32")
  |> add_sums(["F57", "F82", "F107", "F132", "F157", "F182", "F207", "F232", "F257", "F282"], "F32")

  # Target: B33
  |> add_sums(["B58", "B83", "B108", "B133", "B158", "B183", "B208", "B233", "B258", "B283"], "B33")
  |> add_sums(["C58", "C83", "C108", "C133", "C158", "C183", "C208", "C233", "C258", "C283"], "C33")
  |> add_sums(["D58", "D83", "D108", "D133", "D158", "D183", "D208", "D233", "D258", "D283"], "D33")
  |> add_sums(["E58", "E83", "E108", "E133", "E158", "E183", "E208", "E233", "E258", "E283"], "E33")
  |> add_sums(["F58", "F83", "F108", "F133", "F158", "F183", "F208", "F233", "F258", "F283"], "F33")

  |> add_sums(["B26","B33"], "B35")
  |>add_sums(["C26","C33"], "C35")
  |> add_sums(["D26","D33"], "D35")
  |> add_sums(["E26","E33"], "E35")
  |> add_sums(["F26","323"], "F35")

  end

    defp add_sums(map, keys, target_key) do
      sum = Enum.reduce(keys, Decimal.new("0"), fn key, acc ->
        value =
          Map.get(map, key)
          |> case do
            nil -> "0"
            val -> val
          end
          |> String.replace(",", "")
          |> Decimal.new()

        Decimal.add(acc, value)
      end)

      Map.put(map, target_key, format_number(sum))
    end


end
