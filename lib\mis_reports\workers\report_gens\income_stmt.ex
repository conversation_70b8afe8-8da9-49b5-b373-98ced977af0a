defmodule MisReports.Workers.IS do
  @moduledoc """
 Module for generating display data for MisReports Workers IS.

 This module provides a function to generate display data.
 """
 alias MisReports.Workers
 alias MisReports.SourceData
 alias MisReports.Workers.LoansAdvances
 alias MisReports.Mappings
 alias MisReports.Utilities


 def generate_display(data, date, adjustments) do
   adjustments = if adjustments in [nil, []], do: %{}, else: get_adjustment_list(adjustments)
  #  converting date to end date, so that we get the correct adjustments (incase start date is passed)
  #  date = Date.from_iso8601!(date) |> Timex.end_of_month()
   prev_adj = Utilities.get_prev_adjustments(date)

   val = if prev_adj, do: get_prev_adj(prev_adj), else: []
# actual function geting income statement data which also extracts end_date and Start_date from passed date
   ytd_data = SourceData.get_ytd_income_statement(date)
     |> Enum.map(fn ytd_entry ->
       matching_gl = Enum.filter(val, fn adj ->
         adj.debit == ytd_entry.gl_no || adj.credit == ytd_entry.gl_no
       end)
      #  IO.inspect(ytd_entry.gl_no, label: "Processing GL")
      #  IO.inspect(matching_gl, label: "Found adjustments")

       new_difference = Enum.reduce(matching_gl, ytd_entry.actual_this_year_difference, fn adj, acc_diff ->
         Decimal.add(acc_diff, adj.amount)
       end)

     %{ytd_entry | actual_this_year_difference: new_difference}
   end)

  #  sums = sum_ytd_difference(ytd_data)
  #  IO.inspect(sums, label: "year to date numbers")
  #  gbm_sum = sum_gbm_values(data)
  #  IO.inspect(gbm_sum, label: "gbm numbers")

   non_sched_list = format(data, ytd_data, adjustments)
   non_sched_map = non_sched_list
   |> Enum.reduce(%{}, fn %{index: index, value: value}, acc ->
     Map.put(acc, index, value)
   end)

   sched = schedules_data(data, date, ytd_data)
   merged_data = Map.merge(non_sched_map, sched)

   result = merged_data
   |> Enum.reduce(%{}, fn {k, v}, acc ->
     Map.put(acc, k, format_number(v))
   end)

   result = perform_sums(result)
   result
 end


 defp perform_sums(result) do
   result
    |> add_sums(["C12", "C13", "C14", "C15"], "C11")
    |> add_sums(["C20", "C21", "C22"], "C19")
    |> add_sums(["C24", "C25", "C26", "C27", "C28", "C29"], "C23")
    |> add_sums(["C11", "C16", "C19", "C23", "C30", "C31", "C32"], "C33")
    |> add_sums(["C45", "C46", "C47", "C48", "C49"], "C44")
    |> add_sums(["C36", "C37", "C38", "C39", "C40", "C41", "C42", "C43", "C44"], "C35")
    |> add_sums(["C52", "C53"], "C51")
    |> add_sums(["C51", "C54"], "C50")
    |> add_sums(["C35", "C50", "C57", "C58", "C59"], "C60")
    |> sub_sums(["C33", "C60"], "C61")
    |> add_sums(["C64", "C65", "C66", "C67"], "C68")
    |> add_sums(["B64", "B65", "B66", "B67"], "B68")
    |> sub_sums(["B68", "C68"], "D68")
    |> sub_sums(["C61", "C68"], "C69")


    |> add_sums(["C73", "C74", "C75"], "C72")
    |> add_sums(["C77", "C78"], "C76")
    |> add_sums(["C71", "C72", "C76", "C79", "C80"], "C81")
    # |> add_sums(["B71", "B72", "B76", "B79", "B80"], "B81")
    |> add_sums(["C69", "C81"], "C82")
    |> add_sums(["C84", "C85", "C86", "C87", "C88", "C89", "C90", "C91", "C92", "C93", "C94"], "C95")
    |> sub_sums(["C82", "C95"], "C96")
    # |> sub_sums(["B82", "B95"], "B96")
    |> sub_sums(["C96", "B96"], "D96")
    |> sub_sums(["C96", "C97"], "C98")
    |> add_sums(["B24", "B25"], "B23")
    |> add_sums(["B43", "B40", "B44"], "B35")
    |> sub_sums(["C11", "B11"], "D11")
    |> sub_sums(["C19", "B19"], "D19")
    |> sub_sums(["C23", "B23"], "D23")
    |> sub_sums(["C30", "B30"], "D30")
    |> sub_sums(["C31", "B31"], "D31")
    |> sub_sums(["C35", "B35"], "D35")
    |> sub_sums(["C44", "B44"], "D44")
   #  |> sub_sums(["C50", "B55"], "D50")
    |> sub_sums(["C57", "B57"], "D57")
    |> sub_sums(["C59", "B59"], "D59")
    |> sub_sums(["C71", "B71"], "D71")
    |> sub_sums(["C73", "B73"], "D73")
    |> sub_sums(["C77", "B77"], "D77")
    |> sub_sums(["C85", "B85"], "D85")
    |> sub_sums(["C86", "B86"], "D86")
    |> sub_sums(["C87", "B87"], "D87")
    |> sub_sums(["C89", "B89"], "D89")
    |> sub_sums(["C90", "B90"], "D90")
    |> sub_sums(["C92", "B92"], "D92")
    |> sub_sums(["C93", "B93"], "D93")
    |> add_sums(["B11", "B16", "B19", "B23", "B30", "B31", "B32"], "B33")
    |> sub_sums(["C33", "B33"], "D33")
    |> sub_sums(["C51", "B51"], "D51")
    |> sub_sums(["C32", "B32"], "D32")
    |> add_sums(["B51", "B54"], "B50")
    |> add_sums(["B35", "B50", "B57", "B58", "B59"], "B60")
    |> sub_sums(["C60", "B60"], "D60")
    |> sub_sums(["C50", "B50"], "D50")
    |> sub_sums(["B33", "B60"], "B61")
    |> sub_sums(["B61", "B68"], "B69")
    |> sub_sums(["C69", "B69"], "D69")

    |> sub_sums(["C61", "B61"], "D61")
    |> add_sums(["B73", "B74", "B75"], "B72")
    |> sub_sums(["C72", "B72"], "D72")
    |> add_sums(["B77", "B78"], "B76")
    |> sub_sums(["C76", "B76"], "D76")
    |> sub_sums(["C84", "B84"], "D84")
    |> sub_sums(["C94", "B94"], "D94")
    |> sub_sums(["C67", "B67"], "D67")
    |> sub_sums(["C65", "B65"], "D65")
    |> sub_sums(["C97", "B97"], "D97")
    |> add_sums(["B84", "B85", "B86", "B87", "B88", "B89", "B90", "B91", "B92", "B93", "B94"], "B95")
    |> add_sums(["B71", "B72", "B76", "B79", "B80"], "B81")
    |> sub_sums(["C81", "B81"], "D81")
    |> sub_sums(["B80", "C80"], "D80")
    |> add_sums(["B69", "B81"], "B82")
    |> sub_sums(["C82", "B82"], "D82")
    |> sub_sums(["B82", "B95"], "B96")
    |> sub_sums(["C96", "B96"], "D96")
    |> sub_sums(["B96", "B97"], "B98")
    |> sub_sums(["C98", "B98"], "D98")
 end

 def get_adjustment_list(adjustments) do
   adjustments
   |> Enum.reduce(%{}, fn adjustment_map, acc ->
     adjustment_map
     |> Map.get(:adjustment_lines, "[]")
     |> Poison.decode!()
     |> Enum.reduce(acc, fn column, acc_inner ->
       amount = Decimal.new(column["amount"])

       entry = %{
         amount: amount,
         debit: column["debit"],
         credit: column["credit"],
         type_debit: column["type_debit"],
         type_credit: column["type_credit"],
         cur_cat_debit: categorize_cur_cat(column["cur_cat_debit"]),
         cur_cat_credit: categorize_cur_cat(column["cur_cat_credit"]),
         business_unit_debit: categorize_business_unit(column["business_unit_debit"]),
         business_unit_credit: categorize_business_unit(column["business_unit_credit"])
       }

       acc_inner =
         Map.update(acc_inner, column["credit"], entry, fn existing_entry ->
           update_entry(existing_entry, amount)
         end)

       acc_inner =
         Map.update(acc_inner, column["debit"], %{entry | amount: Decimal.negate(amount)}, fn existing_entry ->
           update_entry(existing_entry, Decimal.negate(amount))
         end)

       acc_inner
     end)
   end)
 end


 # def get_adjustment_list(adjustments) do
 #   adjustments.adjustment_lines
 #   |> Poison.decode!()
 #   |> Enum.reduce(%{}, fn column, acc ->
 #     amount = Decimal.new(column["amount"])

 #     entry = %{
 #       amount: amount,
 #       debit: column["debit"],
 #       credit: column["credit"],
 #       type_debit: column["type_debit"],
 #       type_credit: column["type_credit"],
 #       cur_cat_debit: categorize_cur_cat(column["cur_cat_debit"]),
 #       cur_cat_credit: categorize_cur_cat(column["cur_cat_credit"]),
 #       business_unit_debit: categorize_business_unit(column["business_unit_debit"]),
 #       business_unit_credit: categorize_business_unit(column["business_unit_credit"])
 #     }

 #     acc =
 #       Map.update(acc, column["credit"], entry, fn existing_entry ->
 #         update_entry(existing_entry, amount)
 #       end)

 #     acc =
 #       Map.update(acc, column["debit"], %{entry | amount: Decimal.negate(amount)}, fn existing_entry ->
 #         update_entry(existing_entry, Decimal.negate(amount))
 #       end)

 #     acc
 #   end)
 # end

 defp categorize_cur_cat("L"), do: "LCY"
 defp categorize_cur_cat("F"), do: "FCY"
 defp categorize_cur_cat(_), do: ""

 defp categorize_business_unit("C"), do: "Corporate and Investment Banking"
 defp categorize_business_unit("R"), do: "Personal and Business Banking"
 defp categorize_business_unit(_), do: ""

 defp update_entry(existing_entry, new_amount) do
   updated_amount = Decimal.add(existing_entry.amount, new_amount)
   Map.put(existing_entry, :amount, updated_amount)
 end

 def get_prev_adj(adjustments) do
   adjustments
   |> Enum.flat_map(fn adjustment ->
     adjustment_lines = Map.get(adjustment, :adjustment_lines)

     case adjustment_lines do
       nil -> []
       _ ->
         case Poison.decode(adjustment_lines) do
           {:ok, lines} ->
             Enum.map(lines, fn column ->
               amount =
                 case column["amount"] do
                   nil -> Decimal.new(0)
                   value -> Decimal.new(value)
                 end

               %{
                 amount: amount,
                 debit: column["debit"],
                 credit: column["credit"],
                 type_debit: column["type_debit"],
                 type_credit: column["type_credit"],
                 cur_cat_debit: categorize_cur_cat(column["cur_cat_debit"]),
                 cur_cat_credit: categorize_cur_cat(column["cur_cat_credit"]),
                 business_unit_debit: categorize_business_unit(column["business_unit_debit"]),
                 business_unit_credit: categorize_business_unit(column["business_unit_credit"])
               }
             end)

           {:error, _reason} ->
             []
         end
     end
   end)
 end




 defp add_sums(map, keys, target_key) do
   sum = Enum.reduce(keys, Decimal.new("0"), fn key, acc ->
     value =
       Map.get(map, key)
       |> case do
         nil -> "0"
         val -> val
       end
       |> String.replace(",", "")
       |> Decimal.new()

     Decimal.add(acc, value)
   end)

   Map.put(map, target_key, format_number(sum))
 end

 defp sub_sums(map, [first_key | rest_keys], target_key) do
   initial_value =
     Map.get(map, first_key)
     |> case do
       nil -> "0"
       val -> val
     end
     |> String.replace(",", "")
     |> Decimal.new()

   sum = Enum.reduce(rest_keys, initial_value, fn key, acc ->
     value =
       Map.get(map, key)
       |> case do
         nil -> "0"
         val -> val
       end
       |> String.replace(",", "")
       |> Decimal.new()

     Decimal.sub(acc, value)
   end)

   Map.put(map, target_key, format_number(sum))
 end

 def format(data, ytd_data, adjustments) do
   gen_loans_data(data, adjustments) ++
   gen_loans_to_banks_data(data, adjustments) ++
   gen_securities_data(data) ++
   gen_income_deposit_data(ytd_data, adjustments) ++
   gen_credit_card_data(ytd_data, adjustments) ++
   gen_all_other_data(data) ++
   gen_time_data(data, adjustments) ++
   gen_savings_data(data, adjustments) ++
   gen_deposites_data(data, adjustments) ++
   gen_interest_to_banks_data(data, adjustments) ++
   gen_mgmt_data(ytd_data, adjustments) ++
   gen_donations_data(ytd_data, adjustments) ++
   gen_prof_fees_data(ytd_data, adjustments) ++
   gen_insulance_faulds_data(ytd_data, adjustments) ++
   gen_edu_training_data(ytd_data, adjustments) ++
   gen_depress_data(ytd_data, adjustments) ++
   gen_equipment_data(ytd_data, adjustments) ++
   gen_occupancy_data(ytd_data, adjustments) ++
   gen_sub_debt_data(ytd_data, adjustments) ++
   gen_fees_data(ytd_data, adjustments) ++
   gen_fees_fx_data(ytd_data, adjustments) ++
   gen_realised_fxh_gains_data(ytd_data, adjustments) ++
   gen_realised_t_gains_data(ytd_data, adjustments) ++
   gen_loans_data_sap(ytd_data, adjustments) ++
   gen_loans_to_banks_data_sap(ytd_data, adjustments) ++
   gen_securities_tbills_data_sap(ytd_data, adjustments) ++
   gen_securities_bonds_data_sap(ytd_data, adjustments) ++
   gen_income_deposit_data_sap(ytd_data, adjustments) ++
   gen_credit_card_data_sap(ytd_data, adjustments) ++
   gen_all_other_data_sap(ytd_data, adjustments) ++
   gen_deposit_time_data_sap(ytd_data, adjustments) ++
   gen_deposit_data_sap(ytd_data, adjustments) ++
   gen_deposit_lcy_data_sap(ytd_data, adjustments) ++
   gen_test_fxn(ytd_data, adjustments) ++
   gen_interest_to_banks_data_sap(ytd_data, adjustments) ++
   gen_fees_fx_data_sap(ytd_data, adjustments) ++
   gen_realised_fxh_gains_data_sap(ytd_data, adjustments) ++
   gen_sub_debt_data_sap(ytd_data, adjustments) ++
   gen_all_other_sap(ytd_data, adjustments) ++
   gen_commision_fees_data_sap(ytd_data, adjustments) ++
   gen_occupancy_data_sap(ytd_data) ++
   gen_equipment_data_sap(ytd_data) ++
   gen_depress_data_sap(ytd_data) ++
   gen_edu_training_data_sap(ytd_data) ++
   gen_prof_fees_data_sap(ytd_data) ++
   gen_insulance_faulds_data_sap(ytd_data) ++
   gen_mgmt_data_sap(ytd_data) ++
   gen_donations_data_sap(ytd_data) ++
   gen_salary_emp_benefits_data(ytd_data) ++
   gen_other_non_interest_data(ytd_data) ++
   gen_others_provision_data(ytd_data) ++
   gen_specific_data(ytd_data) ++
   gen_general_data(ytd_data) ++
   gen_taxation_data(ytd_data) ++
   gen_other_non_interest_income_data(ytd_data)

 end

 def format_map(data) do
   data = data ++ total_cols()
   Enum.reduce(data, %{}, fn i, a ->
     val = MisReports.Workers.CellFormat.format(i, data, a)
     Map.put(a, i.index, to_string(val))
   end)
 end

 def total_cols() do
   [
     %{index: "C11", value: "0.0"},
     %{index: "C19", value: "0.0"},
     %{index: "C23", value: "0.0"},
     %{index: "C33", value: "0.0"},
     %{index: "C44", value: "0.0"},
     %{index: "C35", value: "0.0"},
     # %{index: "C51", value: "0.0"},
     # %{index: "C54", value: "0.0"},
     %{index: "C50", value: "0.0"},
     %{index: "C60", value: "0.0"},
     %{index: "C61", value: "0.0"},
     %{index: "C68", value: "0.0"},
     %{index: "C69", value: "0.0"},
     %{index: "C72", value: "0.0"},
     %{index: "C81", value: "0.0"},
     %{index: "C82", value: "0.0"},
     %{index: "C84", value: "0.0"},
     %{index: "C91", value: "0.0"},
     %{index: "C94", value: "0.0"},
     # %{index: "C95", value: "0.0"},
     # %{index: "C96", value: "0.0"},
     # %{index: "C97", value: "0.0"},
     # %{index: "C98", value: "0.0"},
   ]
 end

 def format_number(val) do
   case val do
     "0.0" -> ""
     _ ->
       val = to_string(val)
       if val == "" do
         "0"
       else
         v = Decimal.new(val)
         float = Decimal.to_float(v)
         Number.Delimit.number_to_delimited(float, [
           precision: 2,
           delimiter: ",",
           separator: "."
         ])
         |> case do
           "0.00" -> "0"
           num -> String.replace_trailing(num, ".00", "")
         end
       end
   end
 end

 #------------------Interest income from Loans and advances --------------------
 def gen_loans_data(data, adjustments) do
   lcl_retail = get_sum(data, "L", "R", adjustments)
   lcl_corp = get_sum(data, "L", "C", adjustments)

   fcl_retail = get_sum(data, "F", "R", adjustments)
   fcl_corp = get_sum(data, "F", "C", adjustments)
   [
     %{num: 1, index: "C12", value: format_amt(lcl_retail), type: "D"},
     %{num: 2, index: "C13", value: format_amt(lcl_corp), type: "D"},
     %{num: 3, index: "C14", value: format_amt(fcl_retail), type: "D"},
     %{num: 4, index: "C15", value: format_amt(fcl_corp), type: "D"}
   ]
 end

 def get_sum(data, ccy, type, adjustments) do
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   type = type(type)
   adjusted_amt = Decimal.mult(get_filtered_maps(adjustments, type, ccy, lgl()), "-1")

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.business_unit_grp != type or e.sap_gl_acc_no not in lgl()
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 defp type("C"), do: "Corporate and Investment Banking"
 defp type("R"), do: "Personal and Business Banking"

 defp add_and_convert(acc, amt) do
   Decimal.add(acc, amt)
 end

 def format_amt(amt) do
   _amt = if(amt == "0.0", do: Decimal.new("0.0"), else: amt)
   |> Decimal.mult("-1")
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.round(0)
 end

 def mult_by_one(amt) do
  _amt = if(amt == "0.0", do: Decimal.new("0.0"), else: amt)
  # Decimal.abs(amt)
  |> Decimal.div(Decimal.new("1000"))
  |> Decimal.round(0)
end

 def multiply_neg_one(amt) do
   amt = if(amt == "0.0", do: Decimal.new("0.0"), else: amt)

   amt
   |> Decimal.mult(Decimal.new("-1"))
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.round(0)
 end


 def flip_amt(amt) do
   amt =
     if amt == "0.0" do
       Decimal.new("0.0")
     else
       Decimal.new(amt)
     end

   amt
   |> flip_sign()
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.round(0)
 end

 defp flip_sign(amt) do
   if Decimal.positive?(amt) do
     Decimal.negate(amt)
   else
     Decimal.abs(amt)
   end
 end

 def rebase_amt(amt) do
   amt = if(amt == "0.0", do: Decimal.new("0.0"), else: amt)
   Decimal.abs(amt)
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.round(0, :down)
 end

 def lgl() do
   # [
   #   "700085", "705900", "708600", "708605", "708620", "709500", "700095", "700272", "700273",
   #   "703905", "706400", "702300", "735700", "708500", "709800", "708705", "870016", "702000",
   #   "728800", "734260"
   # ]

   Mappings.get_gl("LOAN_ADVANCES_NORMAL_DEPOSITS")
 end


 #"870016", "728800", "702000", removed

 # [""700005", "733200"]

 # ["727500], interest from banks

 #------------------ Interest income from loans to banks --------------------
 def gen_loans_to_banks_data(data, adjustments) do
   local = get_loans_to_banks_data(adjustments, data, "L")
   foreign = get_loans_to_banks_data(adjustments, data, "F")

   [
     %{num: 1, index: "C20", value: format_amt(local), type: "D"},
     %{num: 2, index: "C21", value: format_amt(foreign), type: "D"},
     # %{num: 1, index: "C38", value: lcd_corp, type: "D"},
   ]
 end

 def get_loans_to_banks_data(adjustments, data, ccy) do
   gls = Mappings.get_gl("FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS")
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   adjusted_amt = Decimal.mult(get_gut_by_currency(adjustments, ccy, gls), "-1")

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.sap_gl_acc_no not in gls
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 #------------------ Interest paid to banks and financial institutions --------------------
 def gen_interest_to_banks_data(data, adjustments) do
   local = get_interest_to_banks_data(data, adjustments, "L")
   foregn = get_interest_to_banks_data(data, adjustments, "F")
   [
     %{num: 1, index: "C53", value: mult_by_one(local), type: "D"}, #change from C52 TO 52 & 53
     %{num: 2, index: "C52", value: mult_by_one(foregn), type: "D"},
   ]
 end

 def get_interest_to_banks_data(data, adjustments, ccy) do
   gls = Mappings.get_gl("INTEREST_TO_BANKS")
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   adjusted_amt = Decimal.mult(get_gut_by_currency(adjustments, ccy, gls), "-1")

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.sap_gl_acc_no not in gls
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 #--------------- securities govt bonds and tbills ---------------
 def gen_securities_data(data) do
   gls = Mappings.get_gl("SECURITIES_TREASURY_BILLS")
   tbills = data
   |> Enum.reject(fn e -> e.sap_gl_acc_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)

   tbills = [
     %{num: 5, index: "C24", value: format_amt(tbills), type: "D"}
   ]

   gbonds_gls = Mappings.get_gl("SECURITIES_GOVERNMENT_BONDS")
   gbonds = data
   |> Enum.reject(fn e -> e.sap_gl_acc_no not in gbonds_gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)

   gbonds = [
     %{num: 6, index: "C25", value: format_amt(gbonds), type: "D"}
   ]

   tbills ++ gbonds
 end

 #-------------------leasing income from normal deposits--------------------------
 def gen_income_deposit_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("LEASING_INCOME_FROM_NORMAL_DEPOSITS") #added "721000", "870016" from mm workings
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C30", value: format_amt(inc), type: "D"}]
 end

  #-----------------Credit Cards-----------------------------
  def gen_credit_card_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("CREDIT_CARDS")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C31", value: format_amt(val), type: "D"}]
 end

 #------------------- all other ----------------
 def gen_all_other_data(data) do
   inc = data
   |> Enum.reject(fn e -> e.sap_gl_acc_no not in Mappings.get_gl("ALL_OTHER") end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)

   [%{num: 5, index: "C32", value: format_amt(inc), type: "D"}]
 end

 #-----------------general management-------------------------
 def gen_mgmt_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("MANAGEMENT_FEES")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C92", value: mult_by_one(val), type: "D"}]
 end

 #------------------ donations ----------------
 def gen_donations_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("DONATIONS")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C93", value: mult_by_one(val), type: "D"}]
 end

 #------------- professional fees ----------
 def gen_prof_fees_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("AUDIT_LEGAL_AND_PROFESSIONAL_FEES")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C89", value: mult_by_one(val), type: "D"}]
 end

 #------------------ insurance ----------------
 def gen_insulance_faulds_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("INSURANCE")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C90", value: mult_by_one(val), type: "D"}]
 end

 #------------------ education and training ----------------
 def gen_edu_training_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("EDUCATION_AND_TRAINING")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C88", value: mult_by_one(val), type: "D"}]
 end

 #------------------ suordinated debt ----------------
 def gen_sub_debt_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("SUBORDINATED_DEBT")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C57", value: mult_by_one(val), type: "D"}]
 end

 #------------------ depressi ----------------
 def gen_depress_data(ytd_data, adjustments) do
   adjusted_amt = get_sap_adjustment(adjustments, depress_gl())

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in depress_gl() end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C87", value: mult_by_one(val), type: "D"}]
 end

 def depress_gl() do
   Mappings.get_gl("DEPRECIATION") #
 end

 #-----------------equipment # "457000", "492020", "456600", "490760" -> equipment
 def gen_equipment_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("EQUIPMENT")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C86", value: mult_by_one(val), type: "D"}]
 end

 #-----------------occupancy
 def gen_occupancy_data(ytd_data, adjustments) do
   adjusted_amt = get_sap_adjustment(adjustments, occupancy_gl())

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in occupancy_gl() end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C85", value: mult_by_one(val), type: "D"}]
 end

 def occupancy_gl() do
   Mappings.get_gl("OCCUPANCY")
 end

 #-----------------Total Fees and Commission-------------------------
 def gen_fees_data(ytd_data, adjustments) do
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, fees_gl()), "-1")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in fees_gl() end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C71", value: format_amt(val), type: "D"}]
 end

 def fees_gl() do
   Mappings.get_gl("COMMISION_FEES")
 end

 #--------------Fees from f/x transactions-------------------------------------
 def gen_fees_fx_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("FEES_FROM_FOREIGN_EXCHANGE")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), "-1")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C73", value: format_amt(val), type: "D"}]
 end

 #------------------ Realised gains/ (losses) from foreign exchange holdings ----------------
 def gen_realised_fxh_gains_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), "-1")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C77", value: format_amt(val), type: "D"}]
 end

 #------------------ Realised trading gains/ (losses) ----------------
 def gen_realised_t_gains_data(ytd_data, adjustments) do
   gls = Mappings.get_gl("REALIZED_TRADING_GAINS_LOSSES_FROM_FX")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "C75", value: Decimal.new("0"), type: "D"}]
 end

 #------------------ Time --------------------
 def gen_time_data(data, adjustments) do
   lct_retail = get_time_data(data, "L", "R", adjustments)
   lct_corp = get_time_data(data, "L", "C", adjustments)

   fct_retail = get_time_data(data, "F", "R", adjustments)
   fct_corp = get_time_data(data, "F", "C", adjustments)

   [
     %{num: 1, index: "C45", value: mult_by_one(lct_retail), type: "D"},
     %{num: 2, index: "C46", value: mult_by_one(fct_retail), type: "D"},
     %{num: 1, index: "C47", value: mult_by_one(lct_corp), type: "D"},
     %{num: 2, index: "C48", value: mult_by_one(fct_corp), type: "D"},
   ]
 end

 #------------time deposits-------------------
 def get_time_data(data, ccy, type, adjustments) do
   gls = Mappings.get_gl("TIME")
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   type = time_type(type)
   adjusted_amt = get_filtered_maps(adjustments, type, ccy, gls)

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.business_unit_grp != type or e.sap_gl_acc_no not in gls
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 defp time_type("C"), do: "Corporate and Investment Banking"
 defp time_type("R"), do: "Personal and Business Banking"

 #------------------ deposites --------------------
 def gen_deposites_data(data, adjustments) do
   lcd_retail = get_deposites_data(data, "L", "R", adjustments)
   lcd_corp = get_deposites_data(data, "L", "C", adjustments)

   fcd_retail = get_deposites_data(data, "F", "R", adjustments)
   fcd_corp = get_deposites_data(data, "F", "C", adjustments)

   [
     %{num: 1, index: "C36", value: mult_by_one(lcd_retail), type: "D"},
     %{num: 2, index: "C37", value: mult_by_one(fcd_retail), type: "D"},
     %{num: 1, index: "C38", value: mult_by_one(lcd_corp), type: "D"},
     %{num: 2, index: "C39", value: mult_by_one(fcd_corp), type: "D"},
   ]
 end

 #-----------------demand deposits-------------------
 def get_deposites_data(data, ccy, type, adjustments) do
   gls =  Mappings.get_gl("DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND")
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   type = deposites_type(type)
   adjusted_amt = get_filtered_maps(adjustments, type, ccy, gls)

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.business_unit_grp != type or e.sap_gl_acc_no not in gls
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 defp deposites_type("C"), do: "Corporate and Investment Banking"
 defp deposites_type("R"), do: "Personal and Business Banking"

 #------------------ savings --------------------
 def gen_savings_data(data, adjustments) do
   lcs_retail = get_savings_data(data, "L", "R", adjustments)
   lcs_corp = get_savings_data(data, "L", "C", adjustments)

   fcs_retail = get_savings_data(data, "F", "R", adjustments)
   fcs_corp = get_savings_data(data, "F", "C", adjustments)

   [
     %{num: 1, index: "C40", value: mult_by_one(lcs_retail), type: "D"},
     %{num: 2, index: "C41", value: mult_by_one(fcs_retail), type: "D"},
     %{num: 1, index: "C42", value: mult_by_one(lcs_corp), type: "D"},
     %{num: 2, index: "C43", value: mult_by_one(fcs_corp), type: "D"},
   ]
 end


 #-----------------savings-------------------------
 def get_savings_data(data, ccy, type, adjustments) do
   gls = Mappings.get_gl("DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS")
   ccy = if(ccy == "L", do: "LCY", else: "FCY")
   type = savings_type(type)
   adjusted_amt = get_filtered_maps(adjustments, type, ccy, gls)

   data
   |> Enum.reject(fn e ->
     e.ccy_cat != ccy or e.business_unit_grp != type or e.sap_gl_acc_no not in gls
   end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.mdt_mvmt_in_lcy) end)
   |> Decimal.add(adjusted_amt)
 end

 # NEED CLARITY
 defp savings_type("C"), do: "Corporate and Investment Banking" #"CS02OTHEN Other Entities"
 defp savings_type("R"), do: "Personal and Business Banking"

 def get_filtered_maps(adjustment, business_unit, currency, gls) do
   adjustment
   |> Enum.filter(fn {_key, value} ->
     amount = value[:amount]
     if Decimal.negative?(amount) do
       if value[:debit] in gls and value[:business_unit_debit] == business_unit and value[:cur_cat_debit] == currency do
         true
       else
         value[:credit] in gls and value[:business_unit_credit] == business_unit and value[:cur_cat_credit] == currency
       end
     else
       false
     end
   end)
   |> Enum.map(fn {key, value} -> value end)
   |> Enum.reduce("0.0", fn i, a ->  Decimal.add(a, i.amount) end)
 end

 def get_gut_by_currency(adjustment, currency, gls) do
   adjustment
   |> Enum.filter(fn {_key, value} ->
     amount = value[:amount]
     if Decimal.negative?(amount) do
       if value[:debit] in gls and value[:cur_cat_debit] == currency do
         true
       else
         value[:credit] in gls and value[:cur_cat_credit] == currency
       end
     else
       false
     end
   end)
   |> Enum.map(fn {key, value} -> value end)
   |> Enum.reduce("0.0", fn i, a ->  Decimal.add(a, i.amount) end)
 end

 def get_sap_adjustment(adjustments, gls) do
   adjustments
   |> Enum.filter(fn {_key, value} ->
     amount = value[:amount]
     if Decimal.negative?(amount) do
       if value[:debit] in gls and value[:type_debit] == "SAP" do
         true
       else
         value[:credit] in gls and value[:type_credit]  == "SAP"
       end
     else
       false
     end
   end)
   |> Enum.map(fn {key, value} -> value end)
   |> Enum.reduce("0.0", fn i, a ->  Decimal.add(a, i.amount) end)
 end

 ################################# SCHEDULES DATA #####################

 def schedules_data(_data, date, ytd_data) do
   %{
     "C59"=> Workers.Sh27a.get_total(ytd_data),
     "C84"=> Workers.Sh18b.get_total(ytd_data, date),
     "C64"=> LoansAdvances.Sh03a.generate_display(date)[:list]["C21"] |> to_decimal,
     "C65"=> LoansAdvances.Sh03a.generate_display(date)[:list]["B21"] |> to_decimal,
     "C67"=> schedule_4d(date)["L32"] |> round_num,
     "C80"=> Workers.Sh28b.get_my_total(ytd_data),
     "C91"=> Workers.Sh18a.get_c16_value(date),
     "C94"=> Workers.Sh27.get_total(ytd_data),
     "C97"=> Workers.Sh24.generate_display(date)["C27"] |> to_decimal
   }
 end

 defp to_decimal(value) when is_nil(value), do: Decimal.new("0.0")
 defp to_decimal(value), do: value |> String.replace(",", "") |> Decimal.new()

 def merge_some(map) do
   nf = sum_nf(map)
   c82 = Map.get(map, "C82", Decimal.new(0))
   c97 = Map.get(map, "C97", Decimal.new(0))

   ns = Decimal.sub(c82, nf)
   ne = Decimal.sub(ns, c97)

   Map.merge(map, %{"C95"=> nf, "C96"=> ns, "C98"=> ne, "ne"=> ne})
 end

 def sum_nf(map) do
   Map.take(map, ["C84", "C85", "C86", "C87", "C88", "C89", "C90", "C91", "C92", "C93", "C94"])
   |> Enum.reduce("0.0", fn {k, v}, acc ->
     Decimal.add(acc, v)
   end)
 end

 def round_num(amt) do
   amt =
     case amt do
       nil -> Decimal.new("0.0")
       "0.0" -> Decimal.new("0.0")
       _ -> amt
     end
   amt |> Decimal.round(0)
 end

 def schedule_4d(date) do
   trial_balance = MisReports.SourceData.get_tb_values_4d(date)
   current_date = Date.from_iso8601!(date)
   previous_date = Timex.shift(current_date, months: -1) |> Timex.end_of_month()
     data   =  %{
               "G24" => get_trial_balance_values_current(trial_balance, current_date, ["62000"]),
               "G25" => get_trial_balance_values_current(trial_balance, current_date, ["M114200"]),
               "H24" => get_trial_balance_values_current(trial_balance, current_date, ["59500", "59510", "59520"]),
               "H25" => get_trial_balance_values_current(trial_balance, current_date, ["M105420"]),
               "J24" => get_trial_balance_values_current(trial_balance, current_date, ["210766"]),
               "K24" => get_trial_balance_values_current(trial_balance, current_date, ["311500", "311510", "311520"]),

               "J25" => get_trial_balance_values_current(trial_balance, current_date, ["210767"]),
               "K25" => get_trial_balance_values_current(trial_balance, current_date, ["311530", "311540", "311560"]),

               "G28" => get_trial_balance_values_previous(trial_balance, previous_date, ["62000"]),
               "G29" => get_trial_balance_values_previous(trial_balance, previous_date, ["M114200"]),
               "H28" => get_trial_balance_values_previous(trial_balance, previous_date, ["59500", "59510", "59520"]),
               "H29" => get_trial_balance_values_previous(trial_balance, previous_date, ["M105420"]),
               "J28" => get_trial_balance_values_previous(trial_balance, previous_date, ["210766"]),
               "K28" => get_trial_balance_values_previous(trial_balance, previous_date, ["311500", "311510", "311520"]),

               "J29" => get_trial_balance_values_previous(trial_balance, previous_date, ["210767"]),
               "K29" => get_trial_balance_values_previous(trial_balance, previous_date, ["311530", "311540", "311560"])
           }
       data =     Map.merge(data,
                       %{
                         "L24" => data["G24"] |> Decimal.add(data["H24"]) |> Decimal.add(data["J24"]) |> Decimal.add(data["K24"]),
                         "L25" => data["G25"] |> Decimal.add(data["H25"] ) |> Decimal.add(data["J25"] ) |> Decimal.add(data["K25"] ),
                         "L28" => data["G28"] |> Decimal.add(data["H28"]) |> Decimal.add(data["J28"]) |> Decimal.add(data["K28"]),
                         "L29" => data["G29"] |> Decimal.add(data["H29"] ) |> Decimal.add(data["J29"] ) |> Decimal.add(data["K29"] ),
                         "G32" => Decimal.sub(Decimal.add(data["G24"], data["G25"]), Decimal.add(data["G28"], data["G29"])),#G24 + G25 - G28 + G29
                         "H32" => Decimal.sub(Decimal.add(data["H24"], data["H25"]), Decimal.add(data["H28"], data["H29"])),#H24 + H25 - H28 + H29
                         "I32" => Decimal.sub(Decimal.add(data["I24"] || Decimal.new("0"), Decimal.new("0")), Decimal.add(data["I28"] || Decimal.new("0"), Decimal.new("0"))),#I24 + I25 - I28 + I29
                         "J32" => Decimal.sub(Decimal.add(data["J24"] || Decimal.new("0"), data["J25"] || Decimal.new("0")), Decimal.add(data["J28"] || Decimal.new("0"), data["J29"] || Decimal.new("0"))),#J24 + J25 - J28 + J29
                         "K32" => Decimal.sub(Decimal.add(data["K24"] || Decimal.new("0"), data["K25"] || Decimal.new("0")), Decimal.add(data["K28"] || Decimal.new("0"), data["K29"] || Decimal.new("0")))#K24 + K25 - K28 + K29
                         }
                       )

                 final_data = Map.merge(data, %{"L32" => data["G32"] |> Decimal.add(data["H32"]) |> Decimal.add(data["I32"]) |> Decimal.add(data["J32"]) |> Decimal.add(data["K32"])})
  final_data
 end

 defp get_trial_balance_values_current(trial_balance, current_date, gl) do
   trial_balance |> Enum.filter(fn data -> data["date"] == current_date and data["gl_no"] in gl  end)
   |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, "actual_this_year", Decimal.new("0"))) end)
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.abs()
 end

 defp get_trial_balance_values_previous(trial_balance, previous_date, gl) do
   trial_balance |> Enum.filter(fn data -> data["date"] == previous_date and data["gl_no"] in gl  end)
   |> Enum.reduce(Decimal.new("0"), fn map, acc -> Decimal.add(acc, Map.get(map, "actual_this_year", Decimal.new("0"))) end)
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.abs()

 end

 #---------------------------------SAP Numbers Functions-------------------------------------------
 def gen_loans_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("LOAN_ADVANCES_NORMAL_DEPOSITS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B11", value: format_amt(inc), type: "D"}]
 end

 def gen_loans_to_banks_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("FROM_BANKS_AND_OTHER_FINANCIAL_INSTITUTIONS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B19", value: format_amt(inc), type: "D"}]
 end

 def gen_securities_tbills_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("SECURITIES_TREASURY_BILLS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B24", value: format_amt(inc), type: "D"}]
 end

 def gen_securities_bonds_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("SECURITIES_GOVERNMENT_BONDS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B25", value: format_amt(inc), type: "D"}]
 end

 def gen_income_deposit_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("LEASING_INCOME_FROM_NORMAL_DEPOSITS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B30", value: format_amt(inc), type: "D"}]
 end

 def gen_credit_card_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("CREDIT_CARDS")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B31", value: format_amt(inc), type: "D"}]
 end

 def gen_all_other_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("ALL_OTHER")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B32", value: format_amt(inc), type: "D"}]
 end

 def gen_deposit_time_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("TIME")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B44", value: mult_by_one(inc), type: "D"}]
 end

 def gen_deposit_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("DEPOSITS_FOREIGN_LOCAL_CURRENCY_DEMAND")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B43", value: mult_by_one(inc), type: "D"}]
 end

 def gen_deposit_lcy_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("DEPOSITS_FOREIGN_LOCAL_CURRENCY_SAVINGS")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B40", value: mult_by_one(inc), type: "D"}]
 end

 def gen_test_fxn(ytd_data, adjustments) do
   gls = ["754800", "744800", "740115", "763080", "749000", "734800", "763105", "742200"]
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "X40", value: format_amt(inc), type: "D"}]
 end

 #Interest paid to banks and financial institutions:
 def gen_interest_to_banks_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("INTEREST_TO_BANKS")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B51", value: mult_by_one(inc), type: "D"}]
 end

 def gen_sub_debt_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("SUBORDINATED_DEBT")
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B57", value: mult_by_one(inc), type: "D"}]
 end

 def gen_all_other_sap(ytd_data, adjustments) do
   gls = ["757310", "757320", "757330", "757340", "763460"]
   adjusted_amt = get_sap_adjustment(adjustments, gls)

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B59", value: format_all_other(inc), type: "D"}]
 end

 def format_all_other(amt) do
   amt = if(amt == "0.0", do: Decimal.new("0.0"), else: amt)
   # Decimal.abs(amt)
   |> Decimal.div(Decimal.new("1000"))
   |> Decimal.round(0)
 end

 def gen_commision_fees_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("COMMISION_FEES")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B71", value: format_amt(inc), type: "D"}]
 end

 def gen_fees_fx_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("FEES_FROM_FOREIGN_EXCHANGE")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B73", value: format_amt(inc), type: "D"}]
 end

 def gen_realised_fxh_gains_data_sap(ytd_data, adjustments) do
   gls = Mappings.get_gl("TRADING_INCOME_REALIZED_TRADING_GAINS_LOSSES")
   adjusted_amt = Decimal.mult(get_sap_adjustment(adjustments, gls), Decimal.new(-1))

   inc = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   |> Decimal.add(adjusted_amt)
   [%{num: 5, index: "B77", value: format_amt(inc), type: "D"}]
 end

 def gen_occupancy_data_sap(ytd_data) do
   gls = Mappings.get_gl("OCCUPANCY")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B85", value: mult_by_one(val), type: "D"}]
 end

 def gen_equipment_data_sap(ytd_data) do
   gls = Mappings.get_gl("EQUIPMENT")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B86", value: mult_by_one(val), type: "D"}]
 end

 def gen_depress_data_sap(ytd_data) do
   gls = Mappings.get_gl("DEPRECIATION")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B87", value: mult_by_one(val), type: "D"}]
 end

 def gen_edu_training_data_sap(ytd_data) do
   gls = Mappings.get_gl("EDUCATION_AND_TRAINING")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B88", value: mult_by_one(val), type: "D"}]
 end

 def gen_prof_fees_data_sap(ytd_data) do
   gls = Mappings.get_gl("AUDIT_LEGAL_AND_PROFESSIONAL_FEES")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B89", value: mult_by_one(val), type: "D"}]
 end

 def gen_insulance_faulds_data_sap(ytd_data) do
   gls = Mappings.get_gl("INSURANCE")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B90", value: mult_by_one(val), type: "D"}]
 end

 def gen_mgmt_data_sap(ytd_data) do
   gls = Mappings.get_gl("MANAGEMENT_FEES")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B92", value: mult_by_one(val), type: "D"}]
 end

 def gen_donations_data_sap(ytd_data) do
   gls = Mappings.get_gl("DONATIONS")

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B93", value: mult_by_one(val), type: "D"}]
 end

 #loan loses general
 def gen_general_data(ytd_data) do
   gls = ["775030", "775035", "775040", "775045", "775046", "775050", "775170", "775085",
     "775090", "775095", "775100", "775101", "775105", "775185"
     ]

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B64", value: mult_by_one(val), type: "D"}]
 end

 #loan loses specific
 def gen_specific_data(ytd_data) do
   gls = ["775140", "775145", "775150", "775155", "775156", "775160", "775210" ] #"775160", "775210" newly added

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B65", value: mult_by_one(val), type: "D"}]
 end

 #loan specific others
 def gen_others_provision_data(ytd_data) do
   gls = ["775005", "775000", "775060", "775010", "775025", "775080", "775165", "775175", "775180", "775190"]

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B67", value: mult_by_one(val), type: "D"}]
 end

 def gen_salary_emp_benefits_data(ytd_data) do
   gls =
      [
          "401500", "404500", "401900", "404300", "404302", "406150", "407340", "407320", "407341",
          "492780", "495134", "497725", "497735", "497170", "401901", "403000", "403300", "403410",
          "401000", "403414", "401110", "403415", "407780", "407840", "407845", "401111", "403004",
          "408000", "404100", "403101", "403100", "406100", "402300", "400000", "405104", "402800",
          "402810", "402812", "402819", "400017", "404800", "404900", "405100", "407100", "493340",
          "402802", "407680", "495710", "400035"
      ]

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B84", value: mult_by_one(val), type: "D"}]
 end

 def gen_other_non_interest_income_data(ytd_data) do
   gls = ["833730", "883100", "870540", "870450", "771600", "772000", "771400", "771300", "771605", "774200", "775239", "885090"]

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B80", value: format_amt(val), type: "D"}]
 end

 def gen_other_non_interest_data(ytd_data) do
   gls = [
          "404200", "491330", "493100", "490030", "429800", "491044", "492740", "492800", "492820", "492840", "490016",
          "455900", "490740", "455100", "460000", "460100", "460700", "460900", "461000", "462200", "465400", "465900",
          "466200", "465500", "466300", "466350", "466400", "491850", "470005", "475700", "475300", "476200", "476300",
          "477200", "480000", "480100", "480400", "481300", "481500", "481600", "481900", "482102", "492960", "486300",
          "485000", "486000", "486200", "486600", "486700", "487000", "487400", "491960", "492440", "466100", "490780",
          "490880", "492020", "492180", "492320", "493160", "494300", "493780", "493460", "490220", "490280", "492160",
          "490940", "486210", "490900", "491250", "492892", "492896", "496957", "496959", "461500", "461501", "461502",
          "461503", "461510", "461511", "461512", "496891", "496981", "499291", "499292", "499293", "499294", "499295",
          "499296", "499297", "499298", "499299", "499300", "499302", "499304", "499308", "496866", "490218", "499020",
          "499260", "499060"
        ]

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B94", value: mult_by_one(val), type: "D"}]
 end

 def gen_taxation_data(ytd_data) do
   gls = ["499025", "499035"]    #"M346000"

   val = ytd_data
   |> Enum.reject(fn e -> e.gl_no not in gls end)
   |> Enum.reduce("0.0", fn i, a -> add_and_convert(a, i.actual_this_year_difference) end)
   [%{num: 5, index: "B97", value: mult_by_one(val), type: "D"}]
 end

 #TEST FUNCTIONS
 defp sum_ytd_difference(ytd_data) do
   gl_codes = tb_gls()

   ytd_map =
     ytd_data
     |> Enum.reduce(%{}, fn %{gl_no: gl_no, actual_this_year_difference: difference}, acc ->
       Map.update(acc, gl_no, difference, &Decimal.add(&1, difference))
     end)

   gl_sums =
     gl_codes
     |> Enum.reduce(%{}, fn gl_no, acc ->
       sum = Map.get(ytd_map, gl_no, Decimal.new(0))
       Map.put(acc, gl_no, sum)
     end)

   total_sum =
     gl_sums
     |> Enum.reduce(Decimal.new(0), fn {_gl_no, sum}, acc ->
       Decimal.add(acc, sum)
     end)

   %{
     gl_sums: gl_sums,
     total_sum: total_sum
   }
 end

 defp sum_gbm_values(data) do
   gl_codes = tb_gls()

   data =
     data
     |> Enum.reduce(%{}, fn %{sap_gl_acc_no: sap_gl_acc_no, mdt_mvmt_in_lcy: mdt_mvmt_in_lcy}, acc ->
       mdt_mvmt_in_lcy =
         case mdt_mvmt_in_lcy do
           nil -> Decimal.new(0)
           value -> Decimal.new(value)
         end

       Map.update(acc, sap_gl_acc_no, mdt_mvmt_in_lcy, &Decimal.add(&1, mdt_mvmt_in_lcy))
     end)

   gl_sums =
     gl_codes
     |> Enum.reduce(%{}, fn sap_gl_acc_no, acc ->
       sum = Map.get(data, sap_gl_acc_no, Decimal.new(0))
       Map.put(acc, sap_gl_acc_no, sum)
     end)

   total_sum =
     gl_sums
     |> Enum.reduce(Decimal.new(0), fn {_sap_gl_acc_no, sum}, acc ->
       Decimal.add(acc, sum)
     end)

   %{
     gl_sums: gl_sums,
     total_sum: total_sum
   }
 end

 def tb_gls() do
   [
     "734260", "728800", "702000", "870016", "708705", "709800", "708500", "735700", "702300", "706400",
     "703905", "700273", "700272", "700095", "709500", "708620", "708605", "708600", "705900", "700085"
   ]


 end

end
