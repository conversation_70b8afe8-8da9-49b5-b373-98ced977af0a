defmodule MisReports.Workers.BozReq.Schedule12 do

  def perform(item) do

    decoded_item =
      case item.schedule_12 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-1WSCH121W001",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1192_00001", #required
          "Value" => "#{decoded_item["C17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00002",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00003",
          "Value" => "#{decoded_item["C17"] || "0"}",#required
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00004",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00005",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00006",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00007",
          "Value" => "#0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00008",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00009",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00010",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00011",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00012",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00013",
          "Value" => "#{decoded_item["C17"] || "0"}",#required
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00014",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00015",
          "Value" => "#{decoded_item["C17"] || "0"}",#required
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00016",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00017",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00018",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00019",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00020",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00021",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00022",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00023",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00024",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00025",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00026",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00027",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00028",
          "Value" => "#{decoded_item["C17"] || "0"}",#required
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00029",
          "Value" => "0.0",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1192_00030",
          "Value" => "#{decoded_item["C17"] || "0"}",#required
          "_dataType" => "NUMERIC"
        }
      ],

      "dynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
