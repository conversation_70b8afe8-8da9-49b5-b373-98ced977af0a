defmodule MisReports.Enums.FileTypes do
  @moduledoc """
  Enum for file upload types with their associated process codes and privileges.
  """

  def all_uploads do
    # Remove maintenance items from all_uploads
    finance_files() ++ credit_files()
  end

  def finance_files do
    [
      %{
        value: "ALL_DEAL",
        label: "All deals",
        process_code: 1004,
        privilege: "finance_uploads"
      },
      %{
        value: "CCR",
        label: "Customer Contribution",
        process_code: 1003,
        privilege: "finance_uploads"
      },
      %{
        value: "CUST_SEG",
        label: "GBM File",
        process_code: 1006,
        privilege: "finance_uploads"
      },
      %{
        value: "FX_CASH",
        label: "Fx Cash Flow",
        process_code: 1005,
        privilege: "finance_uploads"
      },
      %{
        value: "RISK_FILE",
        label: "Risk file",
        process_code: 1011,
        privilege: "finance_uploads"
      },
      %{
        value: "SCHEDULE_20",
        label: "Schedule 20 file",
        process_code: 1109,
        privilege: "finance_uploads"
      },
      %{
        value: "GMO_TPINS",
        label: "GMO Tpins",
        process_code: 1020,
        privilege: "finance_uploads"
      },
      %{
        value: "TRIALBALANCEBS",
        label: "Trial Balance Balance Sheet",
        process_code: 1040,
        privilege: "finance_uploads"
      },
      %{
        value: "TRIALBALANCEIS",
        label: "Trial Balance Income Statement",
        process_code: 1041,
        privilege: "finance_uploads"
      },
      %{
        value: "FINANCE_SECTORS",
        label: "Customer details ",
        process_code: 1055,
        privilege: "finance_uploads"
      },

       %{
        value: "NOSTRO",
        label: "Nostro File",
        process_code: 1022,
        privilege: "finance_uploads"
      },
      %{
        value: "WEEKLY_ALL_DEAL",
        label: "All deals",
        process_code: 1023,
        privilege: "finance_uploads"
      },
      %{
        value: "WEEKLY_CCR",
        label: "Customer Contribution",
        process_code: 1024,
        privilege: "finance_uploads"
      },
      %{
        value: "DAILY_GUT",
        label: "GBM File",
        process_code: 1025,
        privilege: "finance_uploads"
      },
      %{
        value: "DAILY_ALL_DEAL",
        label: "Daily All deals",
        process_code: 1107,
        privilege: "finance_uploads"
      },
      %{
        value: "FINACLE_TB",
        label: "Finacle Trial Balance",
        process_code: 1010,
        privilege: "finance_uploads"
      },
    ]
  end

  def credit_files do
    [
      %{
        value: "CREDIT_CARDS",
        label: "Credit Cards",
        process_code: 1012,
        privilege: "credit_uploads"
      },
      %{
        value: "GDP_FILE",
        label: "GDP File",
        process_code: 1013,
        privilege: "credit_uploads"
      },
      %{
        value: "GOVERNMENT_EXPOSURE_LIST",
        label: "Government Exposure List",
        process_code: 1014,
        privilege: "credit_uploads"
      },
      %{
        value: "ACCOUNT_DOMICILE_BRANCH",
        label: "Account Domicile Branch",
        process_code: 1031,
        privilege: "credit_uploads"
      },
      %{
        value: "INSIDER_LENDING",
        label: "Insider Lending",
        process_code: 1015,
        privilege: "credit_uploads"
      },
      %{
        value: "LARGE_LOANS",
        label: "Large Loans",
        process_code: 1016,
        privilege: "credit_uploads"
      },
      %{
        value: "OBDDR",
        label: "OBDDR",
        process_code: 1017,
        privilege: "credit_uploads"
      },
      %{
        value: "RELIEF_LIST",
        label: "Relief List",
        process_code: 1018,
        privilege: "credit_uploads"
      },
      %{
        value: "DAYS_PAST_DUE",
        label: "Days Past Due",
        process_code: 1019,
        privilege: "credit_uploads"
      },
      %{
        value: "CUST_CODE",
        label: "Customer details ",
        process_code: 1056,
        privilege: "credit_uploads"
      },
      %{
        value: "LOAN_SCHEME_CODES",
        label: "Scheme Codes",
        process_code: 1027,
        privilege: "credit_uploads"
      }
    ]
  end



  def finance_maintenance do
    [
      %{
        value: "CURRENT_DEFERRED_TAX",
        label: "Current and Deferred Tax",
        process_code: 1036,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "OFF_BALANCE_SHEET",
        label: "Off Balance Sheet",
        process_code: 1037,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "COUNTERPARTY_SECURITIES",
        label: "Counterparty Securities",
        process_code: 1039,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "TECH_INFRASTRUCTURE",
        label: "Technological Infrastructure",
        process_code: 1032,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "REPOSSESSED_PROPERTIES",
        label: "Repossessed Properties",
        process_code: 1033,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "ENCUMBERED_SECURITIES",
        label: "Encumbered Securities",
        process_code: 1034,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "WEEKLY_BOZ_BALANCE",
        label: "Weekly BOZ Balance",
        process_code: 1106,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "WEEKLY_EXCHANGE_RATES",
        label: "Weekly Exchange Rates",
        process_code: 1108,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "BAL_DUE_DOMESTIC",
        label: "Balance Due Domestic",
        process_code: 1110,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "SHARE_HOLDERS",
        label: "Share Holders",
        process_code: 1111,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "GOV_ACCOUNTS",
        label: "Government Accounts",
        process_code: 1114,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "INSTITUTION_DETAILS",
        label: "Institution Details",
        process_code: 1115,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "EXCHANGE_PLACEMENTS",
        label: "Exchange Placements",
        process_code: 1116,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "EMPLOYEE_STATS",
        label: "Employee Stats",
        process_code: 1117,
        privilege: "finance_uploads",
        is_maintenance: true
      },
      %{
        value: "BENEFITS_EMPLOYEE",
        label: "Employee Benefits",
        process_code: 1118,
        privilege: "finance_uploads",
        is_maintenance: true
      }

    ]
  end

  def credit_maintenance do
    [
      %{
        value: "REGULATORY_CAPITAL",
        label: "Regulatory Capital",
        process_code: 1026,
        privilege: "credit_uploads",
        is_maintenance: true
      },
      %{
        value: "EXCHANGE_RATES",
        label: "Exchange Rates",
        process_code: 1029,
        privilege: "credit_uploads",
        is_maintenance: true
      },
      %{
        value: "ALLOWANCE_LOAN_LOSSES",
        label: "Allowance for Loan Losses",
        process_code: 1030,
        privilege: "credit_uploads",
        is_maintenance: true
      },
      %{
        value: "PAST_DUE_CLASSIFICATION",
        label: "Loans Classification",
        process_code: 1112,
        privilege: "credit_uploads",
        is_maintenance: true
      },
      %{
        value: "DEBTORS_BOOK_ANALYSIS",
        label: "Debtors Book Analysis",
        process_code: 1113,
        privilege: "credit_uploads",
        is_maintenance: true
      }
    ]
  end

  def get_finance_uploads do
    # Use finance_files directly instead of filtering all_uploads
    finance_files()
  end

  def get_credit_uploads do
    # Use credit_files directly instead of filtering all_uploads
    credit_files()
  end

  def get_uploads_by_privilege(privilege)
      when privilege in ["finance_uploads", "credit_uploads"] do
    case privilege do
      "finance_uploads" -> finance_files()
      "credit_uploads" -> credit_files()
    end
  end

  def get_uploads_by_role_privilege(role_str) do
    finance_uploads_allowed = role_str["finance_uploads"]["new"] == "Y"
    credit_uploads_allowed = role_str["credit_uploads"]["new"] == "Y"

    if(finance_uploads_allowed, do: finance_files(), else: []) ++
      if credit_uploads_allowed, do: credit_files(), else: []
  end

  def get_uploads_by_process_code(process_code) do
    (finance_files() ++ credit_files() ++ finance_maintenance() ++ credit_maintenance())
    |> Enum.find(&(&1.process_code == process_code))
  end

  def get_uploads_by_value(value) do
    (finance_files() ++ credit_files() ++ finance_maintenance() ++ credit_maintenance())
    |> Enum.find(&(&1.value == value))
  end

  def get_finance_maintenance do
    # Use finance_maintenance() directly instead of filtering all_uploads
    finance_maintenance()
  end

  def get_credit_maintenance do
    # Use credit_maintenance() directly instead of filtering all_uploads
    credit_maintenance()
  end
end
