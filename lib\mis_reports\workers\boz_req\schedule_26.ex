defmodule MisReports.Workers.BozReq.Schedule26 do
  alias MisReports.Workers.BalanceSheet

  def perform(item) do
    decoded_item =
      case item.schedule_26 do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-9JSCH269J002",
      "instCode" => "#{settings.institution_code}",
      # "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      # "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      # "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
"FinYear" => 2019,
      "StartDate" => "2019-02-01T00:00:00",
      "EndDate" => "2019-02-28T00:00:00",

      "ReturnItemsList" => build_return_items_list(decoded_item),
      "DynamicItemsList" => [
        %{
          "Area" => 453,
          "_areaName" => "ALL OTHER LIABILITIES",
          "DynamicItems" => map_data(decoded_item)
        }
      ]
    }
  end

  defp build_return_items_list(items) do
    [
      %{
        "Code" => "1175_00001",
        "Value" => "#{Map.get(items, "B14", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00002",
        "Value" => "#{Map.get(items, "B15", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00003",
        "Value" => "#{Map.get(items, "B16", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00004",
        "Value" => "#{Map.get(items, "B17", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00005",
        "Value" => "#{Map.get(items, "B18", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00006",
        "Value" => "#{Map.get(items, "B19", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00007",
        "Value" => "#{Map.get(items, "B20", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00008",
        "Value" => "#{Map.get(items, "B21", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00009",
        "Value" => "#{Map.get(items, "B22", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00010",
        "Value" => "#{Map.get(items, "B23", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00011",
        "Value" => "#{Map.get(items, "B24", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00012",
        "Value" => "#{Map.get(items, "B25", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00013",
        "Value" => "#{Map.get(items, "B26", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1175_00014",
        "Value" => "#{Map.get(items, "B31", "0")}",
        "_dataType" => "NUMERIC"
      }
    ]
  end

  defp map_data(records) do
    Enum.flat_map(Enum.with_index(records["list"] || []), fn {map, idx} ->
      index = idx + 1
      [
        %{"Code" => "#{index}.1", "Value" => "#{Map.get(map, "Description (Note 1)", "N/A")}", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.2", "Value" => "#{convert_to_number(Map.get(map, "Balance", 0))}", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.3", "Value" => "#{convert_to_number(Map.get(map, "Rate of Interest (%)", 0))}", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.4", "Value" => "#{format_date(Map.get(map, "Maturity Date"))}", "_dataType" => "DATE"},
        %{"Code" => "#{index}.5", "Value" => "#{Map.get(map, "Currency", "N/A")}", "_dataType" => "TEXT"},
        %{"Code" => "#{index}.6", "Value" => "#{convert_to_number(Map.get(map, "Exchange Rate", 0))}", "_dataType" => "NUMERIC"},
        %{"Code" => "#{index}.7", "Value" => "#{Map.get(map, "Nature of Balance (Note 2)", "N/A")}", "_dataType" => "TEXT"}
      ]
    end)
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, _} -> Decimal.from_float(num)
      :error -> Decimal.new(0)
    end
  end

  defp convert_to_number(value) when is_number(value), do: Decimal.new(value)
  defp convert_to_number(_), do: Decimal.new(0)

  defp format_date(nil), do: "N/A"
  defp format_date(date) do
    Timex.format!(date, "%Y-%m-%dT%H:%M:%SZ", :strftime)
  rescue
    _ -> "N/A"
  end


end
