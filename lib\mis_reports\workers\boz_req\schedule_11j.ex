defmodule MisReports.Workers.BozReq.Schedule11j do

  def perform(item) do

    decoded_item =
      case item.schedule_11j do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-7FSCH11J7F002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{ "Code" => "1154_00001",
          "Value" => "#{format_number(decoded_item["B15"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00002",
          "Value" => "#{format_number(decoded_item["C15"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00003",
          "Value" => "#{format_number(decoded_item["D15"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00004",
          "Value" => "#{format_number(decoded_item["E15"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00005",
          "Value" => "#{convert_to_number(decoded_item["F15"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00006",
          "Value" => "#{format_number(decoded_item["B16"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00007",
          "Value" => "#{format_number(decoded_item["C16"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00008",
          "Value" => "#{format_number(decoded_item["D16"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00009",
          "Value" => "#{format_number(decoded_item["E16"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00010",
          "Value" => "#{convert_to_number(decoded_item["F16"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00011",
          "Value" => "#{format_number(decoded_item["B17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00012",
          "Value" => "#{format_number(decoded_item["C17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00013",
          "Value" => "#{format_number(decoded_item["D17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00014",
          "Value" => "#{format_number(decoded_item["E17"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00015",
          "Value" => "#{convert_to_number(decoded_item["F17"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00016",
          "Value" => "#{format_number(decoded_item["B18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00017",
          "Value" => "#{format_number(decoded_item["C18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00018",
          "Value" => "#{format_number(decoded_item["D18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00019",
          "Value" => "#{format_number(decoded_item["E18"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00020",
          "Value" => "#{convert_to_number(decoded_item["F18"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00021",
          "Value" => "#{format_number(decoded_item["B20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00022",
          "Value" => "#{format_number(decoded_item["C20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00023",
          "Value" => "#{format_number(decoded_item["D20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00024",
          "Value" => "#{format_number(decoded_item["E20"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00025",
          "Value" => "#{convert_to_number(decoded_item["F20"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00026",
          "Value" => "#{format_number(decoded_item["B19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00027",
          "Value" => "#{format_number(decoded_item["C19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00028",
          "Value" => "#{format_number(decoded_item["D19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00029",
          "Value" => "#{format_number(decoded_item["E19"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00030",
          "Value" => "#{convert_to_number(decoded_item["F19"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00031",
          "Value" => "#{format_number(decoded_item["B21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00032",
          "Value" => "#{format_number(decoded_item["C21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00033",
          "Value" => "#{format_number(decoded_item["D21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00034",
          "Value" => "#{format_number(decoded_item["E21"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00035",
          "Value" => "#{convert_to_number(decoded_item["F21"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00036",
          "Value" => "#{format_number(decoded_item["B22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00037",
          "Value" => "#{format_number(decoded_item["C22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00038",
          "Value" => "#{format_number(decoded_item["D22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00039",
          "Value" => "#{format_number(decoded_item["E22"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00040",
          "Value" => "#{convert_to_number(decoded_item["F22"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00041",
          "Value" => "#{format_number(decoded_item["B23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00042",
          "Value" => "#{format_number(decoded_item["C23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00043",
          "Value" => "#{format_number(decoded_item["D23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00044",
          "Value" => "#{format_number(decoded_item["E23"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00045",
          "Value" => "#{convert_to_number(decoded_item["F23"]) || "0"}%",
          "_dataType" => "NUMERIC" },
          %{ "Code" => "1154_00046",
          "Value" => "#{format_number(decoded_item["B24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00047",
          "Value" => "#{format_number(decoded_item["C24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00048",
          "Value" => "#{format_number(decoded_item["D24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00049",
          "Value" => "#{format_number(decoded_item["E24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00050",
          "Value" => "#{convert_to_number(decoded_item["F24"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00051",
          "Value" => "#{format_number(decoded_item["B25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00052",
          "Value" => "#{format_number(decoded_item["C24"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00053",
          "Value" => "#{format_number(decoded_item["D25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00054",
          "Value" => "#{format_number(decoded_item["E25"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00055",
          "Value" => "#{convert_to_number(decoded_item["F25"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00056",
          "Value" => "#{format_number(decoded_item["B26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00057",
          "Value" => "#{format_number(decoded_item["C26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00058",
          "Value" => "#{format_number(decoded_item["D26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00059",
          "Value" => "#{format_number(decoded_item["E26"]) |> Decimal.new() |> Decimal.to_float() || "0"}",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00060",
          "Value" => "#{format_number(decoded_item["F26"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00061",
          "Value" => "#{convert_to_number(decoded_item["B27"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00062",
          "Value" => "#{convert_to_number(decoded_item["C27"]) || "0"}%",
          "_dataType" => "NUMERIC" },
        %{ "Code" => "1154_00063",
          "Value" => "#{convert_to_number(decoded_item["D27"]) || "0"}%",
          "_dataType" => "NUMERIC" }
      ],
        "dynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values like "0"
  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
  # If String.to_float fails, try integer conversion
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

 defp convert_to_number(_), do: 0.0
end
