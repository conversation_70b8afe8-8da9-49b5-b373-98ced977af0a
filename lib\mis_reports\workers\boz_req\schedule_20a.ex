defmodule MisReports.Workers.BozReq.Schedule20a do
  def perform(item) do
    decoded_item =
      case item.schedule_20a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # regulatory_capital = decoded_item["header"]

    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-8KSCH20A8K002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" =>
        "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1166_00001", "Value" => "#{decoded_item["C17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00002", "Value" => "#{decoded_item["D17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00003", "Value" => "#{decoded_item["E17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00004", "Value" => "#{decoded_item["F17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00005", "Value" => "#{decoded_item["G17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00006", "Value" => "#{decoded_item["H17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00007", "Value" => "#{decoded_item["I17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00008", "Value" => "#{decoded_item["J17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00009", "Value" => "#{decoded_item["K17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00010", "Value" => "#{decoded_item["L17"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00011", "Value" => "#{decoded_item["C21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00012", "Value" => "#{decoded_item["D21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00013", "Value" => "#{decoded_item["E21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00014", "Value" => "#{decoded_item["F21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00015", "Value" => "#{decoded_item["G21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00016", "Value" => "#{decoded_item["H21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00017", "Value" => "#{decoded_item["I21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00018", "Value" => "#{decoded_item["J21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00019", "Value" => "#{decoded_item["K21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00020", "Value" => "#{decoded_item["L21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00021", "Value" => "#{decoded_item["M21"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00022", "Value" => "#{decoded_item["C22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00023", "Value" => "#{decoded_item["D22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00024", "Value" => "#{decoded_item["E22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00025", "Value" => "#{decoded_item["F22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00026", "Value" => "#{decoded_item["G22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00027", "Value" => "#{decoded_item["H22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00028", "Value" => "#{decoded_item["I22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00029", "Value" => "#{decoded_item["J22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00030", "Value" => "#{decoded_item["K22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00031", "Value" => "#{decoded_item["L22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00032", "Value" => "#{decoded_item["M22"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00033", "Value" => "#{decoded_item["C23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00034", "Value" => "#{decoded_item["D23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00035", "Value" => "#{decoded_item["E23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00036", "Value" => "#{decoded_item["F23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00037", "Value" => "#{decoded_item["G23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00038", "Value" => "#{decoded_item["H23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00039", "Value" => "#{decoded_item["I23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00040", "Value" => "#{decoded_item["J23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00041", "Value" => "#{decoded_item["K23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00042", "Value" => "#{decoded_item["L23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00043", "Value" => "#{decoded_item["M23"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00044", "Value" => "#{decoded_item["C24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00045", "Value" => "#{decoded_item["D24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00046", "Value" => "#{decoded_item["E24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00047", "Value" => "#{decoded_item["F24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00048", "Value" => "#{decoded_item["G24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00049", "Value" => "#{decoded_item["H24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00050", "Value" => "#{decoded_item["I24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00051", "Value" => "#{decoded_item["J24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00052", "Value" => "#{decoded_item["K24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00053", "Value" => "#{decoded_item["L24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00054", "Value" => "#{decoded_item["M24"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00055", "Value" => "#{decoded_item["C25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00056", "Value" => "#{decoded_item["D25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00057", "Value" => "#{decoded_item["E25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00058", "Value" => "#{decoded_item["F25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00059", "Value" => "#{decoded_item["G25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00060", "Value" => "#{decoded_item["H25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00061", "Value" => "#{decoded_item["I25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00062", "Value" => "#{decoded_item["J25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00063", "Value" => "#{decoded_item["K25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00064", "Value" => "#{decoded_item["L25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00065", "Value" => "#{decoded_item["M25"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00066", "Value" => "#{decoded_item["C26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00067", "Value" => "#{decoded_item["D26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00068", "Value" => "#{decoded_item["E26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00069", "Value" => "#{decoded_item["F26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00070", "Value" => "#{decoded_item["G26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00071", "Value" => "#{decoded_item["H26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00072", "Value" => "#{decoded_item["I26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00073", "Value" => "#{decoded_item["J26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00074", "Value" => "#{decoded_item["K26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00075", "Value" => "#{decoded_item["L26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00076", "Value" => "#{decoded_item["M26"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00077", "Value" => "#{decoded_item["C27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00078", "Value" => "#{decoded_item["D27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00079", "Value" => "#{decoded_item["E27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00080", "Value" => "#{decoded_item["F27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00081", "Value" => "#{decoded_item["G27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00082", "Value" => "#{decoded_item["H27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00083", "Value" => "#{decoded_item["I27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00084", "Value" => "#{decoded_item["J27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00085", "Value" => "#{decoded_item["K27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00086", "Value" => "#{decoded_item["L27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00087", "Value" => "#{decoded_item["M27"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00088", "Value" => "#{decoded_item["C28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00089", "Value" => "#{decoded_item["D28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00090", "Value" => "#{decoded_item["E28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00091", "Value" => "#{decoded_item["F28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00092", "Value" => "#{decoded_item["G28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00093", "Value" => "#{decoded_item["H28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00094", "Value" => "#{decoded_item["I28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00095", "Value" => "#{decoded_item["J28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00096", "Value" => "#{decoded_item["K28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00097", "Value" => "#{decoded_item["L28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00098", "Value" => "#{decoded_item["M28"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00099", "Value" => "#{decoded_item["C29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00100", "Value" => "#{decoded_item["D29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00101", "Value" => "#{decoded_item["E29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00102", "Value" => "#{decoded_item["F29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00103", "Value" => "#{decoded_item["G29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00104", "Value" => "#{decoded_item["H29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00105", "Value" => "#{decoded_item["I29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00106", "Value" => "#{decoded_item["J29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00107", "Value" => "#{decoded_item["K29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00108", "Value" => "#{decoded_item["L29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00109", "Value" => "#{decoded_item["M29"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00110", "Value" => "#{decoded_item["C30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00111", "Value" => "#{decoded_item["D30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00112", "Value" => "#{decoded_item["E30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00113", "Value" => "#{decoded_item["F30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00114", "Value" => "#{decoded_item["G30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00115", "Value" => "#{decoded_item["H30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00116", "Value" => "#{decoded_item["I30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00117", "Value" => "#{decoded_item["J30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00118", "Value" => "#{decoded_item["K30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00119", "Value" => "#{decoded_item["L30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00120", "Value" => "#{decoded_item["M30"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00121", "Value" => "#{decoded_item["C31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00122", "Value" => "#{decoded_item["D31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00123", "Value" => "#{decoded_item["E31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00124", "Value" => "#{decoded_item["F31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00125", "Value" => "#{decoded_item["G31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00126", "Value" => "#{decoded_item["H31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00127", "Value" => "#{decoded_item["I31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00128", "Value" => "#{decoded_item["J31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00129", "Value" => "#{decoded_item["K31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00130", "Value" => "#{decoded_item["L31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00131", "Value" => "#{decoded_item["M31"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00132", "Value" => "#{decoded_item["C32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00133", "Value" => "#{decoded_item["D32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00134", "Value" => "#{decoded_item["E32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00135", "Value" => "#{decoded_item["F32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00136", "Value" => "#{decoded_item["G32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00137", "Value" => "#{decoded_item["H32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00138", "Value" => "#{decoded_item["I32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00139", "Value" => "#{decoded_item["J32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00140", "Value" => "#{decoded_item["K32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00141", "Value" => "#{decoded_item["L32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00142", "Value" => "#{decoded_item["M32"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00143", "Value" => "#{decoded_item["C33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00144", "Value" => "#{decoded_item["D33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00145", "Value" => "#{decoded_item["E33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00146", "Value" => "#{decoded_item["F33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00147", "Value" => "#{decoded_item["G33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00148", "Value" => "#{decoded_item["H33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00149", "Value" => "#{decoded_item["I33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00150", "Value" => "#{decoded_item["J33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00151", "Value" => "#{decoded_item["K33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00152", "Value" => "#{decoded_item["L33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00153", "Value" => "#{decoded_item["M33"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00154", "Value" => "#{decoded_item["C34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00155", "Value" => "#{decoded_item["D34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00156", "Value" => "#{decoded_item["E34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00157", "Value" => "#{decoded_item["F34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00158", "Value" => "#{decoded_item["G34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00159", "Value" => "#{decoded_item["H34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00160", "Value" => "#{decoded_item["I34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00161", "Value" => "#{decoded_item["J34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00162", "Value" => "#{decoded_item["K34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00163", "Value" => "#{decoded_item["L34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00164", "Value" => "#{decoded_item["M34"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00165", "Value" => "#{decoded_item["C35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00166", "Value" => "#{decoded_item["D35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00167", "Value" => "#{decoded_item["E35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00168", "Value" => "#{decoded_item["F35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00169", "Value" => "#{decoded_item["G35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00170", "Value" => "#{decoded_item["H35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00171", "Value" => "#{decoded_item["I35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00172", "Value" => "#{decoded_item["J35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00173", "Value" => "#{decoded_item["K35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00174", "Value" => "#{decoded_item["L35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00175", "Value" => "#{decoded_item["M35"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00176", "Value" => "#{decoded_item["C36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00177", "Value" => "#{decoded_item["D36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00178", "Value" => "#{decoded_item["E36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00179", "Value" => "#{decoded_item["F36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00180", "Value" => "#{decoded_item["G36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00181", "Value" => "#{decoded_item["H36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00182", "Value" => "#{decoded_item["I36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00183", "Value" => "#{decoded_item["J36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00184", "Value" => "#{decoded_item["K36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00185", "Value" => "#{decoded_item["L36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00186", "Value" => "#{decoded_item["M36"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00187", "Value" => "#{decoded_item["C37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00188", "Value" => "#{decoded_item["D37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00189", "Value" => "#{decoded_item["E37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00190", "Value" => "#{decoded_item["F37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00191", "Value" => "#{decoded_item["G37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00192", "Value" => "#{decoded_item["H37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00193", "Value" => "#{decoded_item["I37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00194", "Value" => "#{decoded_item["J37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00195", "Value" => "#{decoded_item["K37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00196", "Value" => "#{decoded_item["L37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00197", "Value" => "#{decoded_item["M37"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00198", "Value" => "#{decoded_item["C38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00199", "Value" => "#{decoded_item["D38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00200", "Value" => "#{decoded_item["E38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00201", "Value" => "#{decoded_item["F38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00202", "Value" => "#{decoded_item["G38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00203", "Value" => "#{decoded_item["H38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00204", "Value" => "#{decoded_item["I38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00205", "Value" => "#{decoded_item["J38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00206", "Value" => "#{decoded_item["K38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00207", "Value" => "#{decoded_item["L38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00208", "Value" => "#{decoded_item["M38"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00209", "Value" => "#{decoded_item["C39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00210", "Value" => "#{decoded_item["D39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00211", "Value" => "#{decoded_item["E39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00212", "Value" => "#{decoded_item["F39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00213", "Value" => "#{decoded_item["G39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00214", "Value" => "#{decoded_item["H39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00215", "Value" => "#{decoded_item["I39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00216", "Value" => "#{decoded_item["J39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00217", "Value" => "#{decoded_item["K39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00218", "Value" => "#{decoded_item["L39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00219", "Value" => "#{decoded_item["M39"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00220", "Value" => "#{decoded_item["C40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00221", "Value" => "#{decoded_item["D40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00222", "Value" => "#{decoded_item["E40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00223", "Value" => "#{decoded_item["F40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00224", "Value" => "#{decoded_item["G40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00225", "Value" => "#{decoded_item["H40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00226", "Value" => "#{decoded_item["I40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00227", "Value" => "#{decoded_item["J40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00228", "Value" => "#{decoded_item["K40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00229", "Value" => "#{decoded_item["L40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00230", "Value" => "#{decoded_item["M40"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00231", "Value" => "#{decoded_item["C41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00232", "Value" => "#{decoded_item["D41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00233", "Value" => "#{decoded_item["E41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00234", "Value" => "#{decoded_item["F41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00235", "Value" => "#{decoded_item["G41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00236", "Value" => "#{decoded_item["H41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00237", "Value" => "#{decoded_item["I41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00238", "Value" => "#{decoded_item["J41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00239", "Value" => "#{decoded_item["K41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00240", "Value" => "#{decoded_item["L41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00241", "Value" => "#{decoded_item["M41"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00242", "Value" => "#{decoded_item["C42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00243", "Value" => "#{decoded_item["D42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00244", "Value" => "#{decoded_item["E42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00245", "Value" => "#{decoded_item["F42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00246", "Value" => "#{decoded_item["G42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00247", "Value" => "#{decoded_item["H42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00248", "Value" => "#{decoded_item["I42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00249", "Value" => "#{decoded_item["J42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00250", "Value" => "#{decoded_item["K42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00251", "Value" => "#{decoded_item["L42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00252", "Value" => "#{decoded_item["M42"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00253", "Value" => "#{decoded_item["C43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00254", "Value" => "#{decoded_item["D43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00255", "Value" => "#{decoded_item["E43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00256", "Value" => "#{decoded_item["F43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00257", "Value" => "#{decoded_item["G43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00258", "Value" => "#{decoded_item["H43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00259", "Value" => "#{decoded_item["I43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00260", "Value" => "#{decoded_item["J43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00261", "Value" => "#{decoded_item["K43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00262", "Value" => "#{decoded_item["L43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00263", "Value" => "#{decoded_item["M43"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00264", "Value" => "#{decoded_item["C44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00265", "Value" => "#{decoded_item["D44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00266", "Value" => "#{decoded_item["E44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00267", "Value" => "#{decoded_item["F44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00268", "Value" => "#{decoded_item["G44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00269", "Value" => "#{decoded_item["H44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00270", "Value" => "#{decoded_item["I44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00271", "Value" => "#{decoded_item["J44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00272", "Value" => "#{decoded_item["K44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00273", "Value" => "#{decoded_item["L44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00274", "Value" => "#{decoded_item["M44"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00275", "Value" => "#{decoded_item["C45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00276", "Value" => "#{decoded_item["D45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00277", "Value" => "#{decoded_item["E45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00278", "Value" => "#{decoded_item["F45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00279", "Value" => "#{decoded_item["G45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00280", "Value" => "#{decoded_item["H45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00281", "Value" => "#{decoded_item["I45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00282", "Value" => "#{decoded_item["J45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00283", "Value" => "#{decoded_item["K45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00284", "Value" => "#{decoded_item["L45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00285", "Value" => "#{decoded_item["M45"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00286", "Value" => "#{decoded_item["C46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00287", "Value" => "#{decoded_item["D46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00288", "Value" => "#{decoded_item["E46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00289", "Value" => "#{decoded_item["F46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00290", "Value" => "#{decoded_item["G46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00291", "Value" => "#{decoded_item["H46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00292", "Value" => "#{decoded_item["I46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00293", "Value" => "#{decoded_item["J46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00294", "Value" => "#{decoded_item["K46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00295", "Value" => "#{decoded_item["L46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00296", "Value" => "#{decoded_item["M46"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00297", "Value" => "#{decoded_item["C47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00298", "Value" => "#{decoded_item["D47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00299", "Value" => "#{decoded_item["E47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00300", "Value" => "#{decoded_item["F47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00301", "Value" => "#{decoded_item["G47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00302", "Value" => "#{decoded_item["H47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00303", "Value" => "#{decoded_item["I47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00304", "Value" => "#{decoded_item["J47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00305", "Value" => "#{decoded_item["K47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00306", "Value" => "#{decoded_item["L47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00307", "Value" => "#{decoded_item["M47"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00308", "Value" => "#{decoded_item["C48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00309", "Value" => "#{decoded_item["D48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00310", "Value" => "#{decoded_item["E48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00311", "Value" => "#{decoded_item["F48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00312", "Value" => "#{decoded_item["G48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00313", "Value" => "#{decoded_item["H48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00314", "Value" => "#{decoded_item["I48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00315", "Value" => "#{decoded_item["J48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00316", "Value" => "#{decoded_item["K48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00317", "Value" => "#{decoded_item["L48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00318", "Value" => "#{decoded_item["M48"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00319", "Value" => "#{decoded_item["C49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00320", "Value" => "#{decoded_item["D49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00321", "Value" => "#{decoded_item["E49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00322", "Value" => "#{decoded_item["F49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00323", "Value" => "#{decoded_item["G49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00324", "Value" => "#{decoded_item["H49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00325", "Value" => "#{decoded_item["I49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00326", "Value" => "#{decoded_item["J49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00327", "Value" => "#{decoded_item["K49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00328", "Value" => "#{decoded_item["L49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00329", "Value" => "#{decoded_item["M49"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00330", "Value" => "#{decoded_item["C50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00331", "Value" => "#{decoded_item["D50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00332", "Value" => "#{decoded_item["E50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00333", "Value" => "#{decoded_item["F50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00334", "Value" => "#{decoded_item["G50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00335", "Value" => "#{decoded_item["H50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00336", "Value" => "#{decoded_item["I50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00337", "Value" => "#{decoded_item["J50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00338", "Value" => "#{decoded_item["K50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00339", "Value" => "#{decoded_item["L50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00340", "Value" => "#{decoded_item["M50"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00341", "Value" => "#{decoded_item["C51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00342", "Value" => "#{decoded_item["D51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00343", "Value" => "#{decoded_item["E51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00344", "Value" => "#{decoded_item["F51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00345", "Value" => "#{decoded_item["G51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00346", "Value" => "#{decoded_item["H51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00347", "Value" => "#{decoded_item["I51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00348", "Value" => "#{decoded_item["J51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00349", "Value" => "#{decoded_item["K51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00350", "Value" => "#{decoded_item["L51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00351", "Value" => "#{decoded_item["M51"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00352", "Value" => "#{decoded_item["C52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00353", "Value" => "#{decoded_item["D52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00354", "Value" => "#{decoded_item["E52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00355", "Value" => "#{decoded_item["F52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00356", "Value" => "#{decoded_item["G52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00357", "Value" => "#{decoded_item["H52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00358", "Value" => "#{decoded_item["I52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00359", "Value" => "#{decoded_item["J52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00360", "Value" => "#{decoded_item["K52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00361", "Value" => "#{decoded_item["L52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00362", "Value" => "#{decoded_item["M52"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00363", "Value" => "#{decoded_item["C53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00364", "Value" => "#{decoded_item["D53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00365", "Value" => "#{decoded_item["E53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00366", "Value" => "#{decoded_item["F53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00367", "Value" => "#{decoded_item["G53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00368", "Value" => "#{decoded_item["H53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00369", "Value" => "#{decoded_item["I53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00370", "Value" => "#{decoded_item["J53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00371", "Value" => "#{decoded_item["K53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00372", "Value" => "#{decoded_item["L53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00373", "Value" => "#{decoded_item["M53"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00374", "Value" => "#{decoded_item["C54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00375", "Value" => "#{decoded_item["D54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00376", "Value" => "#{decoded_item["E54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00377", "Value" => "#{decoded_item["F54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00378", "Value" => "#{decoded_item["G54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00379", "Value" => "#{decoded_item["H54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00380", "Value" => "#{decoded_item["I54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00381", "Value" => "#{decoded_item["J54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00382", "Value" => "#{decoded_item["K54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00383", "Value" => "#{decoded_item["L54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00384", "Value" => "#{decoded_item["M54"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00385", "Value" => "#{decoded_item["C55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00386", "Value" => "#{decoded_item["D55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00387", "Value" => "#{decoded_item["E55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00388", "Value" => "#{decoded_item["F55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00389", "Value" => "#{decoded_item["G55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00390", "Value" => "#{decoded_item["H55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00391", "Value" => "#{decoded_item["I55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00392", "Value" => "#{decoded_item["J55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00393", "Value" => "#{decoded_item["K55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00394", "Value" => "#{decoded_item["L55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00395", "Value" => "#{decoded_item["M55"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00396", "Value" => "#{decoded_item["C56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00397", "Value" => "#{decoded_item["D56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00398", "Value" => "#{decoded_item["E56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00399", "Value" => "#{decoded_item["F56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00400", "Value" => "#{decoded_item["G56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00401", "Value" => "#{decoded_item["H56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00402", "Value" => "#{decoded_item["I56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00403", "Value" => "#{decoded_item["J56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00404", "Value" => "#{decoded_item["K56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00405", "Value" => "#{decoded_item["L56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00406", "Value" => "#{decoded_item["M56"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00407", "Value" => "#{decoded_item["C57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00408", "Value" => "#{decoded_item["D57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00409", "Value" => "#{decoded_item["E57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00410", "Value" => "#{decoded_item["F57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00411", "Value" => "#{decoded_item["G57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00412", "Value" => "#{decoded_item["H57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00413", "Value" => "#{decoded_item["I57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00414", "Value" => "#{decoded_item["J57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00415", "Value" => "#{decoded_item["K57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00416", "Value" => "#{decoded_item["L57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00417", "Value" => "#{decoded_item["M57"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00418", "Value" => "#{decoded_item["C58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00419", "Value" => "#{decoded_item["D58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00420", "Value" => "#{decoded_item["E58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00421", "Value" => "#{decoded_item["F58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00422", "Value" => "#{decoded_item["G58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00423", "Value" => "#{decoded_item["H58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00424", "Value" => "#{decoded_item["I58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00425", "Value" => "#{decoded_item["J58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00426", "Value" => "#{decoded_item["K58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00427", "Value" => "#{decoded_item["L58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00428", "Value" => "#{decoded_item["M58"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00429", "Value" => "#{decoded_item["C59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00430", "Value" => "#{decoded_item["D59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00431", "Value" => "#{decoded_item["E59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00432", "Value" => "#{decoded_item["F59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00433", "Value" => "#{decoded_item["G59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00434", "Value" => "#{decoded_item["H59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00435", "Value" => "#{decoded_item["I59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00436", "Value" => "#{decoded_item["J59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00437", "Value" => "#{decoded_item["K59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00438", "Value" => "#{decoded_item["L59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00439", "Value" => "#{decoded_item["M59"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00440", "Value" => "#{decoded_item["C61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00441", "Value" => "#{decoded_item["D61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00442", "Value" => "#{decoded_item["E61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00443", "Value" => "#{decoded_item["F61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00444", "Value" => "#{decoded_item["G61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00445", "Value" => "#{decoded_item["H61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00446", "Value" => "#{decoded_item["I61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00447", "Value" => "#{decoded_item["J61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00448", "Value" => "#{decoded_item["K61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00449", "Value" => "#{decoded_item["L61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00450", "Value" => "#{decoded_item["M61"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00451", "Value" => "#{decoded_item["C62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00452", "Value" => "#{decoded_item["D62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00453", "Value" => "#{decoded_item["E62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00454", "Value" => "#{decoded_item["F62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00455", "Value" => "#{decoded_item["G62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00456", "Value" => "#{decoded_item["H62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00457", "Value" => "#{decoded_item["I62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00458", "Value" => "#{decoded_item["J62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00459", "Value" => "#{decoded_item["K62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00460", "Value" => "#{decoded_item["L62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00461", "Value" => "#{decoded_item["M62"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00462", "Value" => "#{decoded_item["C65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00463", "Value" => "#{decoded_item["D65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00464", "Value" => "#{decoded_item["E65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00465", "Value" => "#{decoded_item["F65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00466", "Value" => "#{decoded_item["G65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00467", "Value" => "#{decoded_item["H65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00468", "Value" => "#{decoded_item["I65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00469", "Value" => "#{decoded_item["J65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00470", "Value" => "#{decoded_item["K65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00471", "Value" => "#{decoded_item["L65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00472", "Value" => "#{decoded_item["M65"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00473", "Value" => "#{decoded_item["C66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00474", "Value" => "#{decoded_item["D66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00475", "Value" => "#{decoded_item["E66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00476", "Value" => "#{decoded_item["F66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00477", "Value" => "#{decoded_item["G66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00478", "Value" => "#{decoded_item["H66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00479", "Value" => "#{decoded_item["I66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00480", "Value" => "#{decoded_item["J66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00481", "Value" => "#{decoded_item["K66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00482", "Value" => "#{decoded_item["L66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00483", "Value" => "#{decoded_item["M66"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00484", "Value" => "#{decoded_item["C67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00485", "Value" => "#{decoded_item["D67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00486", "Value" => "#{decoded_item["E67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00487", "Value" => "#{decoded_item["F67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00488", "Value" => "#{decoded_item["G67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00489", "Value" => "#{decoded_item["H67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00490", "Value" => "#{decoded_item["I67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00491", "Value" => "#{decoded_item["J67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00492", "Value" => "#{decoded_item["K67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00493", "Value" => "#{decoded_item["L67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00494", "Value" => "#{decoded_item["M67"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00495", "Value" => "#{decoded_item["C68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00496", "Value" => "#{decoded_item["D68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00497", "Value" => "#{decoded_item["E68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00498", "Value" => "#{decoded_item["F68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00499", "Value" => "#{decoded_item["G68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00500", "Value" => "#{decoded_item["H68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00501", "Value" => "#{decoded_item["I68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00502", "Value" => "#{decoded_item["J68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00503", "Value" => "#{decoded_item["K68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00504", "Value" => "#{decoded_item["L68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00505", "Value" => "#{decoded_item["M68"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00506", "Value" => "#{decoded_item["C69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00507", "Value" => "#{decoded_item["D69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00508", "Value" => "#{decoded_item["E69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00509", "Value" => "#{decoded_item["F69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00510", "Value" => "#{decoded_item["G69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00511", "Value" => "#{decoded_item["H69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00512", "Value" => "#{decoded_item["I69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00513", "Value" => "#{decoded_item["J69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00514", "Value" => "#{decoded_item["K69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00515", "Value" => "#{decoded_item["L69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00516", "Value" => "#{decoded_item["M69"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00517", "Value" => "#{decoded_item["C70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00518", "Value" => "#{decoded_item["D70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00519", "Value" => "#{decoded_item["E70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00520", "Value" => "#{decoded_item["F70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00521", "Value" => "#{decoded_item["G70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00522", "Value" => "#{decoded_item["H70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00523", "Value" => "#{decoded_item["I70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00524", "Value" => "#{decoded_item["J70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00525", "Value" => "#{decoded_item["K70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00526", "Value" => "#{decoded_item["L70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00527", "Value" => "#{decoded_item["M70"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00528", "Value" => "#{decoded_item["C71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00529", "Value" => "#{decoded_item["D71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00530", "Value" => "#{decoded_item["E71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00531", "Value" => "#{decoded_item["F71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00532", "Value" => "#{decoded_item["G71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00533", "Value" => "#{decoded_item["H71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00534", "Value" => "#{decoded_item["I71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00535", "Value" => "#{decoded_item["J71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00536", "Value" => "#{decoded_item["K71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00537", "Value" => "#{decoded_item["L71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00538", "Value" => "#{decoded_item["M71"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00539", "Value" => "#{decoded_item["C72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00540", "Value" => "#{decoded_item["D72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00541", "Value" => "#{decoded_item["E72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00542", "Value" => "#{decoded_item["F72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00543", "Value" => "#{decoded_item["G72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00544", "Value" => "#{decoded_item["H72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00545", "Value" => "#{decoded_item["I72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00546", "Value" => "#{decoded_item["J72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00547", "Value" => "#{decoded_item["K72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00548", "Value" => "#{decoded_item["L72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00549", "Value" => "#{decoded_item["M72"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00550", "Value" => "#{decoded_item["C73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00551", "Value" => "#{decoded_item["D73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00552", "Value" => "#{decoded_item["E73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00553", "Value" => "#{decoded_item["F73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00554", "Value" => "#{decoded_item["G73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00555", "Value" => "#{decoded_item["H73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00556", "Value" => "#{decoded_item["I73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00557", "Value" => "#{decoded_item["J73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00558", "Value" => "#{decoded_item["K73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00559", "Value" => "#{decoded_item["L73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00560", "Value" => "#{decoded_item["M73"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00561", "Value" => "#{decoded_item["C74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00562", "Value" => "#{decoded_item["D74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00563", "Value" => "#{decoded_item["E74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00564", "Value" => "#{decoded_item["F74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00565", "Value" => "#{decoded_item["G74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00566", "Value" => "#{decoded_item["H74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00567", "Value" => "#{decoded_item["I74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00568", "Value" => "#{decoded_item["J74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00569", "Value" => "#{decoded_item["K74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00570", "Value" => "#{decoded_item["L74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00571", "Value" => "#{decoded_item["M74"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00572", "Value" => "#{decoded_item["C75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00573", "Value" => "#{decoded_item["D75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00574", "Value" => "#{decoded_item["E75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00575", "Value" => "#{decoded_item["F75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00576", "Value" => "#{decoded_item["G75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00577", "Value" => "#{decoded_item["H75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00578", "Value" => "#{decoded_item["I75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00579", "Value" => "#{decoded_item["J75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00580", "Value" => "#{decoded_item["K75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00581", "Value" => "#{decoded_item["L75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00582", "Value" => "#{decoded_item["M75"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00583", "Value" => "#{decoded_item["C76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00584", "Value" => "#{decoded_item["D76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00585", "Value" => "#{decoded_item["E76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00586", "Value" => "#{decoded_item["F76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00587", "Value" => "#{decoded_item["G76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00588", "Value" => "#{decoded_item["H76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00589", "Value" => "#{decoded_item["I76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00590", "Value" => "#{decoded_item["J76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00591", "Value" => "#{decoded_item["K76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00592", "Value" => "#{decoded_item["L76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00593", "Value" => "#{decoded_item["M76"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00594", "Value" => "#{decoded_item["C77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00595", "Value" => "#{decoded_item["D77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00596", "Value" => "#{decoded_item["E77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00597", "Value" => "#{decoded_item["F77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00598", "Value" => "#{decoded_item["G77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00599", "Value" => "#{decoded_item["H77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00600", "Value" => "#{decoded_item["I77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00601", "Value" => "#{decoded_item["J77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00602", "Value" => "#{decoded_item["K77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00603", "Value" => "#{decoded_item["L77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00604", "Value" => "#{decoded_item["M77"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00605", "Value" => "#{decoded_item["C78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00606", "Value" => "#{decoded_item["D78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00607", "Value" => "#{decoded_item["E78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00608", "Value" => "#{decoded_item["F78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00609", "Value" => "#{decoded_item["G78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00610", "Value" => "#{decoded_item["H78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00611", "Value" => "#{decoded_item["I78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00612", "Value" => "#{decoded_item["J78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00613", "Value" => "#{decoded_item["K78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00614", "Value" => "#{decoded_item["L78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00615", "Value" => "#{decoded_item["M78"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00616", "Value" => "#{decoded_item["C79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00617", "Value" => "#{decoded_item["D79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00618", "Value" => "#{decoded_item["E79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00619", "Value" => "#{decoded_item["F79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00620", "Value" => "#{decoded_item["G79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00621", "Value" => "#{decoded_item["H79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00622", "Value" => "#{decoded_item["I79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00623", "Value" => "#{decoded_item["J79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00624", "Value" => "#{decoded_item["K79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00625", "Value" => "#{decoded_item["L79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00626", "Value" => "#{decoded_item["M79"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00627", "Value" => "#{decoded_item["C80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00628", "Value" => "#{decoded_item["D80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00629", "Value" => "#{decoded_item["E80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00630", "Value" => "#{decoded_item["F80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00631", "Value" => "#{decoded_item["G80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00632", "Value" => "#{decoded_item["H80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00633", "Value" => "#{decoded_item["I80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00634", "Value" => "#{decoded_item["J80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00635", "Value" => "#{decoded_item["K80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00636", "Value" => "#{decoded_item["L80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00637", "Value" => "#{decoded_item["M80"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00638", "Value" => "#{decoded_item["C81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00639", "Value" => "#{decoded_item["D81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00640", "Value" => "#{decoded_item["E81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00641", "Value" => "#{decoded_item["F81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00642", "Value" => "#{decoded_item["G81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00643", "Value" => "#{decoded_item["H81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00644", "Value" => "#{decoded_item["I81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00645", "Value" => "#{decoded_item["J81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00646", "Value" => "#{decoded_item["K81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00647", "Value" => "#{decoded_item["L81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00648", "Value" => "#{decoded_item["M81"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00649", "Value" => "#{decoded_item["C82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00650", "Value" => "#{decoded_item["D82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00651", "Value" => "#{decoded_item["E82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00652", "Value" => "#{decoded_item["F82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00653", "Value" => "#{decoded_item["G82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00654", "Value" => "#{decoded_item["H82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00655", "Value" => "#{decoded_item["I82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00656", "Value" => "#{decoded_item["J82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00657", "Value" => "#{decoded_item["K82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00658", "Value" => "#{decoded_item["L82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00659", "Value" => "#{decoded_item["M82"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00660", "Value" => "#{decoded_item["C83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00661", "Value" => "#{decoded_item["D83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00662", "Value" => "#{decoded_item["E83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00663", "Value" => "#{decoded_item["F83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00664", "Value" => "#{decoded_item["G83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00665", "Value" => "#{decoded_item["H83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00666", "Value" => "#{decoded_item["I83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00667", "Value" => "#{decoded_item["J83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00668", "Value" => "#{decoded_item["K83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00669", "Value" => "#{decoded_item["L83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00670", "Value" => "#{decoded_item["M83"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00671", "Value" => "#{decoded_item["C84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00672", "Value" => "#{decoded_item["D84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00673", "Value" => "#{decoded_item["E84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00674", "Value" => "#{decoded_item["F84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00675", "Value" => "#{decoded_item["G84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00676", "Value" => "#{decoded_item["H84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00677", "Value" => "#{decoded_item["I84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00678", "Value" => "#{decoded_item["J84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00679", "Value" => "#{decoded_item["K84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00680", "Value" => "#{decoded_item["L84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00681", "Value" => "#{decoded_item["M84"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00682", "Value" => "#{decoded_item["C85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00683", "Value" => "#{decoded_item["D85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00684", "Value" => "#{decoded_item["E85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00685", "Value" => "#{decoded_item["F85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00686", "Value" => "#{decoded_item["G85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00687", "Value" => "#{decoded_item["H85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00688", "Value" => "#{decoded_item["I85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00689", "Value" => "#{decoded_item["J85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00690", "Value" => "#{decoded_item["K85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00691", "Value" => "#{decoded_item["L85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00692", "Value" => "#{decoded_item["M85"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00693", "Value" => "#{decoded_item["C86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00694", "Value" => "#{decoded_item["D86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00695", "Value" => "#{decoded_item["E86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00696", "Value" => "#{decoded_item["F86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00697", "Value" => "#{decoded_item["G86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00698", "Value" => "#{decoded_item["H86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00699", "Value" => "#{decoded_item["I86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00700", "Value" => "#{decoded_item["J86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00701", "Value" => "#{decoded_item["K86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00702", "Value" => "#{decoded_item["L86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00703", "Value" => "#{decoded_item["M86"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00704", "Value" => "#{decoded_item["C87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00705", "Value" => "#{decoded_item["D87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00706", "Value" => "#{decoded_item["E87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00707", "Value" => "#{decoded_item["F87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00708", "Value" => "#{decoded_item["G87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00709", "Value" => "#{decoded_item["H87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00710", "Value" => "#{decoded_item["I87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00711", "Value" => "#{decoded_item["J87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00712", "Value" => "#{decoded_item["K87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00713", "Value" => "#{decoded_item["L87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00714", "Value" => "#{decoded_item["M87"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00715", "Value" => "#{decoded_item["C88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00716", "Value" => "#{decoded_item["D88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00717", "Value" => "#{decoded_item["E88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00718", "Value" => "#{decoded_item["F88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00719", "Value" => "#{decoded_item["G88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00720", "Value" => "#{decoded_item["H88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00721", "Value" => "#{decoded_item["I88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00722", "Value" => "#{decoded_item["J88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00723", "Value" => "#{decoded_item["K88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00724", "Value" => "#{decoded_item["L88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00725", "Value" => "#{decoded_item["M88"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00726", "Value" => "#{decoded_item["C89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00727", "Value" => "#{decoded_item["D89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00728", "Value" => "#{decoded_item["E89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00729", "Value" => "#{decoded_item["F89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00730", "Value" => "#{decoded_item["G89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00731", "Value" => "#{decoded_item["H89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00732", "Value" => "#{decoded_item["I89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00733", "Value" => "#{decoded_item["J89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00734", "Value" => "#{decoded_item["K89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00735", "Value" => "#{decoded_item["L89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00736", "Value" => "#{decoded_item["M89"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00737", "Value" => "#{decoded_item["C90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00738", "Value" => "#{decoded_item["D90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00739", "Value" => "#{decoded_item["E90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00740", "Value" => "#{decoded_item["F90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00741", "Value" => "#{decoded_item["G90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00742", "Value" => "#{decoded_item["H90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00743", "Value" => "#{decoded_item["I90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00744", "Value" => "#{decoded_item["J90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00745", "Value" => "#{decoded_item["K90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00746", "Value" => "#{decoded_item["L90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00747", "Value" => "#{decoded_item["M90"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00748", "Value" => "#{decoded_item["C91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00749", "Value" => "#{decoded_item["D91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00750", "Value" => "#{decoded_item["E91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00751", "Value" => "#{decoded_item["F91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00752", "Value" => "#{decoded_item["G91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00753", "Value" => "#{decoded_item["H91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00754", "Value" => "#{decoded_item["I91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00755", "Value" => "#{decoded_item["J91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00756", "Value" => "#{decoded_item["K91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00757", "Value" => "#{decoded_item["L91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00758", "Value" => "#{decoded_item["M91"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00759", "Value" => "#{decoded_item["C92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00760", "Value" => "#{decoded_item["D92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00761", "Value" => "#{decoded_item["E92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00762", "Value" => "#{decoded_item["F92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00763", "Value" => "#{decoded_item["G92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00764", "Value" => "#{decoded_item["H92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00765", "Value" => "#{decoded_item["I92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00766", "Value" => "#{decoded_item["J92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00767", "Value" => "#{decoded_item["K92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00768", "Value" => "#{decoded_item["L92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00769", "Value" => "#{decoded_item["M92"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00770", "Value" => "#{decoded_item["C93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00771", "Value" => "#{decoded_item["D93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00772", "Value" => "#{decoded_item["E93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00773", "Value" => "#{decoded_item["F93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00774", "Value" => "#{decoded_item["G93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00775", "Value" => "#{decoded_item["H93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00776", "Value" => "#{decoded_item["I93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00777", "Value" => "#{decoded_item["J93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00778", "Value" => "#{decoded_item["K93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00779", "Value" => "#{decoded_item["L93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00780", "Value" => "#{decoded_item["M93"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00781", "Value" => "#{decoded_item["C94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00782", "Value" => "#{decoded_item["D94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00783", "Value" => "#{decoded_item["E94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00784", "Value" => "#{decoded_item["F94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00785", "Value" => "#{decoded_item["G94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00786", "Value" => "#{decoded_item["H94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00787", "Value" => "#{decoded_item["I94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00788", "Value" => "#{decoded_item["J94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00789", "Value" => "#{decoded_item["K94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00790", "Value" => "#{decoded_item["L94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00791", "Value" => "#{decoded_item["M94"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00792", "Value" => "#{decoded_item["C95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00793", "Value" => "#{decoded_item["D95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00794", "Value" => "#{decoded_item["E95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00795", "Value" => "#{decoded_item["F95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00796", "Value" => "#{decoded_item["G95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00797", "Value" => "#{decoded_item["H95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00798", "Value" => "#{decoded_item["I95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00799", "Value" => "#{decoded_item["J95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00800", "Value" => "#{decoded_item["K95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00801", "Value" => "#{decoded_item["L95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00802", "Value" => "#{decoded_item["M95"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00803", "Value" => "#{decoded_item["C96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00804", "Value" => "#{decoded_item["D96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00805", "Value" => "#{decoded_item["E96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00806", "Value" => "#{decoded_item["F96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00807", "Value" => "#{decoded_item["G96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00808", "Value" => "#{decoded_item["H96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00809", "Value" => "#{decoded_item["I96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00810", "Value" => "#{decoded_item["J96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00811", "Value" => "#{decoded_item["K96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00812", "Value" => "#{decoded_item["L96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00813", "Value" => "#{decoded_item["M96"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00814", "Value" => "#{decoded_item["C97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00815", "Value" => "#{decoded_item["D97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00816", "Value" => "#{decoded_item["E97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00817", "Value" => "#{decoded_item["F97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00818", "Value" => "#{decoded_item["G97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00819", "Value" => "#{decoded_item["H97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00820", "Value" => "#{decoded_item["I97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00821", "Value" => "#{decoded_item["J97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00822", "Value" => "#{decoded_item["K97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00823", "Value" => "#{decoded_item["L97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00824", "Value" => "#{decoded_item["M97"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00825", "Value" => "#{decoded_item["C98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00826", "Value" => "#{decoded_item["D98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00827", "Value" => "#{decoded_item["E98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00828", "Value" => "#{decoded_item["F98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00829", "Value" => "#{decoded_item["G98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00830", "Value" => "#{decoded_item["H98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00831", "Value" => "#{decoded_item["I98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00832", "Value" => "#{decoded_item["J98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00833", "Value" => "#{decoded_item["K98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00834", "Value" => "#{decoded_item["L98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00835", "Value" => "#{decoded_item["M98"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00836", "Value" => "#{decoded_item["C99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00837", "Value" => "#{decoded_item["D99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00838", "Value" => "#{decoded_item["E99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00839", "Value" => "#{decoded_item["F99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00840", "Value" => "#{decoded_item["G99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00841", "Value" => "#{decoded_item["H99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00842", "Value" => "#{decoded_item["I99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00843", "Value" => "#{decoded_item["J99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00844", "Value" => "#{decoded_item["K99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00845", "Value" => "#{decoded_item["L99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00846", "Value" => "#{decoded_item["M99"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00847", "Value" => "#{decoded_item["C100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00848", "Value" => "#{decoded_item["D100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00849", "Value" => "#{decoded_item["E100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00850", "Value" => "#{decoded_item["F100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00851", "Value" => "#{decoded_item["G100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00852", "Value" => "#{decoded_item["H100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00853", "Value" => "#{decoded_item["I100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00854", "Value" => "#{decoded_item["J100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00855", "Value" => "#{decoded_item["K100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00856", "Value" => "#{decoded_item["L100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00857", "Value" => "#{decoded_item["M100"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00858", "Value" => "#{decoded_item["C101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00859", "Value" => "#{decoded_item["D101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00860", "Value" => "#{decoded_item["E101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00861", "Value" => "#{decoded_item["F101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00862", "Value" => "#{decoded_item["G101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00863", "Value" => "#{decoded_item["H101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00864", "Value" => "#{decoded_item["I101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00865", "Value" => "#{decoded_item["J101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00866", "Value" => "#{decoded_item["K101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00867", "Value" => "#{decoded_item["L101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00868", "Value" => "#{decoded_item["M101"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00869", "Value" => "#{decoded_item["C102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00870", "Value" => "#{decoded_item["D102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00871", "Value" => "#{decoded_item["E102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00872", "Value" => "#{decoded_item["F102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00873", "Value" => "#{decoded_item["G102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00874", "Value" => "#{decoded_item["H102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00875", "Value" => "#{decoded_item["I102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00876", "Value" => "#{decoded_item["J102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00877", "Value" => "#{decoded_item["K102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00878", "Value" => "#{decoded_item["L102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00879", "Value" => "#{decoded_item["M102"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00880", "Value" => "#{decoded_item["C103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00881", "Value" => "#{decoded_item["D103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00882", "Value" => "#{decoded_item["E103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00883", "Value" => "#{decoded_item["F103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00884", "Value" => "#{decoded_item["G103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00885", "Value" => "#{decoded_item["H103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00886", "Value" => "#{decoded_item["I103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00887", "Value" => "#{decoded_item["J103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00888", "Value" => "#{decoded_item["K103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00889", "Value" => "#{decoded_item["L103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00890", "Value" => "#{decoded_item["M103"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00891", "Value" => "#{decoded_item["C104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00892", "Value" => "#{decoded_item["D104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00893", "Value" => "#{decoded_item["E104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00894", "Value" => "#{decoded_item["F104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00895", "Value" => "#{decoded_item["G104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00896", "Value" => "#{decoded_item["H104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00897", "Value" => "#{decoded_item["I104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00898", "Value" => "#{decoded_item["J104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00899", "Value" => "#{decoded_item["K104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00900", "Value" => "#{decoded_item["L104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00901", "Value" => "#{decoded_item["M104"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00902", "Value" => "#{decoded_item["C106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00903", "Value" => "#{decoded_item["D106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00904", "Value" => "#{decoded_item["E106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00905", "Value" => "#{decoded_item["F106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00906", "Value" => "#{decoded_item["G106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00907", "Value" => "#{decoded_item["H106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00908", "Value" => "#{decoded_item["I106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00909", "Value" => "#{decoded_item["J106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00910", "Value" => "#{decoded_item["K106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00911", "Value" => "#{decoded_item["L106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00912", "Value" => "#{decoded_item["M106"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00913", "Value" => "#{decoded_item["C107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00914", "Value" => "#{decoded_item["D107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00915", "Value" => "#{decoded_item["E107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00916", "Value" => "#{decoded_item["F107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00917", "Value" => "#{decoded_item["G107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00918", "Value" => "#{decoded_item["H107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00919", "Value" => "#{decoded_item["I107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00920", "Value" => "#{decoded_item["J107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00921", "Value" => "#{decoded_item["K107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00922", "Value" => "#{decoded_item["L107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00923", "Value" => "#{decoded_item["M107"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00924", "Value" => "#{decoded_item["C109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00925", "Value" => "#{decoded_item["D109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00926", "Value" => "#{decoded_item["E109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00927", "Value" => "#{decoded_item["F109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00928", "Value" => "#{decoded_item["G109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00929", "Value" => "#{decoded_item["H109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00930", "Value" => "#{decoded_item["I109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00931", "Value" => "#{decoded_item["J109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00932", "Value" => "#{decoded_item["K109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00933", "Value" => "#{decoded_item["L109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00934", "Value" => "#{decoded_item["M109"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00935", "Value" => "#{decoded_item["C110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00936", "Value" => "#{decoded_item["D110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00937", "Value" => "#{decoded_item["E110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00938", "Value" => "#{decoded_item["F110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00939", "Value" => "#{decoded_item["G110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00940", "Value" => "#{decoded_item["H110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00941", "Value" => "#{decoded_item["I110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00942", "Value" => "#{decoded_item["J110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00943", "Value" => "#{decoded_item["K110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00944", "Value" => "#{decoded_item["L110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00945", "Value" => "#{decoded_item["M110"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00946", "Value" => "#{decoded_item["C111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00947", "Value" => "#{decoded_item["D111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00948", "Value" => "#{decoded_item["E111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00949", "Value" => "#{decoded_item["F111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00950", "Value" => "#{decoded_item["G111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00951", "Value" => "#{decoded_item["H111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00952", "Value" => "#{decoded_item["I111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00953", "Value" => "#{decoded_item["J111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00954", "Value" => "#{decoded_item["K111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00955", "Value" => "#{decoded_item["L111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00956", "Value" => "#{decoded_item["M111"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00957", "Value" => "#{decoded_item["C112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00958", "Value" => "#{decoded_item["D112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00959", "Value" => "#{decoded_item["E112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00960", "Value" => "#{decoded_item["F112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00961", "Value" => "#{decoded_item["G112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00962", "Value" => "#{decoded_item["H112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00963", "Value" => "#{decoded_item["I112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00964", "Value" => "#{decoded_item["J112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00965", "Value" => "#{decoded_item["K112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00966", "Value" => "#{decoded_item["L112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00967", "Value" => "#{decoded_item["M112"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00968", "Value" => "#{decoded_item["C114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00969", "Value" => "#{decoded_item["D114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00970", "Value" => "#{decoded_item["E114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00971", "Value" => "#{decoded_item["F114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00972", "Value" => "#{decoded_item["G114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00973", "Value" => "#{decoded_item["H114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00974", "Value" => "#{decoded_item["I114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00975", "Value" => "#{decoded_item["J114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00976", "Value" => "#{decoded_item["K114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00977", "Value" => "#{decoded_item["L114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00978", "Value" => "#{decoded_item["M114"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00979", "Value" => "#{decoded_item["C115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00980", "Value" => "#{decoded_item["D115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00981", "Value" => "#{decoded_item["E115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00982", "Value" => "#{decoded_item["F115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00983", "Value" => "#{decoded_item["G115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00984", "Value" => "#{decoded_item["H115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00985", "Value" => "#{decoded_item["I115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00986", "Value" => "#{decoded_item["J115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00987", "Value" => "#{decoded_item["K115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00988", "Value" => "#{decoded_item["L115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00989", "Value" => "#{decoded_item["M115"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00990", "Value" => "#{decoded_item["C116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00991", "Value" => "#{decoded_item["D116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00992", "Value" => "#{decoded_item["E116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00993", "Value" => "#{decoded_item["F116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00994", "Value" => "#{decoded_item["G116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00995", "Value" => "#{decoded_item["H116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00996", "Value" => "#{decoded_item["I116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00997", "Value" => "#{decoded_item["J116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00998", "Value" => "#{decoded_item["K116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_00999", "Value" => "#{decoded_item["L116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01000", "Value" => "#{decoded_item["M116"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01001", "Value" => "#{decoded_item["M118"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01002", "Value" => "#{decoded_item["M119"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01003", "Value" => "#{decoded_item["M120"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01004", "Value" => "#{decoded_item["M121"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01005", "Value" => "#{decoded_item["M122"]}", "_dataType" => "NUMERIC"},
        %{"Code" => "1166_01006", "Value" => "#{decoded_item["M124"]}", "_dataType" => "NUMERIC"}
      ] |> format_map(),
      "DynamicItemsList" => []
    }


  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
