<div class="space-y-10 divide-y divide-gray-900/10">
   <div class="grid grid-cols-1 gap-x-8 gap-y-8 pt-10 md:grid-cols-3">
      <div class="px-4 sm:px-0">
         <h2 class="text-base font-semibold leading-7 text-gray-900">Publication</h2>
         <p class="mt-1 text-sm leading-6 text-gray-600">New Reports includes report type, start and end dates.</p>
      </div>
      <form class="bg-white shadow-sm ring-1 ring-gray-900/5 sm:rounded-xl md:col-span-2" phx-submit="filter-report" phx-change="validate_date">
        <div class="px-4 py-6 sm:p-8">
          <div class="grid max-w-2xl grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="sm:col-span-3 hidden">
                <div class="mt-2 hidden">
                    <input type="hidden" name="report[schedule]" id="schedule" value="quarterly">
                </div>
            </div>
            
            <div class="sm:col-span-3">
              <label for="start-date" class="block text-sm font-medium leading-6 text-gray-900">Start date</label>
              <div class="mt-2">
                <input 
                  type="date" 
                  name="report[start_date]" 
                  id="report-form_start_date" 
                  value={@start_date}  
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                  required
                > 
              </div>
            </div>
      
            <div class="sm:col-span-3">
              <label for="end-date" class="block text-sm font-medium leading-6 text-gray-900">End date</label>
              <div class="mt-2">
                <input 
                  type="date" 
                  name="report[end_date]" 
                  id="report-form_end_date"
                  value={@end_date}
                  readonly
                  class={"block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset #{if @date_error, do: "ring-red-500", else: "ring-gray-300"} placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"}
                >
                <%= if @date_error do %>
                  <p class="mt-1 text-sm text-red-500"><%= @date_error %></p>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      
        <div class="flex items-center justify-end gap-x-6 border-t border-gray-900/10 px-4 py-4 sm:px-8">
          <button type="button" class="text-sm font-semibold leading-6 text-gray-900">Cancel</button>
          <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Proceed</button>
        </div>
      </form>
   </div>
</div>