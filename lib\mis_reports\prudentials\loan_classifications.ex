defmodule MisReports.Prudentials.LoanClassifications do
  use Ecto.Schema
  use Endon
  import Ecto.Changeset

  @cast [
    :loan_classification,
    :days_past_due,
    :past_due_npl_classification,
    :status,
    :maker_id,
    :checker_id,
    :reference
  ]

  @required [
    :loan_classification,
    :days_past_due,
    :past_due_npl_classification
  ]

  @timestamps_opts [autogenerate: {MisReports.Accounts.User.Localtime, :autogenerate, []}]
  schema "tbl_loan_classification" do
    field :loan_classification, :string
    field :days_past_due, :string
    field :past_due_npl_classification, :string
    field :status, :string, default: "D"
    field :reference, :string
    belongs_to :maker, MisReports.Accounts.User, foreign_key: :maker_id, type: :id
    belongs_to :checker, MisReports.Accounts.User, foreign_key: :checker_id, type: :id

    timestamps()
  end

  @doc false
  def changeset(loan_classification, attrs) do
    loan_classification
    |> cast(attrs, @cast)
    |> validate_required(@required)
  end

  def fields, do: [:inserted_at, :updated_at | @cast]


  def insert_loan_classifications_pl do
    MisReports.Repo.transaction(fn ->
      Enum.each(
        [%MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "0-29 days",
          past_due_npl_classification: "Pass"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "30-59 days",
          past_due_npl_classification: "Past due"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "60-89 days",
          past_due_npl_classification: "Special Mention"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "90-119 days",
          past_due_npl_classification: "Special Mention"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "120-179 days",
          past_due_npl_classification: "Special Mention"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "180-269 days",
          past_due_npl_classification: "Special Mention"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "270-364 days",
          past_due_npl_classification: "Special Mention"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "PL",
          days_past_due: "365 days or more",
          past_due_npl_classification: "Special Mention"
        }
      ], fn changeset ->
        case MisReports.Repo.insert(changeset) do
          {:ok, _} -> IO.puts("Record inserted successfully")
          {:error, changeset} -> IO.inspect(changeset.errors)
        end
      end)
    end)
  end

  def insert_loan_classifications_npl do
    MisReports.Repo.transaction(fn ->
      Enum.each(
        [%MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "0-29 days",
          past_due_npl_classification: "Sub-standard 1"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "30-59 days",
          past_due_npl_classification: "Sub-standard 1"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "60-89 days",
          past_due_npl_classification: "Sub-standard 1"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "90-119 days",
          past_due_npl_classification: "Sub-standard 1"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "120-179 days",
          past_due_npl_classification: "Sub-standard 2"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "180-269 days",
          past_due_npl_classification: "Doubtful 1"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "270-364 days",
          past_due_npl_classification: "Doubtful 2"
        },
        %MisReports.Prudentials.LoanClassifications{
          loan_classification: "NPL",
          days_past_due: "365 days or more",
          past_due_npl_classification: "Loss"
        }
      ], fn changeset ->
        case MisReports.Repo.insert(changeset) do
          {:ok, _} -> IO.puts("Record inserted successfully")
          {:error, changeset} -> IO.inspect(changeset.errors)
        end
      end)
    end)
  end
end
