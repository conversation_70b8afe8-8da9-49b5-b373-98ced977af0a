defmodule MisReports.Workers.BozReq.Schedule31f do
  require <PERSON><PERSON>

  def perform(item) do
    decoded_item =
      case item.schedule_31f do
        # Handle nil case
        nil ->
          %{}

        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            # Handle error gracefully
            {:error, _} -> %{}
          end
      end

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-8MSCH331F8M002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" =>
        "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1190_00001",
          "Value" => "#{format_number(decoded_item[:total]) || "0"}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 463,
          "_areaName" => "LOANS FROM BANK OF ZAMBIA - TARGETED MEDIUM TERM REFINANCING FACILITY",
          "DynamicItems" => map_data(decoded_item)
        }
      ]
    }
  end

  def map_data(records) when is_list(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
      index = index + 1

      try do
        [
          %{
            "Code" => "#{index}.1",
            "Value" => safe_decimal_conversion(format_number(map["actual_credit_balance"])),
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.2",
            "Value" => "#{map["currency_code"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{
            "Code" => "#{index}.3",
            "Value" => safe_decimal_conversion(format_number(map["actual_credit_balance"])),
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "#{index}.4",
            "Value" =>
              safe_decimal_conversion(convert_to_number(map["effective_credit_rate_unformatted"])),
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "#{index}.5",
            "Value" => "#{map["account_open_date"] || "N/A"}",
            "_dataType" => "DATE"
          },
          %{
            "Code" => "#{index}.6",
            "Value" => "#{map["account_maturity_date"] || "N/A"}",
            "_dataType" => "DATE"
          },
          %{
            "Code" => "#{index}.7",
            "Value" => safe_decimal_conversion(format_number(map["actual_credit_balance"])),
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "#{index}.8",
            "Value" => "#{map["Perfoming assets"] || "N/A"}",
            "_dataType" => "TEXT"
          },
          %{"Code" => "#{index}.9", "Value" => "3600000", "_dataType" => "NUMERIC"}
        ]
      rescue
        e ->
          Logger.error("Error processing record at index #{index}: #{Exception.message(e)}")
          []
      end
    end)
  end

  def map_data(_), do: []

  # Safe decimal conversion
  defp safe_decimal_conversion(value) do
    case value do
      nil ->
        "0"

      "" ->
        "0"

      str when is_binary(str) ->
        case Decimal.new(String.trim(str)) do
          {:ok, decimal} -> Decimal.to_string(decimal)
          :error -> "0"
        end

      num when is_number(num) ->
        "#{num}"

      _ ->
        "0"
    end
  rescue
    _ -> "0"
  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end

  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values like "0"
  defp convert_to_number(value) when is_binary(value) do
    String.to_float(value)
  rescue
    # If String.to_float fails, try integer conversion
    ArgumentError ->
      String.to_integer(value) * 1.0
  end

  defp convert_to_number(_), do: 0.0
end
