<style>
   *{
   margin: 0;
   padding: 0;
   box-sizing: border-box;
   }
   div.ex1 {
   height: 740px;
   overflow-y: scroll;
   margin-top:3%;
   }
   .bold{
   font-weight: 900;
   }
   .light{
   font-weight: 100;
   }
   .wrapper{
   background: #fff;
   padding: 30px;
   }
   .invoice_wrapper{
   width: 100%;
   max-width: 100%;
   border: 1px solid none
   }
   .invoice_wrapper .header .logo_invoice_wrap,
   .invoice_wrapper .header .bill_total_wrap{
   display:flex;
   justify-content: space-between;
   padding: 30px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap{
   margin-left: 5px;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .title{
   text-transform: uppercase ;
   font-size: 18px;
   color: #0C40A2;
   }
   .invoice_wrapper .header .logo_sec .title_wrap .sub_title{
   font-size: 12px;
   }
   .invoice_wrapper .header .invoice_sec,
   .invoice_wrapper .header .total_wrap{
   text-align: right;
   }
   .invoice_wrapper .header .invoice_sec .invoice{
   font-size: 28px;
   color:blue;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no,
   .invoice_wrapper .header .invoice_sec .date{
   display: flex;
   width: 100%;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: 70%;
   text-align: left;
   }
   .invoice_wrapper .header .invoice_sec .invoice_no span:first-child,
   .invoice_wrapper .header .invoice_sec .date span:first-child{
   width: calc(100%  -70px);
   }
   .invoice_wrapper .header .bill_total_wrap .name
   .invoice_wrapper .header .bill_total_wrap .price{
   font-size: 20px;
   }
   .invoice_wrapper .body .main_table .table_header{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .body .main_table .table_header .row{
   color:#000;
   font-size: 18px;
   border-bottom: 0px;
   }
   .invoice_wrapper .body .main_table  .row{
   display: flex;
   border-bottom: 1px solid #e9e9e9;
   }
   .invoice_wrapper .body .main_table .row .col{
   padding: 9px;
   }
   .invoice_wrapper .body .main_table .row .col.col_no{width: 8%;}
   .invoice_wrapper .body .main_table .row .col.col_des{width: 30%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_price{width: 23%; text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_qty{width: 21%;  text-align: center;}
   .invoice_wrapper .body .main_table .row .col.col_total{width: 20%;  text-align: center;}
   .invoice_wrapper .body .paymethod_grantotal_wrap{
   display: flex;
   justify-content: space-between;
   padding: 5px 0 30px;
   align-items: flex-end;
   padding-top: 2rem;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .paymethod_sec{
   padding-left: 30px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec{
   width: 20%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p{
   display: flex;
   width: 100%;
   padding-bottom: 5px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span{
   padding: 0 10px;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:first-child{
   width: 60%;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p span:last-child span{
   width: 40%;
   text-align: right;
   }
   .invoice_wrapper .body .paymethod_grantotal_wrap .grantotal_sec p:last-child span{
   border-bottom: 1px solid #000;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .invoice_wrapper .footer{
   padding:30px;
   }
   .kanja
   {
   font-size:16px;
   }
   .kanja_peter
   {
   font-size:13px;
   margin-top:1rem
   }
   table {
   white-space: nowrap;
   margin-left:2%;
   }
   table th {
   border: solid 1px gray;
   text-align: left;
   }
   .kanja_chileshe
   {
   font-weight:100;
   background-color:#ffff99;
   }
   .man{
   background-color:#ffff99;
   }
   .kanja_p
   {
   font-weight:100;
   background-color:#c0c0c0;
   }
   .peter:not(input){
   text-align: right;
   }
   .p_kanja
   {
   font-weight:100;
   background-color:#ccccff;
   }
   .man{
   background-color:#ffcc99;
   }
   .mans{
   background-color:#aaf05a;
   }
   table,
   th,
   td {
   border: 0.5px solid gray;
   }
   th, td {
   padding: 2px;
   }
   .grid-container {
   display: grid;
   column-gap: 100px;
   grid-template-columns: auto auto auto;
   padding: 11px;
   width:170%;
   }

   .class-name{
    transition: all 1.5s;
    animation: animation-name 0.5s linear;
  }
  
  @keyframes animation-name{
    from{
      transform: translateX(-100%);
      height: 0px;
    }
   }
</style>

<.form :let={f} for={@changeset} as={:tax} id="loan-product-form" phx-submit="save" phx-change="validate" phx-target={@myself}>
   
   <body>
      <div class="wrapper ex1 class-name">
         <div class="invoice_wrapper"  id="to_print">
            
            <div class="body" style="margin-top:2%">
               <div class="main_table" style="display: flex; flex-direction: column;">
                  <!-- Header container with flex layout -->
                  <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 20px;">
                     <!-- Left side - Title -->
                     <div colspan="12" class="ml-6" style="text-align:center; font-size:13px; font-weight:900">
                        CURRENT AND DEFERRED TAXES
                     </div>
                     
                     <!-- Right side - Report Date -->
                     <div class="sm:col-span-3" style="min-width: 200px;">
                        <label for="report_date" class="block text-sm font-medium leading-6 text-gray-900">Report Date</label>
                        <div class="mt-2">
                           <%= date_input(
                              f,
                              :report_date,
                              autocomplete: "Report Date",
                              placeholder: "Report Date",
                              class: "block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",
                              required: "true"
                           ) %> <%= error_tag(f, :report_date) %>
                        </div>
                     </div>
                  </div>
            
                  <!-- Table section -->
                  <table style="width: 40%;">
                     <tr class="sample">
                        <th colspan="12" style="text-align:center; font-size:13px">Changes in the Current Tax Account</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px">Details</th>
                        <th style="font-size:13px">Amount</th>
                     </tr>
                     <tr style="display: none;">
                        <td class="" style="text-align:left">Opening Balance</td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                              f,
                              :open_balance,
                              class: "h-full kanja_chileshe peter text_left border-none text-gray-900 w-full"    
                           ) %> <%= error_tag(f, :open_balance) %>
                        </td>
                     </tr>
                     <tr>
                        <td class="" style="text-align:left">Tax effect of non-deductible income (+ve)</td>
                        <td class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :tax_effect_non,
                              class: "h-full kanja_chileshe peter text_left border-none text-gray-900 w-full"    
                           ) %> <%= error_tag(f, :tax_effect_non) %>
                        </td>
                     </tr>
                     <tr>
                        <td class="" style="text-align:left">Tax effect of deductible income (-ve)</td>
                        <td class="kanja_chileshe peter">
                           <%= number_input(
                              f,
                              :tax_effect_deductible,
                              class: "h-full kanja_chileshe peter text_left border-none text-gray-900 w-full"    
                           ) %> <%= error_tag(f, :tax_effect_deductible) %>
                        </td>
                     </tr>
                     <tr>
                        <td class="" style="text-align:left">Tax payments during the month</td>
                        <td class="kanja_chileshe" style="text-align:right; color:red">
                           <%= number_input(
                              f,
                              :tax_payments,
                              class: "h-full kanja_chileshe peter text_left border-none text-gray-900 w-full"    
                           ) %> <%= error_tag(f, :tax_payments) %>  
                        </td>
                     </tr>
                  </table>
               </div>
            </div>
            
            <div class="body" style="margin-top:2%">
               <div class="main_table">
                  <table style="width: 140%;">
                     <tr class="sample">
                        <th colspan="12" style="text-align:center; font-size:13px">Changes in the Deferred Tax Account</th>
                     </tr>
                     <tr>
                        <th style="font-size:13px">Details </th>
                        <th style="font-size:13px">Carry forward losses</th>
                        <th style="font-size:13px">Property, Plant & Equipment</th>
                        <th style="font-size:13px">IFRS 9 Provisions</th>
                        <th style="font-size:13px">Fair Value Gains & Losses</th>
                        <th style="font-size:13px">Unrealised Foreign Exchange Gains & Losses</th>
                        <th style="font-size:13px">Others</th>
                        
                     </tr>
                     <tr>
                        <td  class="" style="text-align:left; font-size:13px; font-weight:900">Opening deferred tax (asset)/liability</td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :carry_fwd_opening,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :carry_fwd_opening) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                           f,
                           :prop_opening_asset,
                           class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                           ) %> <%= error_tag(f, :prop_opening_asset) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                           f,
                           :ifrs_opening_assets,
                           class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                           ) %> <%= error_tag(f, :ifrs_opening_assets) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                           f,
                           :fair_opening_assets,
                           class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                           ) %> <%= error_tag(f, :fair_opening_assets) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :unrealised_opening_asset,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :h) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :oth_opening_asset,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :oth_opening_asset) %>
                        </td>                       
                     </tr>

                     <tr>
                        <td  class="" style="text-align:left; ">Movement for the month (P&L)</td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :carry_mvt_month,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :carry_mvt_month) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :prop_mvt_month,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :prop_mvt_month) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :ifrs_mvt_month,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :ifrs_mvt_month) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :fair_mvt_month,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :fair_mvt_month) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :unrealised_mvt_month,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :unrealised_mvt_month) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           
                        </td>
                        
                           
                        
                     </tr>
                     <tr>
                        <td  class="" style="text-align:left; ">Movement for the month (Equity)</td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :carry_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :carry_mvt_equity) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :prop_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :prop_mvt_equity) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :ifrs_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :ifrs_mvt_equity) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :fair_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :fair_mvt_equity) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :unrealised_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :unrealised_mvt_equity) %>
                        </td>
                        <td class="kanja_chileshe" style="text-align:right">
                           <%= number_input(
                                       f,
                                       :oth_mvt_equity,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :oth_mvt_equity) %>
                        </td>
                        
                           
                        
                     </tr>
                     
                   
                  </table>
               </div>
            </div>
            <div class="body" style="margin-top:2%">
               <div class="main_table">
                  <div class="grid-container">
                     <div class="grid-item">
                        <div class="body" style="margin-top:2%">
                           <div class="main_table">
                              <table style="width: 100%;">
                                 <tr class="sample">
                                    <th colspan="12" style="text-align:center; font-size:13px">Other adjustments - Current tax</th>
                                 </tr>
                                 <tr>
                                    <th style="font-size:13px">Description </th>
                                    <th style="font-size:13px">Amount</th>
                                 </tr>
                        
                                 <tr>
                                    <td class="kanja_chileshe" style="text-align:left; ">
                                       <%= text_input(
                                       f,
                                       :b41,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :b41) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right; ">
                                       <%= number_input(
                                       f,
                                       :c41,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :c41) %>
                                    </td>
                                 </tr>
                                 <tr>
                                    <td  class="kanja_chileshe" style="text-align:left; ">
                                       <%= text_input(
                                       f,
                                       :b42,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :b42) %></td>
                                    <td class="kanja_chileshe" style="text-align:right; ">
                                       <%= number_input(
                                       f,
                                       :c42,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :c42) %></td>
                                 </tr>
                                 <tr>
                                    <td  class="kanja_chileshe" style="text-align:left; ">
                                       <%= text_input(
                                       f,
                                       :b43,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :b43) %></td>
                                    <td class="kanja_chileshe" style="text-align:right; ">
                                       <%= number_input(
                                       f,
                                       :c43,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :c43) %></td>
                                 </tr> 
                                 <tr>
                                    <td  class="kanja_chileshe" style="text-align:left; ">
                                       <%= text_input(
                                       f,
                                       :b44,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :b44) %></td>
                                    <td class="kanja_chileshe" style="text-align:right; ">
                                       <%= number_input(
                                       f,
                                       :c44,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :c44) %></td>
                                 </tr> 
                                 <tr>
                                    <td  class="kanja_chileshe" style="text-align:left; ">
                                       <%= text_input(
                                       f,
                                       :b45,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :b45) %></td>
                                    <td class="kanja_chileshe" style="text-align:right; ">
                                       <%= number_input(
                                       f,
                                       :c45,
                                       class: "h-full  kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :c45) %></td>
                                 </tr> 
                              </table>
                           </div>
                        </div>
                     </div>
                     <div class="grid-item">
                        <div class="body" style="margin-top:2%">
                           <div class="main_table">
                              <table style="width: 100%;">
                                 <tr class="sample">
                                    <th colspan="12" style="text-align:center; font-size:13px">Other adjustments -  Deferred tax</th>
                                 </tr>
                                 <tr>
                                    <th style="font-size:13px">Description </th>
                                    <th style="font-size:13px">Amount</th>
                                 </tr>
                                 
                                 <tr> 
                                    <td  class="kanja_chileshe" style="text-align:left;">
                                       <%= text_input(
                                       f,
                                       :deffered_descr,
                                       class: "h-full text_left kanja_chileshe peter  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_descr) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right;">
                                       <%= number_input(
                                       f,
                                       :deffered_amunt,
                                       class: "h-full   kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_amunt) %>
                                    </td>
                                 </tr>
                                 <tr> 
                                    <td  class="kanja_chileshe" style="text-align:left;">
                                       <%= text_input(
                                       f,
                                       :deffered_descr1,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_descr1) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right;">
                                       <%= number_input(
                                       f,
                                       :deffered_amunt1,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_amunt1) %>
                                    </td>
                                 </tr>
                                 <tr> 
                                    <td  class="kanja_chileshe" style="text-align:left;">
                                       <%= text_input(
                                       f,
                                       :deffered_descr2,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_descr2) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right;">
                                       <%= number_input(
                                       f,
                                       :deffered_amunt2,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_amunt2) %>
                                    </td>
                                 </tr>
                                 <tr> 
                                    <td  class="kanja_chileshe" style="text-align:left;">
                                       <%= text_input(
                                       f,
                                       :deffered_descr3,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_descr3) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right;">
                                       <%= number_input(
                                       f,
                                       :deffered_amunt3,
                                       class: "h-full text_left kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_amunt3) %>
                                    </td>
                                 </tr>
                                 <tr> 
                                    <td  class="kanja_chileshe" style="text-align:left;"><%= text_input(
                                       f,
                                       :deffered_descr4,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_descr4) %>
                                    </td>
                                    <td class="kanja_chileshe" style="text-align:right;">
                                       <%= number_input(
                                       f,
                                       :deffered_amunt4,
                                       class: "h-full kanja_chileshe peter text_left  border-none text-gray-900 w-full "    
                                       ) %> <%= error_tag(f, :deffered_amunt4) %>
                                    </td>
                                 </tr>  
                              </table>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>   
            <div class="mt-6 flex items-center justify-end gap-x-6">
               <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" data-dismiss-target="#alert-2">Save</button>
            </div>
         </div>
      </div>
   </body>
</.form>
