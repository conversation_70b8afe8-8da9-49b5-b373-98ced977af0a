require Logger

defmodule MisReports.Workers.BozReq.Schedule18d do
  def perform(item) when is_list(item) do
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => settings.institution_code,
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "EndDate" => item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "ReturnItemsList" => [
        %{
          "Code" => "1204_00293",
          "Value" => "#{length(item)}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 418,
          "_areaName" => "BANK BRANCH AND AGENCIES LISTING",
          "DynamicItems" => map_branch_data(item)
        }
      ]
    }
  end

  def perform(%{end_date: end_date, schedule_18d: schedule_18d}) do
    case Poison.decode(schedule_18d) do
      {:ok, decoded} ->
        Logger.info("Decoded Schedule 18D Data: #{inspect(decoded)}")
        perform(decoded, end_date)

      {:error, error} ->
        Logger.error("Error decoding Schedule 18D: #{inspect(error)}")
        perform([], end_date)
    end
  end

  def perform(branches, end_date) when is_list(branches) do
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => settings.institution_code,
      "FinYear" => end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => Timex.beginning_of_month(end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "EndDate" => end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime),
      "ReturnItemsList" => [
        %{
          "Code" => "1204_00293",
          "Value" => "#{length(branches)}",
          "_dataType" => "NUMERIC"
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 418,
          "_areaName" => "BANK BRANCH AND AGENCIES LISTING",
          "DynamicItems" => map_branch_data(branches)
        }
      ]
    }
  end

  # Fallback for any other case
  def perform(item) do
    Logger.error("Invalid input for Schedule 18D: #{inspect(item)}")

    %{
      "ReturnKey" => "ZM-9MSCH18D9M002",
      "InstCode" => "",
      "ReturnItemsList" => [],
      "DynamicItemsList" => []
    }
  end

  # FIXED: Use atom keys instead of string keys and handle both struct and map formats
  defp map_branch_data(branches) do
    branches
    |> Enum.reject(&is_nil/1)
    |> Enum.with_index(1)
    |> Enum.flat_map(fn {branch, index} ->
      [
        %{"Code" => "#{index}.1", "Value" => format_value(get_branch_field(branch, :name)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.2", "Value" => format_value(get_branch_field(branch, :phy_addr)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.3", "Value" => format_value(get_branch_field(branch, :post_addr)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.4", "Value" => format_value(get_branch_field(branch, :town)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.5", "Value" => format_value(get_branch_field(branch, :province)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.6", "Value" => format_value(get_branch_field(branch, :manager_name)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.7", "Value" => format_value(get_branch_field(branch, :phone)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.8", "Value" => format_value(get_branch_field(branch, :code)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.9", "Value" => format_value(get_branch_field(branch, :email)), "_dataType" => "TEXT"},
        %{"Code" => "#{index}.10", "Value" => format_value(get_branch_field(branch, :number)), "_dataType" => "TEXT"}
      ]
    end)
  end

  # Helper function to safely get branch field - handles both structs and maps
  defp get_branch_field(branch, field) when is_map(branch) do
    # Try atom key first (for structs), then string key (for decoded JSON)
    Map.get(branch, field) || Map.get(branch, Atom.to_string(field))
  end

  defp get_branch_field(_, _), do: nil

  defp format_value(nil), do: "N/A"
  defp format_value(value) when is_binary(value), do: String.trim(value)
  defp format_value(value), do: "#{value}"
end
