defmodule MisReports.Workers.BozReq.Schedule18a do
  def perform(item) do
    decoded_item =
      case item.schedule_18a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # If needed, still format scientific values (unused in the new static lists)
    decoded_item = format_scientific_values(decoded_item)
    settings = MisReports.Utilities.get_comapany_settings_params()
    fin_year = item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer()
    start_date = Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%d", :strftime)
    end_date = item.end_date |> Timex.format!("%Y-%m-%d", :strftime)

    %{
      "returnKey" => "ZM-7PSCH18A7P002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => return_items_list(decoded_item),
      "DynamicItemsList" => dynamic_items_list(decoded_item)
    }
  end

  defp return_items_list(decoded_item) do
    [
      %{"Code" => "1163_00001", "Value" => "#{decoded_item["B14"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00002", "Value" => "#{decoded_item["C14"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00003", "Value" => "#{decoded_item["B15"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00004", "Value" => "#{decoded_item["C15"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00005", "Value" => "#{decoded_item["B16"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00006", "Value" => "#{decoded_item["C16"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00007", "Value" => "#{decoded_item["B17"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00008", "Value" => "#{decoded_item["C17"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00009", "Value" => "#{decoded_item["B18"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00010", "Value" => "#{decoded_item["C18"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00011", "Value" => "#{decoded_item["B19"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00012", "Value" => " #{decoded_item["C19"]} ", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00013", "Value" => "#{decoded_item["B20"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00014", "Value" => "#{decoded_item["C20"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00015", "Value" => "#{decoded_item["B21"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00016", "Value" => "#{decoded_item["C21"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00017", "Value" => "#{decoded_item["B22"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00018", "Value" => "#{decoded_item["C22"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00019", "Value" => "#{decoded_item["B23"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00020", "Value" => "#{decoded_item["C23"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00021", "Value" => "#{decoded_item["B24"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00022", "Value" => "#{decoded_item["C24"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00023", "Value" => "#{decoded_item["B25"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00024", "Value" => "#{decoded_item["C25"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00025", "Value" => "#{decoded_item["B26"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00026", "Value" => "#{decoded_item["C26"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00027", "Value" => "#{decoded_item["B27"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00028", "Value" => "#{decoded_item["C27"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00029", "Value" => "#{decoded_item["B28"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00030", "Value" => "#{decoded_item["C28"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00031", "Value" => "#{decoded_item["B29"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00032", "Value" => "#{decoded_item["C29"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00033", "Value" => "#{decoded_item["B30"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00034", "Value" => "#{decoded_item["C30"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00035", "Value" => "#{decoded_item["B31"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00036", "Value" => "#{decoded_item["C31"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00037", "Value" => "#{decoded_item["B32"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00038", "Value" => "#{decoded_item["C32"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00039", "Value" => "#{decoded_item["B33"]}", "_dataType" => "NUMERIC"},
      %{"Code" => "1163_00040", "Value" => "#{decoded_item["C33"]}", "_dataType" => "NUMERIC"}
    ]
  end

  defp dynamic_items_list(_decoded_item) do
    [
      %{
        "Area" => 446,
        "_areaName" => "MONTHLY FRAUD AND LOSS STATISTICS",
        "DynamicItems" => [
          %{"Code" => "1.1", "Value" => "Details of others", "_dataType" => "TEXT"},
          %{"Code" => "1.2", "Value" => " No. of Incidences ", "_dataType" => "NUMERIC"},
          %{"Code" => "1.3", "Value" => " Value (Kwacha) ", "_dataType" => "NUMERIC"}
        ]
      }
    ]
  end

  def format_scientific_values(map) when is_map(map) do
    (get_in(map, ["list"]) || [])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  def format_scientific_values(_), do: %{}

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end
end
