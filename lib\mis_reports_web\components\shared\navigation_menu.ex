defmodule MisReportsWeb.Components.Shared.NavigationMenu do
  use Phoenix.Component

  def render_navigation_menu(assigns) do
    ~H"""
    <div class="overflow">
      <%= for schedule <- get_schedules(@current_view) do %>
        <%= if Map.has_key?(schedule, :items) do %>
          <div class="mt-2 divide-y divide-gray-200 border-b border-t border-gray-200">
            <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium">
              <div class="dropdown">
                <button class={"dropbtn #{if @report_type in Enum.map(schedule.items, &"#{&1}"), do: "linkselector", else: nil}"}>
                  <%= schedule.title %>
                </button>

                <div class="dropdown-content">
                  <%= for item <- schedule.items do %>
                    <div class="flex flex-col py-2 hover:bg-gray-50 px-2 rounded">
                      <a href="#" phx-click="view_page" phx-disable-with="Loading..." phx-value-value={"#{item}"} class={"mains mb-1 #{if @report_type == "#{item}", do: "linkselector", else: nil}"}>
                        Schedule <%= case item do
                          "schedule_" <> number -> String.upcase(number)
                          _ -> item
                        end %>
                      </a>
                      <%= if not @view_only do %>
                      <div class="mt-2">
                        <button
                           class="w-full flex justify-center items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-0 focus:ring-blue-500"
                           id={"comment-link-#{item}"}
                           phx-click="open_comment_modal"
                           phx-value-schedule={item}>
                          <svg class="mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                          </svg>
                          Comment
                        </button>
                      </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>
          </div>
        <% else %>
          <div class="mt-1 divide-y divide-gray-200 border-b border-t border-gray-200">
            <div class="flex flex-col py-3 hover:bg-gray-50 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300 px-2 rounded">
              <div phx-click="view_page" phx-disable-with="Loading..." phx-value-value={schedule.value} class="mb-1">
                <div value={schedule.value} class={"text-gray-500 #{if @report_type == schedule.value, do: "linkselector", else: nil}"}>
                  <%= schedule.title %>
                </div>
              </div>
               <%= if not @view_only do %>
              <div class="mt-2">
                <button
                   class="w-full flex justify-center items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-0 focus:ring-blue-500"
                   id={"comment-link-#{schedule.value}"}
                   phx-click="open_comment_modal"
                   phx-value-schedule={schedule.value}>
                  <svg class="mr-1 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                  </svg>
                  Comment
                </button>
              </div>
              <% end %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  @income_statement_schedules [
    %{title: "Income Statement", value: "income_statement"},
    %{title: "Schedule 03A", value: "schedule_03A"},
    %{title: "Schedule 04D", value: "schedule_04D"},
    %{
      title: "Schedule 18",
      items: ["schedule_18A", "schedule_18B", "schedule_18C", "schedule_18D"]
    },
    %{title: "Schedule 24", value: "schedule_24"},
    %{title: "Schedule 27", value: "schedule_27"},
    %{title: "Schedule 27A", value: "schedule_27A"},
    %{title: "Schedule 28A", value: "schedule_28A"},
    %{title: "Schedule 28B", value: "schedule_28B"}
  ]

  @balance_sheet_schedules [
    %{title: "Balance Sheet", value: "blc_sheet"},
    %{
      title: "Schedule 01",
      items: ["schedule_01c", "schedule_01D", "schedule_01E", "schedule_01F"]
    },
    %{
      title: "Schedule 02",
      items: ["schedule_2A1", "schedule_02C", "schedule_02G", "schedule_2H"]
    },
    %{
      title: "Schedule 04",
      items: ["schedule_4B", "schedule_4D"]
    },
    %{
      title: "Schedule 05",
      value: "schedule_5B"
    },
    %{
      title: "Schedule 06",
      value: "schedule_6A"
    },
    %{

      title: "Schedule 07", value: "schedule_7A"
    },
    %{
      title: "Schedule 08",
      items: ["schedule_8A", "schedule_8B"]
    },
    %{
      title: "Schedule 09",value: "schedule_9A"},
    %{
      title: "Schedule 10", value: "schedule_10C"},
    %{
      title: "Schedule 11",
      items: ["schedule_11D", "schedule_11E", "schedule_11J"]
    },
    %{title: "Schedule 12", value: "schedule_12"},
    %{title: "Schedule 13", value: "schedule_13"},
    %{title: "Schedule 14", value: "schedule_14"},
    %{title: "Schedule 15", value: "schedule_15"},
    %{
      title: "Schedule 17",
      items: ["schedule_17A", "schedule_17B", "schedule_17C", "schedule_17E"]
    },
    %{title: "Schedule 19", value: "schedule_19"},
    %{
      title: "Schedule 21",
      items: ["schedule_21B", "schedule_21C"]
    },
    %{
      title: "Schedule 22",
      items: ["schedule_22A", "schedule_22B"]
    },
    %{
      title: "Schedule 23",
      items: ["schedule_23A", "schedule_23B"]
    },
    %{title: "Schedule 25", value: "schedule_25"},
    %{title: "Schedule 26", value: "schedule_26"},
    %{
      title: "Schedule 30",
      value: "schedule_30D"
    },
    %{
      title: "Schedule 31",
      items: [ "schedule_31C", "schedule_31D", "schedule_31F"]
    },
    %{
      title: "Schedule 32",value: "schedule_32A",

    }
  ]
  @other_schedules [
    %{title: "Schedule 20A", value: "schedule_20A"},
    %{title: "Schedule 29A", value: "schedule_29A"},
    %{title: "Schedule 30B", value: "schedule_30B"},
    %{title: "Schedule 30C", value: "schedule_30C"}
  ]

  defp get_schedules("income_statement"), do: @income_statement_schedules
  defp get_schedules("balance_sheet"), do: @balance_sheet_schedules
  defp get_schedules("other_schedules"), do: @other_schedules
  defp get_schedules(_), do: @income_statement_schedules
end
