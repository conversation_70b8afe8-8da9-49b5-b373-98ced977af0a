defmodule MisReportsWeb.WklPendingTasksView do
  use MisReportsWeb, :view
  use MisReportsWeb, :custom_component

  def get_dynamic_path(socket, task) do
    # Map the paths to their corresponding route helpers
    params = %{
      reference: task.reference,
      process_id: task.process_id,
      step_id: task.step_id
    }

    case task.req_map do
      "/branch/new" ->
        Routes.branch_index_path(socket, :new, params)
      "/branch/approve" ->
        Routes.branch_index_path(socket, :update_status, params)

      "/loan/business/unit/new" ->
        Routes.business_unit_index_path(socket, :new,params)

      "loan_business_unit/review" ->
        Routes.business_unit_index_path(socket, :update_status,params)

      "/loan/scheme/codes" ->
        Routes.loan_scheme_code_index_path(socket, :new, params)
      "/loan/scheme/approve" ->
        Routes.loan_scheme_code_index_path(socket, :update_status, params)

      "/Securities/view" ->
        Routes.securities_index_path(socket, :new, params)
      "/Securities/approve" ->
        Routes.securities_index_path(socket, :update_status, params)

      "/Prudential/Initial" ->
        Routes.prudential_report_index_path(socket, :index, params )

      "/template/sample" ->
        Routes.source_data_index_path(socket, :new_temp, params )

      "/regulatory/capital/new" ->
        Routes.regulatory_capital_index_path(socket, :new, params )

      "/regulatory/capital/approve" ->
        Routes.regulatory_capital_index_path(socket, :update_status, params )

      "/offblcSheet/sheet/new" ->
        Routes.off_blc_sheet_index_path(socket, :new, params )

      "/offblcSheet/sheet/approve" ->
        Routes.off_blc_sheet_index_path(socket, :update_status, params )

      "/tax/new" ->
        Routes.current_defferred_index_path(socket, :new, params )

      "/tax/approve" ->
        Routes.current_defferred_index_path(socket, :update_status, params )


      "/loan/products/new" ->
        Routes.loan_product_index_path(socket, :new, params )

      "/loan/products/approve" ->
        Routes.loan_product_index_path(socket, :update_status, params )

      "/exchange/rate/new" ->
        Routes.exchange_rate_index_path(socket, :new, params )

      "/exchange/rate/approve" ->
        Routes.exchange_rate_index_path(socket, :update_status, params )

      "/allowances/view" ->
        Routes.allowances_index_path(socket, :new, params )

      "/allowances/approve" ->
        Routes.allowances_index_path(socket, :update_status, params )


      "/technological_infrastructure/new" ->
        Routes.technological_infrastructure_index_path(socket, :new, params )

      "/technological_infrastructure/approve" ->
        Routes.technological_infrastructure_index_path(socket, :update_status, params )


      "/repossed/properties/new" ->
        Routes.repossessed_properties_index_path(socket, :new, params )

      "/repossed/properties/approve" ->
        Routes.repossessed_properties_index_path(socket, :update_status, params )

      "/counterparty/securities/new" ->
        Routes.counterparty_securities_index_path(socket, :new, params )

      "/counterparty/securities/approve" ->
        Routes.counterparty_securities_index_path(socket, :update_status, params )

      "/adjustments/" ->
        Routes.adjustments_index_path(socket, :new, params )

      "/adjustments/approve" ->
        Routes.adjustments_index_path(socket, :update_status, params )

      "/Prudential/Generate" ->
        Routes.income_statement_index_path(socket, :index, params )

      "/Prudential/Review" ->
        Routes.income_statement_index_path(socket, :index, params )
      "/Prudential/Approval" ->
        Routes.income_statement_index_path(socket, :index, params )

      "adjustment/new" ->
        Routes.adjustments_index_path(socket, :new, params )
      "/adjustment/approve" ->
        Routes.adjustments_index_path(socket, :update_status, params )


      "/WeeklyBozBlc/sheet/new" ->
        Routes.weekly_boz_balance_index_path(socket, :new, params )
      "/WeeklyBozBlc/sheet/approve" ->
        Routes.weekly_boz_balance_index_path(socket, :update_status, params )
      "/Weekly-Return/Generate" ->
        Routes.weekly_index_path(socket, :index, params )
      "/Weekly-Return/Review" ->
        Routes.weekly_index_path(socket, :index, params )
      "/Weekly-Report/Approval" ->
        Routes.weekly_index_path(socket, :index, params )
      "/share/holders/new" ->
        Routes.share_holders_index_path(socket, :new, params)

      "/share/holders/approve" ->
        Routes.share_holders_index_path(socket, :update_status, params )

      "/bal/due/new" ->
        Routes.balance_due_domestic_index_path(socket, :new, params )

      "/bal/due/approve" ->
        Routes.balance_due_domestic_index_path(socket, :update_status, params )

      "/loan/classification/new" ->
        Routes.loan_classification_index_path(socket, :new, params )

      "/loan/classification/approve" ->
        Routes.loan_classification_index_path(socket, :update_status, params )

      "/debtors/book/analysis/new" ->
        Routes.debtors_book_analysis_index_path(socket, :new, params )

      "/debtors/book/analysis/approve" ->
        Routes.debtors_book_analysis_index_path(socket, :update_status, params )

      "/gov/accts/new" ->
        Routes.govt_accounts_index_path(socket, :new, params )

      "/gov/accts/approve" ->
        Routes.govt_accounts_index_path(socket, :update_status, params )

      "/exchange/plcmnt/new" ->
        Routes.exchange_placement_index_path(socket, :new, params )

      "/exch/plcmnt/approve" ->
        Routes.exchange_placement_index_path(socket, :update_status, params )

      "/inst/details/new" ->
        Routes.institution_details_index_path(socket, :new, params )

      "/inst/details/approve" ->
        Routes.institution_details_index_path(socket, :update_status, params )

      "/employee/benefit/new" ->
        Routes.benefit_index_path(socket, :new, params )

      "/employee/benefit/approve" ->
        Routes.benefit_index_path(socket, :update_status, params )

      "/employee/stats/new" ->
        Routes.stats_index_path(socket, :new, params )

      "/employee/stats/approve" ->
        Routes.stats_index_path(socket, :update_status, params )
      _ ->
        # Default fallback path or error handling
        Routes.wkl_pending_tasks_index_path(socket, :index)
    end
  end

  def format_timestamp(timestamp) do
    timestamp
    |> Calendar.DateTime.shift_zone!("Africa/Cairo")
    |> Calendar.Strftime.strftime!("%d-%b-%Y %H:%M:%S")
  end
end
