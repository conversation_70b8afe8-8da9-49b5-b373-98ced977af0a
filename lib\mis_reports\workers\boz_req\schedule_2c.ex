defmodule MisReports.Workers.BozReq.Schedule2c do

  def perform(item) do
    exchange_rate = MisReports.Prudentials.usd_rate(item.end_date) |> Decimal.to_float() || Decimal.new("1") |> Decimal.to_float()
    decoded_item =
      case item.schedule_02c do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    dynamic_list = decoded_item["list"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9QSCH2C9Q002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{
          "Code" => "1134_00001",
          "Value" => "#{exchange_rate}",
          "_dataType" => "NUMERIC",
        }
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 434,
          "_areaName" => "LOANS AND ADVANCES INPUT SHEET",
          "DynamicItems" => map_data(dynamic_list) |> format_values()
        }
      ]
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


  def map_data(records) do

    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => map["A"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => map["B"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => map["C"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => map["D"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => map["E"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.6", "Value" => map["F"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.7", "Value" => map["G"], "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.8", "Value" => format_amount(map["H"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.9", "Value" => map["I"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.10", "Value" => map["J"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.11", "Value" => format_amount(map["K"]), "_dataType" => "NUMERIC"}
        ]
      end)
  end

  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  def convert_exponent(%{"coef" => coef, "exp" => exp, "sign" => sign}) do
    Decimal.new(sign, coef, exp) |> Decimal.to_float()
  end

  def convert_exponent(value), do: value

  def format_amount(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end


end
