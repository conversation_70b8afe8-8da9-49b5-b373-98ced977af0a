defmodule MisReportsWeb.Components.TabsForAdj do
  use MisReportsWeb, :live_component
  alias MisReports.Workflow
  alias MisReportsWeb.Router.Helpers, as: Routes

  def mount(socket) do
    active_tab = socket.assigns[:active_tab] || "adjustments"
    {:ok, assign(socket, active_tab: active_tab)}
  end

  def update(assigns, socket) do
    reference = get_reference(assigns)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:reference, reference)
     |> assign(:active_tab, assigns[:active_tab] || "adjustments")}
  end

  def render(assigns) do
    ~H"""
    <div class="w-full">
      <div class="border-b border-gray-200 flex justify-between items-center">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
          <button
            phx-click="switch_tab"
            phx-value-tab="adjustments"
            phx-target={@myself}
            class={"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              #{if @active_tab == "adjustments" do
                "border-indigo-500 text-indigo-600"
              else
                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              end}"}
          >
            Adjustments
          </button>

          <button
            phx-click="switch_tab"
            phx-value-tab="insertions"
            phx-target={@myself}
            class={"whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
              #{if @active_tab == "insertions" do
                "border-indigo-500 text-indigo-600"
              else
                "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              end}"}
          >
            Insertions
          </button>
        </nav>

        <%= if @step_id not in ["67", "10079"] do %>
          <button phx-click="new_action" phx-target={@myself} class="whitespace-nowrap py-2 px-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
            Proceed
          </button>
        <% end %>
      </div>
    </div>
    """
  end

  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    socket = assign(socket, :process_id, socket.assigns.process_id)|> assign(:reference, socket.assigns.reference)|> assign(:step_id, socket.assigns.step_id)

    case socket.assigns.step_id do
      "67" ->
        {:noreply,
         socket
         |> push_redirect(to: get_redirect_path(socket, tab))}

      "10079" ->
        {:noreply,
         socket
         |> push_redirect(to: get_redirect_path(socket, tab))}

      _ ->
        send(self(), {:tab_switched, tab})
        {:noreply, socket |> assign(:active_tab, tab)}
    end
  end

  def handle_event("new_action", params, socket) do
    IO.inspect(params, label: "New Action Params")
    current_user = socket.assigns.current_user
    reference = get_reference(socket.assigns)
    initial_record = MisReports.Utilities.get_initial_record_details(reference)
    process_id = socket.assigns[:process_id]
    current_user_id = to_string(current_user.id)

    IO.inspect(process_id, label: "============PROCESS ID===========")

    case Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           80,
           "",
           "",
           "Proceeding to next stage"
         ) do
      {:ok, _result} ->
        {:noreply,
         socket
         |> put_flash(:info, "Successfully proceeded to next stage")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to proceed: #{reason}")}
    end
  end

  # Add helper functions
  defp get_reference(assigns) do
    cond do
      assigns[:reference] -> assigns.reference
      assigns[:adjustment] && assigns.adjustment.reference -> assigns.adjustment.reference
      true -> nil
    end
  end

  defp get_redirect_path(socket, tab) do
    reference = get_reference(socket.assigns)
    process_id = socket.assigns[:process_id]
    step_id = socket.assigns[:step_id]

    case tab do
      "adjustments" ->
        Routes.adjustments_index_path(socket, :update_status,
          reference: reference,
          process_id: process_id,
          step_id: step_id
        )

      "insertions" ->
        Routes.insertion_index_path(socket, :update_status,
          reference: reference,
          process_id: process_id,
          step_id: step_id
        )
    end
  end
end
