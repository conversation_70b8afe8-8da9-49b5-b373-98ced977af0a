defmodule MisReports.Workers.BozReq.Schedule04b do

  def perform(item) do

    decoded_item =
      case item.schedule_04b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    decoded_item = format_scientific_values(decoded_item)
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-8ISCH4B8I002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "returnItemsList" => [
        %{
          "Code" => "1143_00001",
          "Value" => "#{decoded_item["B15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00002",
          "Value" => "#{decoded_item["D15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00003",
          "Value" => "#{decoded_item["E15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00004",
          "Value" => "#{decoded_item["F15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00005",
          "Value" => "#{decoded_item["G15"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00006",
          "Value" => "#{decoded_item["B17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00007",
          "Value" => "#{decoded_item["D17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00008",
          "Value" => "#{decoded_item["E17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00009",
          "Value" => "#{decoded_item["F17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00010",
          "Value" => "#{decoded_item["G17"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00011",
          "Value" => "#{decoded_item["B19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00012",
          "Value" => "#{decoded_item["C19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00013",
          "Value" => "#{decoded_item["D19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00014",
          "Value" => "#{decoded_item["E19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00015",
          "Value" => "#{decoded_item["F19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00016",
          "Value" => "#{decoded_item["G19"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00017",
          "Value" => "#{decoded_item["B21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00018",
          "Value" => "#{decoded_item["C21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00019",
          "Value" => "#{decoded_item["D21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00020",
          "Value" => "#{decoded_item["E21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00021",
          "Value" => "#{decoded_item["F21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00022",
          "Value" => "#{decoded_item["G21"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00023",
          "Value" => "#{decoded_item["B22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00024",
          "Value" => "#{decoded_item["C22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00025",
          "Value" => "#{decoded_item["D22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00026",
          "Value" => "#{decoded_item["E22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00027",
          "Value" => "#{decoded_item["F22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00028",
          "Value" => "#{decoded_item["G22"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00029",
          "Value" => "#{decoded_item["B23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00030",
          "Value" => "#{decoded_item["C23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00031",
          "Value" => "#{decoded_item["D23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00032",
          "Value" => "#{decoded_item["E23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00033",
          "Value" => "#{decoded_item["F23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00034",
          "Value" => "#{decoded_item["G23"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00035",
          "Value" => "#{decoded_item["B25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00036",
          "Value" => "#{decoded_item["C25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00037",
          "Value" => "#{decoded_item["D25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00038",
          "Value" => "#{decoded_item["E25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00039",
          "Value" => "#{decoded_item["F25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00040",
          "Value" => "#{decoded_item["G25"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00041",
          "Value" => "#{decoded_item["B26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00042",
          "Value" => "#{decoded_item["C26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00043",
          "Value" => "#{decoded_item["D26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00044",
          "Value" => "#{decoded_item["E26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00045",
          "Value" => "#{decoded_item["F26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00046",
          "Value" => "#{decoded_item["G26"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00047",
          "Value" => "#{decoded_item["B27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00048",
          "Value" => "#{decoded_item["C27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00049",
          "Value" => "#{decoded_item["D27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00050",
          "Value" => "#{decoded_item["E27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00051",
          "Value" => "#{decoded_item["F27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00052",
          "Value" => "#{decoded_item["G27"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00053",
          "Value" => "#{decoded_item["B29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00054",
          "Value" => "#{decoded_item["C29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00055",
          "Value" => "#{decoded_item["D29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00056",
          "Value" => "#{decoded_item["E29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00057",
          "Value" => "#{decoded_item["F29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00058",
          "Value" => "#{decoded_item["G29"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00059",
          "Value" => "#{decoded_item["B30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00060",
          "Value" => "#{decoded_item["C30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00061",
          "Value" => "#{decoded_item["D30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00062",
          "Value" => "#{decoded_item["E30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00063",
          "Value" => "#{decoded_item["F30"] || "0"}",
          "_dataType" => "NUMERIC"
        },
        %{
          "Code" => "1143_00064",
          "Value" => "#{decoded_item["G30"] || "0"}",
          "_dataType" => "NUMERIC"
        }
      ]
    }

  end


    def format_scientific_values(map) do
    map
    |> get_in(["list"])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  defp convert_to_number(value) when is_binary(value) do
    # Handle string values like "164,675" or "93,061"
    value
    |> String.replace(",", "")
    |> String.replace(" ", "")
    |> case do
      "" -> 0
      str ->
        case Float.parse(str) do
          {number, _} -> number
          :error -> 0
        end
    end
  end

  # Fallback for nil or other types
  defp convert_to_number(_), do: 0

end
