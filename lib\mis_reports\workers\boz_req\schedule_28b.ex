defmodule MisReports.Workers.BozReq.Schedule28b do
  alias MisReports.Workers.BalanceSheet
  alias MisReports.Utilities

  def perform(item) do
    decoded_item =
      case item.schedule_28b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    formatted_item = format_scientific_values(decoded_item)
    total = total_outstanding_amt(formatted_item["list"] || [])
    settings = Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-VZSCH28BVZ001",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => 2025,
      "StartDate" => "2025-01-01T00:00:00",

      "EndDate" => "2025-01-31T00:00:00",
      "ReturnItemsList" => return_items_list(total),
      "DynamicItemsList" => [
        %{
          "Area" => 469,
          "_areaName" => "OTHER NON-INTEREST INCOME",
          "DynamicItems" => dynamic_items(formatted_item["list"] || [])
        }
      ]
    }
  end

  defp return_items_list(total) do
    [
      %{
        "Code" => "1201_00001",
        "Value" => "#{B13}",
        "_dataType" => "NUMERIC"
      }
    ]
  end

  defp dynamic_items(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
      index = index + 1
      [

%{"Code" => "#{index}.1", "Value" => "#{map["Account Name"] || "N/A"}", "_dataType" => "TEXT"},
%{"Code" => "#{index}.2", "Value" => "#{convert_to_number(map["amount"]) || "N/A"}", "_dataType" => "NUMERIC"},
%{"Code" => "#{index}.1", "Value" => "#{map["Details"] || "N/A"}", "_dataType" => "TEXT"},
      ]
    end)
  end

  defp total_outstanding_amt(items) do
    items
    |> Enum.map(&convert_to_number/1)
    |> Enum.reduce(Decimal.new(0), &get_sum/2)
  end

  defp format_scientific_values(map) do
    list = Map.get(map, "list", [])
    updated_list = Enum.map(list, &convert_scientific/1)
    Map.put(map, "list", updated_list)
  end

  defp convert_scientific(%{"value" => value} = item), do: Map.put(item, "value", convert_to_number(value))
  defp convert_scientific(item), do: item

  defp get_sum(acc, amt) do
    amt_decimal = if is_nil(amt), do: Decimal.new(0), else: amt
    Decimal.add(acc, amt_decimal)
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    cleaned = String.replace(value, ",", "")
    case Integer.parse(cleaned) do
      {int_value, ""} -> Decimal.new(int_value)
      _ -> Decimal.new(String.to_float(cleaned))
    end
  rescue
    _ -> Decimal.new(0)
  end

  defp convert_to_number(_), do: Decimal.new(0)
end
