defmodule MisReports.ScheduleLoader do
  require Logger

  @cache_ttl :timer.hours(24)
  # Cache entries for 24 hours
  @entries_cache_ttl :timer.hours(24)
  # Cache adjustments for 30 minutes
  @adjustments_cache_ttl :timer.hours(24)

  # Complete dependency map
  @schedule_dependencies %{
    # Base schedules (no dependencies)
    # Trial balance
    "schedule_12" => [],
    "income_statement" => [],
    "prev_income_statement" => [],
    "schedule_03A" => [],
    "schedule_02D" => [],
    "schedule_11E" => [],
    "schedule_21C" => ["adjustments"],
    "schedule_21B" => [],
    "schedule_01c" => [],
    "schedule_01E" => [],
    "schedule_01F" => ["adjustments"],

    # Income Statement dependent schedules
    "schedule_13" => ["income_statement", "adjustments"],
    "schedule_27" => ["income_statement", "adjustments"],
    "schedule_27A" => ["income_statement", "adjustments"],
    "schedule_28A" => ["income_statement", "adjustments"],
    "schedule_28B" => ["income_statement", "adjustments"],
    "schedule_24" => ["income_statement"],
    # New schedule with multiple dependencies

    # Trial Balance dependent schedules
    # "schedule_03B" => ["schedule_12"],
    "schedule_18A" => ["schedule_12"],
    "schedule_18B" => ["schedule_12"],
    "schedule_18C" => [],
    "schedule_18D" => [],
    "schedule_02C" => ["schedule_12"],
    # "schedule_02F" => ["schedule_12"],
    "schedule_02G" => ["adjustments"],
    # New schedule with multiple dependencies
    # "schedule_16A" => ["schedule_12", "schedule_18A"],

    # Balance Sheet dependent schedules
    "blc_sheet" => ["schedule_13", "income_statement", "adjustments"],
    "schedule_15" => ["schedule_13", "blc_sheet", "adjustments"],
    "schedule_14" =>["schedule_13", "blc_sheet", "adjustments"],
    "schedule_4D" => ["blc_sheet"],
    # "schedule_17C" => ["schedule_12"],
     "schedule_17E" => ["adjustments"],
    # "schedule_31E" => [],
    "schedule_31F" => ["adjustments"],

    # Adjustment dependent schedules
    "schedule_4B" => ["adjustments"],
    # "schedule_8A" => ["adjustments"],
    "schedule_19" => ["adjustments"],
    "schedule_26" => ["adjustments"],
    "schedule_23B" => ["adjustments"],
    "schedule_9A" => ["adjustments"],

    # Schedule 11 group (independent)
    "schedule_11D" => ["adjustments"],
    "schedule_11F" => [],
    "schedule_11G" => [],
    # "schedule_11H" => [],
    "schedule_11J" => ["adjustments"],
    # "schedule_11K" => [],
    # "schedule_11L" => [],

    # Schedule 21 and above
    "schedule_25" => ["adjustments"],
    "schedule_30D" => [ "adjustments"],
    "schedule_31D" => ["adjustments"],
    # "schedule_31E" => [],
    "schedule_31C" => ["adjustments"],
    "schedule_32A" => [],

    # Add missing schedules from generate_schedule_data
    "schedule_2H" => [],
    # "schedule_6B" => [],

    # Ensure all schedules from case statement are included
    "schedule_23A" => [],
    "schedule_2A1" => [],
    "schedule_5B" => ["adjustments"],
    "schedule_6A" => ["adjustments"],
    "schedule_7A" => ["adjustments"],

    # Independent Schedules
    "schedule_01c" => [],
    # "schedule_02F" => ["schedule_12"],
    # "schedule_8A" => ["adjustments"],
    # "schedule_11H" => [],
    # "schedule_11K" => [],
    # "schedule_11L" => [],
    # "schedule_16A" => ["schedule_12", "schedule_18A"],
    "schedule_19" => ["adjustments"],
    "schedule_17B" => ["adjustments"],
    "schedule_22A" => ["adjustments"],
    "schedule_22B" => [],
    "schedule_31C" => [],

    # Add other schedules dependencies
    # Independent schedule
    "schedule_20A" => [],
    # Depends on income statement
    "schedule_29A" => ["income_statement"],
    # Independent schedule
    "schedule_30B" => [],
    "schedule_30C" => []
  }

  # Updated schedule groups with complete list
  @schedule_groups [
    # Group 1 - Independent Base Schedules
    [
      # Trial balance
      "schedule_12",
      "schedule_23A",
      # Base income statement
      "income_statement",
      "prev_income_statement",
      # Base loan data
      "schedule_03A",
      # Base account data
      "schedule_02D",
      # Base deposits
      "schedule_11E",
      # Base assets
      "schedule_21C",
      "schedule_21B",
      # Independent
      "schedule_32A",
      # Account details
      "schedule_01c",
      # Account summary
      "schedule_01E",
      # Base data
      "schedule_01F",
      "schedule_9A",
      "schedule_2H",
      # "schedule_6B",
      # "schedule_11H",
      # "schedule_11K",
      # "schedule_11L",
      "schedule_25",
      # "schedule_31E",
      "schedule_31F"
    ],
    # Group 2 - Depends on Trial Balance
    [
      # Trial balance dependent
      "schedule_02C",
      # Trial balance dependent
      "schedule_18A",
      "schedule_7A",
      # Deposits data dependent
      "schedule_18B",
      # Trial balance dependent
      "schedule_18C",
      # Trial balance dependent
      "schedule_18D",
      "schedule_11D",
      "schedule_11J"
      # "schedule_17C",
      # "schedule_02F"
      # "schedule_16A"
    ],
    # Group 3 - Income Statement Dependent
    [
      # Depends on income_statement
      # "schedule_13",
      # Income statement dependent
      "schedule_27",
      # Income statement dependent
      "schedule_27A",
      # Income statement dependent
      "schedule_28A",
      # Income statement dependent
      "schedule_28B",
      # Income statement dependent
      "schedule_24",
      "schedule_26",
      "schedule_11F"
      # Add new schedule to appropriate group
    ],
    # Group 4 - Balance Sheet Dependent
    [
      # Depends on schedule_13
      "blc_sheet",
      # Depends on balance sheet
      "schedule_15",
      # Depends on balance sheet
      "schedule_14",
      # Depends on balance sheet
      "schedule_4D",
      # Depends on adjustments
      "schedule_4B",
      # Depends on adjustments
      # "schedule_8A",
      "schedule_23B",
      "schedule_2A1",
      "schedule_17B",
      "schedule_22A",
      "schedule_22B",
      "schedule_5B",
      "schedule_6A",
      "schedule_17E",
      "schedule_19",
      "schedule_31C"
    ],
    # Add other schedules group
    [
      "schedule_20A",
      "schedule_29A",
      "schedule_30B",
      "schedule_30D",
      "schedule_31D",
      "schedule_30C"
    ]
    # New group for complex reports
  ]
  # acc, month, period
  def load_schedules(params) do
    try do
    with {:ok, entries} <- get_entries(params),
         {:ok, adjustments} <- get_adjustments(params) do
      results =
        @schedule_dependencies
        |> Map.keys()
        |> Task.async_stream(
          fn schedule ->
            try do
            {schedule, generate_schedule(schedule, params, entries, adjustments, %{})}
            rescue
              error ->
                Logger.error("Failed to generate schedule #{schedule}",
                  error: inspect(error),
                  stacktrace: __STACKTRACE__
                )

                {schedule, {:error, "Generation failed"}}
            end
          end,
          max_concurrency: 3,
          timeout: 3_000_000,
          on_timeout: :kill_task
        )
        |> Enum.reduce(%{}, fn
          {:ok, {schedule, result}}, acc ->
            Map.put(acc, schedule, result)

          _, acc ->
            acc
        end)

      # Verify cache creation after loading all schedules
      {total, cached, missing} = verify_cached_schedules(params)
      Logger.info("Cache verification: #{cached}/#{total} schedules cached")

      if length(missing) > 0 do
        Logger.warn("Attempting to cache missing schedules")

        Enum.each(missing, fn schedule ->
          result = Map.get(results, schedule)
          cache_key = generate_cache_key(schedule, params, adjustments)
          Cachex.put(:app_prereqs, cache_key, result, ttl: @cache_ttl)
        end)
      end

      {:ok, results}
    end

    catch
      kind, reason ->
        Logger.error("Schedule loading failed",
          kind: kind,
          error: inspect(reason),
          stacktrace: __STACKTRACE__
        )

        {:error, reason}
    end
  end

  # defp load_schedule_group(group, params, entries, adjustments, cached_results) do
  #   # Filter group to only include schedules whose dependencies are met
  #   loadable_schedules =
  #     Enum.filter(group, fn schedule ->
  #       dependencies_met?(schedule, cached_results)
  #     end)

  #   # Load schedules in parallel
  #   tasks =
  #     Enum.map(loadable_schedules, fn schedule ->
  #       Task.async(fn ->
  #         {schedule, generate_schedule(schedule, params, entries, adjustments, cached_results)}
  #       end)
  #     end)

  #   # Await results and merge with existing cache
  #   case tasks do
  #     [] ->
  #       cached_results

  #     _ ->
  #       results = Task.await_many(tasks, 30_000)
  #       Map.merge(cached_results, Map.new(results))
  #   end
  # end

  defp dependencies_met?(schedule, cached_results) do
    dependencies = Map.get(@schedule_dependencies, schedule, [])
    Enum.all?(dependencies, &Map.has_key?(cached_results, &1))
  end

  defp preload_dependencies(schedule, params, entries, adjustments, cached_results) do
    dependencies = Map.get(@schedule_dependencies, schedule, [])

    Enum.reduce(dependencies, cached_results, fn dependency, acc ->
      case Map.get(acc, dependency) do
        nil ->
          result = generate_schedule(dependency, params, entries, adjustments, acc)
          Map.put(acc, dependency, result)

        _ ->
          acc
      end
    end)
  end

  defp generate_schedule(schedule, params, entries, adjustments, cached_results) do
    cache_key = generate_cache_key(schedule, params, adjustments)
    Logger.info("Attempting to generate schedule: #{schedule} with key: #{cache_key}")

    result =
      case Cachex.get(:app_prereqs, cache_key) do
        {:ok, nil} ->
          # Ensure dependencies are loaded first
          updated_cache =
            preload_dependencies(schedule, params, entries, adjustments, cached_results)

          # Generate and cache the result
          result = generate_schedule_data(schedule, params, entries, adjustments, updated_cache)

          # Always attempt to cache the result
          case Cachex.put(:app_prereqs, cache_key, result, ttl: @cache_ttl) do
            {:ok, true} ->
              Logger.info("Cache created for #{schedule}")
              result

            {:error, reason} ->
              Logger.error("Failed to cache #{schedule}: #{inspect(reason)}")
              result
          end

        {:ok, cached_data} ->
          Logger.info("Cache hit for #{schedule}")
          cached_data

        {:error, reason} ->
          Logger.warn("Cache error for #{schedule}: #{inspect(reason)}")
          # On cache error, generate but still try to cache
          result = generate_schedule_data(schedule, params, entries, adjustments, cached_results)
          Cachex.put(:app_prereqs, cache_key, result, ttl: @cache_ttl)
          result
      end

    # Verify cache was created
    case Cachex.exists?(:app_prereqs, cache_key) do
      {:ok, true} ->
        Logger.info("Verified cache exists for #{schedule}")

      {:ok, false} ->
        Logger.warn("Cache verification failed for #{schedule}, attempting to recreate")
        Cachex.put(:app_prereqs, cache_key, result, ttl: @cache_ttl)

      {:error, reason} ->
        Logger.error("Cache verification error for #{schedule}: #{inspect(reason)}")
    end

    result
  end

  defp generate_schedule_data(schedule, params, entries, adjustments, cached_results) do
    try do
    Logger.info("Generating schedule: #{schedule}")
    start_date = params["start_date"]
    end_date = params["end_date"]
    schedule_adjustments = get_schedule_adjustments(schedule, adjustments)
    usd_rate = get_usd_rate(start_date)

    case schedule do
      # Group 1 - Base Schedules (no dependencies)
      "schedule_12" ->
        MisReports.Workers.Sh12.generate_display(start_date, end_date)

      "income_statement" ->
        MisReports.Workers.IS.generate_display(entries, end_date, adjustments)
      "prev_income_statement" ->
        end_date = end_date |> Date.from_iso8601!()
        prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
        date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

     MisReports.Workers.IS.generate_display(entries, date_string, adjustments)

      "schedule_03A" ->
        MisReports.Workers.LoansAdvances.Sh03a.generate_display(end_date).list

      "schedule_02D" ->
        MisReports.Workers.LoansAdvances.Sh02d.generate_display(start_date, end_date).list

      "schedule_11E" ->
        MisReports.Workers.Sh11e.generate_display(end_date)

      "schedule_21C" ->
        MisReports.Workers.Sh21c.generate_display(start_date, end_date, adjustments)

      # Group 2 - Schedules depending on base data
      "schedule_03B" ->
        trial_balance = Map.get(cached_results, "schedule_12")
        MisReports.Workers.Sh03b.generate_display(trial_balance)

      "schedule_4D" ->
        income = Map.get(cached_results, "income_statement")
        balance_sheet = Map.get(cached_results, "blc_sheet")

          MisReports.Workers.LoansAdvances.Sh4d.generate_display(
              balance_sheet,
    end_date,
              adjustments
            ).list

      "schedule_18A" ->
        MisReports.Workers.Sh18a.generate_display(end_date)

      "schedule_18B" ->
        depositors = MisReports.SourceData.number_depositors_data_18b(start_date)
        loans_entry = MisReports.Workers.LoansAdvances.Sh18b.generate_display(start_date)
        trial_balance = Map.get(cached_results, "schedule_12")

        MisReports.Workers.Sh18b.generate_display(
          trial_balance,
          loans_entry,
          depositors,
          start_date
        )

      "schedule_18C" ->
        # MisReports.Workers.Sh18c.generate_display()
        MisReports.Workers.Sh18c.generate()
      # Group 3 - Complex schedules with multiple dependencies
      "schedule_13" ->
          income =  MisReports.Workers.IS.generate_display(entries, end_date, adjustments)
          iso_date = end_date |> Date.from_iso8601!()
        prev_date = Timex.shift(iso_date, months: -1) |> Timex.end_of_month()
        date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")
          prev_income =      MisReports.Workers.IS.generate_display(entries, date_string, adjustments)


              MisReports.Workers.Sh13.generate_display(
              income,
              prev_income,
              end_date
            )

      "blc_sheet" ->
        schedule_13 = Map.get(cached_results, "schedule_13")
        income = Map.get(cached_results, "income_statement")

        MisReports.Workers.BalanceSheet.generate_display(
          schedule_13,
          entries,
          start_date,
          end_date,
          usd_rate,
          adjustments
        )

      "schedule_15" ->
        # Get cached dependencies first
        schedule_13 = Map.get(cached_results, "schedule_13")
        balance_sheet = Map.get(cached_results, "blc_sheet")

        if schedule_13 && balance_sheet do
          # Use cached dependencies
          MisReports.Workers.Sh15.generate_display(
            schedule_13,
            balance_sheet,
        end_date,
        adjustments
          )
        else
          # Fallback to regenerating if cache missing
          end_date = Date.from_iso8601!(end_date)
          prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
          date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

          # Get current month income statement
          income =
            MisReports.Workers.IS.generate_display(entries, end_date, schedule_adjustments)

          # Get previous month income statement
          prev_income =
            MisReports.Workers.IS.generate_display(entries, date_string, schedule_adjustments)

          # Generate schedule 13
          schedule_13 = MisReports.Workers.Sh13.generate_display(income, entries, end_date)

          # Get USD rate


          # Generate balance sheet
          balance_sheet =
            MisReports.Workers.BalanceSheet.generate_display(
              schedule_13,
              entries,
              end_date,
              start_date,
              usd_rate,
              adjustments
            )

          # Generate schedule 15
          MisReports.Workers.Sh15.generate_display(
            schedule_13,
            balance_sheet,
            end_date,
            adjustments
          )
        end

      # Group 4 - Currency and Time Related Schedules
      "schedule_31D" ->
        MisReports.Workers.Sh31d.generate_display(
          end_date,
          usd_rate,
          adjustments
        )

      "schedule_30D" ->
        MisReports.Workers.Sh30d.generate_display(start_date, end_date, adjustments)

      "schedule_23A" ->
        MisReports.Workers.Sh23a.generate_display(end_date, usd_rate)

      "schedule_23B" ->
        MisReports.Workers.Sh23b.generate_display(
    end_date,
          usd_rate,
          adjustments
        )

      "schedule_01c" ->
        MisReports.Workers.Sh01c.generate_display(
          start_date,
          end_date
        )

      "schedule_8A" ->
        MisReports.Workers.LoansAdvances.Sh8a.generate_display(
    end_date,
          adjustments
        )

      "schedule_26" ->
            MisReports.Workers.Sh26.generate_display(
      start_date,
    end_date,
              adjustments
            )

      # Group 5 - Loan Related Schedules
      "schedule_2H" ->
        MisReports.Workers.LoansAdvances.Sh02h.generate_display(end_date, adjustments)

      "schedule_2A1" ->
        MisReports.Workers.LoansAdvances.Sh2a1.generate_display(end_date, adjustments)

      "schedule_17B" ->
            MisReports.Workers.LoansAdvances.Sh17b.generate_display(
    end_date,
              adjustments
            )
      "schedule_22A" ->
            MisReports.Workers.LoansAdvances.Sh22a.generate_display(
    end_date,
              adjustments
            )
      "schedule_22B" ->
              MisReports.Workers.LoansAdvances.Sh22b.generate_display(start_date,end_date)

      "schedule_7A" ->
            MisReports.Workers.LoansAdvances.Sh7a.generate_display(
    end_date,
              adjustments
            )
      "schedule_17E" ->
            MisReports.Workers.Shd17e.generate_display(
    end_date,
              usd_rate,
              adjustments
            )
      "schedule_5B" ->
        MisReports.Workers.LoansAdvances.Sh5b.generate_display(end_date, adjustments)

      "schedule_6A" ->
        MisReports.Workers.LoansAdvances.Sh6a.generate_display(end_date, adjustments)

      "schedule_6B" ->
        MisReports.Workers.LoansAdvances.Sh6b.generate_display(end_date)

      # Group 6 - Remaining Schedules
      "schedule_02G" ->
        MisReports.Workers.Sh02g.generate_display(end_date, adjustments)

      "schedule_11D" ->
        MisReports.Workers.Sh11d.generate_display(
    end_date,
          usd_rate,
          adjustments
        )

      "schedule_11J" ->
        MisReports.Workers.Sh11j.generate_display(
    end_date,
          usd_rate,
          adjustments
        )

      "schedule_28B" ->
        MisReports.Workers.Sh28b.generate_display(
          start_date,
          end_date,
          adjustments
        )

      "schedule_14" ->
            income =
              MisReports.Workers.IS.generate_display(
                entries,
    end_date,
                adjustments
              )

        balance_sheet = Map.get(cached_results, "blc_sheet")

            MisReports.Workers.Sh14.generate_display(
              balance_sheet,
    end_date,
              adjustments
            )

      "schedule_4B" ->
        MisReports.Workers.LoansAdvances.Sh4b.generate_display(
          end_date,
          adjustments
        )

      "schedule_28A" ->
        MisReports.Workers.Sh28a.generate_display(
          start_date,
          end_date,
          adjustments
        )

      "schedule_32A" ->
        MisReports.Workers.Sh32a.generate_display(
          start_date,
    end_date
        )

      "schedule_24" ->
        MisReports.Workers.Sh24.generate_display(end_date)

      "schedule_11G" ->
        MisReports.Workers.Sh11fAndsh11g.generate_display(end_date, "11G")

      "schedule_01E" ->
        MisReports.Workers.Sh01e.generate_display(start_date, end_date)

      "schedule_11F" ->
        MisReports.Workers.Sh11fAndsh11g.generate_display(end_date, "11F")

      "schedule_27" ->
        MisReports.Workers.Sh27.generate_display(
          start_date,
          end_date,
          adjustments
        )

      "schedule_02C" ->
        MisReports.Workers.LoansAdvances.Sh02c.generate_display(
    end_date,
          adjustments
        )

      "schedule_01F" ->
        MisReports.Workers.Sh01f.generate_display(
          start_date,
    end_date
        )

        "schedule_9A" ->
            MisReports.Workers.Sh09a.generate_display(
               start_date,
               end_date
            )

      "schedule_27A" ->
        MisReports.Workers.Sh27a.generate_display(
          start_date,
          end_date,
          adjustments
        )

      "schedule_18D" ->
        MisReports.Workers.Sh18d.generate_display()

      "schedule_25" ->
        MisReports.Workers.Sh25.generate_display(start_date, end_date, adjustments)

      "schedule_31F" ->
        MisReports.Workers.Sh31f.generate_display(end_date, adjustments)

      "schedule_19" ->
        MisReports.Workers.Sh19.generate_display(end_date, adjustments)

      "schedule_31C" ->
        MisReports.Workers.Sh31c.generate_display(
          end_date,
          usd_rate,
          adjustments
        )

      # Add Other Schedules implementations
      "schedule_20A" ->
        MisReports.Workers.Sh20a.generate_display(end_date)

      "schedule_29A" ->
        income = Map.get(cached_results, "income_statement")
        MisReports.Workers.Sh29a.generate_display(end_date)

      "schedule_30B" ->


        MisReports.Workers.Sh30b.generate_display(
          end_date,
          usd_rate
        )

      "schedule_30C" ->

        MisReports.Workers.Sh30c.generate_display(
          end_date,
          usd_rate
        )

      "schedule_21B" ->
            MisReports.Workers.Sh21b.generate_display(entries, adjustments)

      _ ->
        Logger.warn("Unknown schedule type: #{schedule}")
        %{}
    end

    catch
      kind, reason ->
        IO.inspect({kind, reason}, label: "Error in schedule generation")
        Logger.error("Schedule generation failed: #{inspect({kind, reason})}")
        handle_generation_error(schedule, {kind, reason})
    end
  end

  defp get_entries(params) do
    try do
      month = String.replace(params["start_date"], "-", "") |> String.slice(0..5)
      entries = get_cached_entries(month)
      {:ok, entries}
    rescue
      e -> {:error, e}
    end
  end

  defp get_adjustments(params) do
    try do
      adjustments = get_cached_adjustments(params["end_date"])
      {:ok, adjustments}
    rescue
      e -> {:error, e}
    end
  end

  defp clean_data(entries) do
    Enum.map(entries, fn e ->
      case String.length(e.sap_gl_acc_no) > 6 do
        true -> Map.put(e, :sap_gl_acc_no, String.slice(e.sap_gl_acc_no, 4..-1))
        false -> e
      end
    end)
  end

  def invalidate_cache(params) do
    pattern = "*:#{params["start_date"]}:#{params["end_date"]}"

    case Cachex.keys(:app_prereqs) do
      {:ok, keys} ->
        keys
        |> Enum.filter(&String.match?(&1, ~r/#{pattern}/))
        |> Enum.each(&Cachex.del(:app_prereqs, &1))

      _ ->
        :ok
    end
  end

  # Helper function to get entries
  # defp get_entries(date) do
  #   month = String.replace(date, "-", "") |> String.slice(0..5)
  #   MisReports.SourceData.gbm_by_month(month) |> clean_data()
  # end

  defp clean_data(entries) do
    Enum.map(entries, fn e ->
      case String.length(e.sap_gl_acc_no) > 6 do
        true -> Map.put(e, :sap_gl_acc_no, String.slice(e.sap_gl_acc_no, 4..-1))
        false -> e
      end
    end)
  end

  # Cache helpers
  # defp get_cached_or_generate(schedule, cached_data) do
  #   case Map.get(cached_data, schedule) do
  #     nil ->
  #       Logger.warn("Cache miss for #{schedule}, regenerating...")
  #       regenerate_schedule(schedule, cached_data)

  #     data ->
  #       Logger.debug("Cache hit for #{schedule}")
  #       data
  #   end
  # end

  # defp regenerate_schedule(schedule, cached_data) do
  #   # Implement retry logic
  #   retry_with_backoff(fn ->
  #     generate_schedule_data(
  #       schedule,
  #       cached_data.start_date,
  #       cached_data.end_date,
  #       cached_data.adjustments,
  #       cached_data
  #     )
  #   end)
  # end

  # Helper functions for schedule generation
  defp get_usd_rate(date) do
    MisReports.Utilities.get_exchange_rate_by_date_and_code(date, "USD")[:exchange_rate_lcy] ||
      "1"
  end

  defp get_previous_month_end(date) do
    date
    |> Date.from_iso8601!()
    |> Timex.shift(months: -1)
    |> Timex.end_of_month()
    |> Timex.format!("{YYYY}-{0M}-{0D}")
  end

  # Adjustments handling
  # defp get_adjustments(date) do
  #   try do
  #     MisReports.Utilities.get_adjustments(date)
  #   rescue
  #     error ->
  #       Logger.error("Failed to get adjustments: #{inspect(error)}")
  #       %{}
  #   end
  # end

  defp get_schedule_adjustments(schedule, adjustments) do
    case adjustments do
      nil -> %{}
      adjustments when is_map(adjustments) -> Map.get(adjustments, schedule, %{})
      _ -> %{}
    end
  end

  # Cache key generation with adjustments hash
  defp generate_cache_key(schedule, params, adjustments) do
    # Add more granular adjustment tracking
    adjustment_keys =
      case adjustments do
        %{^schedule => specific_adjustments} -> specific_adjustments
        _ -> %{}
      end

    # Include adjustment-specific hash in cache key
    adjustments_hash = :erlang.phash2(adjustment_keys)
    "#{schedule}:#{params["start_date"]}:#{params["end_date"]}:#{adjustments_hash}"
  end

  # Add a function to invalidate cache when adjustments change
  def invalidate_schedule_cache(schedule, date) do
    pattern = "#{schedule}:#{date}:*"

    case Cachex.keys(:app_prereqs) do
      {:ok, keys} ->
        keys
        |> Enum.filter(&String.match?(&1, ~r/#{pattern}/))
        |> Enum.each(&Cachex.del(:app_prereqs, &1))

      _ ->
        :ok
    end
  end

  # Error handling and retry logic
  defp handle_generation_error(schedule, error) do
    Logger.error("Schedule generation failed",
      schedule: schedule,
      error: inspect(error),
      stacktrace: Exception.format_stacktrace()
    )

    %{
      error: true,
      schedule: schedule,
      message: "Failed to generate schedule data",
      timestamp: DateTime.utc_now()
    }
  end

  defp retry_with_backoff(fun, attempts \\ 3, delay \\ 1000) do
    try do
      fun.()
    rescue
      error ->
        if attempts > 0 do
          Logger.warn("Retrying after error, attempts left: #{attempts}")
          Process.sleep(delay)
          retry_with_backoff(fun, attempts - 1, delay * 2)
        else
          reraise error, __STACKTRACE__
        end
    end
  end

  # Add this function to monitor cache creation
  def verify_cached_schedules(params) do
    all_schedules = Map.keys(@schedule_dependencies)

    cached_keys =
      case Cachex.keys(:app_prereqs) do
        {:ok, keys} -> keys
        _ -> []
      end

    missing_schedules =
      Enum.filter(all_schedules, fn schedule ->
        cache_key = generate_cache_key(schedule, params, %{})
        not Enum.any?(cached_keys, &(&1 =~ cache_key))
      end)

    if length(missing_schedules) > 0 do
      Logger.warn("Schedules missing from cache: #{inspect(missing_schedules)}")
    end

    {length(all_schedules), length(cached_keys), missing_schedules}
  end

  def get_cached_entries(month) do
    cache_key = "entries:#{month}"

    case Cachex.get(:app_prereqs, cache_key) do
      {:ok, nil} ->
        entries = MisReports.SourceData.gbm_by_month(month) |> clean_data()
        Cachex.put(:app_prereqs, cache_key, entries, ttl: @entries_cache_ttl)
        entries

      {:ok, cached_entries} ->
        cached_entries

      {:error, _reason} ->
        # On cache error, fallback to direct fetch
        MisReports.SourceData.gbm_by_month(month) |> clean_data()
    end
  end

  def get_cached_adjustments(date) do
    cache_key = "adjustments:#{date}"

    case Cachex.get(:app_prereqs, cache_key) do
      {:ok, nil} ->
        adjustments = MisReports.Utilities.get_adjustments(date)
        Cachex.put(:app_prereqs, cache_key, adjustments, ttl: @adjustments_cache_ttl)
        adjustments

      {:ok, cached_adjustments} ->
        cached_adjustments

      {:error, _reason} ->
        # On cache error, fallback to direct fetch
        MisReports.Utilities.get_adjustments(date)
    end
  end

  def invalidate_adjustments_cache(date) do
    cache_key = "adjustments:#{date}"
    Cachex.del(:app_prereqs, cache_key)

    # Also invalidate any schedule that depends on adjustments
    adjustment_dependent_schedules = [
      "schedule_4B",
      # "schedule_8A",
      "schedule_19",
      "schedule_23B"
      # Add other adjustment-dependent schedules
    ]

    Enum.each(adjustment_dependent_schedules, fn schedule ->
      invalidate_schedule_cache(schedule, date)
    end)
  end

  def clear_all_cache do
    case Cachex.keys(:app_prereqs) do
      {:ok, keys} ->
        # Delete all keys and log the count
        deleted_count = Enum.count(keys)
        Cachex.reset(:app_prereqs)
        Logger.info("Cleared #{deleted_count} items from cache")
        :ok

      {:error, reason} ->
        Logger.error("Failed to clear cache: #{inspect(reason)}")
        :error
    end
  end
end
