defmodule MisReports.Workers.BozReq.Schedule22a do
  def perform(item) do
    exchange_rate = MisReports.Prudentials.usd_rate(item.end_date) |> Decimal.to_float() || Decimal.new("1") |> Decimal.to_float()
    regulatory_capital = if(MisReports.Prudentials.regulatory_capital(item.end_date) == Decimal.new("0"), do: Decimal.new("1"), else: MisReports.Prudentials.regulatory_capital(item.end_date)) |> Decimal.to_float()
    decoded_item =
      case item.schedule_22a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    # regulatory_capital = decoded_item["header"]
    decoded_item = decoded_item["list"]
    # cost_of_funds = decoded_item["header"]["cost_of_funds"]

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "ReturnKey" => "ZM-9FSCH22A9F003",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "StartDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "EndDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%S", :strftime)}",
      "ReturnItemsList" => [
        %{"Code" => "1139_00001", "Value" => "#{exchange_rate}", "_dataType" => "NUMERIC"},
        %{"Code" => "1139_00002", "Value" => "#{regulatory_capital}", "_dataType" => "NUMERIC"}
      ],
      "DynamicItemsList" => [
        %{
          "Area" => 436,
          "_areaName" => "ANALYSIS OF CURRENT, PAST DUE AND NPLS ON GOVERNMENT AND GOVERNMENT RELATED ENTITIES - ON BALANCE SHEET EXPOSURE",
          "DynamicItems" =>[]
        }
      ]
    }


  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end


  def map_data(records) do
    Enum.flat_map(Enum.with_index(records), fn {map, index} ->
        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => map["A"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => map["B"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.3", "Value" => map["C"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.4", "Value" => map["D"], "_dataType" => "TEXT"},
          %{"Code" => "#{index}.5", "Value" => convert_decimal_map_to_decimal(map["E"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.6", "Value" => convert_decimal_map_to_decimal(map["F"]), "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.7", "Value" => map["G"], "_dataType" => "TEXT"}
        ]
      end)
  end

  def format_values(maps) do
    Enum.map(maps, fn map ->
      case Map.get(map, "_dataType") do
        "NUMERIC" ->
          update_numeric_value(map)

        "TEXT" ->
          update_text_value(map)

        "DATE" ->
          update_text_value(map)

        _ ->
          map
      end
    end)
  end

  defp update_numeric_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "0.00")
    else
      map
    end
  end

  defp update_text_value(map) do
    if Map.get(map, "Value") in [nil, ""] do
      Map.put(map, "Value", "'")
    else
      map
    end
  end

  # defp compare_keys(a, b) do
  #   case {String.length(a), String.length(b)} do
  #     {1, 1} -> a <= b  # Compare single-letter keys normally (A-Z)
  #     {1, _} -> true     # Single-letter keys (A-Z) always come before double-letter keys (AA, AB, ...)
  #     {_, 1} -> false    # Double-letter keys come after single-letter keys
  #     _ -> a <= b        # Compare multi-letter keys lexicographically (AA, AB, AC, AD...)
  #   end
  # end

  def convert_exponent(%{"coef" => coef, "exp" => exp, "sign" => sign}) do
    Decimal.new(sign, coef, exp) |> Decimal.to_float()
  end

  def convert_exponent(value), do: value

  def convert_decimal_map_to_decimal(value) do
    value = if(value in [nil, ""], do: 0.00, else: Decimal.new(value["sign"], value["coef"], value["exp"]) |> Decimal.to_float())
    "#{value}"
  end
end
