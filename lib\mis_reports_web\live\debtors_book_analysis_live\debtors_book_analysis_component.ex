defmodule MisReportsWeb.DebtorsBookAnalysisLive.DebtorsBookAnalysisComponent do
  use MisReportsWeb, :live_component
  use PipeTo.Override
  alias MisReports.{Repo}
  alias MisReports.{SourceData, SourceData.DebtorsBookAnalysis}
  alias MisReportsWeb.UserController
  alias MisReportsWeb.Router.Helpers, as: Routes

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.DebtorsBookAnalysisView , "new.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.DebtorsBookAnalysisView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{debtors_book_analysis: debtors_book_analysis} = assigns, socket) do
    changeset = SourceData.change_debtors_book_analysis(debtors_book_analysis)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"debtors_book_analysis" => debtors_book_analysis}, socket) do
    changeset =
      socket.assigns.debtors_book_analysis
      |> DebtorsBookAnalysis.changeset(debtors_book_analysis)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", params, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("update", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_approval(socket, :reject, params)
      "97" -> handle_approval(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, %{"debtors_book_analysis" => params}) do
    audit_msg = "Created a debtors book analysis  \"#{params["type"]}\" with report date \"#{params["report_date"]}\""
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:debtors_book_analysis, DebtorsBookAnalysis.changeset(%DebtorsBookAnalysis{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
        {:ok, %{debtors_book_analysis: debtors_book_analysis}} ->
          case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of debtors book analysis Creation"
             ) do
          {:ok, reference_number} ->
            case SourceData.update_debtors_book_analysis(debtors_book_analysis, %{reference: reference_number}) do
              {:ok, updated_debtors_book_analysis} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "Debtors book analysis created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update debtors book analysis reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Debtors book analysis created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

        {:error, %Ecto.Changeset{} = changeset} ->
          {:noreply, assign(socket, changeset: changeset)}
      end
  end

  def handle_save(socket, :edit, params) do
    debtors_book_analysis = socket.assigns.debtors_book_analysis
    socket
    |> handle_update(params, debtors_book_analysis)
    |> case do
      {:ok, debtors_book_analysis} ->
        {:noreply,
          socket
          |> put_flash(:info, "debtors book analysis updated successfully")
          |> push_redirect(to: Routes.debtors_book_analysis_index_path(socket, :edit, debtors_book_analysis))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, debtors_book_analysis) do
    audit_msg = "debtors book analysis  \"#{params["type"]}\" with report date \"#{params["report_date"]}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:debtors_book_analysis, DebtorsBookAnalysis.changeset(debtors_book_analysis, Map.merge(params, %{"status" => "D", "checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{debtors_book_analysis: debtors_book_analysis, audit_log: _user_log}} ->
        {:ok, debtors_book_analysis}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_approval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Debtors book analysis Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Debtors book analysis rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject debtors book analysis: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_approval(socket, :approve, params) do
    debtors_book_analysis = socket.assigns.debtors_book_analysis
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Debtors book analysis Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      DebtorsBookAnalysis.changeset(debtors_book_analysis, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_debtors_book_analysis}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Debtors book analysis approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Debtors book analysis approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve debtors book analysis")
         |> assign(:changeset, %{debtors_book_analysis.changeset | errors: failed_value.errors})}
    end
  end

end
