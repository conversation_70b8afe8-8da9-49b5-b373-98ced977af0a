defmodule MisReports.Workers.BozReq.Schedule21c do
  alias MisReports.Workers.BalanceSheet

  def perform(item) do
    decoded_item =
      case item.schedule_21c do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end

    # IO.inspect(decoded_item, label: "====================================SCHEDULE 21C DATA====================")

    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-1NSCH21C1N002",
      "instCode" => "#{settings.institution_code}",
     "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
       "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "ReturnItemsList" => build_return_items_list(decoded_item),
      "DynamicItemsList" => [
        %{
          "Area" => 447,
          "_areaName" => "ALL OTHER ACCRUED INTEREST PAYABLE",
          "DynamicItems" => map_data(decoded_item)
        }
      ]
    }
  end

  def map_data(records) do
    # IO.inspect("===================AT THIS POINT IN MAP DATA ====================")

    Enum.flat_map(Enum.with_index(records["list"] || []), fn {map, index} ->

      # IO.inspect("===================AT THIS POINT IN ENUM.FLAT MAP====================")

        index = index + 1
        [
          %{"Code" => "#{index}.1", "Value" => "#{map["Description"] || "N/A"}", "_dataType" => "TEXT"},
          %{"Code" => "#{index}.2", "Value" => "#{convert_to_number(map["Amount"]) || "0"}", "_dataType" => "NUMERIC"},
          %{"Code" => "#{index}.3", "Value" => "#{map["Nature of Balance"] || "N/A"}", "_dataType" => "TEXT"}
        ]
      end)
  end

  def build_return_items_list(items) do
    [
      %{
        "Code" => "1167_00001",
        "Value" => "#{Map.get(items, "B14", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00002",
        "Value" => "#{Map.get(items, "B15", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00003",
        "Value" => "#{Map.get(items, "B16", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00004",
        "Value" => "#{Map.get(items, "B17", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00005",
        "Value" => "#{Map.get(items, "B18", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00006",
        "Value" => "#{Map.get(items, "B19", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00007",
        "Value" => "#{Map.get(items, "B20", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00008",
        "Value" => "#{Map.get(items, "B21", "0")}",
        "_dataType" => "NUMERIC"
      },
      %{
        "Code" => "1167_00009",
        "Value" => "#{Map.get(items, "B25", "0")}",
        "_dataType" => "NUMERIC"
      }
    ]
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    try do
      case Integer.parse(value) do
        {int_value, ""} -> Decimal.new(int_value)
        _ -> Decimal.new(String.to_float(value))
      end
    rescue
      # Handle any parsing errors
      _ -> Decimal.new(0)
    end
  end

  defp convert_to_number(_), do: Decimal.new(0)
end
