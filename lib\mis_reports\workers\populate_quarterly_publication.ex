defmodule MisReports.Workers.PopulateQuarterlyPublication do
  alias MisReports.Workers.Utils
  alias MisReports.Utilities

  # Update cell type constants to exactly match Excelizer requirements
  @cell_types %{
    number: "float",
    string: "string"
  }

  def perform(data) do
    IO.inspect("------------------------POPULATE EXPORTING-----------------------------")
    file = gen_file(data.uuid)

    # Add debug to see file path
    # IO.inspect(file, label: "Destination Excel file")

    # Make sure template exists
    unless File.exists?(file) do
      IO.inspect("ERROR: Destination file doesn't exist: #{file}")
      {:error, :file_not_found}
    end

    case Excelizer.open(file, &populate(&1, data)) do
      :ok ->
        # Verify file was created and has content
        case File.stat(file) do
          {:ok, %{size: size}} when size > 0 ->
            IO.inspect("Successfully generated file with size: #{size} bytes")
            {:ok, file}
          _ ->
            IO.inspect("Warning: Generated file appears empty")
            {:ok, file}
        end
      error ->
        IO.inspect(error, label: "Excel generation error")
        error
    end
  end

  def gen_file(uuid) do
    dir = MisReports.Utilities.get_directory_params()
    datetime = Timex.local() |> Timex.format!("%Y%m%d%H%M", :strftime)
    name = "Quarterly_Publication_Report_#{datetime}.xlsx"
    report = MisReports.Utilities.get_quarterly_publication_report(uuid)
    MisReports.Utilities.update_quarterly_publication_report(report, %{filename: name})
    dest_file = "#{dir.complete}/#{name}"
    template_file = "#{dir.templates}/Quarterly_publication_template.xlsx"

    # Add debug for file paths
    # IO.inspect(template_file, label: "Template file")
    # IO.inspect(dest_file, label: "Destination file")

    # Verify template exists
    unless File.exists?(template_file) do
      IO.inspect("ERROR: Template file not found: #{template_file}")
      raise "Template file not found: #{template_file}"
    end

    # Create directory if it doesn't exist
    File.mkdir_p!(Path.dirname(dest_file))

    # Copy template with explicit error handling
    case File.copy(template_file, dest_file) do
      {:ok, bytes_copied} ->
        IO.inspect("Template copied successfully: #{bytes_copied} bytes")
      {:error, reason} ->
        IO.inspect("Failed to copy template: #{inspect(reason)}")
        raise "Failed to copy template: #{inspect(reason)}"
    end

    dest_file
  end

  def populate(file_id, data) do
    # Parse JSON data from saved report
    income_stmt = data.income_stmt
    blc_sheet = data.blc_sheet
    stmt_capital_pos = data.stmt_capital_pos
    stmt_liquid_pos = data.stmt_liquid_pos

    # Add debug info for all sheets
    # IO.inspect(income_stmt, label: "Income Statement Data")
    # IO.inspect(blc_sheet, label: "Balance Sheet Data")
    # IO.inspect(stmt_capital_pos, label: "Capital Position Data")
    IO.inspect(stmt_liquid_pos, label: "Liquidity Position Data")

    # Check which file_id format is expected by Excelizer
    IO.inspect(file_id, label: "File ID format")

    # Populate all sheets with error handling for each
    try_populate_section(fn -> populate_income_statement(file_id, income_stmt) end, "Quarterly Publication Landscape")
    try_populate_section(fn -> populate_balance_sheet(file_id, blc_sheet) end, "Quarterly Publication Landscape")
    try_populate_section(fn -> populate_capital_position(file_id, stmt_capital_pos) end, "Quarterly Publication Landscape")
    try_populate_section(fn -> populate_liquidity_position(file_id, stmt_liquid_pos) end, "Quarterly Publication Landscape")
    try_populate_section(fn -> gen_info(file_id, data) end, "General Info")

    # Force save with debug
    IO.inspect("Saving workbook...")
    result = Excelizer.Workbook.save(file_id)
    IO.inspect(result, label: "Save result")
    result
  end

  # Helper to safely try populating each section
  defp try_populate_section(func, section_name) do
    try do
      IO.inspect("Starting population of #{section_name}")
      func.()
      IO.inspect("Successfully populated #{section_name}")
    rescue
      e -> IO.inspect("Error populating #{section_name}: #{inspect(e)}")
    end
  end

  defp populate_income_statement(file_id, data) do
    # Get current quarter from the data structure with proper string keys
    current_quarter_key = data
                         |> Map.get("quarterly_totals_by_key", %{})
                         |> Map.keys()
                         |> List.first()

    IO.inspect(current_quarter_key, label: "Current Quarter")

    # Define cell mappings with their data sources
    income_stmt_cells = [
      {:regular, "E16", :loans_and_advances_from_normal_deposits},
      {:regular, "E17", :from_banks_and_other_financial_institutions},
      {:regular, "E18", :securities},
      {:regular, "E20", :total_interest_income},
      {:regular, "E23", :deposits},
      {:regular, "E25", :interest_paid_to_banks_and_financial_institutions},
      {:regular, "E21", :total_interest_expense},
      {:regular, "E29", :net_interest_income},
      {:regular, "E31", :total_provisions},
      {:regular, "E33", :net_interest_income_after_provisions},
      {:regular, "E43", :total_non_interest_income},
      {:regular, "E44", :net_interest_and_other_income},
      {:regular, "E49", :total_non_interest_expenses},
      {:regular, "E51", :income_loss_before_taxes},
      {:regular, "E57", :income_loss_after_taxes},
      {:regular, "E61", :income_loss_after_taxes},

      # C-prefixed keys from quarterly_totals_by_key
      {:c_value, "E19", "C32"}, # Other interest income,done
      {:c_value, "E24", "C57"}, # Subordinated debt interest,done
      {:c_value, "E26", "C59"}, # Other interest expense done
      {:c_value, "E36", "C71"}, # Commissions done
      {:c_value, "E38", "C73"}, # Fees from forex transactions done
      {:c_value, "E39", "C77"}, # Realized trading gains done
      {:c_value, "E40", "C74"}, # Unrealized forex gains done
      {:c_value, "E41", "C80"}, # Other income done
      {:c_value, "E47", "C87"}, # Depreciation done
      {:c_value, "E48", "C94"}, # Other expenses done
      {:c_value, "E55", "C97"}  # Taxation done
    ]

    # Populate QTD values - each type from its respective map in the same quarter
    Enum.each(income_stmt_cells, fn
      {:regular, cell, key} ->
        value = get_in(data, ["sum_quarterly_totals_by_keys", current_quarter_key, Atom.to_string(key)]) || "-"
        set_cell_with_debug(file_id, "Quarterly Publication Landscape", cell, value)

      {:c_value, cell, key} ->
        value = get_in(data, ["quarterly_totals_by_key", current_quarter_key, key]) || "-"
        set_cell_with_debug(file_id, "Quarterly Publication Landscape", cell, value)
    end)

    # Populate YTD values with proper string keys
    Enum.each(income_stmt_cells, fn
      {:regular, cell, key} ->
        ytd_cell = String.replace(cell, "E", "G")
        value = get_in(data, ["ytd_values", "sum_across_quarters", Atom.to_string(key)]) || "-"
        set_cell_with_debug(file_id, "Quarterly Publication Landscape", ytd_cell, value)

      {:c_value, cell, key} ->
        ytd_cell = String.replace(cell, "E", "G")
        value = get_in(data, ["ytd_values", "sum_across_quarters_2", key]) || "-"
        set_cell_with_debug(file_id, "Quarterly Publication Landscape", ytd_cell, value)
    end)
  end

  # Improved helper function for setting cells with debug
  defp set_cell_with_debug(file_id, sheet, cell, value) do
    formatted_value = format_cell_value(value)
    # IO.inspect("Setting #{sheet} cell #{cell} to #{inspect(formatted_value)}")

    try do
      case formatted_value do
        %{type: "float", value: num} when is_number(num) ->
          Excelizer.Cell.set_cell_value(file_id, "Quarterly Publication Landscape", cell, "float", num)
        %{type: "string", value: str} ->
          Excelizer.Cell.set_cell_value(file_id, "Quarterly Publication Landscape", cell, "string", str)
      end
    rescue
      e ->
        IO.inspect("Error setting cell #{cell}: #{inspect(e)}")
        {:error, e}
    end
  end

  # Improved helper to properly format cell values
  defp format_cell_value(value) when is_binary(value) do
    # Remove commas and try to convert to number
    clean_value = String.replace(value, ",", "")
    case Float.parse(clean_value) do
      {num, ""} -> %{type: "float", value: num}
      _ -> %{type: "string", value: value}
    end
  end
  defp format_cell_value(nil), do: %{type: "float", value: 0.0}
  defp format_cell_value("-"), do: %{type: "float", value: 0.0}
  defp format_cell_value(num) when is_number(num), do: %{type: "float", value: num * 1.0}
  defp format_cell_value(_), do: %{type: "float", value: 0.0}

  defp populate_balance_sheet(file_id, data) do
    cells = [
      {"K16", "B11"},  # Notes and Coins
      {"K17", "B16"},  # Balances with Bank of Zambia
      {"K18", "B22"},  # Balances with Banks in Zambia
      {"K19", "B26"},  # Balances with Banks abroad
      {"K20", "B29"},  # Investments in Securities
      {"K21", "B42"},  # Net Loans and Advances
      {"K22", "B48"},  # Interbranch Balances
      {"K23", "B49"},  # Fixed Assets
      {"K24", "B50"},  # Other Assets
      {"K26", "B60"},  # Total Assets
      {"K32", "B63"},  # Deposits
      {"K33", "B67"},  # Due to Bank of Zambia
      {"K34", "B72"},  # Due to Banks in Zambia
      {"K35", "B76"},  # Due to Banks abroad
      {"K36", "B81"},  # Other Liabilities
      {"K37", "B91"},  # OTHER borrowed funds
      {"K38", "B96"},  # Shareholders' equity
      {"K40", "B105"},  # Total liabilities and equity
      {"K45", "B108"}, # Contingent liabilities
      {"K46", "B117"}, # Commitments
      {"K47", "B118"}
    ]

    Enum.each(cells, fn {excel_cell, data_key} ->
      value = Map.get(data, data_key, "-")
      set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, value)
    end)
  end

  defp populate_capital_position(file_id, data) do
    cells = [
      {"O17", "C21"}, # Paid-up common shares
      {"O18", "-"},
      {"O19", "C24"}, # Retained earnings
      {"O20", "C35"}, # General Reserve
      {"O21", "C26"}, # Statutory Reserves
      {"O22", "C28"}, # Sub total
      {"O24", "C25"}, # Other adjustments
      {"O25", "C47"}, # Total Primary Capital
      {"O28", "C50"}, # Eligible subordinated term debt
      {"O29", "C53"}, # Revaluation Reserve
      {"O30", "C55"}, # Eligible Secondary Capital
      {"O32", "C61"}, # Eligible Total Capital
      {"O36", "C58"}, # Minimum Total Capital Requirement
      {"O38", "C60"}, # Excess/Deficiency - FIXED cell reference
      {"O40", "C62"}  # Risk Based Assets
    ]

    Enum.each(cells, fn {excel_cell, data_key} ->
      value = if data_key == "-" do
        "-"
      else
        Map.get(data, data_key, "-")
      end

      set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, value)
    end)
  end

  # Helper function to parse numbers that could be either integers or floats
  defp parse_number(string) do
    case Float.parse(string) do
      {num, _} -> num
      :error ->
        case Integer.parse(string) do
          {num, _} -> num * 1.0  # Convert integer to float
          :error -> 0.0  # Default value for invalid numbers
        end
    end
  end

  defp populate_liquidity_position(file_id, data) do
    cells = [
      {"S18", "B64"},  # Demand Deposits
      {"S19", "B65"},  # Savings Deposits
      {"S20", "B66"},  # Time Deposits
      {"S21", "-"},    # Bills Payable
      {"S22", "B63"},  # Total Deposits
      {"S30", "B11"},  # Notes and coins
      {"S32", "B19"},  # Current account
      {"S33", "statutory"},  # Statutory deposit accounts - Changed from atom to string
      {"S34", "-"},    # OMO deposits
      {"S35", "-"},    # Other balances
      {"S36", "Treasury_bills"},  # Treasury Bills - Changed from atom to string
      {"S37", "-"},    # Money at call
      {"S38", "-"},    # Bills of exchange
      {"S39", "local_registered_securities"},  # Local registered securities - Changed from atom to string
      {"S41", "-"},    # Items in transit
      {"S42", "TOTAL_ASSETS"},  # Total Liquid Assets
      {"S47", "liquid_assets_percentage"},
      {"S49", "total_liquid_assets_percentage"}
    ]

    Enum.each(cells, fn {excel_cell, data_key} ->
      value = case data_key do
        # Handle decimal map values with better error handling
        key when key in ["statutory", "Treasury_bills", "local_registered_securities"] ->
          decimal_map = Map.get(data, key, %{})
          IO.inspect(decimal_map, label: "Decimal map for #{key}")
          case decimal_map do
            %{"sign" => sign, "exp" => exp, "coef" => coef} when is_number(sign) and is_number(exp) and is_number(coef) ->
              num = coef * :math.pow(10, exp) * sign
              set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, num)
            _ ->
              IO.inspect("Invalid decimal map for #{key}: #{inspect(decimal_map)}")
              set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, "-")
          end

        # Handle string values
        key when is_binary(key) ->
          if key == "-" do
            set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, "-")
          else
            value = Map.get(data, key, "-")
            set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, value)
          end

        # Handle any other case
        _ ->
          IO.inspect("Unhandled data key type: #{inspect(data_key)}")
          set_cell_with_debug(file_id, "Quarterly Publication Landscape", excel_cell, "-")
      end
    end)

    # Try using correct arity for setting ratio cells
    # try_set_ratio_cells(file_id)
  end

  # Try multiple approaches to set ratio cells based on error analysis
  defp try_set_ratio_cells(file_id) do
    try do
      # Using correct arity (5) for set_cell_value
      result1 = Excelizer.Cell.set_cell_value(file_id, "Quarterly Publication Landscape", "S47", @cell_types.string, "15.8%")
      IO.inspect(result1, label: "Ratio cell S47 result")

      result2 = Excelizer.Cell.set_cell_value(file_id, "Quarterly Publication Landscape", "S49", @cell_types.string, "45.9%")
      IO.inspect(result2, label: "Ratio cell S49 result")
    rescue
      e ->
        IO.inspect("Failed to set ratio cells: #{inspect(e)}")
        {:error, e}
    end
  end

  # New helper function to handle both integer and float string conversions
  defp get_number_value(value) when is_binary(value) do
    value
    |> String.replace(",", "")
    |> String.trim()
    |> parse_number()
  end
  defp get_number_value(_), do: 0.0

  defp gen_info(file_id, data) do
    try do
      # Get the date - it's already a Date struct, not a string
      date = data.end_date
      IO.inspect(date, label: "Date value")

      # Format the Date struct directly
      formatted_date = case date do
        %Date{} ->
          # If it's already a Date struct, format it directly
          Timex.format!(date, "%d %B %Y", :strftime) |> String.upcase()

        date_string when is_binary(date_string) ->
          # If it's actually a string, then try to parse it
          case Timex.parse(date_string, "{YYYY}-{0M}-{0D}") do
            {:ok, parsed_date} ->
              Timex.format!(parsed_date, "%d %B %Y", :strftime) |> String.upcase()
            _ ->
              "UNKNOWN DATE" # Fallback value
          end

        _ ->
          # For any other unexpected type
          IO.inspect(date, label: "Unexpected date format")
          "UNKNOWN DATE" # Fallback value
      end

      IO.inspect(formatted_date, label: "Formatted date")

      # Headers for each section
      headers = [
        {"D6", "QUARTERLY FINANCIAL STATEMENTS - #{formatted_date}"},
        {"D11", "INCOME STATEMENT - FOR QUARTER ENDED #{formatted_date}"},
        {"J11", "BALANCE SHEET AS AT #{formatted_date}"},
        {"N11", "STATEMENT OF CAPITAL POSITION AS AT #{formatted_date}"},
        {"R11", "STATEMENT OF LIQUIDITY POSITION AS AT #{formatted_date}"}
      ]

      # Set each header with proper error handling
      Enum.each(headers, fn {cell, text} ->
        case Excelizer.Cell.set_cell_value(file_id, "Quarterly Publication Landscape", cell, "string", text) do
          {:ok, _} -> :ok
          error -> IO.inspect("Error setting #{cell}: #{inspect(error)}")
        end
      end)

      IO.inspect("Successfully set headers")
      {:ok, "Headers updated successfully"}
    rescue
      e ->
        IO.inspect("Error in gen_info: #{inspect(e)}")
        IO.inspect(e, label: "Full error details")
        {:error, e}
    end
  end
end
