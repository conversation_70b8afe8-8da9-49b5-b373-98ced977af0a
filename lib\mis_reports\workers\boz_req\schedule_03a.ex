defmodule MisReports.Workers.BozReq.Schedule03a do
  alias MisReports.{Utilities}

  def perform(item) do

    decoded_item =
      case item.schedule_03a do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end
    decoded_item = format_scientific_values(decoded_item)
    # IO.inspect(decoded_item, label: "===============================")
    usd_rate =
    Utilities.get_exchange_rate_by_date_and_code(item.end_date, "USD")[:exchange_rate_lcy] || "1"

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()
    %{
      "returnKey" => "ZM-7LSCH3A7L002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
          %{
            "Code" => "1141_00001",
            "Value" => "#{decoded_item["B14"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00002",
            "Value" => "#{decoded_item["C14"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00003",
            "Value" => "#{decoded_item["D14"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00004",
            "Value" => "#{decoded_item["B15"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00005",
            "Value" => "#{decoded_item["D15"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00006",
            "Value" => "#{decoded_item["B16"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00007",
            "Value" => "#{decoded_item["D16"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00008",
            "Value" => "#{decoded_item["B17"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00009",
            "Value" => "#{decoded_item["C17"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00010",
            "Value" => "#{decoded_item["D17"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00011",
            "Value" => "#{decoded_item["B18"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00012",
            "Value" => "#{decoded_item["C18"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00013",
            "Value" => "#{decoded_item["D18"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00014",
            "Value" => "#{decoded_item["B19"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00015",
            "Value" => "#{decoded_item["D19"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00016",
            "Value" => "#{decoded_item["B20"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00017",
            "Value" => "#{decoded_item["C20"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00018",
            "Value" => "#{decoded_item["D20"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00019",
            "Value" => "#{decoded_item["B21"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00020",
            "Value" => "#{decoded_item["C21"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00021",
            "Value" => "#{decoded_item["D21"] || "0"}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00022",
            "Value" => "#{usd_rate}",
            "_dataType" => "NUMERIC"
          },
          %{
            "Code" => "1141_00023",
            "Value" => "#{decoded_item["D24"] || "0"}",
            "_dataType" => "NUMERIC"
          }
        ],
        "DynamicItemsList" => [
          %{
            "Area" => 438,
            "_areaName" => "CHANGES IN THE ALLOWANCE FOR LOAN LOSSES ACCOUNT",
            "DynamicItems" => [
              %{
                "Code" => "1.1",
                "Value" => "#{decoded_item["A26"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "1.2",
                "Value" => "#{decoded_item["B26"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "1.3",
                "Value" => "#{decoded_item["C26"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "1.4",
                "Value" => "#{decoded_item["D26"] || "0"}",
                "_dataType" => "NUMERIC"
              },
              %{
                "Code" => "1.5",
                "Value" => "#{decoded_item["E26"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "2.1",
                "Value" => "#{decoded_item["A27"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "2.2",
                "Value" => "#{decoded_item["B27"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "2.3",
                "Value" => "#{decoded_item["C27"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "2.4",
                "Value" => "#{decoded_item["D27"] || "0"}",
                "_dataType" => "NUMERIC"
              },
              %{
                "Code" => "2.5",
                "Value" => "#{decoded_item["E27"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "3.1",
                "Value" => "#{decoded_item["A28"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "3.2",
                "Value" => "#{decoded_item["B28"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "3.3",
                "Value" => "#{decoded_item["C28"] || "0"}",
                "_dataType" => "TEXT"
              },
              %{
                "Code" => "3.4",
                "Value" => "#{decoded_item["D28"] || "0"}",
                "_dataType" => "NUMERIC"
              },
              %{
                "Code" => "3.5",
                "Value" => "#{decoded_item["E28"] || "0"}",
                "_dataType" => "TEXT"
              }
            ]
          }
        ]

    }
  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end

  def format_scientific_values(map) do
    map
    |> get_in(["list"])
    |> Enum.map(fn {key, value} ->
      {key, convert_to_number(value)}
    end)
    |> Map.new()
  end

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    coef * :math.pow(10, exp) * sign
  end

  # Handle string values - return text as-is, convert numbers if possible
  defp convert_to_number(value) when is_binary(value) do
    case Integer.parse(value) do
      {number, ""} -> number * 1.0
      _ -> case Float.parse(value) do
        {number, ""} -> number
        _ -> value  # Return the original string if it's not a number
      end
    end
  end

  defp convert_to_number(_), do: 0.0
end
