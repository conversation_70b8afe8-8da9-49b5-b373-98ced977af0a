defmodule MisReportsWeb.BalanceSheetLive.Index do
  use MisReportsWeb, :live_view
  use MisReportsWeb, :custom_component

  on_mount MisReportsWeb.UserLiveAuth
  alias MisReportsWeb.LiveHelpers
  alias MisReportsWeb.IncomeStatementLive.Index, as: IS
  alias MisReports.{Utilities, Repo, Prudentials.PrudReport, Utilities.FileExport}
  alias MisReportsWeb.UserController

  @impl true
  def mount(_params, _session, socket) do
    assigns = [
      report_type: "",
      loader: false,
      save_btn: true,
      confirm_btn: false,
      accounts: "",
      download_btn: false,
      show_modal: false,
      income_statement_data: nil,
      balance_sheet_data: nil,
      # Set default tab
      active_tab: "balance_sheet"
    ]

    {:ok, assign(socket, assigns)}
  end

  @impl true
  def handle_event("show_modal", _params, socket) do
    {:noreply, assign(socket, :show_modal, true)}
  end

  @impl true
  def handle_event("hide_modal", _params, socket) do
    {:noreply, assign(socket, :show_modal, false)}
  end

  @impl true
  def handle_event("view_page", %{"value" => schedule_type}, socket) do
    data = socket.assigns.entries
    usd_rate = socket.assigns.usd_rate
    adjustments = socket.assigns.adjustments
    # saved_report = socket.assigns.saved_report
    filter_params =
      Map.merge(socket.assigns.filter_params, %{
        "end_start" => socket.assigns.filter_params["start_date"]
      })

    header = IS.gen_header(filter_params["start_date"])

    # new_prudential?(saved_report, schedule_type, data, filter_params, usd_rate)
    new_prudential?(nil, schedule_type, data, filter_params, usd_rate, adjustments)

    assigns = [
      report_type: schedule_type,
      loader: true,
      filter_params: filter_params,
      data: nil,
      header: header
    ]

    {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_event("table_sort", %{"sort_by" => sort_field, "sort_dir" => dir}, socket) do
    {:noreply, handle_table_sort(socket.assigns.live_action, sort_field, dir, socket)}
  end

  @impl true
  def handle_event("table_length", %{"page_size" => page_size}, socket) do
    case socket.assigns.live_action do
      :prudential_report_list ->
        {:noreply, assign(socket, page_size: page_size) |> prudential_report_list()}

      _ ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("table_search", %{"isearch" => isearch}, socket) do
    case socket.assigns.live_action do
      :prudential_report_list ->
        {:noreply, assign(socket, isearch: isearch) |> prudential_report_list()}

      _ ->
        {:noreply, socket}
    end
  end

  def handle_event("filter-report", %{"report" => params}, socket) do
    month = String.replace(params["start_date"], "-", "") |> String.slice(0..5)

    saved_report =
      MisReports.Prudentials.get_prudential_report(
        String.replace(params["start_date"], "-", "") |> String.slice(4..5),
        "MONTHLY",
        String.replace(params["start_date"], "-", "") |> String.slice(0..3)
      )

    entries = MisReports.SourceData.gbm_by_month(month)
    IO.inspect(month, label: "====================== month ======================")
    # entries = (if saved_report, do: [], else: MisReports.SourceData.gbm_by_month(month))
    # save_btn = (if saved_report, do: false, else: true)
    save_btn = false
    # prud_status = (if saved_report, do: saved_report.status, else: "NEW")
    prud_status = "NEW"

    usd_rate =
      Utilities.get_exchange_rate_by_date_and_code(params["start_date"], "USD")[
        :exchange_rate_lcy
      ] || "1"

    adjustments = Utilities.get_adjustments(params["start_date"])
    report_type = params["schedule"]
    settings = MisReports.Utilities.get_comapany_settings_params()

    socket =
      socket
      |> assign(page: %{prev: "Balance", current: "Sheet"})
      |> assign(entries: entries)
      |> assign(filter_params: params)
      |> assign(settings: settings)
      |> assign(prud_status: prud_status)
      |> assign(saved_report: saved_report)
      |> assign(usd_rate: usd_rate)
      |> assign(save_btn: save_btn)
      |> assign(adjustments: adjustments)

    handle_event("view_page", %{"value" => "#{report_type}"}, socket)
  end

  def handle_event("save-report", _params, socket) do
    send(self(), {:save_schedule})
    assigns = [loader: true]
    {:noreply, assign(socket, assigns)}
  end

  def handle_event("auth-report", _params, socket) do
    send(self(), {:auth_schedule})
    assigns = [loader: true]

    {:noreply, assign(socket, assigns)}
  end

  def handle_event("submit-report", %{"uuid" => uuid}, socket) do
    Task.start(fn -> MisReports.Workers.BsaReturns.send(uuid) end)
    message = %{message: %{info: "Prudential report submitted successfully."}}
    {:noreply, push_event(socket, "notification", message)}
  end

  @impl true
  def handle_info({_ref, {:load_schedule, result, filter_params, schedule_type}}, socket) do


        assigns = [
          report_type: schedule_type,
          loader: false,
          filter_params: filter_params,
          data: result,
          download_btn: false
        ]

        {:noreply, assign(socket, assigns)}
  end

  @impl true
  def handle_info({:save_schedule}, socket) do
    IO.inspect("====================== SAVING DATA =============================")
    report_date = socket.assigns.filter_params["start_date"]
    month = String.replace(report_date, "-", "") |> String.slice(4..5)
    year = String.replace(report_date, "-", "") |> String.slice(0..3)
    audit_msg = "Created New Prudential report for Month: #{month} and Year: #{year}"
    user = socket.assigns.current_user
    socket = assign(socket, loader: false)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(
      :report,
      FileExport.changeset(%FileExport{maker_id: user.id}, %{report_date: report_date})
    )
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{report: _report, audit_log: _user_log}} ->
        message = %{message: %{info: "Prudential report confirmation successful."}}

        {:noreply, push_event(socket, "notification", message)}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        message = %{message: %{info: "Prudential report confirmation successful."}}

        {:noreply, push_event(socket, "notification", message)}
        # error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        # {:noreply, push_event(socket, "notification", %{message: %{error: error_msg}})}
    end
  end

  def handle_info({:auth_schedule}, socket) do
    IO.inspect("====================== SAVING DATA =============================")
    filter_params = socket.assigns.filter_params
    month = String.replace(filter_params["start_date"], "-", "") |> String.slice(4..5)
    year = String.replace(filter_params["start_date"], "-", "") |> String.slice(0..3)
    socket = assign(socket, loader: false)
    user = socket.assigns.current_user
    saved_report = socket.assigns.saved_report

    status =
      if user.type == "FINANCE", do: "PENDING_CREDIT_APPROVAL", else: "PENDING_FINANCE_APPROVAL"

    audit_msg =
      "Updated Prudential report for Month: #{month} and Year: #{year} Status flag: #{status}"

    Ecto.Multi.new()
    |> Ecto.Multi.update(:update, PrudReport.changeset(saved_report, %{status: status}))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, _report} ->
        message = %{message: %{info: "Prudential report approved successfully."}}
        {:noreply, push_event(socket, "notification", message)}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        error_msg = UserController.traverse_errors(failed_value.errors) |> Enum.join("\r\n")
        {:noreply, push_event(socket, "notification", %{message: %{error: error_msg}})}
    end
  end

  @impl true
  def handle_info({:DOWN, _ref, :process, _pid, :normal}, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply,
     socket
     |> apply_action(socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, __params) do
    socket
    |> assign(page: %{prev: "Prudential", current: "Reports"})
    |> assign(data: [])
    |> assign(entries: [])
  end

  defp apply_action(socket, :prudential_report_list, _params) do
    assign(socket,
      page: 1,
      page_size: 10,
      isearch: nil,
      sort_by: {:asc, :id},
      length_menu: [10, 25, 50, 100, 300, 500, 1000]
    )
    |> prudential_report_list()
  end

  def page_name(:index), do: "Income Statement"
  defp new_prudential?(nil, schedule_type, data, filter_params, usd_rate, adjustments) do
    Task.async(fn ->
      result =
        case schedule_type do
          "schedule_03A" ->
            MisReports.Workers.LoansAdvances.Sh03a.generate_display(filter_params["end_date"]).list


          "schedule_4D" ->
            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

              end_date = filter_params["end_date"] |> Date.from_iso8601!()
            prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)


            shd13 =
              MisReports.Workers.Sh13.generate_display(
              income,
              prev_income,
              filter_params["end_date"]
            )

            balance_sheet =
              MisReports.Workers.BalanceSheet.generate_display(
                shd13,
                data,
                filter_params["start_date"],
                filter_params["end_date"],
                usd_rate,
                adjustments
              )

            MisReports.Workers.LoansAdvances.Sh4d.generate_display(
              balance_sheet,
              filter_params["end_date"],
              adjustments
            ).list

          "schedule_4B" ->
            MisReports.Workers.LoansAdvances.Sh4b.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_8A" ->
            MisReports.Workers.LoansAdvances.Sh8a.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_18A" ->
            MisReports.Workers.Sh18a.generate_display(filter_params["end_date"])

          "schedule_01F" ->
            MisReports.Workers.Sh01f.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_9A" ->
            MisReports.Workers.Sh09a.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_31C" ->
            MisReports.Workers.Sh31c.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )


          "schedule_21B" ->
            MisReports.Workers.Sh21b.generate_display(data, adjustments)

          "schedule_02C" ->
            MisReports.Workers.LoansAdvances.Sh02c.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_2H" ->
            MisReports.Workers.LoansAdvances.Sh02h.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_02D" ->
            MisReports.Workers.LoansAdvances.Sh02d.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            ).list

          "schedule_2A1" ->
            MisReports.Workers.LoansAdvances.Sh2a1.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_17B" ->
            MisReports.Workers.LoansAdvances.Sh17b.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_22A" ->
            MisReports.Workers.LoansAdvances.Sh22a.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_22B" ->
            MisReports.Workers.LoansAdvances.Sh22b.generate_display(filter_params["start_date"],filter_params["end_date"])

          "schedule_5B" ->
            MisReports.Workers.LoansAdvances.Sh5b.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_6B" ->
            MisReports.Workers.LoansAdvances.Sh6b.generate_display(filter_params["end_date"])

          "schedule_6A" ->
            MisReports.Workers.LoansAdvances.Sh6a.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_7A" ->
            MisReports.Workers.LoansAdvances.Sh7a.generate_display(
              filter_params["end_date"],
              adjustments
            )

          "schedule_02G" ->
            MisReports.Workers.Sh02g.generate_display(filter_params["end_date"], adjustments)

          "schedule_11D" ->
            MisReports.Workers.Sh11d.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_11J" ->
            MisReports.Workers.Sh11j.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_11G" ->
            MisReports.Workers.Sh11fAndsh11g.generate_display(filter_params["end_date"], "11G")

          "schedule_11F" ->
            MisReports.Workers.Sh11fAndsh11g.generate_display(filter_params["end_date"], "11F")

          "schedule_11E" ->
            MisReports.Workers.Sh11e.generate_display(filter_params["end_date"])

          "schedule_31F" ->
            MisReports.Workers.Sh31f.generate_display(filter_params["end_date"], adjustments)

          "schedule_01E" ->
            MisReports.Workers.Sh01e.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_01c" ->
            MisReports.Workers.Sh01c.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_25" ->
            MisReports.Workers.Sh25.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_21C" ->
            MisReports.Workers.Sh21c.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "blc_sheet" ->
            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

              end_date = filter_params["end_date"] |> Date.from_iso8601!()
            prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)


            shd13 =
              MisReports.Workers.Sh13.generate_display(
              income,
              prev_income,
              filter_params["end_date"]
            )


            MisReports.Workers.BalanceSheet.generate_display(
              shd13,
              data,
              filter_params["start_date"],
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_15"  ->
            end_date = filter_params["end_date"] |> Date.from_iso8601!
            prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")
            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)

            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

            shd13 = MisReports.Workers.Sh13.generate_display(income,prev_income, filter_params["end_date"])

            blc_sheet =
              MisReports.Workers.BalanceSheet.generate_display(
                shd13,
                data,
                filter_params["start_date"],
                filter_params["end_date"],
                usd_rate,
                adjustments
              )

            MisReports.Workers.Sh15.generate_display(
              shd13,
              blc_sheet,
              filter_params["end_date"],
              adjustments
            )

          "schedule_12" ->
            MisReports.Workers.Sh12.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_13" ->
            end_date = filter_params["end_date"] |> Date.from_iso8601!()
            prev_date = Timex.shift(end_date, months: -1) |> Timex.end_of_month()
            date_string = Timex.format!(prev_date, "{YYYY}-{0M}-{0D}")

            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

            prev_income = MisReports.Workers.IS.generate_display(data, date_string, adjustments)

            MisReports.Workers.Sh13.generate_display(
              income,
              prev_income,
              filter_params["end_date"]
            )

          "schedule_30D" ->
            MisReports.Workers.Sh30d.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_31D" ->
            MisReports.Workers.Sh31d.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_19" ->
            MisReports.Workers.Sh19.generate_display(filter_params["end_date"], adjustments)

          "schedule_26" ->
            MisReports.Workers.Sh26.generate_display(
              filter_params["start_date"],
              filter_params["end_date"],
              adjustments
            )

          "schedule_32A" ->
            MisReports.Workers.Sh32a.generate_display(
              filter_params["start_date"],
              filter_params["end_date"]
            )

          "schedule_23A" ->
            MisReports.Workers.Sh23a.generate_display(filter_params["end_date"], usd_rate)

          "schedule_23B" ->
            MisReports.Workers.Sh23b.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_17E" ->
            MisReports.Workers.Shd17e.generate_display(
              filter_params["end_date"],
              usd_rate,
              adjustments
            )

          "schedule_14" ->
            income =
              MisReports.Workers.IS.generate_display(
                data,
                filter_params["end_date"],
                adjustments
              )

            shd13 = MisReports.Workers.Sh13.generate_display(income, filter_params["end_date"])

            blc_sheet =
              MisReports.Workers.BalanceSheet.generate_display(
                shd13,
                data,
                filter_params["start_date"],
                filter_params["end_date"],
                usd_rate,
                adjustments
              )

            MisReports.Workers.Sh14.generate_display(
              blc_sheet,
              filter_params["end_date"],
              adjustments
            )

          _ ->
            nil
        end

      send(self(), {:load_schedule, result, filter_params, schedule_type})
    end)
  end

  defp prudential_report_list(%{assigns: params} = socket) do
    data =
      case connected?(socket) do
        true ->
          MisReports.Utilities.prudential_report_list(params)

        false ->
          LiveHelpers.empty_scrivener_page()
      end

    assign(socket, report_items: data.entries)
    |> assign(page_num: data.page_number)
    |> assign(report_type: "prudential_report_list")
    |> assign(total_pages: data.total_pages)
    |> assign(table_info: LiveHelpers.table_info(data))
    |> assign(table_pages: LiveHelpers.table_pagination(data))
  end

  def handle_table_sort(:prudential_report_list, sort_field, dir, socket) do
    sort_by =
      Enum.find_value(
        [:status, :month, :year, :inserted_at],
        fn field ->
          if String.to_existing_atom(sort_field) == field do
            {String.to_existing_atom(dir), field}
          end
        end
      )

    socket
    |> assign(sort_by: sort_by)
    |> prudential_report_list()
  end

  # Add this function to your BalanceSheetLive.Index module
  def render_balance_sheet_menu(assigns) do
    ~H"""
    <div class="mt-2 divide-y divide-gray-200 border-b border-t border-gray-200">
      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="blc_sheet">
        <div value="blc_sheet" class={"text-gray-500 #{if @report_type == "blc_sheet", do: "linkselector", else: nil}"}>
          Balance Sheet
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_01E">
        <div value="schedule_01E" class={"text-gray-500 #{if @report_type == "schedule_01E", do: "linkselector", else: nil}"}>
          Schedule 01E
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_01c">
        <div value="schedule_01c" class={"text-gray-500 #{if @report_type == "schedule_01c", do: "linkselector", else: nil}"}>
          Schedule 01C
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_25">
        <div value="schedule_25" class={"text-gray-500 #{if @report_type == "schedule_25", do: "linkselector", else: nil}"}>
          Schedule 25
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_21C">
        <div value="schedule_21C" class={"text-gray-500 #{if @report_type == "schedule_21C", do: "linkselector", else: nil}"}>
          Schedule 21C
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_15">
        <div value="schedule_15" class={"text-gray-500 #{if @report_type == "schedule_15", do: "linkselector", else: nil}"}>
          Schedule 15
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_12">
        <div value="schedule_12" class={"text-gray-500 #{if @report_type == "schedule_12", do: "linkselector", else: nil}"}>
          Schedule 12
        </div>
      </div>

      <div class="flex justify-between py-3 hover:bg-gray-200 text-sm font-medium card transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300" phx-click="view_page" phx-disable-with="Loading..." phx-value-value="schedule_13">
        <div value="schedule_13" class={"text-gray-500 #{if @report_type == "schedule_13", do: "linkselector", else: nil}"}>
          Schedule 13
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    # Update both active_tab and report_type
    socket =
      socket
      |> assign(:active_tab, tab)
      # This ensures the correct menu is shown
      |> assign(:report_type, tab)
      # Add this helper function
      |> clear_report_data()

    {:noreply, socket}
  end

  # Helper to clear report data when switching tabs
  defp clear_report_data(socket) do
    assign(socket, %{
      income_statement_data: nil,
      balance_sheet_data: nil
    })
  end
end
