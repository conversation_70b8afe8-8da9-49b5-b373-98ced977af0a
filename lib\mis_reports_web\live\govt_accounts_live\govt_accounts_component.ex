defmodule MisReportsWeb.GovtAccountsLive.GovtAccountsComponent do
  use MisReportsWeb, :live_component
  alias MisReports.{SourceData, Repo}
  alias MisReports.SourceData.GovtAccounts
  alias MisReportsWeb.UserController

  @impl true
  def render(assigns) do
    ~H"""
    <div>
      <%= case @action do %>
        <% action when action in [:new, :edit] -> %>
          <%= Phoenix.View.render(MisReportsWeb.GovtAccountsView, "govt_account.html", assigns) %>
        <% :update_status -> %>
          <%= Phoenix.View.render(MisReportsWeb.GovtAccountsView, "approve_new.html", assigns) %>
        <% _ -> %>
          <div class="text-red-600">Invalid action</div>
      <% end %>
    </div>
    """
  end

  @impl true
  def update(%{govt_accounts: govt_accounts} = assigns, socket) do
    changeset = SourceData.change_govt_accounts(govt_accounts)

    process_id = Map.get(assigns, :process_id)
    reference = Map.get(assigns, :reference)
    step_id = Map.get(assigns, :step_id)
    action = Map.get(assigns, :action, :new)

    {:ok,
     socket
     |> assign(assigns)
     |> assign(:process_id, process_id)
     |> assign(:reference, reference)
     |> assign(:step_id, step_id)
     |> assign(:action, action)
     |> assign(:changeset, %{changeset | errors: %{}})}
  end

  @impl true
  def handle_event("validate", %{"govt_accounts" => govt_accounts}, socket) do
    changeset =
      socket.assigns.govt_accounts
      |> GovtAccounts.changeset(govt_accounts)
      |> Map.put(:action, :validate)

    {:noreply, assign(socket, :changeset, changeset)}
  end

  def handle_event("save", %{"govt_accounts" => params}, socket) do
    handle_save(socket, socket.assigns.action, params)
  end

  @impl true
  def handle_event("save", %{"action" => action} = params, socket) do
    case action do
      "96" -> handle_approval(socket, :reject, params)
      "97" -> handle_approval(socket, :approve, params)
      _ ->
        {:noreply,
         socket
         |> put_flash(:error, "Invalid action")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  def handle_save(socket, :new, params) do
    audit_msg =  "Created new government account \"#{params["account_name"]}\" "
    user = socket.assigns.current_user
    current_user_id = to_string(user.id)

    Ecto.Multi.new()
    |> Ecto.Multi.insert(:govt_accounts, GovtAccounts.changeset(%GovtAccounts{maker_id: user.id}, params))
    |> UserController.audit_log(user.id, audit_msg)
    |> Repo.transaction()
    |> case do
        {:ok, %{govt_accounts: govt_accounts}} ->
          case MisReports.Workflow.call_workflow(
               socket.assigns.reference,
               socket.assigns.process_id,
               current_user_id,
               80,
               "",
               "",
               "Submission of goverment accounts Creation"
             ) do
          {:ok, reference_number} ->
            case SourceData.update_govt_accounts(govt_accounts, %{reference: reference_number}) do
              {:ok, updated_govt_accounts} ->
                {:noreply,
                 socket
                 |> put_flash(:info, "goverment accounts created successfully. Reference: #{reference_number}")
                 |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}

              {:error, changeset} ->
                {:noreply,
                 socket
                 |> put_flash(:error, "Failed to update goverment accounts reference")
                 |> assign(:changeset, changeset)}
            end

          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "goverment accounts created but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end

        {:error, %Ecto.Changeset{} = changeset} ->
          {:noreply, assign(socket, changeset: changeset)}
      end
  end

  def handle_save(socket, :edit, params) do
    govt_accounts = socket.assigns.govt_accounts
    socket
    |> handle_update(params, govt_accounts)
    |> case do
      {:ok, govt_accounts} ->
        {:noreply,
         socket
         |> put_flash(:info, "Government account updated successfully")
         |> push_redirect(to: Routes.govt_accounts_index_path(socket, :edit, govt_accounts))}

      {:error, %Ecto.Changeset{} = changeset} ->
        {:noreply, assign(socket, changeset: changeset)}
    end
  end

  def handle_update(socket, params, govt_accounts) do
    audit_msg = "Updated govt account  \"#{govt_accounts.account_name}\""

    Ecto.Multi.new()
    |> Ecto.Multi.update(:govt_accounts, GovtAccounts.changeset(govt_accounts, Map.merge(params, %{"checker" => nil})))
    |> UserController.audit_log(socket.assigns.current_user.id, audit_msg)
    |> Repo.transaction()
    |> case do
      {:ok, %{govt_accounts: govt_accounts, audit_log: _user_log}} ->
        {:ok, govt_accounts}

      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:error, failed_value}
    end
  end

  defp handle_approval(socket, :reject, params) do
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    current_user_id = to_string(socket.assigns.current_user.id)
    comment = "Government accountst Rejected"
    action_id = params["action"] || "96"

    case MisReports.Workflow.call_workflow(
           reference,
           process_id,
           current_user_id,
           action_id,
           "",
           "",
           comment
         ) do
      {:ok, reference_number} ->
        {:noreply,
         socket
         |> put_flash(:info, "Government accounts rejected successfully. Reference: #{reference_number}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :update_status, %{reference: reference_number}))}
      {:error, reason} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to reject government accounts: #{reason}")
         |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
    end
  end

  defp handle_approval(socket, :approve, params) do
    govt_accounts = socket.assigns.govt_accounts
    current_user = socket.assigns.current_user
    reference = socket.assigns.reference
    process_id = socket.assigns.process_id
    action_id = params["action"] || "97"
    comment = "Government accounts Approval"

    Ecto.Multi.new()
    |> Ecto.Multi.update(
      :update,
      GovtAccounts.changeset(govt_accounts, %{
        status: "A",
        checker_id: current_user.id,
      })
    )
    |> Repo.transaction()
    |> case do
      {:ok, %{update: updated_govt_accounts}} ->
        case MisReports.Workflow.call_workflow(
               reference,
               process_id,
               to_string(current_user.id),
               action_id,
               "",
               "",
               comment
             ) do
          {:ok, reference_number} ->
            {:noreply,
             socket
             |> put_flash(:info, "Government accounts approved successfully. Reference: #{reference_number}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index, %{reference: reference_number}))}
          {:error, reason} ->
            {:noreply,
             socket
             |> put_flash(:error, "Government accounts approved but workflow failed: #{reason}")
             |> push_redirect(to: Routes.wkl_pending_tasks_index_path(socket, :index))}
        end
      {:error, _failed_operation, failed_value, _changes_so_far} ->
        {:noreply,
         socket
         |> put_flash(:error, "Failed to approve Government accounts")
         |> assign(:changeset, %{govt_accounts.changeset | errors: failed_value.errors})}
    end
  end

end
