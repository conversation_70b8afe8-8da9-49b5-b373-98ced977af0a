defmodule MisReports.Workers.BozReq.Schedule30b do

  def perform(item) do

    decoded_item =
      case item.schedule_30b do
        nil -> %{}  # Handle nil case
        content ->
          case Poison.decode(content) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}  # Handle error gracefully
          end
      end
    decoded_item = format_map(decoded_item)
    # IO.inspect(decoded_item, label: "===============================")

    #decode item
    settings = MisReports.Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-2LSCH30B2L002",
      "instCode" => "#{settings.institution_code}",
      "finYear" => item.end_date |> Timex.format!("%Y", :strftime) |> String.to_integer(),
      "startDate" => "#{Timex.beginning_of_month(item.end_date) |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "endDate" => "#{item.end_date |> Timex.format!("%Y-%m-%dT%H:%M:%SZ", :strftime)}",
      "returnItemsList" => [
          %{ "Code" => "1182_00892", "Value" => "#{decoded_item["C125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00893", "Value" => "#{decoded_item["D125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00894", "Value" => "#{decoded_item["E125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00895", "Value" => "#{decoded_item["F125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00896", "Value" => "#{decoded_item["G125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00897", "Value" => "#{decoded_item["H125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00898", "Value" => "#{decoded_item["I125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00899", "Value" => "#{decoded_item["J125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00900", "Value" => "#{decoded_item["K125"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00901", "Value" => "#{decoded_item["B126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00902", "Value" => "#{decoded_item["C126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00903", "Value" => "#{decoded_item["D126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00904", "Value" => "#{decoded_item["E126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00905", "Value" => "#{decoded_item["F126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00906", "Value" => "#{decoded_item["G126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00907", "Value" => "#{decoded_item["H126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00908", "Value" => "#{decoded_item["I126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00909", "Value" => "#{decoded_item["J126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00910", "Value" => "#{decoded_item["K126"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00911", "Value" => "#{decoded_item["B127"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00912", "Value" => "#{decoded_item["C127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00913", "Value" => "#{decoded_item["D127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00914", "Value" => "#{decoded_item["E127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00915", "Value" => "#{decoded_item["F127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00916", "Value" => "#{decoded_item["G127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00917", "Value" => "#{decoded_item["H127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00918", "Value" => "#{decoded_item["I127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00919", "Value" => "#{decoded_item["J127"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00920", "Value" => "#{decoded_item["K127"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00921", "Value" => "#{decoded_item["B128"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00922", "Value" => "#{decoded_item["C128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00001", "Value" => "#{decoded_item["B15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00002", "Value" => "#{decoded_item["C15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00003", "Value" => "#{decoded_item["D15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00004", "Value" => "#{decoded_item["E15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00005", "Value" => "#{decoded_item["F15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00006", "Value" => "#{decoded_item["G15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00007", "Value" => "#{decoded_item["H15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00008", "Value" => "#{decoded_item["I15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00009", "Value" => "#{decoded_item["J15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00010", "Value" => "#{decoded_item["K15"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00011", "Value" => "#{decoded_item["B16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00012", "Value" => "#{decoded_item["C16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00013", "Value" => "#{decoded_item["D16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00014", "Value" => "#{decoded_item["E16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00015", "Value" => "#{decoded_item["F16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00016", "Value" => "#{decoded_item["G16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00017", "Value" => "#{decoded_item["H16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00018", "Value" => "#{decoded_item["I16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00019", "Value" => "#{decoded_item["J16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00020", "Value" => "#{decoded_item["K16"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00021", "Value" => "#{decoded_item["B17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00022", "Value" => "#{decoded_item["C17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00023", "Value" => "#{decoded_item["D17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00024", "Value" => "#{decoded_item["E17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00025", "Value" => "#{decoded_item["F17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00026", "Value" => "#{decoded_item["G17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00027", "Value" => "#{decoded_item["H17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00028", "Value" => "#{decoded_item["I17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00029", "Value" => "#{decoded_item["J17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00030", "Value" => "#{decoded_item["K17"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00031", "Value" => "#{decoded_item["B18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00923", "Value" => "#{decoded_item["D128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00924", "Value" => "#{decoded_item["E128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00925", "Value" => "#{decoded_item["F128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00926", "Value" => "#{decoded_item["G128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00927", "Value" => "#{decoded_item["H128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00928", "Value" => "#{decoded_item["I128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00929", "Value" => "#{decoded_item["J128"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00930", "Value" => "#{decoded_item["K128"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00931", "Value" => "#{decoded_item["B131"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00932", "Value" => "#{decoded_item["C131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00933", "Value" => "#{decoded_item["D131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00934", "Value" => "#{decoded_item["E131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00935", "Value" => "#{decoded_item["F131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00936", "Value" => "#{decoded_item["G131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00937", "Value" => "#{decoded_item["H131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00938", "Value" => "#{decoded_item["I131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00939", "Value" => "#{decoded_item["J131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00940", "Value" => "#{decoded_item["K131"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00941", "Value" => "#{decoded_item["B132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00942", "Value" => "#{decoded_item["C132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00943", "Value" => "#{decoded_item["D132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00944", "Value" => "#{decoded_item["E132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00945", "Value" => "#{decoded_item["F132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00946", "Value" => "#{decoded_item["G132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00947", "Value" => "#{decoded_item["H132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00948", "Value" => "#{decoded_item["I132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00949", "Value" => "#{decoded_item["J132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00950", "Value" => "#{decoded_item["K132"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00951", "Value" => "#{decoded_item["B133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00952", "Value" => "#{decoded_item["C133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00953", "Value" => "#{decoded_item["D133"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00032", "Value" => "#{decoded_item["C18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00033", "Value" => "#{decoded_item["D18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00034", "Value" => "#{decoded_item["E18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00035", "Value" => "#{decoded_item["F18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00036", "Value" => "#{decoded_item["G18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00037", "Value" => "#{decoded_item["H18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00038", "Value" => "#{decoded_item["I18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00039", "Value" => "#{decoded_item["J18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00040", "Value" => "#{decoded_item["K18"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00041", "Value" => "#{decoded_item["B19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00042", "Value" => "#{decoded_item["C19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00043", "Value" => "#{decoded_item["D19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00044", "Value" => "#{decoded_item["E19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00045", "Value" => "#{decoded_item["F19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00046", "Value" => "#{decoded_item["G19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00047", "Value" => "#{decoded_item["H19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00048", "Value" => "#{decoded_item["I19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00049", "Value" => "#{decoded_item["J19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00050", "Value" => "#{decoded_item["K19"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00051", "Value" => "#{decoded_item["B20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00052", "Value" => "#{decoded_item["C20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00053", "Value" => "#{decoded_item["D20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00054", "Value" => "#{decoded_item["E20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00055", "Value" => "#{decoded_item["F20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00056", "Value" => "#{decoded_item["G20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00057", "Value" => "#{decoded_item["H20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00058", "Value" => "#{decoded_item["I20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00059", "Value" => "#{decoded_item["J20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00060", "Value" => "#{decoded_item["K20"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00061", "Value" => "#{decoded_item["B21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00954", "Value" => "#{decoded_item["E133"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00955", "Value" => "#{decoded_item["F133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00956", "Value" => "#{decoded_item["G133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00957", "Value" => "#{decoded_item["H133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00958", "Value" => "#{decoded_item["I133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00959", "Value" => "#{decoded_item["J133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00960", "Value" => "#{decoded_item["K133"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00961", "Value" => "#{decoded_item["B134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00962", "Value" => "#{decoded_item["C134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00963", "Value" => "#{decoded_item["D134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00964", "Value" => "#{decoded_item["E134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00965", "Value" => "#{decoded_item["F134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00966", "Value" => "#{decoded_item["G134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00967", "Value" => "#{decoded_item["H134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00968", "Value" => "#{decoded_item["I134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00969", "Value" => "#{decoded_item["J134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00970", "Value" => "#{decoded_item["K134"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00971", "Value" => "#{decoded_item["B135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00972", "Value" => "#{decoded_item["C135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00973", "Value" => "#{decoded_item["D135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00974", "Value" => "#{decoded_item["E135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00975", "Value" => "#{decoded_item["F135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00976", "Value" => "#{decoded_item["G135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00977", "Value" => "#{decoded_item["H135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00978", "Value" => "#{decoded_item["I135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00979", "Value" => "#{decoded_item["J135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00980", "Value" => "#{decoded_item["K135"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00981", "Value" => "#{decoded_item["B136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00982", "Value" => "#{decoded_item["C136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00983", "Value" => "#{decoded_item["D136"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00062", "Value" => "#{decoded_item["C21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00063", "Value" => "#{decoded_item["D21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00064", "Value" => "#{decoded_item["E21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00065", "Value" => "#{decoded_item["F21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00066", "Value" => "#{decoded_item["G21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00067", "Value" => "#{decoded_item["H21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00068", "Value" => "#{decoded_item["I21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00069", "Value" => "#{decoded_item["J21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00070", "Value" => "#{decoded_item["K21"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00071", "Value" => "#{decoded_item["B22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00072", "Value" => "#{decoded_item["C22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00073", "Value" => "#{decoded_item["D22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00074", "Value" => "#{decoded_item["E22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00075", "Value" => "#{decoded_item["F22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00076", "Value" => "#{decoded_item["G22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00077", "Value" => "#{decoded_item["H22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00078", "Value" => "#{decoded_item["I22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00079", "Value" => "#{decoded_item["J22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00080", "Value" => "#{decoded_item["K22"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00081", "Value" => "#{decoded_item["B23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00082", "Value" => "#{decoded_item["C23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00083", "Value" => "#{decoded_item["D23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00084", "Value" => "#{decoded_item["E23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00085", "Value" => "#{decoded_item["F23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00086", "Value" => "#{decoded_item["G23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00087", "Value" => "#{decoded_item["H23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00088", "Value" => "#{decoded_item["I23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00089", "Value" => "#{decoded_item["J23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00090", "Value" => "#{decoded_item["K23"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00091", "Value" => "#{decoded_item["B24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00092", "Value" => "#{decoded_item["C24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00093", "Value" => "#{decoded_item["D24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00094", "Value" => "#{decoded_item["E24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00095", "Value" => "#{decoded_item["F24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00096", "Value" => "#{decoded_item["G24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00097", "Value" => "#{decoded_item["H24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00098", "Value" => "#{decoded_item["I24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00099", "Value" => "#{decoded_item["J24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00100", "Value" => "#{decoded_item["K24"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00101", "Value" => "#{decoded_item["B25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00102", "Value" => "#{decoded_item["C25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00103", "Value" => "#{decoded_item["D25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00104", "Value" => "#{decoded_item["E25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00105", "Value" => "#{decoded_item["F25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00106", "Value" => "#{decoded_item["G25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00107", "Value" => "#{decoded_item["H25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00108", "Value" => "#{decoded_item["I25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00109", "Value" => "#{decoded_item["J25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00110", "Value" => "#{decoded_item["K25"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00111", "Value" => "#{decoded_item["B26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00112", "Value" => "#{decoded_item["C26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00113", "Value" => "#{decoded_item["D26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00114", "Value" => "#{decoded_item["E26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00115", "Value" => "#{decoded_item["F26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00116", "Value" => "#{decoded_item["G26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00117", "Value" => "#{decoded_item["H26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00118", "Value" => "#{decoded_item["I26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00119", "Value" => "#{decoded_item["J26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00120", "Value" => "#{decoded_item["K26"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00121", "Value" => "#{decoded_item["B28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00122", "Value" => "#{decoded_item["C28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00123", "Value" => "#{decoded_item["D28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00124", "Value" => "#{decoded_item["E28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00125", "Value" => "#{decoded_item["F28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00126", "Value" => "#{decoded_item["G28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00127", "Value" => "#{decoded_item["H28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00128", "Value" => "#{decoded_item["I28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00129", "Value" => "#{decoded_item["J28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00130", "Value" => "#{decoded_item["K28"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00131", "Value" => "#{decoded_item["B31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00132", "Value" => "#{decoded_item["C31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00133", "Value" => "#{decoded_item["D31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00134", "Value" => "#{decoded_item["E31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00135", "Value" => "#{decoded_item["F31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00136", "Value" => "#{decoded_item["G31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00137", "Value" => "#{decoded_item["H31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00138", "Value" => "#{decoded_item["I31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00139", "Value" => "#{decoded_item["J31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00140", "Value" => "#{decoded_item["K31"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00141", "Value" => "#{decoded_item["B32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00142", "Value" => "#{decoded_item["C32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00143", "Value" => "#{decoded_item["D32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00144", "Value" => "#{decoded_item["E32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00145", "Value" => "#{decoded_item["F32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00146", "Value" => "#{decoded_item["G32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00147", "Value" => "#{decoded_item["H32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00148", "Value" => "#{decoded_item["I32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00149", "Value" => "#{decoded_item["J32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00150", "Value" => "#{decoded_item["K32"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00151", "Value" => "#{decoded_item["B33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00152", "Value" => "#{decoded_item["C33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00153", "Value" => "#{decoded_item["D33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00154", "Value" => "#{decoded_item["E33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00155", "Value" => "#{decoded_item["F33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00156", "Value" => "#{decoded_item["G33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00157", "Value" => "#{decoded_item["H33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00158", "Value" => "#{decoded_item["I33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00159", "Value" => "#{decoded_item["J33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00160", "Value" => "#{decoded_item["K33"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00161", "Value" => "#{decoded_item["B34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00162", "Value" => "#{decoded_item["C34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00163", "Value" => "#{decoded_item["D34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00164", "Value" => "#{decoded_item["E34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00165", "Value" => "#{decoded_item["F34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00166", "Value" => "#{decoded_item["G34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00167", "Value" => "#{decoded_item["H34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00168", "Value" => "#{decoded_item["I34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00169", "Value" => "#{decoded_item["J34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00170", "Value" => "#{decoded_item["K34"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00171", "Value" => "#{decoded_item["B35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00172", "Value" => "#{decoded_item["C35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00173", "Value" => "#{decoded_item["D35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00174", "Value" => "#{decoded_item["E35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00175", "Value" => "#{decoded_item["F35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00176", "Value" => "#{decoded_item["G35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00177", "Value" => "#{decoded_item["H35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00178", "Value" => "#{decoded_item["I35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00179", "Value" => "#{decoded_item["J35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00180", "Value" => "#{decoded_item["K35"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00181", "Value" => "#{decoded_item["B36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00182", "Value" => "#{decoded_item["C36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00183", "Value" => "#{decoded_item["D36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00184", "Value" => "#{decoded_item["E36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00185", "Value" => "#{decoded_item["F36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00186", "Value" => "#{decoded_item["G36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00187", "Value" => "#{decoded_item["H36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00188", "Value" => "#{decoded_item["I36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00189", "Value" => "#{decoded_item["J36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00190", "Value" => "#{decoded_item["K36"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00191", "Value" => "#{decoded_item["B37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00192", "Value" => "#{decoded_item["C37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00193", "Value" => "#{decoded_item["D37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00194", "Value" => "#{decoded_item["E37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00195", "Value" => "#{decoded_item["F37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00196", "Value" => "#{decoded_item["G37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00197", "Value" => "#{decoded_item["H37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00198", "Value" => "#{decoded_item["I37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00199", "Value" => "#{decoded_item["J37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00200", "Value" => "#{decoded_item["K37"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00201", "Value" => "#{decoded_item["B38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00202", "Value" => "#{decoded_item["C38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00203", "Value" => "#{decoded_item["D38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00204", "Value" => "#{decoded_item["E38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00205", "Value" => "#{decoded_item["F38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00206", "Value" => "#{decoded_item["G38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00207", "Value" => "#{decoded_item["H38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00208", "Value" => "#{decoded_item["I38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00209", "Value" => "#{decoded_item["J38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00210", "Value" => "#{decoded_item["K38"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00211", "Value" => "#{decoded_item["B39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00212", "Value" => "#{decoded_item["C39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00213", "Value" => "#{decoded_item["D39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00214", "Value" => "#{decoded_item["E39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00215", "Value" => "#{decoded_item["F39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00216", "Value" => "#{decoded_item["G39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00217", "Value" => "#{decoded_item["H39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00218", "Value" => "#{decoded_item["I39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00219", "Value" => "#{decoded_item["J39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00220", "Value" => "#{decoded_item["K39"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00221", "Value" => "#{decoded_item["B40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00222", "Value" => "#{decoded_item["C40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00223", "Value" => "#{decoded_item["D40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00224", "Value" => "#{decoded_item["E40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00225", "Value" => "#{decoded_item["F40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00226", "Value" => "#{decoded_item["G40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00227", "Value" => "#{decoded_item["H40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00228", "Value" => "#{decoded_item["I40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00229", "Value" => "#{decoded_item["J40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00230", "Value" => "#{decoded_item["K40"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00231", "Value" => "#{decoded_item["B41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00232", "Value" => "#{decoded_item["C41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00233", "Value" => "#{decoded_item["D41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00234", "Value" => "#{decoded_item["E41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00235", "Value" => "#{decoded_item["F41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00236", "Value" => "#{decoded_item["G41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00237", "Value" => "#{decoded_item["H41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00238", "Value" => "#{decoded_item["I41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00239", "Value" => "#{decoded_item["J41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00240", "Value" => "#{decoded_item["K41"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00241", "Value" => "#{decoded_item["B42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00242", "Value" => "#{decoded_item["C42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00243", "Value" => "#{decoded_item["D42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00244", "Value" => "#{decoded_item["E42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00245", "Value" => "#{decoded_item["F42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00246", "Value" => "#{decoded_item["G42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00247", "Value" => "#{decoded_item["H42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00248", "Value" => "#{decoded_item["I42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00249", "Value" => "#{decoded_item["J42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00250", "Value" => "#{decoded_item["K42"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00251", "Value" => "#{decoded_item["B44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00252", "Value" => "#{decoded_item["C44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00253", "Value" => "#{decoded_item["D44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00254", "Value" => "#{decoded_item["E44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00255", "Value" => "#{decoded_item["F44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00256", "Value" => "#{decoded_item["G44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00257", "Value" => "#{decoded_item["H44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00258", "Value" => "#{decoded_item["I44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00259", "Value" => "#{decoded_item["J44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00260", "Value" => "#{decoded_item["K44"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00261", "Value" => "#{decoded_item["B46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00262", "Value" => "#{decoded_item["C46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00263", "Value" => "#{decoded_item["D46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00264", "Value" => "#{decoded_item["E46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00265", "Value" => "#{decoded_item["F46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00266", "Value" => "#{decoded_item["G46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00267", "Value" => "#{decoded_item["H46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00268", "Value" => "#{decoded_item["I46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00269", "Value" => "#{decoded_item["J46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00270", "Value" => "#{decoded_item["K46"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00271", "Value" => "#{decoded_item["B47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00272", "Value" => "#{decoded_item["C47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00273", "Value" => "#{decoded_item["D47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00274", "Value" => "#{decoded_item["E47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00275", "Value" => "#{decoded_item["F47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00276", "Value" => "#{decoded_item["G47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00277", "Value" => "#{decoded_item["H47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00278", "Value" => "#{decoded_item["I47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00279", "Value" => "#{decoded_item["J47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00280", "Value" => "#{decoded_item["K47"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00281", "Value" => "#{decoded_item["B50"] || "0"}", "_dataType" => "NUMERIC" }, # no items on template
          %{ "Code" => "1182_00282", "Value" => "#{decoded_item["C50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00283", "Value" => "#{decoded_item["D50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00284", "Value" => "#{decoded_item["E50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00285", "Value" => "#{decoded_item["F50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00286", "Value" => "#{decoded_item["G50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00287", "Value" => "#{decoded_item["H50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00288", "Value" => "#{decoded_item["I50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00289", "Value" => "#{decoded_item["J50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00290", "Value" => "#{decoded_item["K50"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00291", "Value" => "#{decoded_item["B51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00292", "Value" => "#{decoded_item["C51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00293", "Value" => "#{decoded_item["D51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00294", "Value" => "#{decoded_item["E51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00295", "Value" => "#{decoded_item["F51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00296", "Value" => "#{decoded_item["G51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00297", "Value" => "#{decoded_item["H51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00298", "Value" => "#{decoded_item["I51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00299", "Value" => "#{decoded_item["J51"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00300", "Value" => "#{decoded_item["K51"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00301", "Value" => "#{decoded_item["B54"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00302", "Value" => "#{decoded_item["C54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00303", "Value" => "#{decoded_item["D54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00304", "Value" => "#{decoded_item["E54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00305", "Value" => "#{decoded_item["F54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00306", "Value" => "#{decoded_item["G54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00307", "Value" => "#{decoded_item["H54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00308", "Value" => "#{decoded_item["I54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00309", "Value" => "#{decoded_item["J54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00310", "Value" => "#{decoded_item["K54"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00311", "Value" => "#{decoded_item["B55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00312", "Value" => "#{decoded_item["C55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00313", "Value" => "#{decoded_item["D55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00314", "Value" => "#{decoded_item["E55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00315", "Value" => "#{decoded_item["F55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00316", "Value" => "#{decoded_item["G55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00317", "Value" => "#{decoded_item["H55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00318", "Value" => "#{decoded_item["I55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00319", "Value" => "#{decoded_item["J55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00320", "Value" => "#{decoded_item["K55"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00321", "Value" => "#{decoded_item["B56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00322", "Value" => "#{decoded_item["C56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00323", "Value" => "#{decoded_item["D56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00324", "Value" => "#{decoded_item["E56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00325", "Value" => "#{decoded_item["F56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00326", "Value" => "#{decoded_item["G56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00327", "Value" => "#{decoded_item["H56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00328", "Value" => "#{decoded_item["I56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00329", "Value" => "#{decoded_item["J56"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00330", "Value" => "#{decoded_item["K56"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00331", "Value" => "#{decoded_item["B62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00332", "Value" => "#{decoded_item["C62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00333", "Value" => "#{decoded_item["D62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00334", "Value" => "#{decoded_item["E62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00335", "Value" => "#{decoded_item["F62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00336", "Value" => "#{decoded_item["G62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00337", "Value" => "#{decoded_item["H62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00338", "Value" => "#{decoded_item["I62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00339", "Value" => "#{decoded_item["J62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00340", "Value" => "#{decoded_item["K62"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00341", "Value" => "#{decoded_item["B63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00342", "Value" => "#{decoded_item["C63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00343", "Value" => "#{decoded_item["D63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00344", "Value" => "#{decoded_item["E63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00345", "Value" => "#{decoded_item["F63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00346", "Value" => "#{decoded_item["G63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00347", "Value" => "#{decoded_item["H63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00348", "Value" => "#{decoded_item["I63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00349", "Value" => "#{decoded_item["J63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00350", "Value" => "#{decoded_item["K63"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00351", "Value" => "#{decoded_item["B64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00352", "Value" => "#{decoded_item["C64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00353", "Value" => "#{decoded_item["D64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00354", "Value" => "#{decoded_item["E64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00355", "Value" => "#{decoded_item["F64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00356", "Value" => "#{decoded_item["G64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00357", "Value" => "#{decoded_item["H64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00358", "Value" => "#{decoded_item["I64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00359", "Value" => "#{decoded_item["J64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00360", "Value" => "#{decoded_item["K64"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00361", "Value" => "#{decoded_item["B65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00362", "Value" => "#{decoded_item["C65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00363", "Value" => "#{decoded_item["D65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00364", "Value" => "#{decoded_item["E65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00365", "Value" => "#{decoded_item["F65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00366", "Value" => "#{decoded_item["G65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00367", "Value" => "#{decoded_item["H65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00368", "Value" => "#{decoded_item["I65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00369", "Value" => "#{decoded_item["J65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00370", "Value" => "#{decoded_item["K65"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00371", "Value" => "#{decoded_item["B66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00372", "Value" => "#{decoded_item["C66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00373", "Value" => "#{decoded_item["D66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00374", "Value" => "#{decoded_item["E66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00375", "Value" => "#{decoded_item["F66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00376", "Value" => "#{decoded_item["G66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00377", "Value" => "#{decoded_item["H66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00378", "Value" => "#{decoded_item["I66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00379", "Value" => "#{decoded_item["J66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00380", "Value" => "#{decoded_item["K66"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00381", "Value" => "#{decoded_item["B67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00382", "Value" => "#{decoded_item["C67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00383", "Value" => "#{decoded_item["D67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00384", "Value" => "#{decoded_item["E67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00385", "Value" => "#{decoded_item["F67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00386", "Value" => "#{decoded_item["G67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00387", "Value" => "#{decoded_item["H67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00388", "Value" => "#{decoded_item["I67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00389", "Value" => "#{decoded_item["J67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00390", "Value" => "#{decoded_item["K67"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00391", "Value" => "#{decoded_item["B68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00392", "Value" => "#{decoded_item["C68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00393", "Value" => "#{decoded_item["D68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00394", "Value" => "#{decoded_item["E68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00395", "Value" => "#{decoded_item["F68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00396", "Value" => "#{decoded_item["G68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00397", "Value" => "#{decoded_item["H68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00398", "Value" => "#{decoded_item["I68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00399", "Value" => "#{decoded_item["J68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00400", "Value" => "#{decoded_item["K68"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00401", "Value" => "#{decoded_item["B69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00402", "Value" => "#{decoded_item["C69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00403", "Value" => "#{decoded_item["D69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00404", "Value" => "#{decoded_item["E69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00405", "Value" => "#{decoded_item["F69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00406", "Value" => "#{decoded_item["G69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00407", "Value" => "#{decoded_item["H69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00408", "Value" => "#{decoded_item["I69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00409", "Value" => "#{decoded_item["J69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00410", "Value" => "#{decoded_item["K69"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00411", "Value" => "#{decoded_item["B70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00412", "Value" => "#{decoded_item["C70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00413", "Value" => "#{decoded_item["D70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00414", "Value" => "#{decoded_item["E70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00415", "Value" => "#{decoded_item["F70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00416", "Value" => "#{decoded_item["G70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00417", "Value" => "#{decoded_item["H70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00418", "Value" => "#{decoded_item["I70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00419", "Value" => "#{decoded_item["J70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00420", "Value" => "#{decoded_item["K70"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00421", "Value" => "#{decoded_item["B71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00422", "Value" => "#{decoded_item["C71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00423", "Value" => "#{decoded_item["D71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00424", "Value" => "#{decoded_item["E71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00425", "Value" => "#{decoded_item["F71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00426", "Value" => "#{decoded_item["G71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00427", "Value" => "#{decoded_item["H71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00428", "Value" => "#{decoded_item["I71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00429", "Value" => "#{decoded_item["J71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00430", "Value" => "#{decoded_item["K71"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00431", "Value" => "#{decoded_item["B72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00432", "Value" => "#{decoded_item["C72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00433", "Value" => "#{decoded_item["D72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00434", "Value" => "#{decoded_item["E72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00435", "Value" => "#{decoded_item["F72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00436", "Value" => "#{decoded_item["G72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00437", "Value" => "#{decoded_item["H72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00438", "Value" => "#{decoded_item["I72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00439", "Value" => "#{decoded_item["J72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00440", "Value" => "#{decoded_item["K72"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00441", "Value" => "#{decoded_item["B73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00442", "Value" => "#{decoded_item["C73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00443", "Value" => "#{decoded_item["D73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00444", "Value" => "#{decoded_item["E73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00445", "Value" => "#{decoded_item["F73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00446", "Value" => "#{decoded_item["G73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00447", "Value" => "#{decoded_item["H73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00448", "Value" => "#{decoded_item["I73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00449", "Value" => "#{decoded_item["J73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00450", "Value" => "#{decoded_item["K73"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00451", "Value" => "#{decoded_item["B74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00452", "Value" => "#{decoded_item["C74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00453", "Value" => "#{decoded_item["D74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00454", "Value" => "#{decoded_item["E74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00455", "Value" => "#{decoded_item["F74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00456", "Value" => "#{decoded_item["G74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00457", "Value" => "#{decoded_item["H74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00458", "Value" => "#{decoded_item["I74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00459", "Value" => "#{decoded_item["J74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00460", "Value" => "#{decoded_item["K74"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00461", "Value" => "#{decoded_item["B75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00462", "Value" => "#{decoded_item["C75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00463", "Value" => "#{decoded_item["D75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00464", "Value" => "#{decoded_item["E75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00465", "Value" => "#{decoded_item["F75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00466", "Value" => "#{decoded_item["G75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00467", "Value" => "#{decoded_item["H75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00468", "Value" => "#{decoded_item["I75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00469", "Value" => "#{decoded_item["J75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00470", "Value" => "#{decoded_item["K75"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00471", "Value" => "#{decoded_item["B76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00472", "Value" => "#{decoded_item["C76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00473", "Value" => "#{decoded_item["D76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00474", "Value" => "#{decoded_item["E76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00475", "Value" => "#{decoded_item["F76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00476", "Value" => "#{decoded_item["G76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00477", "Value" => "#{decoded_item["H76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00478", "Value" => "#{decoded_item["I76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00479", "Value" => "#{decoded_item["J76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00480", "Value" => "#{decoded_item["K76"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00481", "Value" => "#{decoded_item["B77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00482", "Value" => "#{decoded_item["C77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00483", "Value" => "#{decoded_item["D77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00484", "Value" => "#{decoded_item["E77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00485", "Value" => "#{decoded_item["F77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00486", "Value" => "#{decoded_item["G77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00984", "Value" => "#{decoded_item["E136"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00985", "Value" => "#{decoded_item["F136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00986", "Value" => "#{decoded_item["G136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00987", "Value" => "#{decoded_item["H136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00988", "Value" => "#{decoded_item["I136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00989", "Value" => "#{decoded_item["J136"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00990", "Value" => "#{decoded_item["K136"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00487", "Value" => "#{decoded_item["H77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00488", "Value" => "#{decoded_item["I77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00489", "Value" => "#{decoded_item["J77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00490", "Value" => "#{decoded_item["K77"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00491", "Value" => "#{decoded_item["B78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00492", "Value" => "#{decoded_item["C78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00493", "Value" => "#{decoded_item["D78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00494", "Value" => "#{decoded_item["E78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00495", "Value" => "#{decoded_item["F78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00496", "Value" => "#{decoded_item["G78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00497", "Value" => "#{decoded_item["H78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00498", "Value" => "#{decoded_item["I78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00499", "Value" => "#{decoded_item["J78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00500", "Value" => "#{decoded_item["K78"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00501", "Value" => "#{decoded_item["B79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00502", "Value" => "#{decoded_item["C79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00503", "Value" => "#{decoded_item["D79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00504", "Value" => "#{decoded_item["E79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00505", "Value" => "#{decoded_item["F79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00506", "Value" => "#{decoded_item["G79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00507", "Value" => "#{decoded_item["H79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00508", "Value" => "#{decoded_item["I79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00509", "Value" => "#{decoded_item["J79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00510", "Value" => "#{decoded_item["K79"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00511", "Value" => "#{decoded_item["B80"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00512", "Value" => "#{decoded_item["C80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00513", "Value" => "#{decoded_item["D80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00514", "Value" => "#{decoded_item["E80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00515", "Value" => "#{decoded_item["F80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00516", "Value" => "#{decoded_item["G80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00517", "Value" => "#{decoded_item["H80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00518", "Value" => "#{decoded_item["I80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00519", "Value" => "#{decoded_item["J80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00520", "Value" => "#{decoded_item["K80"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00521", "Value" => "#{decoded_item["B81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00522", "Value" => "#{decoded_item["C81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00523", "Value" => "#{decoded_item["D81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00524", "Value" => "#{decoded_item["E81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00525", "Value" => "#{decoded_item["F81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00526", "Value" => "#{decoded_item["G81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00527", "Value" => "#{decoded_item["H81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00528", "Value" => "#{decoded_item["I81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00529", "Value" => "#{decoded_item["J81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00530", "Value" => "#{decoded_item["K81"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00531", "Value" => "#{decoded_item["B82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00532", "Value" => "#{decoded_item["C82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00533", "Value" => "#{decoded_item["D82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00534", "Value" => "#{decoded_item["E82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00535", "Value" => "#{decoded_item["F82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00536", "Value" => "#{decoded_item["G82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00537", "Value" => "#{decoded_item["H82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00538", "Value" => "#{decoded_item["I82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00539", "Value" => "#{decoded_item["J82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00540", "Value" => "#{decoded_item["K82"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00541", "Value" => "#{decoded_item["B83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00542", "Value" => "#{decoded_item["C83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00543", "Value" => "#{decoded_item["D83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00544", "Value" => "#{decoded_item["E83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00545", "Value" => "#{decoded_item["F83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00546", "Value" => "#{decoded_item["G83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00547", "Value" => "#{decoded_item["H83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00548", "Value" => "#{decoded_item["I83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00549", "Value" => "#{decoded_item["J83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00550", "Value" => "#{decoded_item["K83"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00551", "Value" => "#{decoded_item["B84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00552", "Value" => "#{decoded_item["C84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00553", "Value" => "#{decoded_item["D84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00554", "Value" => "#{decoded_item["E84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00555", "Value" => "#{decoded_item["F84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00556", "Value" => "#{decoded_item["G84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00557", "Value" => "#{decoded_item["H84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00558", "Value" => "#{decoded_item["I84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00559", "Value" => "#{decoded_item["J84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00560", "Value" => "#{decoded_item["K84"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00561", "Value" => "#{decoded_item["B85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00562", "Value" => "#{decoded_item["C85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00563", "Value" => "#{decoded_item["D85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00564", "Value" => "#{decoded_item["E85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00565", "Value" => "#{decoded_item["F85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00566", "Value" => "#{decoded_item["G85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00567", "Value" => "#{decoded_item["H85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00568", "Value" => "#{decoded_item["I85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00569", "Value" => "#{decoded_item["J85"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00570", "Value" => "#{decoded_item["K85"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00571", "Value" => "#{decoded_item["B87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00572", "Value" => "#{decoded_item["C87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00573", "Value" => "#{decoded_item["D87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00574", "Value" => "#{decoded_item["E87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00575", "Value" => "#{decoded_item["F87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00576", "Value" => "#{decoded_item["G87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00577", "Value" => "#{decoded_item["H87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00578", "Value" => "#{decoded_item["I87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00579", "Value" => "#{decoded_item["J87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00580", "Value" => "#{decoded_item["K87"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00581", "Value" => "#{decoded_item["B88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00582", "Value" => "#{decoded_item["C88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00583", "Value" => "#{decoded_item["D88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00584", "Value" => "#{decoded_item["E88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00585", "Value" => "#{decoded_item["F88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00586", "Value" => "#{decoded_item["G88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00587", "Value" => "#{decoded_item["H88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00588", "Value" => "#{decoded_item["I88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00589", "Value" => "#{decoded_item["J88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00590", "Value" => "#{decoded_item["K88"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00591", "Value" => "#{decoded_item["B91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00592", "Value" => "#{decoded_item["C91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00593", "Value" => "#{decoded_item["D91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00594", "Value" => "#{decoded_item["E91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00595", "Value" => "#{decoded_item["F91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00596", "Value" => "#{decoded_item["G91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00597", "Value" => "#{decoded_item["H91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00598", "Value" => "#{decoded_item["I91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00599", "Value" => "#{decoded_item["J91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00600", "Value" => "#{decoded_item["K91"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00601", "Value" => "#{decoded_item["B92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00602", "Value" => "#{decoded_item["C92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00603", "Value" => "#{decoded_item["D92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00604", "Value" => "#{decoded_item["E92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00605", "Value" => "#{decoded_item["F92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00606", "Value" => "#{decoded_item["G92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00607", "Value" => "#{decoded_item["H92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00608", "Value" => "#{decoded_item["I92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00609", "Value" => "#{decoded_item["J92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00610", "Value" => "#{decoded_item["K92"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00611", "Value" => "#{decoded_item["B93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00612", "Value" => "#{decoded_item["C93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00613", "Value" => "#{decoded_item["D93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00614", "Value" => "#{decoded_item["E93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00615", "Value" => "#{decoded_item["F93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00616", "Value" => "#{decoded_item["G93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00617", "Value" => "#{decoded_item["H93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00618", "Value" => "#{decoded_item["I93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00619", "Value" => "#{decoded_item["J93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00620", "Value" => "#{decoded_item["K93"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00621", "Value" => "#{decoded_item["B94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00622", "Value" => "#{decoded_item["C94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00623", "Value" => "#{decoded_item["D94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00624", "Value" => "#{decoded_item["E94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00625", "Value" => "#{decoded_item["F94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00626", "Value" => "#{decoded_item["G94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00627", "Value" => "#{decoded_item["H94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00628", "Value" => "#{decoded_item["I94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00629", "Value" => "#{decoded_item["J94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00630", "Value" => "#{decoded_item["K94"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00631", "Value" => "#{decoded_item["B95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00632", "Value" => "#{decoded_item["C95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00633", "Value" => "#{decoded_item["D95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00634", "Value" => "#{decoded_item["E95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00635", "Value" => "#{decoded_item["F95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00636", "Value" => "#{decoded_item["G95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00637", "Value" => "#{decoded_item["H95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00638", "Value" => "#{decoded_item["I95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00639", "Value" => "#{decoded_item["J95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00640", "Value" => "#{decoded_item["K95"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00641", "Value" => "#{decoded_item["B96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00642", "Value" => "#{decoded_item["C96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00643", "Value" => "#{decoded_item["D96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00644", "Value" => "#{decoded_item["E96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00645", "Value" => "#{decoded_item["F96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00646", "Value" => "#{decoded_item["G96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00647", "Value" => "#{decoded_item["H96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00648", "Value" => "#{decoded_item["I96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00649", "Value" => "#{decoded_item["J96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00650", "Value" => "#{decoded_item["K96"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00651", "Value" => "#{decoded_item["B97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00652", "Value" => "#{decoded_item["C97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00653", "Value" => "#{decoded_item["D97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00654", "Value" => "#{decoded_item["E97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00655", "Value" => "#{decoded_item["F97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00656", "Value" => "#{decoded_item["G97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00657", "Value" => "#{decoded_item["H97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00658", "Value" => "#{decoded_item["I97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00659", "Value" => "#{decoded_item["J97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00660", "Value" => "#{decoded_item["K97"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00661", "Value" => "#{decoded_item["B98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00662", "Value" => "#{decoded_item["C98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00663", "Value" => "#{decoded_item["D98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00664", "Value" => "#{decoded_item["E98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00665", "Value" => "#{decoded_item["F98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00666", "Value" => "#{decoded_item["G98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00667", "Value" => "#{decoded_item["H98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00668", "Value" => "#{decoded_item["I98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00669", "Value" => "#{decoded_item["J98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00670", "Value" => "#{decoded_item["K98"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00671", "Value" => "#{decoded_item["B99"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00672", "Value" => "#{decoded_item["C99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00673", "Value" => "#{decoded_item["D99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00674", "Value" => "#{decoded_item["E99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00675", "Value" => "#{decoded_item["F99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00676", "Value" => "#{decoded_item["G99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00677", "Value" => "#{decoded_item["H99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00678", "Value" => "#{decoded_item["I99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00679", "Value" => "#{decoded_item["J99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00680", "Value" => "#{decoded_item["K99"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00681", "Value" => "#{decoded_item["B100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00682", "Value" => "#{decoded_item["C100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00683", "Value" => "#{decoded_item["D100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00684", "Value" => "#{decoded_item["E100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00685", "Value" => "#{decoded_item["F100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00686", "Value" => "#{decoded_item["G100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00687", "Value" => "#{decoded_item["H100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00688", "Value" => "#{decoded_item["I100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00689", "Value" => "#{decoded_item["J100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00690", "Value" => "#{decoded_item["K100"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00691", "Value" => "#{decoded_item["B101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00692", "Value" => "#{decoded_item["C101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00693", "Value" => "#{decoded_item["D101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00694", "Value" => "#{decoded_item["E101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00695", "Value" => "#{decoded_item["F101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00696", "Value" => "#{decoded_item["G101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00697", "Value" => "#{decoded_item["H101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00698", "Value" => "#{decoded_item["I101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00699", "Value" => "#{decoded_item["J101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00700", "Value" => "#{decoded_item["K101"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00701", "Value" => "#{decoded_item["B102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00702", "Value" => "#{decoded_item["C102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00703", "Value" => "#{decoded_item["D102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00704", "Value" => "#{decoded_item["E102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00705", "Value" => "#{decoded_item["F102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00706", "Value" => "#{decoded_item["G102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00707", "Value" => "#{decoded_item["H102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00708", "Value" => "#{decoded_item["I102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00709", "Value" => "#{decoded_item["J102"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00710", "Value" => "#{decoded_item["K102"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00711", "Value" => "#{decoded_item["B103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00712", "Value" => "#{decoded_item["C103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00713", "Value" => "#{decoded_item["D103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00714", "Value" => "#{decoded_item["E103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00715", "Value" => "#{decoded_item["F103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00716", "Value" => "#{decoded_item["G103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00717", "Value" => "#{decoded_item["H103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00718", "Value" => "#{decoded_item["I103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00719", "Value" => "#{decoded_item["J103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00720", "Value" => "#{decoded_item["K103"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00721", "Value" => "#{decoded_item["B104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00722", "Value" => "#{decoded_item["C104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00723", "Value" => "#{decoded_item["D104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00724", "Value" => "#{decoded_item["E104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00725", "Value" => "#{decoded_item["F104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00726", "Value" => "#{decoded_item["G104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00727", "Value" => "#{decoded_item["H104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00728", "Value" => "#{decoded_item["I104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00729", "Value" => "#{decoded_item["J104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00730", "Value" => "#{decoded_item["K104"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00731", "Value" => "#{decoded_item["B105"] || "0"}", "_dataType" => "NUMERIC" }, # no items on template
          %{ "Code" => "1182_00732", "Value" => "#{decoded_item["C105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00733", "Value" => "#{decoded_item["D105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00734", "Value" => "#{decoded_item["E105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00735", "Value" => "#{decoded_item["F105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00736", "Value" => "#{decoded_item["G105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00737", "Value" => "#{decoded_item["H105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00738", "Value" => "#{decoded_item["I105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00739", "Value" => "#{decoded_item["J105"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00740", "Value" => "#{decoded_item["K105"] || "0"}l", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00741", "Value" => "#{decoded_item["B106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00742", "Value" => "#{decoded_item["C106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00743", "Value" => "#{decoded_item["D106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00744", "Value" => "#{decoded_item["E106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00745", "Value" => "#{decoded_item["F106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00746", "Value" => "#{decoded_item["G106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00747", "Value" => "#{decoded_item["H106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00748", "Value" => "#{decoded_item["I106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00749", "Value" => "#{decoded_item["J106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00750", "Value" => "#{decoded_item["K106"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00751", "Value" => "#{decoded_item["B107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00752", "Value" => "#{decoded_item["C107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00753", "Value" => "#{decoded_item["D107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00754", "Value" => "#{decoded_item["E107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00755", "Value" => "#{decoded_item["F107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00756", "Value" => "#{decoded_item["G107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00757", "Value" => "#{decoded_item["H107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00758", "Value" => "#{decoded_item["I107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00759", "Value" => "#{decoded_item["J107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00760", "Value" => "#{decoded_item["K107"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00761", "Value" => "#{decoded_item["B108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00762", "Value" => "#{decoded_item["C108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00763", "Value" => "#{decoded_item["D108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00764", "Value" => "#{decoded_item["E108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00765", "Value" => "#{decoded_item["F108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00766", "Value" => "#{decoded_item["G108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00767", "Value" => "#{decoded_item["H108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00768", "Value" => "#{decoded_item["I108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00769", "Value" => "#{decoded_item["J108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00770", "Value" => "#{decoded_item["K108"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00771", "Value" => "#{decoded_item["B109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00772", "Value" => "#{decoded_item["C109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00773", "Value" => "#{decoded_item["D109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00774", "Value" => "#{decoded_item["E109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00775", "Value" => "#{decoded_item["F109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00776", "Value" => "#{decoded_item["G109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00777", "Value" => "#{decoded_item["H109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00778", "Value" => "#{decoded_item["I109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00779", "Value" => "#{decoded_item["J109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00780", "Value" => "#{decoded_item["K109"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00781", "Value" => "#{decoded_item["B110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00782", "Value" => "#{decoded_item["C110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00783", "Value" => "#{decoded_item["D110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00784", "Value" => "#{decoded_item["E110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00785", "Value" => "#{decoded_item["F110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00786", "Value" => "#{decoded_item["G110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00787", "Value" => "#{decoded_item["H110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00788", "Value" => "#{decoded_item["I110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00789", "Value" => "#{decoded_item["J110"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00790", "Value" => "#{decoded_item["K110"] || "0"}", "_dataType" => "NUMERIC" },# no items on template
          %{ "Code" => "1182_00791", "Value" => "#{decoded_item["B111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00792", "Value" => "#{decoded_item["C111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00793", "Value" => "#{decoded_item["D111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00794", "Value" => "#{decoded_item["E111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00795", "Value" => "#{decoded_item["F111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00796", "Value" => "#{decoded_item["G111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00797", "Value" => "#{decoded_item["H111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00798", "Value" => "#{decoded_item["I111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00799", "Value" => "#{decoded_item["J111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00800", "Value" => "#{decoded_item["K111"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00801", "Value" => "#{decoded_item["B112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00802", "Value" => "#{decoded_item["C112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00803", "Value" => "#{decoded_item["D112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00804", "Value" => "#{decoded_item["E112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00805", "Value" => "#{decoded_item["F112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00806", "Value" => "#{decoded_item["G112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00807", "Value" => "#{decoded_item["H112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00808", "Value" => "#{decoded_item["I112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00809", "Value" => "#{decoded_item["J112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00810", "Value" => "#{decoded_item["K112"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00811", "Value" => "#{decoded_item["B113"] || "0"}", "_dataType" => "NUMERIC" }, #NO items on template
          %{ "Code" => "1182_00812", "Value" => "#{decoded_item["C113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00813", "Value" => "#{decoded_item["D113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00814", "Value" => "#{decoded_item["E113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00815", "Value" => "#{decoded_item["F113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00816", "Value" => "#{decoded_item["G113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00817", "Value" => "#{decoded_item["H113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00818", "Value" => "#{decoded_item["I113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00819", "Value" => "#{decoded_item["J113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00820", "Value" => "#{decoded_item["K113"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00821", "Value" => "#{decoded_item["B114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00822", "Value" => "#{decoded_item["C114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00823", "Value" => "#{decoded_item["D114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00824", "Value" => "#{decoded_item["E114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00825", "Value" => "#{decoded_item["F114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00826", "Value" => "#{decoded_item["G114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00827", "Value" => "#{decoded_item["H114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00828", "Value" => "#{decoded_item["I114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00829", "Value" => "#{decoded_item["J114"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00830", "Value" => "#{decoded_item["K114"] || "0"}", "_dataType" => "NUMERIC" },#NO items on template
          %{ "Code" => "1182_00831", "Value" => "#{decoded_item["B116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00832", "Value" => "#{decoded_item["C116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00833", "Value" => "#{decoded_item["D116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00834", "Value" => "#{decoded_item["E116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00835", "Value" => "#{decoded_item["F116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00836", "Value" => "#{decoded_item["G116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00837", "Value" => "#{decoded_item["H116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00838", "Value" => "#{decoded_item["I116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00839", "Value" => "#{decoded_item["J116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00840", "Value" => "#{decoded_item["K116"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00841", "Value" => "#{decoded_item["B117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00842", "Value" => "#{decoded_item["C117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00843", "Value" => "#{decoded_item["D117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00844", "Value" => "#{decoded_item["E117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00845", "Value" => "#{decoded_item["F117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00846", "Value" => "#{decoded_item["G117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00847", "Value" => "#{decoded_item["H117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00848", "Value" => "#{decoded_item["I117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00849", "Value" => "#{decoded_item["J117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00850", "Value" => "#{decoded_item["K117"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00851", "Value" => "#{decoded_item["B119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00852", "Value" => "#{decoded_item["C119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00853", "Value" => "#{decoded_item["D119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00854", "Value" => "#{decoded_item["E119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00855", "Value" => "#{decoded_item["F119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00856", "Value" => "#{decoded_item["G119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00857", "Value" => "#{decoded_item["H119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00858", "Value" => "#{decoded_item["I119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00859", "Value" => "#{decoded_item["J119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00860", "Value" => "#{decoded_item["K119"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00861", "Value" => "#{decoded_item["B120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00862", "Value" => "#{decoded_item["C120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00863", "Value" => "#{decoded_item["D120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00864", "Value" => "#{decoded_item["E120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00865", "Value" => "#{decoded_item["F120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00866", "Value" => "#{decoded_item["G120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00867", "Value" => "#{decoded_item["H120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00868", "Value" => "#{decoded_item["I120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00869", "Value" => "#{decoded_item["J120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00870", "Value" => "#{decoded_item["K120"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00871", "Value" => "#{decoded_item["B121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00872", "Value" => "#{decoded_item["C121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00873", "Value" => "#{decoded_item["D121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00874", "Value" => "#{decoded_item["E121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00875", "Value" => "#{decoded_item["F121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00876", "Value" => "#{decoded_item["G121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00877", "Value" => "#{decoded_item["H121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00878", "Value" => "#{decoded_item["I121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00879", "Value" => "#{decoded_item["J121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00880", "Value" => "#{decoded_item["K121"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00881", "Value" => "#{decoded_item["B122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00882", "Value" => "#{decoded_item["C122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00883", "Value" => "#{decoded_item["D122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00884", "Value" => "#{decoded_item["E122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00885", "Value" => "#{decoded_item["F122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00886", "Value" => "#{decoded_item["G122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00887", "Value" => "#{decoded_item["H122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00888", "Value" => "#{decoded_item["I122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00889", "Value" => "#{decoded_item["J122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00890", "Value" => "#{decoded_item["K122"] || "0"}", "_dataType" => "NUMERIC" },
          %{ "Code" => "1182_00891", "Value" => "#{decoded_item["B125"] || "0"}", "_dataType" => "NUMERIC" }
        ],
        "DynamicItemsList" => []
    }

  end

  def format_map(map) do
    Enum.map(map, fn {key, value} ->
      {key, format_number(value)}
    end)
    |> Map.new()
  end
  defp format_number(string) do
    String.replace(string || "0", ",", "")
  end
end
