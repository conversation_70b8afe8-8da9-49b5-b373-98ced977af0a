defmodule MisReports.Workers.BozReq.Schedule27a do
  alias MisReports.Utilities

  def perform(item) do
    decoded_item =
      case item.schedule_27a do
        nil -> %{}
        schedule when is_binary(schedule) ->
          case Poison.decode(schedule) do
            {:ok, decoded} -> decoded
            {:error, _} -> %{}
          end
      end

    formatted_item = format_scientific_values(decoded_item)
    settings = Utilities.get_comapany_settings_params()

    %{
      "returnKey" => "ZM-2NSCH27A2N002",
      "InstCode" => "#{settings.institution_code}",
      "FinYear" => 2014,
      "StartDate" => "2014-01-01T00:00:00",
      "EndDate" => "2014-01-31T00:00:00",
      "ReturnItemsList" => return_items_list(formatted_item),
      "DynamicItemsList" => dynamic_items_list(formatted_item)
    }
  end

  defp format_scientific_values(decoded_item) do
    list = get_in(decoded_item, ["list"]) || []
    updated_list = Enum.map(list, &process_item/1)
    Map.put(decoded_item, "list", updated_list)
  end

  defp process_item(%{"value" => value} = item) do
    Map.put(item, "value", convert_to_number(value))
  end
  defp process_item(item), do: item

  defp convert_to_number(%{"sign" => sign, "exp" => exp, "coef" => coef}) do
    value = coef * :math.pow(10, exp) * sign
    Decimal.from_float(value)
  end

  defp convert_to_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, _} -> Decimal.from_float(num)
      :error -> Decimal.new(0)
    end
  rescue
    _ -> Decimal.new(0)
  end

  defp convert_to_number(_), do: Decimal.new(0)

  defp return_items_list(decoded_item) do
    [
      %{
        "Code" => "1177_00001",
        "Value" => "#{get_in(decoded_item, ["B13"]) || Decimal.new(0)}",
        "_dataType" => "NUMERIC"
      }
    ]
  end

  defp dynamic_items_list(decoded_item) do
    list = get_in(decoded_item, ["list"]) || []

    [
      %{
        "Area" => 455,
        "_areaName" => "OTHER INTEREST EXPENSES",
        "DynamicItems" => generate_dynamic_items(list)
      }
    ]
  end

  defp generate_dynamic_items(items) do
    Enum.flat_map(Enum.with_index(items), fn {item, index} ->
      idx = index + 1
      [
        %{"Code" => "#{idx}.1", "Value" => "#{item["Account Name"] || "N/A"}", "_dataType" => "TEXT"},
        %{"Code" => "#{idx}.2", "Value" => "#{convert_to_number(item["amount"]) || Decimal.new(0)}", "_dataType" => "NUMERIC"},
        %{"Code" => "#{idx}.3", "Value" => "#{item["Details"] || "N/A"}", "_dataType" => "TEXT"}
      ]
    end)
  end
end
